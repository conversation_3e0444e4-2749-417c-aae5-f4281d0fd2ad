"""
Examples for using the shared API system

This module provides examples and templates for using the shared API system
to create API clients for different services.
"""

from .api_v1_config import create_api_v1_configuration, API_V1_CONFIG_EXAMPLE
from .new_api_config import create_new_api_configuration, NEW_API_CONFIG_EXAMPLE

__all__ = [
    "create_api_v1_configuration",
    "API_V1_CONFIG_EXAMPLE",
    "create_new_api_configuration", 
    "NEW_API_CONFIG_EXAMPLE",
]
