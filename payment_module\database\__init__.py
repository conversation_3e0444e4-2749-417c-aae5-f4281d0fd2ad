"""
Database operations for payment processing

This module contains all database operations:
- Payment operations (save, update, retrieve)
- User operations (balance, transactions)
"""

# Import all database functions
try:
    from .payment_operations import (
        save_payment_details,
        get_payment_by_track_id,
        get_payment_by_order_id,
        update_payment_status,
        update_payment_status_atomic,
        get_user_payments,
        get_user_pending_payments,
        get_latest_payment,
        ensure_payment_indexes,
        get_payment_statistics,
        get_payments_by_date_range,
        search_payments,
        get_payment_analytics,
        cleanup_old_payments,
        backup_payments,
        restore_payments,
    )
except ImportError as e:
    # Create placeholder functions if import fails
    def save_payment_details(*args, **kwargs):
        raise NotImplementedError(f"payment_operations not available: {e}")
    
    def get_payment_by_track_id(*args, **kwargs):
        raise NotImplementedError(f"payment_operations not available: {e}")
    
    def update_payment_status(*args, **kwargs):
        raise NotImplementedError(f"payment_operations not available: {e}")
    
    # Define other placeholders...
    get_payment_by_order_id = save_payment_details
    update_payment_status_atomic = save_payment_details
    get_user_payments = save_payment_details
    get_user_pending_payments = save_payment_details
    get_latest_payment = save_payment_details
    ensure_payment_indexes = save_payment_details
    get_payment_statistics = save_payment_details
    get_payments_by_date_range = save_payment_details
    search_payments = save_payment_details
    get_payment_analytics = save_payment_details
    cleanup_old_payments = save_payment_details
    backup_payments = save_payment_details
    restore_payments = save_payment_details

try:
    from .user_operations import (
        get_user,
        get_or_create_user,
        get_user_balance,
        update_user_balance,
        get_user_balance_async,
        update_user_balance_async,
        add_transaction,
        add_transaction_async,
        get_user_transactions,
    )
except ImportError as e:
    # Create placeholder functions if import fails
    def get_user(*args, **kwargs):
        raise NotImplementedError(f"user_operations not available: {e}")
    
    get_or_create_user = get_user
    get_user_balance = get_user
    update_user_balance = get_user
    get_user_balance_async = get_user
    update_user_balance_async = get_user
    add_transaction = get_user
    add_transaction_async = get_user
    get_user_transactions = get_user

# Export all functions
__all__ = [
    # Payment operations
    'save_payment_details',
    'get_payment_by_track_id',
    'get_payment_by_order_id',
    'update_payment_status',
    'update_payment_status_atomic',
    'get_user_payments',
    'get_user_pending_payments',
    'get_latest_payment',
    'ensure_payment_indexes',
    'get_payment_statistics',
    'get_payments_by_date_range',
    'search_payments',
    'get_payment_analytics',
    'cleanup_old_payments',
    'backup_payments',
    'restore_payments',
    
    # User operations
    'get_user',
    'get_or_create_user',
    'get_user_balance',
    'update_user_balance',
    'get_user_balance_async',
    'update_user_balance_async',
    'add_transaction',
    'add_transaction_async',
    'get_user_transactions',
]
