"""
Enhanced UI components for response processor display.
Provides consistent formatting and user-friendly display of API responses.
"""

from typing import Dict, Any, List, Optional
import json
from datetime import datetime

from utils.central_logger import get_logger

logger = get_logger()


class ResponseDisplayFormatter:
    """Enhanced formatter for response processor UI components."""
    
    def __init__(self):
        self.max_json_length = 15000  # Prevent telegram message limits
        self.truncation_threshold = 10000
    
    def format_api_response_summary(self, 
                                   endpoint: str, 
                                   response_data: Dict[str, Any],
                                   processing_time: Optional[float] = None) -> str:
        """
        Format API response summary for Telegram display.
        
        Args:
            endpoint: API endpoint name
            response_data: Response data to summarize
            processing_time: Optional processing time in seconds
            
        Returns:
            Formatted summary string
        """
        try:
            summary = f"🔍 <b>API Response Summary</b>\n\n"
            summary += f"📡 <b>Endpoint:</b> <code>{endpoint}</code>\n"
            
            if processing_time:
                summary += f"⏱️ <b>Processing Time:</b> {processing_time*1000:.1f}ms\n"
            
            summary += f"📅 <b>Timestamp:</b> {datetime.now().strftime('%H:%M:%S')}\n\n"
            
            # Response structure analysis
            summary += self._analyze_response_structure(response_data)
            
            # Content preview
            preview = self._generate_content_preview(response_data)
            if preview:
                summary += f"\n📋 <b>Content Preview:</b>\n{preview}"
            
            return summary
            
        except Exception as e:
            logger.error(f"Error formatting response summary: {e}")
            return f"❌ Error formatting response summary: {e}"
    
    def format_card_extraction_summary(self, 
                                     extracted_cards: List[Dict[str, Any]],
                                     processing_time: float) -> str:
        """
        Format card extraction results for display.
        
        Args:
            extracted_cards: List of extracted card data
            processing_time: Processing time in seconds
            
        Returns:
            Formatted summary string
        """
        try:
            card_count = len(extracted_cards)
            
            summary = f"💳 <b>Card Extraction Results</b>\n\n"
            summary += f"📊 <b>Cards Found:</b> {card_count}\n"
            summary += f"⏱️ <b>Processing Time:</b> {processing_time*1000:.1f}ms\n"
            summary += f"📅 <b>Extracted At:</b> {datetime.now().strftime('%H:%M:%S')}\n\n"
            
            if card_count == 0:
                summary += "ℹ️ No cards found in the response.\n"
                return summary
            
            # Sample card analysis
            sample_card = extracted_cards[0]
            valid_fields = [k for k, v in sample_card.items() if v not in ["", None, "Unknown"]]
            
            summary += f"🔍 <b>Sample Card Analysis:</b>\n"
            summary += f"• Valid Fields: {len(valid_fields)}\n"
            summary += f"• Sample Fields: {', '.join(valid_fields[:5])}\n"
            
            if len(valid_fields) > 5:
                summary += f"• Plus {len(valid_fields) - 5} more fields\n"
            
            # Cards preview (limit to first few)
            if card_count <= 3:
                summary += f"\n💳 <b>All Cards:</b>\n"
                for i, card in enumerate(extracted_cards, 1):
                    summary += self._format_single_card_preview(card, i)
            else:
                summary += f"\n💳 <b>First 3 Cards:</b>\n"
                for i, card in enumerate(extracted_cards[:3], 1):
                    summary += self._format_single_card_preview(card, i)
                summary += f"\n📝 <i>...and {card_count - 3} more cards</i>\n"
            
            return summary
            
        except Exception as e:
            logger.error(f"Error formatting card extraction summary: {e}")
            return f"❌ Error formatting extraction summary: {e}"
    
    def _analyze_response_structure(self, data: Dict[str, Any]) -> str:
        """Analyze and format response structure."""
        try:
            analysis = "📊 <b>Structure Analysis:</b>\n"
            
            if isinstance(data, dict):
                analysis += f"• Type: Dictionary ({len(data)} keys)\n"
                
                # Common API v3 patterns
                if "sections" in data:
                    sections = data["sections"]
                    if isinstance(sections, list):
                        analysis += f"• Sections: {len(sections)} found\n"
                        
                        total_tables = 0
                        for section in sections:
                            if isinstance(section, dict) and "tables" in section:
                                tables = section.get("tables", [])
                                if isinstance(tables, list):
                                    total_tables += len(tables)
                        
                        if total_tables > 0:
                            analysis += f"• Tables: {total_tables} total\n"
                
                if "rows" in data and "headers" in data:
                    headers = data["headers"]
                    rows = data["rows"]
                    analysis += f"• Table Format: {len(headers)} columns, {len(rows)} rows\n"
                
                # Key sampling
                if len(data) <= 8:
                    analysis += f"• Keys: {', '.join(data.keys())}\n"
                else:
                    sample_keys = list(data.keys())[:5]
                    analysis += f"• Keys: {', '.join(sample_keys)}... (+{len(data)-5} more)\n"
            
            elif isinstance(data, list):
                analysis += f"• Type: List ({len(data)} items)\n"
            
            else:
                analysis += f"• Type: {type(data).__name__}\n"
            
            return analysis
            
        except Exception as e:
            return f"• Structure: Analysis failed ({e})\n"
    
    def _generate_content_preview(self, data: Any, max_lines: int = 8) -> str:
        """Generate a content preview for the response."""
        try:
            if isinstance(data, dict):
                preview_lines = []
                line_count = 0
                
                for key, value in data.items():
                    if line_count >= max_lines:
                        preview_lines.append("...")
                        break
                    
                    if isinstance(value, (str, int, float, bool)):
                        value_str = str(value)
                        if len(value_str) > 50:
                            value_str = value_str[:47] + "..."
                        preview_lines.append(f"<code>{key}: {value_str}</code>")
                        line_count += 1
                    elif isinstance(value, list):
                        preview_lines.append(f"<code>{key}: [list with {len(value)} items]</code>")
                        line_count += 1
                    elif isinstance(value, dict):
                        preview_lines.append(f"<code>{key}: {{dict with {len(value)} keys}}</code>")
                        line_count += 1
                
                return "\n".join(preview_lines)
            
            return "<i>Complex data structure - use JSON view for details</i>"
            
        except Exception as e:
            return f"<i>Preview generation failed: {e}</i>"
    
    def _format_single_card_preview(self, card: Dict[str, Any], index: int) -> str:
        """Format a single card for preview display."""
        try:
            preview = f"\n🔸 <b>Card {index}:</b>\n"
            
            # Show key fields first
            priority_fields = ['number', 'brand', 'type', 'bank', 'country']
            shown_fields = []
            
            for field in priority_fields:
                if field in card and card[field] not in ["", None, "Unknown"]:
                    value = str(card[field])
                    if len(value) > 30:
                        value = value[:27] + "..."
                    preview += f"  • {field.title()}: <code>{value}</code>\n"
                    shown_fields.append(field)
            
            # Show other fields if space allows
            other_fields = [k for k in card.keys() if k not in shown_fields and card[k] not in ["", None, "Unknown"]]
            if other_fields and len(shown_fields) < 4:
                remaining_slots = 4 - len(shown_fields)
                for field in other_fields[:remaining_slots]:
                    value = str(card[field])
                    if len(value) > 30:
                        value = value[:27] + "..."
                    preview += f"  • {field.title()}: <code>{value}</code>\n"
            
            return preview
            
        except Exception as e:
            return f"\n🔸 <b>Card {index}:</b> <i>Display error: {e}</i>\n"
    
    def format_json_for_telegram(self, data: Any, compact: bool = False) -> str:
        """
        Format JSON data for Telegram display with proper truncation.
        
        Args:
            data: Data to format as JSON
            compact: Whether to use compact formatting
            
        Returns:
            Formatted JSON string suitable for Telegram
        """
        try:
            if compact:
                json_str = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            else:
                json_str = json.dumps(data, indent=2, ensure_ascii=False)
            
            # Truncate if too long
            if len(json_str) > self.max_json_length:
                truncated = json_str[:self.truncation_threshold]
                
                # Try to end at a reasonable point
                last_line_break = truncated.rfind('\n')
                if last_line_break > self.truncation_threshold - 500:
                    truncated = truncated[:last_line_break]
                
                json_str = truncated + f"\n\n... (truncated, {len(json_str) - len(truncated)} chars omitted)"
            
            return f"<pre><code class=\"json\">{json_str}</code></pre>"
            
        except Exception as e:
            logger.error(f"Error formatting JSON for Telegram: {e}")
            return f"<pre>JSON formatting error: {e}</pre>"


# Global instance for easy access
_display_formatter = None

def get_display_formatter() -> ResponseDisplayFormatter:
    """Get the global display formatter instance."""
    global _display_formatter
    if _display_formatter is None:
        _display_formatter = ResponseDisplayFormatter()
    return _display_formatter