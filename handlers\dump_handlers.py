"""
Telegram bot handlers for dump operations (both dumps v1 and vdumps v2)
"""

from __future__ import annotations

from typing import Dict, Any, Optional

from aiogram import Router, F
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
from aiogram.filters import Command, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from models.user import User
from services.dump_service import get_dump_service, DumpService
from services.user_service import get_user_service
from utils.keyboards import back_keyboard
from utils.loading_animations import LoadingStages, BROWSE_STAGES


def format_price(price) -> str:
    """Simple price formatting function"""
    try:
        if isinstance(price, (int, float)):
            return f"{price:.2f}"
        elif isinstance(price, str):
            return f"{float(price):.2f}"
        return "0.00"
    except (ValueError, TypeError):
        return "0.00"


from utils.central_logger import get_logger

logger = get_logger()


class DumpStates(StatesGroup):
    """States for dump operations"""

    BROWSING_DUMPS_V1 = State()
    BROWSING_VDUMPS_V2 = State()
    DUMP_DETAILS = State()
    DUMP_FILTERS = State()


class DumpHandlers:
    """Handlers for dump operations"""

    def __init__(self):
        self.dump_service = get_dump_service()
        self.user_service = get_user_service()

    async def show_dump_menu(self, message: Message, state: FSMContext) -> None:
        """Show main dump menu"""
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🗂️ DUMPS v1", callback_data="dumps_v1_list"
                    ),
                    InlineKeyboardButton(
                        text="📦 VDUMPS v2", callback_data="vdumps_v2_list"
                    ),
                ],
                [
                    InlineKeyboardButton(text="🛒 Cart", callback_data="dump_cart"),
                    InlineKeyboardButton(text="📋 Orders", callback_data="dump_orders"),
                ],
                [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")],
            ]
        )

        text = (
            "🗂️ <b>DUMP Cards</b>\n\n"
            "Choose your dump type:\n"
            "• <b>DUMPS v1</b> - Full card dumps with complete data sets\n"
            "• <b>VDUMPS v2</b> - Virtual dumps with enhanced features\n\n"
            "💡 Both types use the same authentication and cart system"
        )

        await message.edit_text(text, reply_markup=keyboard)

    async def list_dumps_v1(
        self, callback: CallbackQuery, state: FSMContext, page: int = 1
    ) -> None:
        """List dumps v1 with pagination"""
        try:
            # Get filters from state
            data = await state.get_data()
            filters = data.get("dump_filters", {})
            
            # Show loading stages concurrently with dumps loading
            async def dumps_work():
                return await self.dump_service.browse_dumps(
                    page=page, limit=10, filters=filters, api_version="v1"
                )
            
            response = await LoadingStages.run_concurrent_loading(
                callback,
                BROWSE_STAGES,
                dumps_work(),
                operation_name="Dumps V1 Loading"
            )

            if not response.get("success"):
                await callback.message.edit_text(
                    "❌ Failed to load dumps v1. Please try again.",
                    reply_markup=back_keyboard("dump_menu"),
                )
                return

            dumps = response.get("data", [])
            total_count = response.get("totalCount", 0)

            if not dumps:
                text = "📭 No dumps v1 found with current filters."
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔄 Reset Filters",
                                callback_data="reset_dump_filters",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="⬅️ Back", callback_data="dump_menu"
                            )
                        ],
                    ]
                )
                await callback.message.edit_text(text, reply_markup=keyboard)
                return

            # Create dump list text
            text = f"🗂️ <b>DUMPS v1</b> (Page {page})\n"
            text += f"📊 Total: {total_count} dumps\n\n"

            # Create inline keyboard with dumps
            keyboard_buttons = []

            for dump in dumps[:10]:  # Limit to 10 per page
                dump_info = (
                    f"💳 {dump.get('bin', 'N/A')} | "
                    f"{dump.get('brand', 'N/A')} | "
                    f"${format_price(dump.get('price', 0))} | "
                    f"{dump.get('country', 'N/A')}"
                )
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text=dump_info,
                            callback_data=f"dump_v1_details_{dump.get('_id')}",
                        )
                    ]
                )

            # Add pagination and control buttons
            control_buttons = []

            # Previous page
            if page > 1:
                control_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Prev", callback_data=f"dumps_v1_page_{page-1}"
                    )
                )

            # Page info
            max_page = (total_count + 9) // 10  # Ceiling division
            control_buttons.append(
                InlineKeyboardButton(
                    text=f"{page}/{max_page}", callback_data="page_info"
                )
            )

            # Next page
            if page * 10 < total_count:
                control_buttons.append(
                    InlineKeyboardButton(
                        text="Next ➡️", callback_data=f"dumps_v1_page_{page+1}"
                    )
                )

            keyboard_buttons.append(control_buttons)

            # Add filter and back buttons
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔍 Filters", callback_data="dump_v1_filters"
                    ),
                    InlineKeyboardButton(text="🛒 Cart", callback_data="dump_cart"),
                ]
            )
            keyboard_buttons.append(
                [InlineKeyboardButton(text="⬅️ Back", callback_data="dump_menu")]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(text, reply_markup=keyboard)
            await state.set_state(DumpStates.BROWSING_DUMPS_V1)

        except Exception as e:
            logger.error(f"Error listing dumps v1: {e}", exc_info=True)
            await callback.message.edit_text(
                "❌ Error loading dumps v1. Please try again.",
                reply_markup=back_keyboard("dump_menu"),
            )

    async def list_vdumps_v2(
        self, callback: CallbackQuery, state: FSMContext, page: int = 1
    ) -> None:
        """List vdumps v2 with pagination"""

        try:
            # Get filters from state if any
            data = await state.get_data()
            filters = data.get("vdump_filters", {})

            response = await self.dump_service.list_vdumps(
                page=page, limit=10, **filters
            )

            if not response.get("success"):
                await callback.message.edit_text(
                    "❌ Failed to load vdumps v2. Please try again.",
                    reply_markup=back_keyboard("dump_menu"),
                )
                return

            vdumps = response.get("data", [])
            total_count = response.get("totalCount", 0)

            if not vdumps:
                text = "📭 No vdumps v2 found with current filters."
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔄 Reset Filters",
                                callback_data="reset_vdump_filters",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="⬅️ Back", callback_data="dump_menu"
                            )
                        ],
                    ]
                )
                await callback.message.edit_text(text, reply_markup=keyboard)
                return

            # Create vdump list text
            text = f"📦 <b>VDUMPS v2</b> (Page {page})\n"
            text += f"📊 Total: {total_count} vdumps\n\n"

            # Create inline keyboard with vdumps
            keyboard_buttons = []

            for vdump in vdumps[:10]:  # Limit to 10 per page
                vdump_info = (
                    f"💳 {vdump.get('bin', 'N/A')} | "
                    f"{vdump.get('brand', 'N/A')} | "
                    f"${format_price(vdump.get('price', 0))} | "
                    f"{vdump.get('country', 'N/A')}"
                )
                keyboard_buttons.append(
                    [
                        InlineKeyboardButton(
                            text=vdump_info,
                            callback_data=f"vdump_v2_details_{vdump.get('_id')}",
                        )
                    ]
                )

            # Add pagination and control buttons
            control_buttons = []

            # Previous page
            if page > 1:
                control_buttons.append(
                    InlineKeyboardButton(
                        text="⬅️ Prev", callback_data=f"vdumps_v2_page_{page-1}"
                    )
                )

            # Page info
            max_page = (total_count + 9) // 10  # Ceiling division
            control_buttons.append(
                InlineKeyboardButton(
                    text=f"{page}/{max_page}", callback_data="page_info"
                )
            )

            # Next page
            if page * 10 < total_count:
                control_buttons.append(
                    InlineKeyboardButton(
                        text="Next ➡️", callback_data=f"vdumps_v2_page_{page+1}"
                    )
                )

            keyboard_buttons.append(control_buttons)

            # Add filter and back buttons
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔍 Filters", callback_data="vdump_v2_filters"
                    ),
                    InlineKeyboardButton(text="🛒 Cart", callback_data="dump_cart"),
                ]
            )
            keyboard_buttons.append(
                [InlineKeyboardButton(text="⬅️ Back", callback_data="dump_menu")]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(text, reply_markup=keyboard)
            await state.set_state(DumpStates.BROWSING_VDUMPS_V2)

        except Exception as e:
            logger.error(f"Error listing vdumps v2: {e}", exc_info=True)
            await callback.message.edit_text(
                "❌ Error loading vdumps v2. Please try again.",
                reply_markup=back_keyboard("dump_menu"),
            )

    async def show_dump_details(
        self, callback: CallbackQuery, state: FSMContext, dump_id: str, dump_type: str
    ) -> None:
        """Show dump details"""

        try:
            # Get dump details from previous listings or fetch again
            data = await state.get_data()

            # Create details text (you can enhance this by fetching individual dump details if available)
            text = f"🗂️ <b>Dump Details</b>\n\n"
            text += f"ID: {dump_id}\n"
            text += f"Type: {dump_type.upper()}\n\n"
            text += "Use the buttons below to add to cart or buy directly:"

            if dump_type == "vdump_v2":
                keyboard_buttons = [
                    [
                        InlineKeyboardButton(
                            text="🛒 Add to Cart",
                            callback_data=f"add_vdump_cart_{dump_id}",
                        ),
                        InlineKeyboardButton(
                            text="💳 Buy Now", callback_data=f"buy_vdump_{dump_id}"
                        ),
                    ]
                ]
            else:  # dump_v1
                keyboard_buttons = [
                    [
                        InlineKeyboardButton(
                            text="🛒 Add to Cart",
                            callback_data=f"add_dump_cart_{dump_id}",
                        )
                    ]
                ]

            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="⬅️ Back", callback_data=f"{dump_type}s_list"
                    )
                ]
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
            await callback.message.edit_text(text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error showing dump details: {e}", exc_info=True)
            await callback.message.edit_text(
                "❌ Error loading dump details.",
                reply_markup=back_keyboard("dump_menu"),
            )

    async def add_dump_to_cart(
        self, callback: CallbackQuery, dump_id: str, product_table_name: str
    ) -> None:
        """Add dump to cart"""
        await callback.answer("Adding to cart...")

        try:
            response = await self.dump_service.add_dump_to_cart(
                dump_id=int(dump_id), product_table_name=product_table_name
            )

            if response.get("success"):
                await callback.answer("✅ Added to cart!", show_alert=True)
            else:
                await callback.answer("❌ Failed to add to cart", show_alert=True)

        except Exception as e:
            logger.error(f"Error adding dump to cart: {e}", exc_info=True)
            await callback.answer("❌ Error adding to cart", show_alert=True)

    async def buy_vdump_directly(self, callback: CallbackQuery, dump_id: str) -> None:
        """Buy vdump directly (v2 only)"""
        await callback.answer("Processing purchase...")

        try:
            response = await self.dump_service.buy_vdump(int(dump_id))

            if response.get("success"):
                await callback.answer("✅ Purchase successful!", show_alert=True)
                await callback.message.edit_text(
                    "✅ <b>Purchase Successful!</b>\n\n"
                    f"VDump ID: {dump_id}\n"
                    "Check your orders to download the dump data.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="📋 Orders", callback_data="dump_orders"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="⬅️ Back", callback_data="dump_menu"
                                )
                            ],
                        ]
                    ),
                )
            else:
                await callback.answer("❌ Purchase failed", show_alert=True)

        except Exception as e:
            logger.error(f"Error buying vdump: {e}", exc_info=True)
            await callback.answer("❌ Error processing purchase", show_alert=True)

    async def show_dump_cart(self, callback: CallbackQuery) -> None:
        """Show dump cart contents"""

        try:
            response = await self.dump_service.get_cart()

            if not response.get("success"):
                await callback.message.edit_text(
                    "❌ Failed to load cart.", reply_markup=back_keyboard("dump_menu")
                )
                return

            cart_items = response.get("data", [])
            total_price = response.get("totalCartPrice", 0)

            if not cart_items:
                text = "🛒 <b>Your Cart is Empty</b>\n\nAdd some dumps to get started!"
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🗂️ Browse Dumps", callback_data="dumps_v1_list"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="⬅️ Back", callback_data="dump_menu"
                            )
                        ],
                    ]
                )
            else:
                text = f"🛒 <b>Your Cart</b>\n\n"

                for item in cart_items:
                    text += (
                        f"💳 {item.get('bin', 'N/A')} | "
                        f"{item.get('brand', 'N/A')} | "
                        f"${format_price(item.get('price', 0))}\n"
                        f"📍 {item.get('state', 'N/A')}, {item.get('country', 'N/A')}\n\n"
                    )

                text += f"💰 <b>Total: ${format_price(total_price)}</b>"

                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💳 Checkout", callback_data="dump_checkout"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🗑️ Clear Cart", callback_data="clear_dump_cart"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="⬅️ Back", callback_data="dump_menu"
                            )
                        ],
                    ]
                )

            await callback.message.edit_text(text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error showing dump cart: {e}", exc_info=True)
            await callback.message.edit_text(
                "❌ Error loading cart.", reply_markup=back_keyboard("dump_menu")
            )

    async def checkout_dump_cart(self, callback: CallbackQuery) -> None:
        """Checkout dump cart"""
        await callback.answer("Processing checkout...")

        try:
            response = await self.dump_service.checkout_cart()

            if response.get("success"):
                await callback.answer("✅ Checkout successful!", show_alert=True)
                await callback.message.edit_text(
                    "✅ <b>Checkout Successful!</b>\n\n"
                    "Your order has been processed. Check your orders to download the dump data.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="📋 Orders", callback_data="dump_orders"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="⬅️ Back", callback_data="dump_menu"
                                )
                            ],
                        ]
                    ),
                )
            else:
                await callback.answer("❌ Checkout failed", show_alert=True)

        except Exception as e:
            logger.error(f"Error checking out dump cart: {e}", exc_info=True)
            await callback.answer("❌ Error processing checkout", show_alert=True)

    async def show_dump_orders(self, callback: CallbackQuery, page: int = 1) -> None:
        """Show dump orders"""

        try:
            response = await self.dump_service.get_dump_orders(page=page, limit=10)

            if not response.get("success"):
                await callback.message.edit_text(
                    "❌ Failed to load orders.", reply_markup=back_keyboard("dump_menu")
                )
                return

            orders = response.get("data", [])
            total_count = response.get("totalCount", 0)

            if not orders:
                text = "📋 <b>No Orders Found</b>\n\nYou haven't made any dump purchases yet."
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🗂️ Browse Dumps", callback_data="dumps_v1_list"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="⬅️ Back", callback_data="dump_menu"
                            )
                        ],
                    ]
                )
            else:
                text = f"📋 <b>Your Orders</b> (Page {page})\n"
                text += f"📊 Total: {total_count} orders\n\n"

                keyboard_buttons = []

                for order in orders:
                    order_info = (
                        f"💳 {order.get('bin', 'N/A')} | "
                        f"${format_price(order.get('price', 0))} | "
                        f"{order.get('status', 'N/A')}"
                    )
                    keyboard_buttons.append(
                        [
                            InlineKeyboardButton(
                                text=order_info,
                                callback_data=f"download_dump_{order.get('_id')}",
                            )
                        ]
                    )

                # Add pagination if needed
                control_buttons = []
                if page > 1:
                    control_buttons.append(
                        InlineKeyboardButton(
                            text="⬅️ Prev", callback_data=f"dump_orders_page_{page-1}"
                        )
                    )
                if page * 10 < total_count:
                    control_buttons.append(
                        InlineKeyboardButton(
                            text="Next ➡️", callback_data=f"dump_orders_page_{page+1}"
                        )
                    )

                if control_buttons:
                    keyboard_buttons.append(control_buttons)

                keyboard_buttons.append(
                    [InlineKeyboardButton(text="⬅️ Back", callback_data="dump_menu")]
                )

                keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Error showing dump orders: {e}", exc_info=True)
            await callback.message.edit_text(
                "❌ Error loading orders.", reply_markup=back_keyboard("dump_menu")
            )

    async def download_dump(self, callback: CallbackQuery, order_id: str) -> None:
        """Download dump data"""
        await callback.answer("Downloading dump data...")

        try:
            response = await self.dump_service.download_single_dump(int(order_id))

            if response.get("success"):
                dump_data = response.get("data", "")

                # Send dump data as a message (you might want to send as file instead)
                text = f"📄 <b>Dump Data</b>\n\nOrder ID: {order_id}\n\n```\n{dump_data}\n```"

                await callback.message.reply(
                    text,
                    parse_mode="HTML",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="📋 Back to Orders",
                                    callback_data="dump_orders",
                                )
                            ]
                        ]
                    ),
                )

                await callback.answer("✅ Dump data downloaded!", show_alert=True)

            else:
                await callback.answer(
                    "❌ Failed to download dump data", show_alert=True
                )

        except Exception as e:
            logger.error(f"Error downloading dump: {e}", exc_info=True)
            await callback.answer("❌ Error downloading dump data", show_alert=True)


def get_dump_router() -> Router:
    """Get dump router with handlers"""
    router = Router()
    handlers = DumpHandlers()

    # Command handlers
    @router.message(Command("dumps"))
    async def cmd_dumps(message: Message, state: FSMContext):
        await handlers.show_dump_menu(message, state)

    # Callback handlers
    @router.callback_query(F.data == "dump_menu")
    async def cb_dump_menu(callback: CallbackQuery, state: FSMContext):
        await handlers.show_dump_menu(callback.message, state)

    @router.callback_query(F.data == "dumps_v1_list")
    async def cb_dumps_v1_list(callback: CallbackQuery, state: FSMContext):
        await handlers.list_dumps_v1(callback, state)

    @router.callback_query(F.data.startswith("dumps_v1_page_"))
    async def cb_dumps_v1_page(callback: CallbackQuery, state: FSMContext):
        page = int(callback.data.split("_")[-1])
        await handlers.list_dumps_v1(callback, state, page)

    @router.callback_query(F.data == "vdumps_v2_list")
    async def cb_vdumps_v2_list(callback: CallbackQuery, state: FSMContext):
        await handlers.list_vdumps_v2(callback, state)

    @router.callback_query(F.data.startswith("vdumps_v2_page_"))
    async def cb_vdumps_v2_page(callback: CallbackQuery, state: FSMContext):
        page = int(callback.data.split("_")[-1])
        await handlers.list_vdumps_v2(callback, state, page)

    @router.callback_query(F.data.startswith("dump_v1_details_"))
    async def cb_dump_v1_details(callback: CallbackQuery, state: FSMContext):
        dump_id = callback.data.split("_")[-1]
        await handlers.show_dump_details(callback, state, dump_id, "dump_v1")

    @router.callback_query(F.data.startswith("vdump_v2_details_"))
    async def cb_vdump_v2_details(callback: CallbackQuery, state: FSMContext):
        dump_id = callback.data.split("_")[-1]
        await handlers.show_dump_details(callback, state, dump_id, "vdump_v2")

    @router.callback_query(F.data.startswith("add_dump_cart_"))
    async def cb_add_dump_cart(callback: CallbackQuery):
        dump_id = callback.data.split("_")[-1]
        await handlers.add_dump_to_cart(callback, dump_id, "dumps")

    @router.callback_query(F.data.startswith("add_vdump_cart_"))
    async def cb_add_vdump_cart(callback: CallbackQuery):
        dump_id = callback.data.split("_")[-1]
        await handlers.add_dump_to_cart(callback, dump_id, "vdumps")

    @router.callback_query(F.data.startswith("buy_vdump_"))
    async def cb_buy_vdump(callback: CallbackQuery):
        dump_id = callback.data.split("_")[-1]
        await handlers.buy_vdump_directly(callback, dump_id)

    @router.callback_query(F.data == "dump_cart")
    async def cb_dump_cart(callback: CallbackQuery):
        await handlers.show_dump_cart(callback)

    @router.callback_query(F.data == "dump_checkout")
    async def cb_dump_checkout(callback: CallbackQuery):
        await handlers.checkout_dump_cart(callback)

    @router.callback_query(F.data == "dump_orders")
    async def cb_dump_orders(callback: CallbackQuery):
        await handlers.show_dump_orders(callback)

    @router.callback_query(F.data.startswith("dump_orders_page_"))
    async def cb_dump_orders_page(callback: CallbackQuery):
        page = int(callback.data.split("_")[-1])
        await handlers.show_dump_orders(callback, page)

    @router.callback_query(F.data.startswith("download_dump_"))
    async def cb_download_dump(callback: CallbackQuery):
        order_id = callback.data.split("_")[-1]
        await handlers.download_dump(callback, order_id)

    return router
