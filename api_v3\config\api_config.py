"""
API v3 Configuration

Configuration management for API v3.
"""

from __future__ import annotations

import os
from typing import Optional

from pydantic import BaseModel, Field

from ..utils.tor_detector import auto_detect_tor_port, TorPortDetector

from utils.central_logger import get_logger

logger = get_logger()


class APIV3Config(BaseModel):
    """Configuration for API v3 with performance optimizations"""

    base_url: str = Field(..., description="API base URL")
    username: str = Field(..., description="Username for authentication")
    password: str = Field(..., description="Password for authentication")
    use_socks_proxy: bool = Field(
        default=False, description="Whether to use SOCKS proxy"
    )
    socks_url: str = Field(
        default="socks5h://127.0.0.1:9150", description="SOCKS proxy URL"
    )
    timeout: int = Field(default=25, description="Request timeout in seconds (optimized)")
    
    # Performance optimization settings
    connection_pool_size: int = Field(default=15, description="HTTP connection pool size")
    max_retries: int = Field(default=2, description="Maximum request retries")
    cache_ttl: int = Field(default=300, description="Response cache TTL in seconds")
    max_cache_size: int = Field(default=1000, description="Maximum cached responses")
    enable_compression: bool = Field(default=True, description="Enable HTTP compression")
    
    class Config:
        """Pydantic config"""
        frozen = False


def get_api_v3_config_from_env() -> Optional[APIV3Config]:
    """
    Load API v3 configuration from environment variables.

    Expected environment variables:
    - EXTERNAL_V3_BASE_URL or API_V3_BASE_URL
    - EXTERNAL_V3_USERNAME or API_V3_USERNAME
    - EXTERNAL_V3_PASSWORD or API_V3_PASSWORD
    - EXTERNAL_V3_USE_TOR_PROXY or API_V3_USE_SOCKS_PROXY (optional)
    - EXTERNAL_V3_SOCKS_URL or API_V3_SOCKS_URL (optional)
    """
    # Try different environment variable names for compatibility
    base_url = (
        os.getenv("EXTERNAL_V3_BASE_URL")
        or os.getenv("API_V3_BASE_URL")
        or os.getenv("DEMO_API_BASE")
    )
    username = (
        os.getenv("EXTERNAL_V3_USERNAME")
        or os.getenv("API_V3_USERNAME")
        or os.getenv("USERNAME")
    )
    password = (
        os.getenv("EXTERNAL_V3_PASSWORD")
        or os.getenv("API_V3_PASSWORD")
        or os.getenv("PASSWORD")
    )

    if not base_url or not username or not password:
        logger.warning("API v3 configuration not found in environment variables")
        return None

    # Check for proxy settings
    use_socks_proxy = (
        os.getenv("EXTERNAL_V3_USE_TOR_PROXY", "").lower() in ("true", "1", "yes")
        or os.getenv("API_V3_USE_SOCKS_PROXY", "").lower() in ("true", "1", "yes")
        or os.getenv("USE_SOCKS_PROXY", "").lower() in ("true", "1", "yes")
        or ".onion" in base_url
    )

    # Try to get SOCKS URL from environment first
    socks_url = (
        os.getenv("EXTERNAL_V3_SOCKS_URL")
        or os.getenv("API_V3_SOCKS_URL")
        or os.getenv("SOCKS_URL")
    )

    # If no SOCKS URL is configured and we need proxy, auto-detect Tor port
    if not socks_url and use_socks_proxy:
        logger.info("No SOCKS URL configured, attempting to auto-detect Tor port...")
        auto_detected_url = auto_detect_tor_port()
        if auto_detected_url:
            socks_url = auto_detected_url
            logger.info(f"✅ Auto-detected Tor SOCKS proxy: {socks_url}")
        else:
            logger.warning("❌ Could not auto-detect Tor port, using default")
            socks_url = "socks5h://127.0.0.1:9150"
    elif not socks_url:
        # Default fallback
        socks_url = "socks5h://127.0.0.1:9150"

    return APIV3Config(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks_proxy,
        socks_url=socks_url,
    )


def create_api_v3_configuration(
    base_url: Optional[str] = None,
    username: Optional[str] = None,
    password: Optional[str] = None,
    use_socks_proxy: Optional[bool] = None,
    socks_url: Optional[str] = None,
) -> APIV3Config:
    """
    Create API v3 configuration.

    Args:
        base_url: API base URL (if None, loads from env)
        username: Username (if None, loads from env)
        password: Password (if None, loads from env)
        use_socks_proxy: Whether to use SOCKS proxy (if None, loads from env)
        socks_url: SOCKS proxy URL (if None, loads from env)

    Returns:
        APIV3Config instance
    """
    # Try to load from environment if not provided
    if not base_url or not username or not password:
        env_config = get_api_v3_config_from_env()
        if env_config:
            return APIV3Config(
                base_url=base_url or env_config.base_url,
                username=username or env_config.username,
                password=password or env_config.password,
                use_socks_proxy=(
                    use_socks_proxy
                    if use_socks_proxy is not None
                    else env_config.use_socks_proxy
                ),
                socks_url=socks_url or env_config.socks_url,
            )

    # Validate required fields
    if not base_url:
        raise ValueError("base_url is required for API v3 configuration")
    if not username:
        raise ValueError("username is required for API v3 configuration")
    if not password:
        raise ValueError("password is required for API v3 configuration")

    # Auto-detect proxy need for .onion domains
    if use_socks_proxy is None:
        use_socks_proxy = ".onion" in base_url

    # Final auto-detection attempt if still no SOCKS URL
    if not socks_url and use_socks_proxy:
        auto_detected_url = auto_detect_tor_port()
        if auto_detected_url:
            socks_url = auto_detected_url
            logger.info(f"✅ Final auto-detection successful: {socks_url}")
        else:
            socks_url = "socks5h://127.0.0.1:9150"
            logger.warning("❌ Auto-detection failed, using default port 9150")

    return APIV3Config(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks_proxy,
        socks_url=socks_url or "socks5h://127.0.0.1:9150",
    )

