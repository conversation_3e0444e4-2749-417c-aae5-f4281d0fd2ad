# API v1 Cart Endpoints - Quick Reference

## Service Initialization

```python
from api_v1.services.cart_service import get_cart_service

cart_service = get_cart_service()
```

## Endpoints

### 1. View Cart
```python
result = await cart_service.view_cart(user_id="12345")
# Returns: {"items": [...], "total_price": 23.98, "item_count": 2}
```

### 2. List Orders
```python
result = await cart_service.list_orders(
    category="hq",
    page=1,
    limit=10,
    user_id="12345"
)
# Returns: {"orders": [...], "total_count": 35, "page": 1, "total_pages": 4}
```

### 3. View Order
```python
result = await cart_service.view_order(
    order_id=347387,
    category="hq",
    user_id="12345"
)
# Returns: {"order": {...full order details...}}
```

### 4. Check Order
```python
result = await cart_service.check_order(
    order_id=347387,
    category="hq",
    user_id="12345"
)
# Returns: {"order": {...with status: "NonRefundable"...}}
```

### 5. Download Order
```python
result = await cart_service.download_order(
    order_id=347387,
    category="hq",
    user_id="12345"
)
# Returns: {"raw_text": "...", "headers": "...", "data": "...", "format": "pipe-delimited"}
```

## Response Structure

All methods return a `BaseResponse` object:

```python
response.success       # True/False
response.message       # "Operation successful"
response.data          # {...response data...}
response.error_code    # "ERROR_CODE" or None
response.timestamp     # datetime object
```

## Error Handling

```python
try:
    result = await cart_service.view_cart()
    if result.success:
        # Process data
        data = result.data
    else:
        # Handle error
        print(f"Error: {result.message} (code: {result.error_code})")
except Exception as e:
    print(f"Exception: {e}")
```

## Common Error Codes

- `CONFIG_NOT_FOUND` - API configuration not found
- `CART_FETCH_ERROR` - Failed to fetch cart
- `ORDERS_FETCH_ERROR` - Failed to fetch orders
- `ORDER_VIEW_ERROR` - Failed to view order
- `ORDER_CHECK_ERROR` - Failed to check order
- `ORDER_DOWNLOAD_ERROR` - Failed to download order
- `INVALID_RESPONSE` - Invalid API response

## Examples

### Complete Workflow
```python
# 1. View cart
cart = await cart_service.view_cart()
print(f"Cart: {cart.data['item_count']} items, ${cart.data['total_price']}")

# 2. List orders
orders = await cart_service.list_orders(page=1, limit=5)
print(f"Orders: {orders.data['total_count']} total")

# 3. View first order
if orders.data['orders']:
    order_id = orders.data['orders'][0]['_id']
    order = await cart_service.view_order(order_id)
    print(f"Order #{order_id}: {order.data['order']['status']}")
    
    # 4. Check order (if allowed)
    if order.data['order'].get('canCheck'):
        check = await cart_service.check_order(order_id)
        print(f"Checked: {check.success}")
    
    # 5. Download order
    download = await cart_service.download_order(order_id)
    with open(f"order_{order_id}.txt", "w") as f:
        f.write(download.data['raw_text'])
```

## Import Paths

```python
# Service
from api_v1.services.cart_service import get_cart_service, CartService, CartServiceError

# Base classes
from api_v1.core.base import BaseResponse, BaseService

# Exceptions
from api_v1.core.exceptions import APIv1Exception
```

## Configuration

The service uses the unified API configuration. Ensure you have:

1. Valid `api_v1_base` configuration in database
2. Login token in credentials
3. Session cookies configured
4. Base URL set correctly

## Documentation

- **Full API Docs**: [CART_ENDPOINTS.md](./CART_ENDPOINTS.md)
- **Integration Guide**: [INTEGRATION_GUIDE.md](./INTEGRATION_GUIDE.md)
- **Examples**: [examples/cart_service_example.py](./examples/cart_service_example.py)
- **Implementation Summary**: [IMPLEMENTATION_SUMMARY.md](./IMPLEMENTATION_SUMMARY.md)

## Support

For issues or questions, refer to the complete documentation files above.

