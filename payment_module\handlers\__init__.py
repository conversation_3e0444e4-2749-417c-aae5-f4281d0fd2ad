"""
Payment handlers for Telegram bot

This module contains all the Telegram bot handlers for payment processing:
- Deposit handler
- Payment verification handler
- Manual verification handler
"""

# Import routers from handler modules
try:
    from .deposit import router as deposit_router
except ImportError:
    deposit_router = None

try:
    from .payment_verification import router as payment_verification_router
except ImportError:
    payment_verification_router = None

try:
    from .manual_verification import router as manual_verification_router
except ImportError:
    manual_verification_router = None

# Export all routers
__all__ = [
    'deposit_router',
    'payment_verification_router',
    'manual_verification_router',
]
