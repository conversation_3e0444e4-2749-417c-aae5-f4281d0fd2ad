#!/usr/bin/env python3
"""
Initialize API Configurations for Admin Panel

This script creates sample API v1 and v2 configurations for testing the admin panel
when the database is not available or needs to be populated with initial data.
"""

import sys
import os
import asyncio
from typing import Dict, Any, List

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

async def create_sample_api_configs():
    """Create sample API configurations for testing"""
    try:
        print("🔧 INITIALIZING API CONFIGURATIONS")
        print("=" * 50)
        
        # Try to connect to database first
        print("\n1️⃣ Connecting to database...")
        
        try:
            from database.connection import connect_to_database, get_db_manager
            await connect_to_database()
            print("✅ Database connection successful")
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            print("💡 Creating in-memory configurations for testing...")
            await create_in_memory_configs()
            return
        
        print("\n2️⃣ Creating API configurations...")
        
        from admin.services.shared_api_admin_service import get_shared_api_admin_service
        admin_service = get_shared_api_admin_service()
        
        # Create API v1 configuration
        print("   Creating API v1...")
        try:
            success, message, config = await admin_service.create_api_configuration(
                name="api1",
                base_url="https://api1.example.com",
                created_by="admin_system",
                display_name="API v1 - Primary BIN API",
                description="Primary BIN lookup API with comprehensive card data",
                environment="production",
                category="bin_cards",
                auth_type="bearer_token",
                auth_data={"token": "sample-token-v1"}
            )
            
            if success:
                print(f"   ✅ API v1 created: {message}")
            else:
                print(f"   ⚠️ API v1 already exists or error: {message}")
                
        except Exception as e:
            print(f"   ❌ Error creating API v1: {e}")
        
        # Create API v2 configuration
        print("   Creating API v2...")
        try:
            success = await admin_service.ensure_api_v2_enabled()
            
            if success:
                print(f"   ✅ API v2 enabled successfully")
            else:
                print(f"   ⚠️ API v2 already exists or error")
                
        except Exception as e:
            print(f"   ❌ Error creating API v2: {e}")
        
        # Create additional sample API
        print("   Creating sample API...")
        try:
            success, message, config = await admin_service.create_api_configuration(
                name="sample_api",
                base_url="https://api.sample.com",
                created_by="admin_system",
                display_name="Sample API - Demo",
                description="Sample API for testing admin panel functionality",
                environment="development",
                category="general",
                auth_type="api_key",
                auth_data={"api_key": "demo-key", "header": "X-API-Key"}
            )
            
            if success:
                print(f"   ✅ Sample API created: {message}")
            else:
                print(f"   ⚠️ Sample API already exists or error: {message}")
                
        except Exception as e:
            print(f"   ❌ Error creating sample API: {e}")
        
        print("\n3️⃣ Verifying configurations...")
        
        try:
            api_configs = await admin_service.list_api_configurations()
            print(f"✅ Total configurations: {len(api_configs)}")
            
            for config in api_configs:
                status_icon = "🟢" if config.get("enabled", True) else "🔴"
                print(f"   {status_icon} {config['name']} - {config['display_name']}")
                print(f"      URL: {config['base_url']}")
                print(f"      Environment: {config['environment']}")
                print(f"      Auth: {config['auth_type']}")
                print()
                
        except Exception as e:
            print(f"❌ Error verifying configurations: {e}")
        
        print("🎯 INITIALIZATION COMPLETE")
        print("✅ API configurations are ready for admin panel testing")
        
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        import traceback
        traceback.print_exc()

async def create_in_memory_configs():
    """Create in-memory configurations for testing without database"""
    print("📝 Creating in-memory API configurations...")
    
    # Sample configurations that would normally be in database
    sample_configs = [
        {
            "name": "api1",
            "display_name": "API v1 - Primary BIN API",
            "description": "Primary BIN lookup API with comprehensive card data",
            "base_url": "https://api1.example.com",
            "environment": "production",
            "enabled": True,
            "category": "bin_cards",
            "auth_type": "bearer_token",
            "endpoints_count": 5,
            "health_status": "healthy",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        {
            "name": "api2",
            "display_name": "API v2 - BASE 2 Browse API",
            "description": "Secondary BIN API with VHQ endpoints",
            "base_url": "https://api2.example.com",
            "environment": "production",
            "enabled": True,
            "category": "bin_cards",
            "auth_type": "bearer_token",
            "endpoints_count": 3,
            "health_status": "healthy",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        {
            "name": "sample_api",
            "display_name": "Sample API - Demo",
            "description": "Sample API for testing admin panel functionality",
            "base_url": "https://api.sample.com",
            "environment": "development",
            "enabled": True,
            "category": "general",
            "auth_type": "api_key",
            "endpoints_count": 2,
            "health_status": "unknown",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]
    
    print(f"✅ Created {len(sample_configs)} in-memory configurations:")
    for config in sample_configs:
        print(f"   • {config['name']} - {config['display_name']}")
    
    print("\n💡 To use these configurations:")
    print("   1. Start MongoDB database")
    print("   2. Run this script again to persist configurations")
    print("   3. Or modify admin service to use in-memory fallback")

if __name__ == "__main__":
    asyncio.run(create_sample_api_configs())
