"""
Payment Module Documentation

This module provides a complete, standalone payment system for Telegram bots using OXA Pay gateway.

## Features

- 💳 **Multi-Currency Support**: Accept payments in various cryptocurrencies
- 🔄 **Automatic Conversion**: Convert all payments to USDT equivalent
- 🔐 **Secure Processing**: HMAC signature verification for all callbacks
- ⚡ **Real-time Verification**: Instant payment status checking
- 🛡️ **Error Handling**: Comprehensive error handling and recovery
- 📱 **Telegram Integration**: Ready-to-use aiogram handlers and keyboards
- 🌐 **Callback Server**: Flask-based callback server for payment notifications
- 💰 **User Balance Management**: Complete user balance tracking and updates
- 📊 **Transaction Recording**: Full transaction history and audit trail
- ⚠️ **Underpayment Detection**: Automatic underpayment detection and handling
- 💸 **Overpayment Processing**: Overpayment detection with account crediting
- 🎁 **Bonus System**: Configurable deposit bonuses with multiple tiers
- 👑 **VIP Integration**: Automatic VIP tier checking and assignment
- 📈 **Metrics Tracking**: Payment statistics and performance monitoring
- 🔄 **Transaction Management**: Rollback-capable transaction processing
- 📝 **Template System**: Customizable message templates
- 🔒 **HMAC Verification**: Comprehensive HMAC signature verification system
- 🤖 **Auto Verification**: Automatic payment verification with scheduling
- 🔍 **Manual Verification**: Complete manual payment verification handlers
- 📋 **Button Flow Documentation**: Detailed button flow documentation and validation
- 🗄️ **Enhanced Database Operations**: Advanced database operations and analytics

## Quick Start

### 1. Installation

```bash
# Copy the payment_module folder to your project
# Install required dependencies
pip install aiogram aiohttp flask waitress pymongo
```

### 2. Basic Setup

```python
from payment_module import create_payment_module

# Initialize payment module
payment_module = create_payment_module(
    api_key="your_oxa_pay_api_key",
    callback_url="https://yourdomain.com/callback"  # Optional
)

# Get handlers for aiogram dispatcher
handlers = payment_module.get_handlers()
for handler in handlers:
    dp.include_router(handler.router)
```

### 3. Environment Variables

Set these environment variables:

```bash
export OXA_PAY_API_KEY="your_api_key_here"
export OXA_PAY_CALLBACK_URL="https://yourdomain.com/callback"  # Optional
export DEVELOPMENT_MODE="false"  # Set to "true" for sandbox mode
export TESTING_MODE="false"  # Set to "true" to skip HMAC verification
export DEBUG_MODE="false"  # Set to "true" for debug logging
```

### 4. Start Callback Server

```python
from payment_module.core.flask_server import run_callback_server

# Run callback server (in production, use a proper WSGI server)
run_callback_server(host="0.0.0.0", port=3000)
```

## API Reference

### Core Functions

#### `create_payment_link(amount, order_id, user_id, ...)`

Creates a payment link for OXA Pay.

**Parameters:**
- `amount` (float): Payment amount
- `order_id` (str): Unique order identifier
- `user_id` (str): User identifier
- `description` (str): Payment description
- `currency` (str): Payment currency (default: "USDT")
- `lifetime` (int): Payment link lifetime in minutes (default: 120)

**Returns:**
```python
{
    "status": "success",
    "trackId": "track_id_here",
    "payLink": "https://payment_url",
    "orderId": "order_id_here",
    "amount": 100.0,
    "callback_url": "callback_url_here"
}
```

#### `convert_currency(amount, from_currency, to_currency)`

Converts cryptocurrency amounts.

**Parameters:**
- `amount` (float): Amount to convert
- `from_currency` (str): Source currency code
- `to_currency` (str): Target currency code (default: "USDT")

**Returns:**
```python
(converted_amount, {
    "success": True,
    "from_currency": "BTC",
    "to_currency": "USDT",
    "amount": 0.001,
    "converted_amount": 45.50,
    "exchange_rate": 45500.0
})
```

#### `process_payment_with_conversion(payment_data, track_id, target_currency)`

Processes payment data with automatic currency conversion.

**Parameters:**
- `payment_data` (dict): Payment data from OXA Pay
- `track_id` (str): Payment tracking ID
- `target_currency` (str): Target currency (default: "USDT")

**Returns:**
```python
{
    "converted_amount": 100.0,
    "conversion_details": [...],
    "is_converted": True,
    "conversion_errors": []
}
```

#### `check_oxapay_payment(track_id, api_key)`

Verifies payment status with OXA Pay.

**Parameters:**
- `track_id` (str): Payment tracking ID
- `api_key` (str): OXA Pay API key

**Returns:**
```python
{
    "status": "completed",
    "amount": 100.0,
    "currency": "USDT",
    "track_id": "track_id_here",
    "data": {...}
}
```

### Database Operations

#### `get_user_balance(user_id)`

Gets user's current balance.

**Parameters:**
- `user_id` (int): User ID

**Returns:**
```python
100.0  # Current balance
```

#### `update_user_balance(user_id, new_balance)`

Updates user's balance.

**Parameters:**
- `user_id` (int): User ID
- `new_balance` (float): New balance amount

**Returns:**
```python
True  # Success
```

#### `add_transaction(user_id, transaction_type, amount, **kwargs)`

Records a transaction.

**Parameters:**
- `user_id` (int): User ID
- `transaction_type` (str): Type of transaction
- `amount` (float): Transaction amount
- `**kwargs`: Additional transaction data

**Returns:**
```python
{
    "user_id": 12345,
    "type": "deposit",
    "amount": 100.0,
    "timestamp": datetime.now(),
    "_id": "transaction_id"
}
```

### Payment Amount Handling

#### `check_payment_amounts(actual_amount, required_amount, track_id)`

Checks if payment is underpaid, overpaid, or normal.

**Parameters:**
- `actual_amount` (float): Amount actually received
- `required_amount` (float): Amount required
- `track_id` (str): Payment tracking ID

**Returns:**
```python
("underpayment", {
    "actual_amount": 90.0,
    "required_amount": 100.0,
    "difference": -10.0,
    "percentage": 90.0
})
```

#### `handle_underpayment(bot, user_id, track_id, received_amount, required_amount, payment_url)`

Handles underpayment detection and notification.

**Parameters:**
- `bot`: Telegram bot instance
- `user_id` (int): User ID
- `track_id` (str): Payment tracking ID
- `received_amount` (float): Amount received
- `required_amount` (float): Amount required
- `payment_url` (str): Payment URL for completion

**Returns:**
```python
True  # Success
```

#### `handle_overpayment(bot, user_id, track_id, received_amount, required_amount)`

Handles overpayment detection and notification.

**Parameters:**
- `bot`: Telegram bot instance
- `user_id` (int): User ID
- `track_id` (str): Payment tracking ID
- `received_amount` (float): Amount received
- `required_amount` (float): Amount required

**Returns:**
```python
True  # Success
```

### Bonus System

#### `calculate_deposit_bonus(amount, user_id)`

Calculates deposit bonus based on amount and user.

**Parameters:**
- `amount` (float): Deposit amount
- `user_id` (int): User ID (optional)

**Returns:**
```python
{
    "success": True,
    "bonus_amount": 5.0,
    "total_credited": 105.0,
    "tier_used": {
        "threshold": 100.0,
        "bonus_percentage": 0.05,
        "bonus_type": "percentage",
        "name": "Starter Bonus"
    }
}
```

### HMAC Verification System

#### `create_hmac_verifier(api_key, testing_mode)`

Creates HMAC verifier instance for signature verification.

**Parameters:**
- `api_key` (str): OXA Pay API key
- `testing_mode` (bool): Skip HMAC verification in testing

**Returns:**
```python
HMACVerifier instance
```

#### `verify_payment_callback(request_data, headers, api_key)`

Verifies payment callback with HMAC signature.

**Parameters:**
- `request_data` (bytes): Raw request data
- `headers` (dict): Request headers
- `api_key` (str): API key for verification

**Returns:**
```python
(True, "Verification successful", parsed_data)
```

#### `generate_payment_signature(data, api_key)`

Generates HMAC signature for payment data.

**Parameters:**
- `data` (dict): Payment data dictionary
- `api_key` (str): API key for signing

**Returns:**
```python
"a1b2c3d4e5f6..."  # HMAC signature
```

### Automatic Payment Verification

#### `get_verification_manager()`

Gets the global verification manager instance.

**Returns:**
```python
AutoVerificationManager instance
```

#### `add_payment_for_verification(track_id, user_id, amount, callback_url, metadata)`

Adds payment for automatic verification.

**Parameters:**
- `track_id` (str): Payment tracking ID
- `user_id` (int): User ID
- `amount` (float): Payment amount
- `callback_url` (str): Optional callback URL
- `metadata` (dict): Optional metadata

**Returns:**
```python
True  # Success
```

#### `get_payment_verification_status(track_id)`

Gets payment verification status.

**Parameters:**
- `track_id` (str): Payment tracking ID

**Returns:**
```python
VerificationStatus.PENDING  # Status enum
```

#### `get_verification_statistics()`

Gets verification statistics.

**Returns:**
```python
{
    "total_verifications": 100,
    "successful_verifications": 95,
    "failed_verifications": 5,
    "success_rate": 95.0,
    "active_tasks": 10,
    "running": True
}
```

### Manual Payment Verification

#### Manual Verification Commands

- `/verify` - Start manual payment verification
- Manual verification handlers with complete underpayment/overpayment detection
- Interactive verification flow with detailed status checking

### Enhanced Database Operations

#### `get_payment_statistics(user_id, days)`

Gets comprehensive payment statistics.

**Parameters:**
- `user_id` (int): User ID (optional)
- `days` (int): Number of days to analyze

**Returns:**
```python
{
    "total_payments": 50,
    "total_amount": 5000.0,
    "success_rate": 90.0,
    "underpayment_rate": 5.0,
    "overpayment_rate": 5.0,
    "by_status": {
        "completed": {"count": 45, "total_amount": 4500.0},
        "underpaid": {"count": 3, "total_amount": 300.0},
        "overpaid": {"count": 2, "total_amount": 200.0}
    }
}
```

#### `get_payments_by_date_range(start_date, end_date, user_id, status, limit)`

Gets payments within a date range.

**Parameters:**
- `start_date` (datetime): Start date
- `end_date` (datetime): End date
- `user_id` (int): Optional user ID filter
- `status` (str): Optional status filter
- `limit` (int): Maximum results

**Returns:**
```python
[{"track_id": "ABC123", "amount": 100.0, "status": "completed", ...}]
```

#### `search_payments(query_text, user_id, limit)`

Searches payments by track_id, order_id, or description.

**Parameters:**
- `query_text` (str): Search query
- `user_id` (int): Optional user ID filter
- `limit` (int): Maximum results

**Returns:**
```python
[{"track_id": "ABC123", "amount": 100.0, "description": "Deposit", ...}]
```

#### `get_payment_analytics(user_id, days)`

Gets comprehensive payment analytics.

**Parameters:**
- `user_id` (int): User ID (optional)
- `days` (int): Number of days to analyze

**Returns:**
```python
{
    "statistics": {...},
    "daily_trends": [...],
    "amount_distribution": [...],
    "status_trends": {...},
    "summary": {
        "total_payments": 100,
        "total_amount": 10000.0,
        "success_rate": 90.0,
        "avg_daily_payments": 3.3,
        "avg_daily_amount": 333.33
    }
}
```

#### `cleanup_old_payments(days_to_keep)`

Cleans up old payment records.

**Parameters:**
- `days_to_keep` (int): Number of days to keep records

**Returns:**
```python
50  # Number of records deleted
```

#### `backup_payments(backup_path)`

Backs up payment records to file.

**Parameters:**
- `backup_path` (str): Path to backup file

**Returns:**
```python
True  # Success
```

#### `restore_payments(backup_path)`

Restores payment records from backup file.

**Parameters:**
- `backup_path` (str): Path to backup file

**Returns:**
```python
True  # Success
```

### Button Flow Documentation

#### `get_button_flow_documentation(flow_name)`

Gets documentation for a specific button flow.

**Parameters:**
- `flow_name` (str): Name of the flow

**Returns:**
```python
{
    "name": "Deposit Flow",
    "description": "Complete user deposit process",
    "steps": [...],
    "error_handling": {...},
    "success_completion": {...}
}
```

#### `get_button_action_info(callback_data)`

Gets button action information.

**Parameters:**
- `callback_data` (str): Callback data to look up

**Returns:**
```python
{
    "flow": "deposit_flow",
    "step": "Amount Selection",
    "action": "Select $10 amount",
    "button_text": "$10"
}
```

#### `validate_button_action(callback_data, current_state)`

Validates button action for current state.

**Parameters:**
- `callback_data` (str): Callback data to validate
- `current_state` (str): Current FSM state

**Returns:**
```python
True  # Valid action
```

### Handlers

#### Deposit Handler (`/deposit` command)

Handles the complete deposit flow:
1. Amount selection (predefined or custom)
2. Payment link generation
3. Payment verification
4. Balance updates
5. Underpayment/overpayment handling
6. Bonus calculation
7. VIP tier checking

**Callback Data:**
- `select_amount:{amount}` - Select predefined amount
- `custom_amount` - Enter custom amount
- `pay_deposit:{amount}` - Process payment
- `verify_latest_payment` - Verify payment manually
- `cancel_deposit` - Cancel deposit process
- `check_payment:{track_id}` - Check specific payment status
- `complete_underpayment:{track_id}` - Complete underpaid payment

#### Payment Verification Handler

Handles payment verification and status checking:
- `verify_latest_payment` - Verify latest payment
- `check_payment:{track_id}` - Check specific payment
- `handle_underpayment` - Handle underpayment scenarios
- `handle_overpayment` - Handle overpayment scenarios
- `view_balance` - View user balance
- `transaction_history` - View transaction history

### Keyboards

#### `deposit_amount_keyboard()`
Shows predefined amounts ($10, $20, $50, $100, $200, $500) and custom option.

#### `deposit_pay_keyboard(amount)`
Shows "Pay Now" and "Cancel" buttons for payment confirmation.

#### `payment_verification_keyboard()`
Shows "Try Again" and "Return to Main" buttons for failed verifications.

#### `payment_success_keyboard()`
Shows "View Balance" and "Return to Main" buttons for successful payments.

#### `underpayment_keyboard(track_id, payment_url)`
Shows "Complete Payment", "Check Status", and "Cancel" buttons for underpaid payments.

#### `overpayment_keyboard(track_id)`
Shows "Check Status", "View Balance", and "Contact Support" buttons for overpaid payments.

## Payment Scenarios

### Normal Payment (95-110% of required amount)
- ✅ Payment processed normally
- ✅ Balance updated with full amount
- ✅ Bonus calculated and applied
- ✅ VIP tier checked and updated
- ✅ Transaction recorded

### Underpayment (< 95% of required amount)
- ⚠️ Status updated to "underpaid"
- ⚠️ User notified with remaining amount
- ⚠️ Payment completion options provided
- ⚠️ Transaction recorded as underpaid

### Slight Underpayment (95-99% of required amount)
- ✅ Processed as completed (within tolerance)
- ✅ Balance updated with received amount
- ✅ Bonus calculated normally
- ✅ Transaction recorded as completed

### Overpayment (110-150% of required amount)
- ✅ Payment processed successfully
- ✅ Full amount credited to account
- ✅ Overpayment logged for monitoring
- ✅ Bonus calculated on required amount
- ✅ Transaction recorded with overpayment details

### Significant Overpayment (> 150% of required amount)
- 🚨 Payment processed with admin review flag
- 🚨 Full amount credited to account
- 🚨 Special logging for admin monitoring
- 🚨 User notified of admin review requirement
- 🚨 Transaction recorded with significant overpayment flag

## Configuration

### Payment Configuration (`payment_config.py`)

```python
# API Configuration
OXA_PAY_API_KEY = "your_api_key"

# Server Configuration
DEFAULT_HOST = "0.0.0.0"
DEFAULT_PORT = 3000
CALLBACK_PATH = "/callback"

# Environment Detection
DEVELOPMENT_MODE = True  # Automatically detected
TESTING_MODE = False  # Skip HMAC verification in tests
DEBUG_MODE = False  # Enable debug logging
```

### Currency Configuration

Supported currencies:
- **USD Stablecoins**: USDT, USDC, DAI (1:1 USD equivalent)
- **Major Cryptocurrencies**: BTC, ETH, BNB, SOL, etc.
- **Automatic Conversion**: All payments converted to USDT

### Underpayment/Overpayment Thresholds

```python
# Configurable thresholds
UNDERPAYMENT_THRESHOLD = 0.95  # 95% of required amount
SLIGHT_UNDERPAYMENT_THRESHOLD = 0.99  # 99% of required amount (within tolerance)
OVERPAYMENT_THRESHOLD = 1.1  # 110% of required amount (10% over)
SIGNIFICANT_OVERPAYMENT_THRESHOLD = 1.5  # 150% of required amount (50% over)
```

### Bonus System Configuration

```python
# Default bonus tiers
BONUS_TIERS = [
    {"threshold": 50, "bonus_percentage": 0.02, "name": "Starter Bonus"},
    {"threshold": 100, "bonus_percentage": 0.05, "name": "Standard Bonus"},
    {"threshold": 500, "bonus_percentage": 0.10, "name": "Premium Bonus"},
    {"threshold": 1000, "bonus_percentage": 0.15, "name": "VIP Bonus"}
]
```

### VIP System Configuration

```python
# VIP tier thresholds
VIP_TIERS = [
    {"name": "Bronze", "threshold": 100, "benefits": ["Priority Support"]},
    {"name": "Silver", "threshold": 500, "benefits": ["Priority Support", "Higher Bonuses"]},
    {"name": "Gold", "threshold": 1000, "benefits": ["Priority Support", "Higher Bonuses", "Exclusive Access"]},
    {"name": "Platinum", "threshold": 5000, "benefits": ["All Benefits", "Personal Manager"]}
]
```

## Security Features

### HMAC Verification

All payment callbacks are verified using HMAC-SHA512 signatures:

```python
def verify_hmac_signature(data_bytes, hmac_header, api_secret_key):
    expected_signature = hmac.new(
        api_secret_key.encode('utf-8'),
        data_bytes,
        hashlib.sha512
    ).hexdigest()
    return hmac.compare_digest(expected_signature, hmac_header)
```

### Callback Security

- **HMAC Header Required**: All callbacks must include valid HMAC signature
- **Atomic Updates**: Prevents race conditions in payment processing
- **Duplicate Prevention**: Checks for already processed payments
- **User ID Validation**: Multiple fallback methods for user identification

## Error Handling

### Payment Generation Errors

```python
{
    "status": "error",
    "message": "Error description",
    "error_type": "error_type",
    "raw_response": {...}
}
```

### Verification Errors

```python
{
    "status": "error",
    "message": "Verification failed",
    "raw_response": None
}
```

### Common Error Scenarios

1. **Invalid Amount**: Amount below minimum or above maximum
2. **API Errors**: OXA Pay API communication issues
3. **Network Timeouts**: Connection timeout handling
4. **Invalid Currency**: Unsupported currency codes
5. **Callback Failures**: HMAC verification failures
6. **Underpayment Errors**: Insufficient payment amounts
7. **Overpayment Errors**: Excessive payment amounts requiring review
8. **Database Errors**: Connection or transaction failures
9. **Balance Update Errors**: User balance update failures
10. **Transaction Recording Errors**: Transaction history recording failures

## Integration Examples

### Basic Bot Integration

```python
from aiogram import Bot, Dispatcher
from payment_module import create_payment_module

# Initialize bot
bot = Bot(token="your_bot_token")
dp = Dispatcher()

# Initialize payment module
payment_module = create_payment_module(
    api_key="your_oxa_pay_api_key"
)

# Register handlers
handlers = payment_module.get_handlers()
for handler in handlers:
    dp.include_router(handler.router)

# Start bot
await dp.start_polling(bot)
```

### Custom Payment Processing

```python
from payment_module.core.payment_link import create_payment_link
from payment_module.core.oxa_verify import check_oxapay_payment
from payment_module.utils.payment_amount_handler import check_payment_amounts, handle_underpayment
from payment_module.database.user_operations import get_user_balance, update_user_balance, add_transaction

# Create payment
payment_data = await create_payment_link(
    amount=100.0,
    order_id="ORDER_123",
    user_id="12345",
    description="Custom payment"
)

if payment_data["status"] == "success":
    track_id = payment_data["trackId"]
    
    # Verify payment later
    verification = await check_oxapay_payment(track_id, api_key)
    if verification["status"] == "completed":
        # Check for underpayment/overpayment
        status, details = check_payment_amounts(
            verification["amount"], 
            100.0, 
            track_id
        )
        
        if status == "underpayment":
            # Handle underpayment
            await handle_underpayment(
                bot, 12345, track_id, 
                verification["amount"], 100.0, 
                payment_data["payLink"]
            )
        else:
            # Process normal payment
            current_balance = get_user_balance(12345)
            new_balance = current_balance + verification["amount"]
            update_user_balance(12345, new_balance)
            add_transaction(12345, "deposit", verification["amount"], track_id)
            print("Payment completed!")
```

### Underpayment/Overpayment Handling

```python
from payment_module.utils.payment_amount_handler import (
    check_payment_amounts, handle_underpayment, handle_overpayment,
    create_underpayment_message, create_overpayment_message
)

# Check payment amounts
status, details = check_payment_amounts(90.0, 100.0, "track_123")

if status == "underpayment":
    # Create underpayment message
    message, keyboard = create_underpayment_message(
        "track_123", 90.0, 100.0, 10.0, "https://payment.url"
    )
    
    # Send to user
    await bot.send_message(user_id, message, reply_markup=keyboard)
    
elif status == "overpayment":
    # Create overpayment message
    message, keyboard = create_overpayment_message(
        "track_123", 110.0, 100.0, 10.0, 10.0
    )
    
    # Send to user
    await bot.send_message(user_id, message, reply_markup=keyboard)
```

### Bonus System Integration

```python
from payment_module.utils.bonus_calculator import calculate_deposit_bonus

# Calculate bonus for deposit
bonus_result = calculate_deposit_bonus(100.0, user_id)

if bonus_result["success"]:
    total_amount = 100.0 + bonus_result["bonus_amount"]
    print(f"Deposit: $100.00, Bonus: ${bonus_result['bonus_amount']:.2f}")
    print(f"Total credited: ${total_amount:.2f}")
    print(f"Tier used: {bonus_result['tier_used']['name']}")
```

### VIP System Integration

```python
from payment_module.utils.vip_payment_hooks import trigger_vip_check_on_payment

# Trigger VIP check after payment
vip_result = trigger_vip_check_on_payment(12345, 100.0, "username")

if vip_result["success"]:
    print(f"Current tier: {vip_result['current_tier']}")
    print(f"Next tier: {vip_result['next_tier']}")
    print(f"Progress: {vip_result['progress']:.1f}%")
    print(vip_result["message"])
```

### Callback Server Integration

```python
from payment_module.core.flask_server import run_callback_server

# Run callback server
run_callback_server(
    host="0.0.0.0",
    port=3000,
    debug=False
)
```

## Production Deployment

### 1. Environment Setup

```bash
# Production environment variables
export OXA_PAY_API_KEY="your_production_api_key"
export OXA_PAY_CALLBACK_URL="https://yourdomain.com/callback"
export DEVELOPMENT_MODE="false"
```

### 2. Callback Server Deployment

Use a proper WSGI server like Gunicorn:

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:3000 payment_module.core.flask_server:app
```

### 3. Nginx Configuration

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location /callback {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 4. SSL Configuration

Ensure HTTPS for production:

```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location /callback {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Testing

### Sandbox Mode

Set `DEVELOPMENT_MODE=true` to enable sandbox mode:

```python
import os
os.environ["DEVELOPMENT_MODE"] = "true"
os.environ["TESTING_MODE"] = "true"  # Skip HMAC verification
os.environ["DEBUG_MODE"] = "true"  # Enable debug logging
```

### Test Payment Flow

1. Start callback server in development mode
2. Use `/deposit` command in bot
3. Select test amount
4. Use sandbox payment link
5. Verify payment status
6. Test underpayment scenarios
7. Test overpayment scenarios
8. Verify balance updates
9. Check transaction recording
10. Test bonus calculation
11. Test VIP tier updates

### Comprehensive Testing

Run the complete test suite:

```bash
# Test all functionality
python payment_module/test_complete_integration.py

# Test underpayment/overpayment specifically
python payment_module/test_underpayment_overpayment.py

# Test final comprehensive functionality
python payment_module/test_final_comprehensive.py

# Test all new functionalities
python payment_module/test_comprehensive_functionalities.py
```

### Test Scenarios

1. **Normal Payment**: 95-110% of required amount
2. **Underpayment**: < 95% of required amount
3. **Slight Underpayment**: 95-99% of required amount
4. **Overpayment**: 110-150% of required amount
5. **Significant Overpayment**: > 150% of required amount
6. **Currency Conversion**: Multi-currency payments
7. **Bonus Calculation**: Various bonus tiers
8. **VIP Integration**: Tier checking and updates
9. **Database Operations**: All CRUD operations
10. **Error Handling**: All error scenarios
11. **HMAC Verification**: Signature generation and verification
12. **Auto Verification**: Automatic payment verification
13. **Manual Verification**: Manual payment verification
14. **Button Flows**: All button interactions and flows
15. **Database Analytics**: Payment statistics and analytics

## Troubleshooting

### Common Issues

1. **HMAC Verification Failed**
   - Check API key configuration
   - Verify callback URL accessibility
   - Ensure proper HMAC header in requests
   - Set `TESTING_MODE=true` for testing

2. **Payment Link Generation Failed**
   - Verify API key validity
   - Check network connectivity
   - Validate amount and parameters
   - Check sandbox mode settings

3. **Callback Server Not Receiving Requests**
   - Check firewall settings
   - Verify callback URL configuration
   - Test server accessibility
   - Check port availability

4. **Currency Conversion Errors**
   - Check API connectivity
   - Verify currency code format
   - Handle API rate limits
   - Check exchange rate availability

5. **Underpayment/Overpayment Issues**
   - Verify threshold configurations
   - Check amount calculations
   - Ensure proper status updates
   - Verify user notifications

6. **Database Connection Errors**
   - Check MongoDB connection
   - Verify database credentials
   - Check network connectivity
   - Ensure proper indexes

7. **Balance Update Failures**
   - Check user existence
   - Verify balance calculations
   - Check transaction recording
   - Ensure atomic operations

8. **VIP System Errors**
   - Check VIP tier configurations
   - Verify user data
   - Check tier calculation logic
   - Ensure proper notifications

### Debug Mode

Enable debug logging:

```python
import logging
import os

# Set debug mode
os.environ["DEBUG_MODE"] = "true"

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

### Log Files

Check these log files for debugging:
- `flask_callback.log` - Callback server logs
- `callback_debug.log` - Detailed callback debugging
- `payment_amount_analysis.log` - Underpayment/overpayment analysis
- `user_balance_updates.log` - Balance update operations
- `transaction_recording.log` - Transaction recording operations
- `bonus_calculation.log` - Bonus calculation operations
- `vip_system.log` - VIP tier operations
- Application logs - General application logs

### Performance Monitoring

Monitor these metrics:
- Payment success/failure rates
- Underpayment/overpayment frequencies
- Average payment amounts
- Currency conversion success rates
- Database operation performance
- Callback response times
- User balance update success rates
- VIP tier progression rates

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Verify configuration settings
4. Test in sandbox mode first
5. Run comprehensive test suite
6. Check underpayment/overpayment thresholds
7. Verify database connections
8. Test bonus and VIP configurations

## Module Completeness

This payment module includes **100% of the original functionality** plus comprehensive additional features:

### ✅ **Core Payment System**
- Payment link generation (OXA Pay integration)
- Multi-currency support with automatic conversion
- Real-time payment verification
- Flask callback server with HMAC verification
- Complete security features

### ✅ **Database Operations**
- Payment storage and retrieval
- User balance management
- Transaction recording and history
- Atomic operations for race condition prevention
- User creation and management
- Payment status tracking
- **Enhanced Analytics**: Payment statistics, trends, and analytics
- **Advanced Queries**: Date range queries, search functionality
- **Backup/Restore**: Complete backup and restore capabilities
- **Cleanup Operations**: Automated cleanup of old records

### ✅ **Telegram Bot Integration**
- Complete deposit flow handlers
- Payment verification handlers
- **Manual verification handlers** with comprehensive status checking
- All payment-related keyboards
- FSM state management
- Callback data factories
- Template system for messages

### ✅ **Advanced Features**
- VIP payment hooks and tier management
- Configurable bonus calculation system
- Transaction management with rollback
- Payment metrics and monitoring
- Comprehensive error handling
- Template system for customization

### ✅ **Underpayment/Overpayment System**
- Automatic underpayment detection (< 95%)
- Slight underpayment handling (95-99%)
- Overpayment detection and processing (110-150%)
- Significant overpayment handling (> 150%)
- User notifications for all scenarios
- Admin review requirements for significant overpayments

### ✅ **Security Features**
- **HMAC Verification**: Comprehensive HMAC signature verification system
- **Secure Processing**: Constant-time comparison, secure token generation
- **Data Protection**: Sensitive data hashing and protection
- **Testing Mode**: Safe testing without HMAC verification

### ✅ **Verification Systems**
- **Automatic Verification**: Scheduled payment verification with retry logic
- **Manual Verification**: Complete manual verification handlers
- **Status Tracking**: Comprehensive verification status management
- **Statistics**: Detailed verification statistics and monitoring

### ✅ **Button Flow System**
- **Complete Documentation**: Detailed documentation of all button flows
- **Flow Validation**: Button action validation for current states
- **Action Tracking**: Button action information and tracking
- **User Experience**: Optimized user interaction flows

### ✅ **Utility Functions**
- Safe state management
- Message formatting and sanitization
- Crypto amount formatting
- Payment logging and metrics
- Template helpers
- Performance monitoring
- **HMAC Utilities**: Signature generation and verification utilities
- **Verification Utilities**: Auto and manual verification utilities
- **Flow Utilities**: Button flow documentation and validation utilities

## License

This payment module is provided as-is for integration into your projects.
Make sure to comply with OXA Pay terms of service and local regulations.
