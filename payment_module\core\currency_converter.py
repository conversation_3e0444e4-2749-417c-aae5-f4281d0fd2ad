import aiohttp
import logging
import json
import time
from typing import Dict, Any, Optional, Tuple, Union

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Cache for currency prices to avoid excessive API calls
_currency_price_cache = {"timestamp": 0, "prices": {}}

# Cache expiry time in seconds (5 minutes)
CACHE_EXPIRY_TIME = 300

# USD-pegged stablecoins that should be treated as $1.00 USD equivalent
USD_STABLECOINS = {
    "USDT",  # Tether
    "USDC",  # USD Coin
    "DAI",   # Dai Stablecoin
    "BUSD",  # Binance USD
}

# Fixed USD price for stablecoins (in USD)
STABLECOIN_USD_PRICE = 1.0


def is_usd_stablecoin(currency: str) -> bool:
    """
    Check if a currency is a USD-pegged stablecoin.

    Args:
        currency: Currency code to check

    Returns:
        bool: True if the currency is a USD-pegged stablecoin
    """
    return currency.upper().strip() in USD_STABLECOINS


async def fetch_currency_prices() -> Dict[str, float]:
    """
    Fetch current cryptocurrency prices from OXA Pay API.

    Returns:
        dict: Dictionary mapping currency codes to their USD price
    """
    current_time = time.time()

    # Check if cache is still valid
    if (
        _currency_price_cache["timestamp"] > 0
        and current_time - _currency_price_cache["timestamp"] < CACHE_EXPIRY_TIME
        and _currency_price_cache["prices"]
    ):
        logger.info("Using cached currency prices")
        return _currency_price_cache["prices"]

    # Try multiple endpoints for price data
    endpoints = [
        {
            "url": "https://api.oxapay.com/v1/common/prices",
            "type": "oxapay",
            "name": "OXA Pay Prices",
        }
    ]

    logger.info("Fetching current currency prices from OXA Pay API")

    try:
        async with aiohttp.ClientSession() as session:
            for endpoint in endpoints:
                endpoint_url = endpoint["url"]
                endpoint_type = endpoint["type"]
                endpoint_name = endpoint["name"]

                try:
                    logger.debug(f"Trying endpoint: {endpoint_name} ({endpoint_url})")
                    async with session.get(endpoint_url, timeout=5) as response:
                        if response.status == 200:
                            data = await response.json()
                            logger.debug(f"Response from {endpoint_name}: {data}")

                            # Extract prices from the response
                            prices = {}

                            if endpoint_type == "oxapay":
                                # Handle OXA Pay response formats
                                if data.get("status") == 200 and "data" in data:
                                    data_content = data["data"]

                                    # Format 1: List of dictionaries with currency and price
                                    if (
                                        isinstance(data_content, list)
                                        and len(data_content) > 0
                                    ):
                                        for item in data_content:
                                            if isinstance(item, dict):
                                                currency = item.get("currency")
                                                price = (
                                                    item.get("price")
                                                    or item.get("rate")
                                                    or item.get("value")
                                                )
                                                if currency and price:
                                                    try:
                                                        prices[currency] = float(price)
                                                    except (ValueError, TypeError):
                                                        logger.error(
                                                            f"Invalid price format for {currency}: {price}"
                                                        )

                                    # Format 2: Dictionary with currency codes as keys
                                    elif isinstance(data_content, dict):
                                        for currency, price in data_content.items():
                                            if price and currency:
                                                try:
                                                    prices[currency] = float(price)
                                                except (ValueError, TypeError):
                                                    logger.error(
                                                        f"Invalid price format for {currency}: {price}"
                                                    )

                            # If we got prices, use them
                            if prices:
                                _currency_price_cache["timestamp"] = current_time
                                _currency_price_cache["prices"] = prices
                                logger.info(
                                    f"Updated currency prices from {endpoint_name}: {len(prices)} currencies"
                                )
                                return prices
                        else:
                            error_text = await response.text()
                            logger.warning(
                                f"API error from {endpoint_name} - {response.status}: {error_text}"
                            )

                except aiohttp.ClientError as e:
                    logger.warning(f"HTTP error from {endpoint_name}: {e}")
                except Exception as e:
                    logger.warning(
                        f"Error processing response from {endpoint_name}: {e}"
                    )

    except Exception as e:
        logger.exception(f"Unexpected error fetching currency prices: {e}")

    return _currency_price_cache.get("prices", {})


async def convert_currency(
    amount: Union[float, str], from_currency: str, to_currency: str = "USDT"
) -> Tuple[float, Dict[str, Any]]:
    """
    Convert an amount from one cryptocurrency to another.

    Args:
        amount: The amount to convert
        from_currency: Source currency code (e.g., "BNB")
        to_currency: Target currency code (e.g., "USDT")

    Returns:
        tuple: (converted_amount, conversion_info)
    """
    # Validate and convert amount
    try:
        amount_float = float(amount)
    except (ValueError, TypeError):
        return 0.0, {"success": False, "error": "Invalid amount format"}

    if amount_float < 0:
        return 0.0, {"success": False, "error": "Negative amounts not allowed"}

    # Sanitize currency codes
    from_currency = from_currency.strip().upper()
    to_currency = to_currency.strip().upper()

    # Same currency check
    if from_currency == to_currency:
        return amount_float, {
            "success": True,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "amount": amount_float,
            "converted_amount": amount_float,
            "exchange_rate": 1.0,
        }

    # Stablecoin to stablecoin (1:1)
    if is_usd_stablecoin(from_currency) and is_usd_stablecoin(to_currency):
        return amount_float, {
            "success": True,
            "stablecoin_conversion": True,
            "exchange_rate": 1.0,
            "converted_amount": amount_float,
        }

    # Fetch prices
    prices = await fetch_currency_prices()
    if not prices:
        return 0.0, {"success": False, "error": "Failed to fetch currency prices"}

    # Get prices (use fixed price for stablecoins)
    from_price = STABLECOIN_USD_PRICE if is_usd_stablecoin(from_currency) else prices.get(from_currency)
    to_price = STABLECOIN_USD_PRICE if is_usd_stablecoin(to_currency) else prices.get(to_currency)

    if not from_price or not to_price:
        return 0.0, {"success": False, "error": "Price not available"}

    # Calculate conversion
    exchange_rate = from_price / to_price
    converted_amount = amount_float * exchange_rate

    return converted_amount, {
        "success": True,
        "from_currency": from_currency,
        "to_currency": to_currency,
        "amount": amount_float,
        "converted_amount": converted_amount,
        "exchange_rate": exchange_rate,
        "from_price_usd": from_price,
        "to_price_usd": to_price,
    }


async def process_payment_with_conversion(
    payment_data: Dict[str, Any], track_id: str, target_currency: str = "USDT"
) -> Dict[str, Any]:
    """
    Process payment data and convert to target currency.

    Args:
        payment_data: Payment data from OXA Pay
        track_id: Payment tracking ID
        target_currency: Target currency (default: USDT)

    Returns:
        dict: Conversion result with details
    """
    result = {
        "original_data": payment_data.copy(),
        "converted_amount": 0.0,
        "conversion_details": [],
        "currency": target_currency,
        "is_converted": False,
        "conversion_errors": [],
        "track_id": track_id,
    }

    transactions = payment_data.get("txs", [])
    if not transactions:
        return result

    total_converted = 0.0
    details = []

    for idx, tx in enumerate(transactions):
        tx_currency = tx.get("currency", "USDT").upper()
        
        # Check for auto-conversion
        if "auto_convert_amount" in tx and tx.get("auto_convert_amount") is not None:
            converted = float(tx.get("auto_convert_amount", 0))
            original = float(tx.get("sent_amount", 0))
            
            # USDT fix
            if converted == 0 and is_usd_stablecoin(tx_currency):
                converted = original
            
            details.append({
                "tx_index": idx,
                "method": "auto_converted",
                "original_amount": original,
                "original_currency": tx_currency,
                "converted_amount": converted,
                "converted_currency": target_currency,
            })
            total_converted += converted
            result["is_converted"] = True
        else:
            # Manual conversion
            tx_amount = float(tx.get("amount", 0) or tx.get("value", 0))
            
            if tx_currency != target_currency:
                converted, info = await convert_currency(tx_amount, tx_currency, target_currency)
                if info.get("success"):
                    details.append({
                        "tx_index": idx,
                        "method": "manual_converted",
                        "original_amount": tx_amount,
                        "original_currency": tx_currency,
                        "converted_amount": converted,
                        "converted_currency": target_currency,
                        "exchange_rate": info.get("exchange_rate", 0),
                    })
                    total_converted += converted
                    result["is_converted"] = True
            else:
                details.append({
                    "tx_index": idx,
                    "method": "no_conversion_needed",
                    "original_amount": tx_amount,
                    "original_currency": tx_currency,
                    "converted_amount": tx_amount,
                    "converted_currency": target_currency,
                })
                total_converted += tx_amount

    result["converted_amount"] = total_converted
    result["conversion_details"] = details
    return result


def format_conversion_display(
    conversion_result: Dict[str, Any], fee_rate: float = 0.0
) -> str:
    """Format conversion details for display."""
    if not conversion_result.get("is_converted"):
        return ""

    details = conversion_result.get("conversion_details", [])
    if not details:
        return ""

    lines = [
        "💱 <b>Payment Processing Details</b>\n",
        "<b>━━━━━━━━━━━━━━━━━━</b>",
        "<b>CONVERSION SUMMARY</b>",
        "<b>━━━━━━━━━━━━━━━━━━</b>\n",
    ]

    currency = conversion_result.get("currency", "USDT")
    
    for detail in details:
        method = detail["method"]
        orig_amt = f"{detail['original_amount']:.8f}".rstrip("0").rstrip(".")
        conv_amt = f"{detail['converted_amount']:.4f}"
        
        if method == "auto_converted":
            lines.append(f"🏦 Auto: <code>{orig_amt}</code> {detail['original_currency']} → <code>{conv_amt}</code> {currency}")
        elif method == "manual_converted":
            lines.append(f"🔄 Manual: <code>{orig_amt}</code> {detail['original_currency']} → <code>{conv_amt}</code> {currency}")
        else:
            lines.append(f"✓ Direct: <code>{orig_amt}</code> {detail['original_currency']}")

    total = conversion_result.get("converted_amount", 0)
    
    # Apply fee if provided
    if fee_rate > 0:
        fee = total * fee_rate / (1 - fee_rate)
        total += fee
        lines.append(f"\n📊 Fee ({fee_rate*100:.1f}%): <code>+{fee:.2f}</code> {currency}")

    lines.append(f"\n💎 <b>Total: {total:.2f} {currency}</b>")
    return "\n".join(lines)


async def get_usdt_equivalent_amount(
    amount: Union[float, str], currency: str, track_id: str = "unknown"
) -> Tuple[float, bool, str]:
    """Get USDT equivalent amount."""
    try:
        amount_float = float(amount)
    except (ValueError, TypeError):
        return 0.0, False, "Invalid amount"

    if is_usd_stablecoin(currency):
        return amount_float, True, ""

    converted, info = await convert_currency(amount_float, currency, "USDT")
    if info.get("success"):
        return converted, True, ""
    
    return 0.0, False, info.get("error", "Conversion failed")
