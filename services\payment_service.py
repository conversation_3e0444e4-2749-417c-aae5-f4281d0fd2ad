"""
Payment Service Integration
Integrates the payment module with the existing bot architecture
"""

from __future__ import annotations

import os
import logging
from typing import Optional, Dict, Any, Tuple, List
from datetime import datetime

from config.settings import get_settings
from utils.central_logger import get_logger

logger = get_logger()


class PaymentService:
    """Service for handling cryptocurrency payments using the payment module"""
    
    def __init__(self):
        self.settings = get_settings()
        self.payment_module = None
        self._initialize_payment_module()
    
    def _initialize_payment_module(self):
        """Initialize the payment module with current settings"""
        try:
            # Set environment variables for payment module
            api_key = self.settings.OXA_PAY_API_KEY or "test_key"
            os.environ["OXA_PAY_API_KEY"] = api_key
            
            # Use ngrok URL for callbacks (required for external access)
            if self.settings.OXA_PAY_CALLBACK_URL and self.settings.OXA_PAY_CALLBACK_URL.strip():
                callback_url = self.settings.OXA_PAY_CALLBACK_URL
            else:
                # Use ngrok URL for callbacks - this is required for external payment providers
                callback_url = "https://relative-mostly-cougar.ngrok-free.app/callback"
                logger.info(f"Using ngrok callback URL: {callback_url}")
            
            os.environ["OXA_PAY_CALLBACK_URL"] = callback_url
            os.environ["DEVELOPMENT_MODE"] = str(self.settings.PAYMENT_DEVELOPMENT_MODE).lower()
            os.environ["TESTING_MODE"] = str(self.settings.PAYMENT_TESTING_MODE).lower()
            os.environ["DEBUG_MODE"] = str(self.settings.PAYMENT_DEBUG_MODE).lower()
            
            # Import and initialize payment module
            from payment_module import create_payment_module
            
            self.payment_module = create_payment_module(
                api_key=api_key,
                callback_url=callback_url
            )
            
            logger.info(f"Payment module initialized successfully with callback URL: {callback_url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize payment module: {e}")
            self.payment_module = None
    
    def is_available(self) -> bool:
        """Check if payment module is available and configured"""
        return (
            self.payment_module is not None and 
            bool(self.settings.OXA_PAY_API_KEY.strip())
        )
    
    async def create_payment_link(
        self, 
        amount: float, 
        user_id: int, 
        description: str = "Deposit"
    ) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Create a payment link for deposit
        
        Args:
            amount: Payment amount in USD
            user_id: Telegram user ID
            description: Payment description
            
        Returns:
            Tuple of (success, payment_data, error_message)
        """
        if not self.is_available():
            return False, None, "Payment system not available"
        
        try:
            # Validate amount
            if amount < self.settings.PAYMENT_MIN_AMOUNT:
                return False, None, f"Amount must be at least ${self.settings.PAYMENT_MIN_AMOUNT}"
            
            if amount > self.settings.PAYMENT_MAX_AMOUNT:
                return False, None, f"Amount must not exceed ${self.settings.PAYMENT_MAX_AMOUNT}"
            
            # Generate unique order ID
            order_id = f"deposit_{user_id}_{int(datetime.now().timestamp())}"
            
            # Create payment link
            core_functions = self.payment_module.get_core_functions()
            create_payment_link = core_functions['create_payment_link']
            
            payment_data = await create_payment_link(
                amount=amount,
                order_id=order_id,
                user_id=str(user_id),
                description=description
            )
            
            if payment_data and payment_data.get('status') == 'success':
                # Save payment to database for callback processing
                database_ops = self.payment_module.get_database_operations()
                save_payment_details = database_ops['save_payment_details']
                
                track_id = payment_data.get('trackId')
                # Convert track_id to string to ensure consistency with callback data
                track_id_str = str(track_id) if track_id else None
                
                if not track_id_str:
                    logger.error(f"❌ No track_id returned from payment gateway for user {user_id}")
                    return False, None, "No track ID received from payment gateway"
                
                saved = await save_payment_details(
                    track_id=track_id_str,
                    order_id=order_id,
                    user_id=user_id,
                    amount=amount,
                    currency="USDT",
                    status="pending",
                    description=description,
                    pay_link=payment_data.get('payLink'),
                    created_at=datetime.now()
                )
                
                if saved:
                    logger.info(f"✅ Payment link created and saved for user {user_id}: ${amount}, track_id={track_id}")
                else:
                    logger.error(f"❌ Payment link created but save failed for user {user_id} - callbacks will NOT work!")
                    return False, None, "Failed to save payment to database"
                
                return True, payment_data, None
            else:
                error_msg = payment_data.get('error', 'Failed to create payment link') if payment_data else 'No response from payment service'
                logger.error(f"Payment link creation failed for user {user_id}: {error_msg}")
                return False, None, error_msg
                
        except Exception as e:
            logger.error(f"Error creating payment link for user {user_id}: {e}")
            return False, None, f"Payment system error: {str(e)}"
    
    async def verify_payment(self, track_id: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        Verify payment status
        
        Args:
            track_id: Payment track ID
            
        Returns:
            Tuple of (success, verification_data, error_message)
        """
        if not self.is_available():
            return False, None, "Payment system not available"
        
        try:
            core_functions = self.payment_module.get_core_functions()
            check_payment = core_functions['check_payment']
            
            verification_data = await check_payment(track_id, self.settings.OXA_PAY_API_KEY)
            
            # The check_payment function returns the raw API data directly
            if verification_data and verification_data.get('status') != 'error':
                # The API response has a nested structure: {status: 200, data: {status: "paid", ...}}
                # We need to check the nested data.status field
                payment_data = verification_data.get('data', {})
                payment_status = str(payment_data.get('status', '')).lower()
                
                if payment_status in ['completed', 'confirmed', 'success', 'paid', 'manual_accept']:
                    logger.info(f"Payment verified successfully: {track_id} (status: {payment_status})")
                    return True, payment_data, None
                else:
                    logger.warning(f"Payment not completed for {track_id}: status={payment_status}")
                    return False, payment_data, f"Payment status is {payment_status}, not completed"
            else:
                error_msg = verification_data.get('error', 'Payment verification failed') if verification_data else 'No response from payment service'
                logger.warning(f"Payment verification failed for {track_id}: {error_msg}")
                return False, verification_data, error_msg
                
        except Exception as e:
            logger.error(f"Error verifying payment {track_id}: {e}")
            return False, None, f"Payment verification error: {str(e)}"
    
    async def get_user_payment_history(self, user_id: int) -> Tuple[bool, list, Optional[str]]:
        """
        Get user's payment history
        
        Args:
            user_id: Telegram user ID
            
        Returns:
            Tuple of (success, payment_history, error_message)
        """
        if not self.is_available():
            return False, [], "Payment system not available"
        
        try:
            database_ops = self.payment_module.get_database_operations()
            get_user_payments = database_ops['get_user_payments']
            
            payments = await get_user_payments(user_id, completed_only=True)
            
            logger.debug(f"Retrieved {len(payments)} payments for user {user_id}")
            return True, payments, None
            
        except Exception as e:
            logger.error(f"Error getting payment history for user {user_id}: {e}")
            return False, [], f"Database error: {str(e)}"
    
    async def update_user_balance(self, user_id: int, amount: float, transaction_type: str = "deposit") -> Tuple[bool, Optional[str]]:
        """
        Update user balance after successful payment
        
        Args:
            user_id: Telegram user ID
            amount: Amount to add to balance
            transaction_type: Type of transaction
            
        Returns:
            Tuple of (success, error_message)
        """
        if not self.is_available():
            return False, "Payment system not available"
        
        try:
            database_ops = self.payment_module.get_database_operations()
            update_user_balance = database_ops['update_user_balance']
            add_transaction = database_ops['add_transaction']
            
            # Update balance
            success = await update_user_balance(user_id, amount)
            
            if success:
                # Record transaction
                await add_transaction(
                    user_id=user_id,
                    amount=amount,
                    transaction_type=transaction_type,
                    description=f"Payment deposit: ${amount:.2f}"
                )
                
                logger.info(f"Updated balance for user {user_id}: +${amount}")
                return True, None
            else:
                logger.error(f"Failed to update balance for user {user_id}")
                return False, "Failed to update balance"
                
        except Exception as e:
            logger.error(f"Error updating balance for user {user_id}: {e}")
            return False, f"Balance update error: {str(e)}"
    
    def get_payment_keyboards(self) -> Dict[str, Any]:
        """Get payment-related keyboards"""
        if not self.is_available():
            return {}
        
        try:
            return self.payment_module.get_keyboards()
        except Exception as e:
            logger.error(f"Error getting payment keyboards: {e}")
            return {}
    
    def get_payment_handlers(self) -> list:
        """Get payment handlers for registration"""
        if not self.is_available():
            return []
        
        try:
            return self.payment_module.get_handlers()
        except Exception as e:
            logger.error(f"Error getting payment handlers: {e}")
            return []
    
    def get_payment_states(self) -> Dict[str, Any]:
        """Get payment FSM states"""
        if not self.is_available():
            return {}
        
        try:
            return self.payment_module.get_states()
        except Exception as e:
            logger.error(f"Error getting payment states: {e}")
            return {}
    
    async def start_callback_server(self) -> bool:
        """Start the payment callback server"""
        if not self.is_available():
            logger.warning("Cannot start callback server: payment module not available")
            return False
        
        try:
            core_functions = self.payment_module.get_core_functions()
            run_callback_server = core_functions['run_callback_server']
            
            # Start server in background using thread executor
            import asyncio
            import concurrent.futures
            
            def run_server():
                run_callback_server(
                    host="0.0.0.0", 
                    port=self.settings.PAYMENT_CALLBACK_PORT
                )
            
            # Run server in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                loop.run_in_executor(executor, run_server)
            
            logger.info(f"Payment callback server started on port {self.settings.PAYMENT_CALLBACK_PORT}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start callback server: {e}")
            return False
    
    # Enhanced Payment Features
    
    async def check_payment_amounts(self, actual_amount: float, required_amount: float, track_id: str) -> Tuple[str, Dict[str, Any]]:
        """Check if payment is underpaid, overpaid, or normal"""
        if not self.is_available():
            return "error", {"error": "Payment system not available"}
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            check_payment_amounts = utility_functions['check_payment_amounts']
            
            return await check_payment_amounts(actual_amount, required_amount, track_id)
            
        except Exception as e:
            logger.error(f"Error checking payment amounts: {e}")
            return "error", {"error": str(e)}
    
    async def handle_underpayment(self, bot, user_id: int, track_id: str, received_amount: float, required_amount: float, payment_url: Optional[str] = None) -> bool:
        """Handle underpayment detection and notification"""
        if not self.is_available():
            return False
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            handle_underpayment = utility_functions['handle_underpayment']
            
            return await handle_underpayment(bot, user_id, track_id, received_amount, required_amount, payment_url)
            
        except Exception as e:
            logger.error(f"Error handling underpayment: {e}")
            return False
    
    async def handle_overpayment(self, bot, user_id: int, track_id: str, received_amount: float, required_amount: float) -> bool:
        """Handle overpayment detection and notification"""
        if not self.is_available():
            return False
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            handle_overpayment = utility_functions['handle_overpayment']
            
            return await handle_overpayment(bot, user_id, track_id, received_amount, required_amount)
            
        except Exception as e:
            logger.error(f"Error handling overpayment: {e}")
            return False
    
    async def handle_payment_completion(self, bot, user_id: int, track_id: str, received_amount: float, required_amount: float) -> bool:
        """Handle successful payment completion notification"""
        if not self.is_available():
            return False
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            handle_payment_completion = utility_functions['handle_payment_completion']
            
            return await handle_payment_completion(bot, user_id, track_id, received_amount, required_amount)
            
        except Exception as e:
            logger.error(f"Error handling payment completion: {e}")
            return False
    
    
    def get_payment_statistics(self, user_id: Optional[int] = None, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive payment statistics"""
        if not self.is_available():
            return {"error": "Payment system not available"}
        
        try:
            database_ops = self.payment_module.get_database_operations()
            get_payment_statistics = database_ops['get_payment_statistics']
            
            return get_payment_statistics(user_id, days)
            
        except Exception as e:
            logger.error(f"Error getting payment statistics: {e}")
            return {"error": str(e)}
    
    def search_payments(self, query_text: str, user_id: Optional[int] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Search payments by track_id, order_id, or description"""
        if not self.is_available():
            return []
        
        try:
            database_ops = self.payment_module.get_database_operations()
            search_payments = database_ops['search_payments']
            
            return search_payments(query_text, user_id, limit)
            
        except Exception as e:
            logger.error(f"Error searching payments: {e}")
            return []
    
    def get_payment_analytics(self, user_id: Optional[int] = None, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive payment analytics"""
        if not self.is_available():
            return {"error": "Payment system not available"}
        
        try:
            database_ops = self.payment_module.get_database_operations()
            get_payment_analytics = database_ops['get_payment_analytics']
            
            return get_payment_analytics(user_id, days)
            
        except Exception as e:
            logger.error(f"Error getting payment analytics: {e}")
            return {"error": str(e)}
    
    # HMAC Verification Functions
    
    def verify_payment_callback(self, request_data: bytes, headers: Dict[str, str]) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """Verify payment callback with HMAC signature"""
        if not self.is_available():
            return False, "Payment system not available", None
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            verify_payment_callback = utility_functions['verify_payment_callback']
            
            return verify_payment_callback(request_data, headers, self.settings.OXA_PAY_API_KEY)
            
        except Exception as e:
            logger.error(f"Error verifying payment callback: {e}")
            return False, str(e), None
    
    def generate_payment_signature(self, data: Dict[str, Any]) -> str:
        """Generate HMAC signature for payment data"""
        if not self.is_available():
            return ""
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            generate_payment_signature = utility_functions['generate_payment_signature']
            
            return generate_payment_signature(data, self.settings.OXA_PAY_API_KEY)
            
        except Exception as e:
            logger.error(f"Error generating payment signature: {e}")
            return ""
    
    # Auto Verification Functions
    
    async def start_auto_verification(self) -> bool:
        """Start automatic payment verification (requires running event loop)"""
        if not self.is_available():
            return False
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            start_auto_verification = utility_functions['start_auto_verification']
            get_verification_manager = utility_functions['get_verification_manager']
            
            # Set API key for verification manager
            manager = get_verification_manager()
            manager.set_api_key(self.settings.OXA_PAY_API_KEY)
            
            # Start auto verification (this creates an async task)
            start_auto_verification()
            return True
            
        except Exception as e:
            logger.error(f"Error starting auto verification: {e}")
            return False
    
    def stop_auto_verification(self) -> bool:
        """Stop automatic payment verification"""
        if not self.is_available():
            return False
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            stop_auto_verification = utility_functions['stop_auto_verification']
            
            return stop_auto_verification()
            
        except Exception as e:
            logger.error(f"Error stopping auto verification: {e}")
            return False
    
    async def add_payment_for_verification(self, track_id: str, user_id: int, amount: float, callback_url: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Add payment for automatic verification"""
        if not self.is_available():
            return False
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            add_payment_for_verification = utility_functions['add_payment_for_verification']
            
            return await add_payment_for_verification(track_id, user_id, amount, callback_url, metadata)
            
        except Exception as e:
            logger.error(f"Error adding payment for verification: {e}")
            return False
    
    def get_verification_statistics(self) -> Dict[str, Any]:
        """Get verification statistics"""
        if not self.is_available():
            return {"error": "Payment system not available"}
        
        try:
            utility_functions = self.payment_module.get_utility_functions()
            get_verification_statistics = utility_functions['get_verification_statistics']
            
            return get_verification_statistics()
            
        except Exception as e:
            logger.error(f"Error getting verification statistics: {e}")
            return {"error": str(e)}


# Global payment service instance
_payment_service: Optional[PaymentService] = None


def get_payment_service() -> PaymentService:
    """Get the global payment service instance"""
    global _payment_service
    if _payment_service is None:
        _payment_service = PaymentService()
    return _payment_service
