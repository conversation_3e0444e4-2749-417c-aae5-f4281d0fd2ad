"""
API v1 Validation Utilities

DEPRECATED: This module now imports from the central validation utilities.
Use utils.validation directly instead of this module.

All validation functions have been consolidated into utils.validation
for single source of truth across the codebase.
"""

# Import all validation functions from central location
from utils.validation import (
    ValidationError,
    validate_url,
    validate_api_key,
    validate_service_name,
    validate_endpoint_config,
    validate_credentials,
    validate_http_method,
)

# Re-export for backward compatibility
__all__ = [
    'ValidationError',
    'validate_url',
    'validate_api_key', 
    'validate_service_name',
    'validate_endpoint_config',
    'validate_credentials',
    'validate_http_method',
]
