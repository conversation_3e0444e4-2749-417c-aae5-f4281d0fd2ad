"""
Cart Service Usage Examples

Demonstrates how to use the API v1 cart service for:
- Viewing cart contents
- Listing orders
- Viewing order details
- Checking orders
- Downloading order data
"""

import asyncio
from api_v1.services.cart_service import get_cart_service


async def example_view_cart():
    """Example: View cart contents"""
    print("\n=== View Cart Example ===")
    
    cart_service = get_cart_service(config_name="api_v1_base")
    
    # Get cart contents
    result = await cart_service.view_cart(user_id="example_user")
    
    if result.success:
        print(f"✓ Cart retrieved successfully")
        print(f"  Total items: {result.data.get('item_count', 0)}")
        print(f"  Total price: ${result.data.get('total_price', 0)}")
        print(f"  Items:")
        
        for item in result.data.get('items', []):
            print(f"    - {item.get('brand', 'N/A')} {item.get('bin', 'N/A')} - ${item.get('price', 0)}")
    else:
        print(f"✗ Failed to get cart: {result.message}")


async def example_list_orders():
    """Example: List orders"""
    print("\n=== List Orders Example ===")
    
    cart_service = get_cart_service(config_name="api_v1_base")
    
    # Get first page of orders
    result = await cart_service.list_orders(
        category="hq",
        page=1,
        limit=10,
        user_id="example_user"
    )
    
    if result.success:
        print(f"✓ Orders retrieved successfully")
        print(f"  Total orders: {result.data.get('total_count', 0)}")
        print(f"  Page: {result.data.get('page', 1)}/{result.data.get('total_pages', 1)}")
        print(f"  Orders:")
        
        for order in result.data.get('orders', [])[:5]:  # Show first 5
            print(f"    - Order #{order.get('_id')} - {order.get('status')} - ${order.get('price', 0)}")
            print(f"      {order.get('brand', 'N/A')} {order.get('bin', 'N/A')} - {order.get('country', 'N/A')}")
    else:
        print(f"✗ Failed to list orders: {result.message}")


async def example_view_order():
    """Example: View single order details"""
    print("\n=== View Order Example ===")
    
    cart_service = get_cart_service(config_name="api_v1_base")
    
    # View a specific order (replace with actual order ID)
    order_id = 347387
    
    result = await cart_service.view_order(
        order_id=order_id,
        category="hq",
        user_id="example_user"
    )
    
    if result.success:
        order = result.data.get('order', {})
        print(f"✓ Order details retrieved")
        print(f"  Order ID: {order.get('_id')}")
        print(f"  Status: {order.get('status')}")
        print(f"  Price: ${order.get('price', 0)}")
        print(f"  Card: {order.get('brand', 'N/A')} {order.get('bin', 'N/A')}")
        print(f"  Exp: {order.get('exp', 'N/A')}")
        print(f"  Country: {order.get('country', 'N/A')}")
        print(f"  Created: {order.get('createdAt', 'N/A')}")
        print(f"  Can Check: {order.get('canCheck', 0)}")
    else:
        print(f"✗ Failed to view order: {result.message}")


async def example_check_order():
    """Example: Mark order as checked"""
    print("\n=== Check Order Example ===")
    
    cart_service = get_cart_service(config_name="api_v1_base")
    
    # Check a specific order (replace with actual order ID)
    order_id = 347387
    
    result = await cart_service.check_order(
        order_id=order_id,
        category="hq",
        user_id="example_user"
    )
    
    if result.success:
        order = result.data.get('order', {})
        print(f"✓ Order checked successfully")
        print(f"  Order ID: {order.get('_id')}")
        print(f"  Status: {order.get('status')}")
        print(f"  Checked At: {order.get('checkedAt', 'N/A')}")
    else:
        print(f"✗ Failed to check order: {result.message}")


async def example_download_order():
    """Example: Download order data"""
    print("\n=== Download Order Example ===")
    
    cart_service = get_cart_service(config_name="api_v1_base")
    
    # Download a specific order (replace with actual order ID)
    order_id = 347387
    
    result = await cart_service.download_order(
        order_id=order_id,
        category="hq",
        user_id="example_user"
    )
    
    if result.success:
        print(f"✓ Order downloaded successfully")
        print(f"  Format: {result.data.get('format', 'N/A')}")
        print(f"  Headers: {result.data.get('headers', 'N/A')}")
        print(f"\nRaw Data:")
        print(result.data.get('raw_text', 'N/A'))
    else:
        print(f"✗ Failed to download order: {result.message}")


async def example_health_check():
    """Example: Health check"""
    print("\n=== Health Check Example ===")
    
    cart_service = get_cart_service(config_name="api_v1_base")
    
    result = await cart_service.health_check()
    
    if result.success:
        print(f"✓ Service is healthy")
        print(f"  Config loaded: {result.data.get('config_loaded', False)}")
        print(f"  Base URL: {result.data.get('base_url', 'N/A')}")
    else:
        print(f"✗ Service unhealthy: {result.message}")


async def main():
    """Run all examples"""
    print("=" * 60)
    print("API v1 Cart Service Examples")
    print("=" * 60)
    
    try:
        # Health check first
        await example_health_check()
        
        # View cart
        await example_view_cart()
        
        # List orders
        await example_list_orders()
        
        # View single order
        await example_view_order()
        
        # Check order
        await example_check_order()
        
        # Download order
        await example_download_order()
        
    except Exception as e:
        print(f"\n✗ Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

