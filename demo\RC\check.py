#curl
curl ^"https://ronaldo-club.to/api/cards/hq/check^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.9^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg1_=EZ1mVA0ckHP0l8lLMdOf; _ga=GA1.1.221336348.1756931255; testcookie=1; __ddg9_=**************; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk3ODcwLCJpYXQiOjE3NjEyNTA0MjMsImV4cCI6MTc2Mzg0MjQyM30.rYuVegs7nOa3v66bv8t8dKoxceNDi9l8fq2ldDZU5gI; _ga_KZWCRF57VT=GS2.1.s1761250368^$o6^$g1^$t1761251098^$j59^$l0^$h0; __ddg8_=xiVdYuBkoPbFZhlj; __ddg10_=1761251136^" ^
  -H ^"origin: https://ronaldo-club.to^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/orders/cards/hq^" ^
  -H ^"sec-ch-ua: ^\^"Google Chrome^\^";v=^\^"141^\^", ^\^"Not?A_Brand^\^";v=^\^"8^\^", ^\^"Chromium^\^";v=^\^"141^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36^" ^
  --data-raw ^"^{^\^"id^\^":347387^}^"

#fetch
fetch("https://ronaldo-club.to/api/cards/hq/check", {
  "headers": {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Google Chrome\";v=\"141\", \"Not?A_Brand\";v=\"8\", \"Chromium\";v=\"141\"",
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "\"Android\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin"
  },
  "referrer": "https://ronaldo-club.to/orders/cards/hq",
  "body": "{\"id\":347387}",
  "method": "POST",
  "mode": "cors",
  "credentials": "include"
});

#request_headers
:authority
ronaldo-club.to
:method
POST
:path
/api/cards/hq/check
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
en-US,en;q=0.9
content-length
13
content-type
application/json
cookie
__ddg1_=EZ1mVA0ckHP0l8lLMdOf; _ga=GA1.1.221336348.1756931255; testcookie=1; __ddg9_=**************; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTk3ODcwLCJpYXQiOjE3NjEyNTA0MjMsImV4cCI6MTc2Mzg0MjQyM30.rYuVegs7nOa3v66bv8t8dKoxceNDi9l8fq2ldDZU5gI; _ga_KZWCRF57VT=GS2.1.s1761250368$o6$g1$t1761251098$j59$l0$h0; __ddg8_=xiVdYuBkoPbFZhlj; __ddg10_=1761251136
origin
https://ronaldo-club.to
priority
u=1, i
referer
https://ronaldo-club.to/orders/cards/hq
sec-ch-ua
"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"
sec-ch-ua-mobile
?1
sec-ch-ua-platform
"Android"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36

#response_headers
access-control-allow-credentials
true
access-control-allow-headers
Origin, X-Requested-With, Content-Type, Accept, Authorization
access-control-allow-methods
GET,HEAD,OPTIONS,POST,PUT,DELETE
access-control-allow-origin
https://ronaldo-club.to
content-length
910
content-security-policy
upgrade-insecure-requests;
content-type
application/json; charset=utf-8
date
Thu, 23 Oct 2025 20:26:01 GMT
etag
W/"38e-tEtwoB4wg9ktjyqCHg7JWiDW5HU"
server
ddos-guard
set-cookie
__ddg8_=oUoKQjwdQQMdzSEn; Domain=.ronaldo-club.to; Path=/; Expires=Thu, 23-Oct-2025 20:45:55 GMT
set-cookie
__ddg10_=1761251155; Domain=.ronaldo-club.to; Path=/; Expires=Thu, 23-Oct-2025 20:45:55 GMT
set-cookie
__ddg9_=**************; Domain=.ronaldo-club.to; Path=/; Expires=Thu, 23-Oct-2025 20:45:55 GMT
x-powered-by
Express

#response
{"success":true,"data":{"_id":347387,"user_id":197870,"api_user_id":null,"seller_id":"Not Allowed","product_id":1896680,"status":"NonRefundable","createdAt":"2025-10-23T20:22:38.000Z","refundAt":null,"start_Date":"2025-10-24T06:22:38.000Z","viewedAt":"2025-10-23T20:25:36.000Z","check_Date":null,"isviewed":1,"price":"1.3980","canCheck":0,"checkedAt":"2025-10-23T20:26:01.000Z","base":"25SEP_80VR_PHONE_EMAIL_IP4","cc":"****************","exp":"10/25","cvv":"720","expmonth":null,"expyear":null,"name":"Tupac Solorzano","firstname":null,"lastname":null,"email":"<EMAIL>","phone":"***********","country":"US","state":"VA","city":"Big Stone Gap","zip":"24219","address":"214 Cherokee Avenue East","level":"PREPAID","bank":"PATHWARD, NATIONAL ASSOCIATION","brand":"MASTERCARD","type":"DEBIT","ip":"**************","dob":null,"dl":null,"ssn":null,"mmn":null,"refundable":1,"ua":null,"other":null}}