"""
User and wallet management service
"""

from __future__ import annotations

import asyncio
from utils.central_logger import get_logger
from typing import Op<PERSON>, Tuple
from pymongo.errors import Duplicate<PERSON>eyError, OperationFailure

from database.connection import get_collection, database_transaction
from models import User, Wallet, Transaction, TransactionType
from config.settings import get_settings
from utils.performance import (
    QueryOptimizer,
    monitor_performance,
    optimize_collection_query,
    async_cache,
)

logger = get_logger()


class UserService:
    """Service for user and wallet operations"""

    def __init__(self):
        self.settings = get_settings()
        self.users_collection = get_collection("users")
        self.wallets_collection = get_collection("wallets")
        self.transactions_collection = get_collection("transactions")

    @async_cache(ttl_seconds=300)  # Cache for 5 minutes
    @monitor_performance("get_user_by_telegram_id")
    async def get_user_by_telegram_id(self, telegram_id: int) -> Optional[User]:
        """Get user by Telegram ID"""
        if telegram_id <= 0:
            raise ValueError("Invalid telegram_id: must be positive")

        try:
            doc = await self.users_collection.find_one({"telegram_id": telegram_id})
            return User.from_mongo(doc) if doc else None
        except ValueError as e:
            logger.error(f"Validation error for telegram_id {telegram_id}: {e}")
            raise
        except Exception as e:
            logger.error(
                f"Database error getting user by telegram_id {telegram_id}: {e}"
            )
            raise RuntimeError(f"Failed to retrieve user data") from e

    async def create_user(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        first_name: Optional[str] = None,
        language_code: Optional[str] = None,
        role: Optional[str] = None,
        active: Optional[bool] = None,
    ) -> User:
        """Create a new user with wallet using atomic upsert"""
        try:
            async with database_transaction():
                # Create user object first
                user = User(
                    telegram_id=telegram_id,
                    username=username,
                    first_name=first_name,
                    language_code=language_code,
                    consent_ack=True,
                )
                
                # Override with admin-specific fields if provided
                if role:
                    user.role = role
                if active is not None:
                    user.active = active
                
                user_doc = user.to_mongo()

                # Use atomic upsert to handle race conditions
                result = await self.users_collection.update_one(
                    {"telegram_id": telegram_id},
                    {"$setOnInsert": user_doc},
                    upsert=True
                )

                # Get the user document
                user_doc = await self.users_collection.find_one({"telegram_id": telegram_id})
                user = User.from_mongo(user_doc)

                # If this was a new insertion, create wallet and initial transaction
                if result.upserted_id:
                    logger.info(f"Created new user {user.id} with telegram_id {telegram_id}")
                    
                    # Create wallet with zero balance
                    wallet = Wallet(
                        user_id=str(user.id),
                        currency=self.settings.DEFAULT_CURRENCY,
                        balance=0.0,
                        daily_cap=self.settings.DAILY_SPEND_CAP,
                        monthly_cap=self.settings.MONTHLY_SPEND_CAP,
                    )
                    await self.wallets_collection.insert_one(wallet.to_mongo())
                else:
                    logger.debug(f"User with telegram_id {telegram_id} already exists, returning existing user")

                return user

        except Exception as e:
            logger.error(f"Failed to create user with telegram_id {telegram_id}: {e}")
            raise

    async def ensure_user_and_wallet(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        first_name: Optional[str] = None,
        language_code: Optional[str] = None,
    ) -> User:
        """Get existing user or create new one with wallet using atomic upsert"""
        try:
            async with database_transaction():
                # Create user object for potential insertion
                user = User(
                    telegram_id=telegram_id,
                    username=username,
                    first_name=first_name,
                    language_code=language_code,
                    consent_ack=True,
                )
                user_doc = user.to_mongo()

                # Use atomic upsert - this prevents race conditions entirely
                # $setOnInsert only sets values if this creates a new document
                result = await self.users_collection.update_one(
                    {"telegram_id": telegram_id},
                    {"$setOnInsert": user_doc},
                    upsert=True
                )

                # Get the user document (either newly created or existing)
                user_doc = await self.users_collection.find_one({"telegram_id": telegram_id})
                user = User.from_mongo(user_doc)

                # If this was a new insertion, create wallet and initial transaction
                if result.upserted_id:
                    logger.info(f"Created new user {user.id} with telegram_id {telegram_id}")
                    
                    # Create wallet with zero balance
                    wallet = Wallet(
                        user_id=str(user.id),
                        currency=self.settings.DEFAULT_CURRENCY,
                        balance=0.0,
                        daily_cap=self.settings.DAILY_SPEND_CAP,
                        monthly_cap=self.settings.MONTHLY_SPEND_CAP,
                    )
                    await self.wallets_collection.insert_one(wallet.to_mongo())
                else:
                    logger.debug(f"Retrieved existing user {user.id} with telegram_id {telegram_id}")
                    
                    # Update user profile if needed and different from what was requested
                    updated = False
                    updates = {}
                    
                    if username and user.username != username:
                        updates["username"] = username
                        updated = True
                    if first_name and user.first_name != first_name:
                        updates["first_name"] = first_name
                        updated = True
                    if language_code and user.language_code != language_code:
                        updates["language_code"] = language_code
                        updated = True

                    if updated:
                        from models.base import now_utc
                        updates["updated_at"] = now_utc()
                        await self.users_collection.update_one(
                            {"_id": user.id}, {"$set": updates}
                        )
                        # Update local object
                        for key, value in updates.items():
                            if key != "updated_at":
                                setattr(user, key, value)

                return user

        except Exception as e:
            logger.error(f"Failed to ensure user with telegram_id {telegram_id}: {e}")
            raise


    async def get_wallet_by_user_id(self, user_id: str) -> Optional[Wallet]:
        """Get wallet by user ID"""
        try:
            doc = await self.wallets_collection.find_one({"user_id": user_id})
            return Wallet.from_mongo(doc) if doc else None
        except Exception as e:
            logger.error(f"Failed to get wallet for user {user_id}: {e}")
            raise

    # Backwards-compatible helpers used by other services
    async def get_wallet(self, user_id: str) -> Optional[Wallet]:
        """Alias for get_wallet_by_user_id for compatibility."""
        return await self.get_wallet_by_user_id(user_id)

    async def update_wallet(self, wallet: Wallet) -> bool:
        """Persist wallet state to database.

        Updates the wallet document identified by wallet.user_id using to_update_dict().
        Returns True if an update was acknowledged.
        """
        try:
            if not wallet or not getattr(wallet, "user_id", None):
                raise ValueError("wallet.user_id is required to update wallet")

            res = await self.wallets_collection.update_one(
                {"user_id": wallet.user_id}, {"$set": wallet.to_update_dict()}
            )
            # For in-memory simulation, res may be True. For Mongo, check matched count
            if isinstance(res, bool):
                return res
            return getattr(res, "matched_count", 0) > 0
        except Exception as e:
            logger.error(f"Failed to update wallet for user {getattr(wallet,'user_id', None)}: {e}")
            return False

    @monitor_performance("add_funds")
    async def add_funds(
        self, user_id: str, amount: float, currency: str = "USD"
    ) -> Wallet:
        """Add funds to user's wallet"""
        # Input validation
        if not user_id or not user_id.strip():
            raise ValueError("User ID cannot be empty")
        if amount <= 0:
            raise ValueError("Amount must be positive")
        if amount > 10000:  # Reasonable upper limit
            raise ValueError("Amount too large (max $10,000)")
        if not currency or len(currency) != 3:
            raise ValueError("Invalid currency code")

        try:
            async with database_transaction():
                wallet = await self.get_wallet_by_user_id(user_id)
                if not wallet:
                    raise ValueError(f"Wallet not found for user {user_id}")

                # Check if wallet is locked
                if wallet.locked:
                    raise ValueError("Wallet is locked")

                # Update wallet balance
                wallet.credit(amount)
                await self.wallets_collection.update_one(
                    {"user_id": user_id}, {"$set": wallet.to_update_dict()}
                )

                # Create transaction record
                transaction = Transaction(
                    user_id=user_id,
                    type=TransactionType.ADD_FUNDS,
                    amount=amount,
                    currency=currency,
                    reference="manual_add_funds",
                )
                await self.transactions_collection.insert_one(transaction.to_mongo())

                logger.info(f"Added {amount} {currency} to wallet for user {user_id}")

                # Check for low balance warning after adding funds
                if (
                    wallet.balance < wallet.daily_cap * 0.1
                ):  # Less than 10% of daily cap
                    logger.info(
                        f"Low balance detected for user {user_id}: ${wallet.balance:.2f}"
                    )

                return wallet

        except ValueError as e:
            logger.warning(f"Validation error adding funds for user {user_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Database error adding funds for user {user_id}: {e}")
            raise RuntimeError("Failed to add funds to wallet") from e

    @monitor_performance("debit_wallet")
    async def debit_wallet(
        self,
        user_id: str,
        amount: float,
        currency: str = "USD",
        reference: str | None = None,
        metadata: dict | None = None,
        idempotency_key: str | None = None,
    ) -> Tuple[Wallet, Transaction]:
        """Debit amount from user's wallet"""
        if amount <= 0:
            raise ValueError("Amount must be positive")

        try:
            async with database_transaction():
                wallet = await self.get_wallet_by_user_id(user_id)
                if not wallet:
                    raise ValueError(f"Wallet not found for user {user_id}")

                # If idempotency key is provided and matching transaction exists, return it
                if idempotency_key:
                    try:
                        existing = await self.transactions_collection.find_one({
                            "hash": idempotency_key
                        })
                        if existing:
                            from models import Transaction as TxModel
                            return wallet, TxModel.from_mongo(existing)
                    except Exception:
                        # proceed if lookup fails
                        pass

                if not wallet.can_spend(amount):
                    raise ValueError("Insufficient funds or wallet locked")

                # Update wallet balance
                wallet.debit(amount)
                await self.wallets_collection.update_one(
                    {"user_id": user_id}, {"$set": wallet.to_update_dict()}
                )

                # Create transaction record with enhanced tracking
                api_version = metadata.get("api_version") if metadata else None
                transaction = Transaction(
                    user_id=user_id,
                    type=TransactionType.PURCHASE,
                    amount=amount,
                    currency=currency,
                    reference=reference or "purchase_debit",
                    api_version=api_version,
                    metadata=metadata,
                )
                # Apply deterministic hash if provided
                if idempotency_key:
                    transaction.hash = idempotency_key
                # Insert transaction
                ins = await self.transactions_collection.insert_one(
                    transaction.to_mongo()
                )
                try:
                    transaction.id = getattr(ins, "inserted_id", transaction.id)
                except Exception:
                    pass

                logger.info(
                    f"Debited {amount} {currency} from wallet for user {user_id}"
                )
                return wallet, transaction

        except Exception as e:
            logger.error(f"Failed to debit wallet for user {user_id}: {e}")
            raise

    async def delete_user_data(self, telegram_id: int) -> int:
        """Delete a user by telegram_id and all related data.

        Returns number of records deleted (best-effort estimate).
        """
        try:
            deleted = 0

            # Lookup user
            user_doc = await self.users_collection.find_one(
                {"telegram_id": telegram_id}
            )
            if not user_doc:
                return 0

            user_oid = user_doc.get("_id")
            user_id_str = str(user_oid)

            # Delete related collections first
            tx_res = await self.transactions_collection.delete_many(
                {"user_id": user_id_str}
            )
            deleted += getattr(tx_res, "deleted_count", 0)

            wallets_res = await self.wallets_collection.delete_many(
                {"user_id": user_id_str}
            )
            deleted += getattr(wallets_res, "deleted_count", 0)

            # Finally delete the user
            users_res = await self.users_collection.delete_one({"_id": user_oid})
            deleted += getattr(users_res, "deleted_count", 0)

            logger.info(
                f"Deleted user data for telegram_id={telegram_id} count={deleted}"
            )
            return deleted
        except Exception as e:
            logger.error(
                f"Failed to delete user data for telegram_id {telegram_id}: {e}"
            )
            raise

    async def delete_user_by_id(self, user_id: str) -> int:
        """Delete a user (by _id) and related data. Returns removed count."""
        try:
            deleted = 0
            # Find user
            user_doc = await self.users_collection.find_one({"_id": user_id})
            if not user_doc:
                # try ObjectId
                try:
                    from bson import ObjectId

                    oid = ObjectId(user_id)
                    user_doc = await self.users_collection.find_one({"_id": oid})
                except Exception:
                    user_doc = None
            if not user_doc:
                return 0
            user_oid = user_doc.get("_id")
            user_id_str = str(user_oid)
            tx_res = await self.transactions_collection.delete_many(
                {"user_id": user_id_str}
            )
            deleted += getattr(tx_res, "deleted_count", 0)
            wallets_res = await self.wallets_collection.delete_many(
                {"user_id": user_id_str}
            )
            deleted += getattr(wallets_res, "deleted_count", 0)
            users_res = await self.users_collection.delete_one({"_id": user_oid})
            deleted += getattr(users_res, "deleted_count", 0)
            return deleted
        except Exception as e:
            logger.error(f"Failed to delete user by id {user_id}: {e}")
            return 0

    async def bulk_set_role(self, user_ids: list[str], role: str) -> int:
        count = 0
        for uid in user_ids:
            ok = await self.set_user_role(uid, role)
            count += 1 if ok else 0
        return count

    async def bulk_set_active(self, user_ids: list[str], active: bool) -> int:
        count = 0
        for uid in user_ids:
            ok = await self.set_user_active(uid, active)
            count += 1 if ok else 0
        return count

    async def bulk_delete_users(self, user_ids: list[str]) -> int:
        total = 0
        for uid in user_ids:
            total += await self.delete_user_by_id(uid)
        return total

    # --- Admin-facing helpers ---------------------------------------------------
    async def list_users(self, page: int = 1, per_page: int = 10):
        """List users sorted by created_at desc with pagination"""
        skip = max(0, (page - 1) * per_page)
        cursor = (
            self.users_collection.find({})
            .sort("created_at", -1)
            .skip(skip)
            .limit(per_page)
        )
        docs = await cursor.to_list(per_page)
        # Use absolute import to avoid relative import beyond top-level
        from models import User as UserModel

        return [UserModel.from_mongo(d) for d in docs]

    async def count_users(self) -> int:
        try:
            return await self.users_collection.count_documents({})
        except Exception as e:
            logger.error(f"Failed to count users: {e}")
            return 0

    @monitor_performance("user_search")
    async def search_users(self, query: str, page: int = 1, per_page: int = 10):
        """Search users by username substring (case-insensitive) or telegram_id exact.

        Uses optimized database queries instead of Python-side filtering.
        """
        try:
            # Build optimized query
            mongo_query = QueryOptimizer.build_user_search_query(query or "")

            # Get total count for pagination
            total_count = await self.users_collection.count_documents(mongo_query)

            # Calculate pagination
            skip = max(0, (page - 1) * per_page)

            # Execute optimized query with pagination
            cursor = (
                self.users_collection.find(mongo_query)
                .sort("created_at", -1)
                .skip(skip)
                .limit(per_page)
            )
            docs = await cursor.to_list(length=per_page)

            # Convert to User objects
            from models import User as UserModel

            users = [UserModel.from_mongo(d) for d in docs]

            return users, total_count

        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            return [], 0

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        try:
            doc = await self.users_collection.find_one({"_id": user_id})
            if not doc:
                # attempt ObjectId lookup if needed
                try:
                    from bson import ObjectId

                    oid = ObjectId(user_id)
                    doc = await self.users_collection.find_one({"_id": oid})
                except Exception:
                    pass
            return User.from_mongo(doc) if doc else None
        except Exception as e:
            logger.error(f"Failed to get user by id {user_id}: {e}")
            return None

    async def set_user_role(self, user_id: str, role: str) -> bool:
        valid_roles = {"user", "admin", "moderator"}
        if role not in valid_roles:
            raise ValueError("Invalid role")
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            user.role = role
            user.update_timestamp()
            await self.users_collection.update_one(
                {"_id": user.id}, {"$set": user.to_update_dict()}
            )
            return True
        except Exception as e:
            logger.error(f"Failed to set role for user {user_id}: {e}")
            return False

    async def set_user_active(self, user_id: str, active: bool) -> bool:
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            user.active = active
            user.update_timestamp()
            await self.users_collection.update_one(
                {"_id": user.id}, {"$set": user.to_update_dict()}
            )
            return True
        except Exception as e:
            logger.error(f"Failed to set active for user {user_id}: {e}")
            return False

    async def export_users_csv(self) -> bytes:
        """Export all users to CSV bytes"""
        try:
            cursor = self.users_collection.find({}).sort("created_at", -1)
            docs = await cursor.to_list(None)
            import csv
            import io

            buf = io.StringIO()
            writer = csv.writer(buf)
            writer.writerow(
                [
                    "id",
                    "telegram_id",
                    "username",
                    "first_name",
                    "role",
                    "active",
                    "created_at",
                ]
            )
            for d in docs:
                writer.writerow(
                    [
                        str(d.get("_id")),
                        d.get("telegram_id"),
                        d.get("username") or "",
                        d.get("first_name") or "",
                        d.get("role") or "user",
                        d.get("active", True),
                        d.get("created_at"),
                    ]
                )
            return buf.getvalue().encode("utf-8")
        except Exception as e:
            logger.error(f"Failed to export users: {e}")
            return b""


# Global instance helper for compatibility with modules expecting a singleton
_user_service: Optional[UserService] = None


def get_user_service() -> UserService:
    """Get global UserService instance (singleton-style)."""
    global _user_service
    if _user_service is None:
        _user_service = UserService()
    return _user_service
