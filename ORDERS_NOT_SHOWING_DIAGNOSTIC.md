# Orders Not Showing - Diagnostic & Fix Guide

## 🔍 Problem

User reports: "my recently purchased card is not showing in my orders"

## 📋 Potential Causes & Analysis

### 1. **Circular Reference Error (FIXED)** ✅

**Previous Issue**: Purchase records failed to save due to BSON encoding errors from circular references.

**Error Logs**:
```
❌ Failed to create purchase record: maximum recursion depth exceeded while encoding an object to BSON
```

**Fix Applied**:
- Enhanced `_clean_for_bson` with visited set tracking (Lines 895-961 in `checkout_queue_service.py`)
- Clean entire `purchase_doc` before database insertion (Line 3148 in `checkout_queue_service.py`)
- Graceful handling of JSON serialization failures (Lines 234-244 in `card_data_extractor.py`)

**Status**: ✅ Fixed - New purchases should now be saved correctly

### 2. **Query Sorting**

**Current Implementation**: `services/order_management_service.py`
```python
async def get_user_orders_paginated(
    self,
    user_id: str,
    page: int = 1,
    page_size: int = 5,
    status_filter: Optional[str] = None,
    sort_by: str = "created_at",  # ✅ Correct field
    sort_order: int = -1  # ✅ DESC (most recent first)
):
    query = {"user_id": user_id}
    if status_filter:
        query["status"] = status_filter
    
    cursor = (
        self.purchases.find(query)
        .sort(sort_by, sort_order)  # ✅ Sorts by created_at DESC
        .skip(skip)
        .limit(page_size)
    )
```

**Status**: ✅ Correct - Sorting logic is proper

### 3. **Purchase Record Creation**

**Flow**: `services/checkout_queue_service.py` (Lines 3095-3160)

```python
# For each item in checkout
for item in items:
    card_pk = str(item.get("card_id", ""))
    
    # Create Purchase object with BaseDocument (has created_at)
    purchase = Purchase(
        user_id=user_id,
        sku=f"card_{card_pk}",
        price=float(item.get("price_at_add", 0)),
        # ... other fields
    )
    
    # Convert to mongo document
    purchase_doc = purchase.to_mongo()  # ✅ Has created_at from BaseDocument
    
    # Add raw_data and extracted_cards
    if raw_data:
        clean_raw_data = self._clean_for_bson(raw_data)
        purchase_doc["raw_data"] = clean_raw_data
    
    if extracted_cards:
        clean_extracted_cards = self._clean_for_bson(extracted_cards)
        purchase_doc["extracted_cards"] = clean_extracted_cards
    
    # CRITICAL FIX: Clean entire document
    purchase_doc = self._clean_for_bson(purchase_doc)  # ✅ NEW
    
    # Insert to database
    await purchases_collection.insert_one(purchase_doc)
    logger.info(f"✅ Purchase record created successfully for card {card_pk}")
```

**Status**: ✅ Fixed - Now handles circular references properly

### 4. **Unavailable Cards Handling (NEW)** ✅

**New Feature**: If some cards become unavailable during checkout:
- User is notified which cards are unavailable
- Checkout continues with available cards
- Only available cards create purchase records

**Status**: ✅ Working as designed

## 🧪 Testing Steps

### Step 1: Check for Old Failed Purchases

**Before the fix**, purchases might have failed without creating records.

**To check logs**:
```powershell
cd logs
Get-Content all.log -Tail 500 | Select-String -Pattern "❌ Failed to create purchase record"
```

If you see errors, those purchases **did NOT create records** (before the fix).

### Step 2: Test New Purchase

With the bot running **after the circular reference fix**:

1. **Add a card to cart**
2. **Click "Buy Now"**
3. **Monitor logs** for:
   ```
   ✅ Purchase record created successfully for card...
   ```
4. **Check "My Orders"** - Card should appear

### Step 3: Verify Database Directly

**Using MongoDB Compass or MongoDB Shell**:

```javascript
// Connect to database
use demo_wallet_bot

// Count total purchases
db.purchases.countDocuments({})

// Get recent purchases
db.purchases.find().sort({created_at: -1}).limit(10)

// Check for a specific user
db.purchases.find({user_id: "YOUR_USER_ID"}).sort({created_at: -1})
```

## 🔧 Potential Issues & Solutions

### Issue A: Old Purchases Missing (Before Fix)

**Symptom**: Purchases made before October 26, 2025 (before circular reference fix) don't show up.

**Reason**: Those purchases failed to save due to BSON errors.

**Solution**: Those purchases are lost. User needs to repurchase. The system is now fixed.

**User Communication**:
```
⚠️ Some older purchases may not have been saved correctly due to a system issue 
that has now been fixed. 

✅ New purchases will now save properly. If you don't see a recent purchase, 
please contact support for assistance.
```

### Issue B: Circular Reference Markers in Data

**Symptom**: Some fields show `<<CIRCULAR_REFERENCE>>` in the database.

**Impact**: Data is saved, but some nested fields might have markers instead of actual data.

**Check**:
```javascript
db.purchases.find({"raw_data": {$regex: "CIRCULAR_REFERENCE"}}).count()
```

**Solution**: This is expected and safe. The important card data (`extracted_cards`) should still be complete.

### Issue C: Created Recently But Not Showing

**Possible Causes**:

1. **Checkout failed** - Check logs for errors
   ```
   ❌ Checkout failed after 1 attempts
   ```

2. **All cards unavailable** - Check for notification
   ```
   ❌ All cards are no longer available
   ```

3. **Balance insufficient** - Check for balance error
   ```
   ❌ Insufficient funds
   ```

4. **Cache issue** - Orders list might be cached

**Solutions**:

1. **Check logs** for actual error
2. **Restart bot** to clear any caching
3. **Try purchasing again** with the fixed code

## 📊 Verification Checklist

After restarting the bot with fixes:

- [ ] Bot starts without errors
- [ ] Can view existing orders (if any)
- [ ] Can add card to cart
- [ ] Checkout completes successfully
- [ ] Logs show: `✅ Purchase record created successfully`
- [ ] Checkout completes successfully
- [ ] Card appears in "My Orders"
- [ ] Can view card details
- [ ] Can unmask/check card (if applicable)

## 🚀 Recommended Actions

### For User

1. **Restart the bot** to load the fixed code
   ```bash
   # Stop the bot (Ctrl+C)
   # Start the bot again
   python main.py
   ```

2. **Test a new purchase**:
   - Add a small/cheap card to cart
   - Complete checkout
   - Check "My Orders"

3. **Check logs** if issues persist:
   ```powershell
   cd logs
   Get-Content all.log -Tail 100
   ```

### For Developer

1. **Monitor logs** during next checkout:
   ```
   ✅ Purchase record created successfully
   ✅ Added raw_data to purchase record
   ✅ Added X extracted_cards to purchase record
   ```

2. **If still failing**, check for:
   - Syntax errors in modified files
   - Database connection issues
   - Permission issues

3. **Verify database insertion**:
   - Connect to MongoDB
   - Check purchases collection
   - Verify `created_at` field exists

## 📝 Files Modified (Recent Fixes)

1. ✅ `services/checkout_queue_service.py` (Lines 895-961, 3148)
   - Enhanced circular reference handling
   - Complete document cleaning before insertion

2. ✅ `handlers/orders_handlers.py` (Lines 981-1049)
   - Same circular reference handling for card updates

3. ✅ `utils/card_data_extractor.py` (Lines 234-244)
   - Graceful JSON serialization failure handling

4. ✅ `services/checkout_queue_service.py` (Lines 2616-2680, 1837-1917, 726-785)
   - Unavailable cards handling with user notification

## 📅 Timeline

- **Before Oct 26, 2025**: Purchases may have failed silently
- **Oct 26, 2025**: Circular reference fix implemented
- **Current**: System should save purchases correctly

---

## 🎯 Expected Behavior NOW

### Successful Checkout
```
1. User clicks "Buy Now"
2. Cart validation passes (or partial with available cards)
3. Balance deducted
4. External API checkout succeeds
5. ✅ Purchase record created with cleaned data
6. ✅ Card appears in "My Orders" immediately
7. User can view/unmask/check card
```

### Failed Checkout
```
1. User clicks "Buy Now"
2. Validation or API call fails
3. ❌ Clear error message to user
4. ❌ No purchase record created (expected)
5. Balance refunded if deducted
```

---

**Status**: ✅ **System Fixed - Ready for Testing**

Please restart the bot and test a new purchase to confirm the fix is working!

