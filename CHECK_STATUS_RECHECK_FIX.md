# Check Status Recheck Button Fix

## Issue
When checking card status, if the API returns a status of "checking", "started", "pending", or "processing", the UI was showing either:
- A "checked" status button (incorrect)
- A timer-based check button (not desired)

Instead, it should show a "🔄 Recheck Status" button **without any timer** until the correct final status is received from the API.

## Solution
Updated multiple handlers to properly detect when a card is in a "checking" state and display the recheck button without a timer.

### Changes Made

#### 1. `cb_check_card` Hand<PERSON> (lines 5511-5546)
**Location:** `handlers/orders_handlers.py`

**What Changed:**
- Added logic to detect if the card status is in a checking state (`checking`, `started`, `pending`, `processing`)
- When detected, sets:
  - `check_status = "checking"`
  - `can_check_again = True`
  - `expiry_timestamp = None` (no timer)
- For completed statuses (`nonrefundable`, `refunded`, `live`, `dead`, etc.), sets:
  - `check_status = "completed"`
  - `can_check_again = False`

**Result:** After a check operation, if the status is still "checking", the UI shows "🔄 Recheck Status" button without timer.

#### 2. `cb_view_purchased_card` - API v1 Logic (lines 2657-2665)
**Location:** `handlers/orders_handlers.py`

**What Changed:**
- Updated the logic for "started", "pending", "checking", and "processing" statuses
- Changed from showing timer-based check button to showing recheck button without timer
- Sets:
  - `check_status = "checking"`
  - `can_check = True`
  - `expiry_timestamp = None`

**Result:** When viewing a card with pending/checking status, shows "🔄 Recheck Status" without timer.

#### 3. `cb_view_purchased_card` - API v3 Logic (lines 2712-2720)
**Location:** `handlers/orders_handlers.py`

**What Changed:**
- Added check for card status at the beginning of the logic
- If status is in checking state, shows recheck button without timer
- If status is completed, follows existing logic

**Result:** API v3 cards with pending/checking status also show recheck button without timer.

#### 4. `cb_view_pending_card` Handler (lines 5644-5653)
**Location:** `handlers/orders_handlers.py`

**What Changed:**
- Added status check at the beginning of keyboard creation
- If card status is "checking", "started", "pending", or "processing", shows "🔄 Recheck Status" button
- Otherwise, follows existing logic for timer-based check

**Result:** Pending card view properly shows recheck button without timer.

## Status Detection
The fix detects the following statuses as "checking in progress":
- `checking`
- `started`
- `pending`
- `processing`

The following statuses are considered "completed":
- `nonrefundable` (LIVE)
- `refunded` (DEAD)
- `live`
- `active`
- `valid`
- `approved`
- `dead`
- `invalid`
- `declined`

## UI Behavior

### Before Fix
- Status "checking" → Shows "✅ Status Checked" or timer-based button
- User cannot recheck until timer expires
- Confusing when check is still in progress

### After Fix
- Status "checking" → Shows "🔄 Recheck Status" button
- Button has NO timer
- User can recheck immediately at any time
- Button remains visible until status changes to completed

## Technical Details

The fix leverages the existing `create_card_view_keyboard` function in `utils/post_checkout_ui.py` (lines 409-415), which already had logic to show "🔄 Recheck Status" when `check_status == "checking"`. The issue was that handlers were not properly setting `check_status = "checking"` when receiving pending/checking statuses from the API.

## Testing Recommendations

1. **Test Check Flow:**
   - Perform a card check
   - If API returns "checking" status, verify "🔄 Recheck Status" button appears without timer
   - Click recheck button, verify it works without delay
   - Continue rechecking until status changes to completed

2. **Test View Card Flow:**
   - View a card with "Started" or "Pending" status
   - Verify "🔄 Recheck Status" button appears without timer
   - Click to check, verify functionality

3. **Test Completed Status:**
   - Check a card that returns "NonRefundable" or "Refunded"
   - Verify check button is removed or shows "✅ Status Checked"
   - Verify user cannot recheck

## Files Modified
- `handlers/orders_handlers.py` (4 locations updated)

## Date
October 26, 2025

