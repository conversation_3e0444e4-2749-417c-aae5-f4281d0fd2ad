"""
Circuit Breaker Pattern Implementation

This module implements the circuit breaker pattern to prevent cascading failures
and reduce load on failing services. When a service fails repeatedly, the circuit
breaker "opens" and prevents further requests for a specified time period.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
from utils.central_logger import get_logger

logger = get_logger()

from utils.api_logging import logger


class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Blocking requests due to failures
    HALF_OPEN = "half_open"  # Testing if service has recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5          # Number of failures before opening
    recovery_timeout: int = 60          # Seconds to wait before trying again
    success_threshold: int = 3          # Successes needed to close circuit
    timeout: int = 30                   # Request timeout in seconds
    
    
@dataclass
class CircuitBreakerStats:
    """Statistics for circuit breaker"""
    state: CircuitState
    failure_count: int
    success_count: int
    last_failure_time: Optional[datetime]
    last_success_time: Optional[datetime]
    total_requests: int
    total_failures: int
    total_successes: int
    opened_at: Optional[datetime]
    next_attempt_time: Optional[datetime]


class CircuitBreaker:
    """
    Circuit breaker implementation for protecting against cascading failures.
    
    States:
    - CLOSED: Normal operation, requests pass through
    - OPEN: Service is failing, requests are blocked
    - HALF_OPEN: Testing if service has recovered
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        
        # State tracking
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.last_success_time: Optional[datetime] = None
        self.opened_at: Optional[datetime] = None
        
        # Statistics
        self.total_requests = 0
        self.total_failures = 0
        self.total_successes = 0
        
        # Lock for thread safety
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function through the circuit breaker.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenError: When circuit is open
            Exception: Original function exceptions when circuit is closed
        """
        async with self._lock:
            self.total_requests += 1
            
            # Check if circuit should be opened
            if self.state == CircuitState.CLOSED and self._should_open():
                await self._open_circuit()
            
            # Check if circuit should transition to half-open
            elif self.state == CircuitState.OPEN and self._should_attempt_reset():
                await self._half_open_circuit()
            
            # Block requests if circuit is open
            if self.state == CircuitState.OPEN:
                next_attempt = self.opened_at + timedelta(seconds=self.config.recovery_timeout)
                raise CircuitBreakerOpenError(
                    f"Circuit breaker '{self.name}' is OPEN. "
                    f"Next attempt at {next_attempt.strftime('%H:%M:%S')}"
                )
        
        # Execute the function
        try:
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            await self._on_success()
            return result
            
        except Exception as e:
            await self._on_failure(e)
            raise
    
    def _should_open(self) -> bool:
        """Check if circuit should be opened due to failures"""
        return self.failure_count >= self.config.failure_threshold
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit should attempt to reset (half-open)"""
        if not self.opened_at:
            return False
        
        recovery_time = self.opened_at + timedelta(seconds=self.config.recovery_timeout)
        return datetime.utcnow() >= recovery_time
    
    async def _open_circuit(self):
        """Open the circuit breaker"""
        self.state = CircuitState.OPEN
        self.opened_at = datetime.utcnow()
        self.success_count = 0
        
        logger.warning(
            f"🔴 Circuit breaker '{self.name}' OPENED due to {self.failure_count} failures. "
            f"Will retry at {(self.opened_at + timedelta(seconds=self.config.recovery_timeout)).strftime('%H:%M:%S')}"
        )
    
    async def _half_open_circuit(self):
        """Transition circuit to half-open state"""
        self.state = CircuitState.HALF_OPEN
        self.success_count = 0
        self.failure_count = 0
        
        logger.info(f"🟡 Circuit breaker '{self.name}' is HALF-OPEN, testing service recovery")
    
    async def _close_circuit(self):
        """Close the circuit breaker (normal operation)"""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.opened_at = None
        
        logger.info(f"🟢 Circuit breaker '{self.name}' CLOSED, service recovered")
    
    async def _on_success(self):
        """Handle successful request"""
        async with self._lock:
            self.total_successes += 1
            self.last_success_time = datetime.utcnow()
            
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    await self._close_circuit()
            elif self.state == CircuitState.CLOSED:
                # Reset failure count on success
                self.failure_count = 0
    
    async def _on_failure(self, exception: Exception):
        """Handle failed request"""
        async with self._lock:
            self.total_failures += 1
            self.last_failure_time = datetime.utcnow()
            
            if self.state in [CircuitState.CLOSED, CircuitState.HALF_OPEN]:
                self.failure_count += 1
                
                # If in half-open and we get a failure, go back to open
                if self.state == CircuitState.HALF_OPEN:
                    await self._open_circuit()
    
    def get_stats(self) -> CircuitBreakerStats:
        """Get current circuit breaker statistics"""
        next_attempt_time = None
        if self.state == CircuitState.OPEN and self.opened_at:
            next_attempt_time = self.opened_at + timedelta(seconds=self.config.recovery_timeout)
        
        return CircuitBreakerStats(
            state=self.state,
            failure_count=self.failure_count,
            success_count=self.success_count,
            last_failure_time=self.last_failure_time,
            last_success_time=self.last_success_time,
            total_requests=self.total_requests,
            total_failures=self.total_failures,
            total_successes=self.total_successes,
            opened_at=self.opened_at,
            next_attempt_time=next_attempt_time
        )
    
    def is_available(self) -> bool:
        """Check if the circuit allows requests"""
        if self.state == CircuitState.OPEN:
            return self._should_attempt_reset()
        return True
    
    def get_failure_rate(self) -> float:
        """Get current failure rate as percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.total_failures / self.total_requests) * 100
    
    async def reset(self):
        """Manually reset the circuit breaker"""
        async with self._lock:
            await self._close_circuit()
            logger.info(f"🔄 Circuit breaker '{self.name}' manually reset")


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class CircuitBreakerManager:
    """
    Manager for multiple circuit breakers
    """
    
    def __init__(self):
        self.breakers: Dict[str, CircuitBreaker] = {}
    
    def get_breaker(self, name: str, config: CircuitBreakerConfig = None) -> CircuitBreaker:
        """Get or create a circuit breaker"""
        if name not in self.breakers:
            self.breakers[name] = CircuitBreaker(name, config)
        return self.breakers[name]
    
    def get_all_stats(self) -> Dict[str, CircuitBreakerStats]:
        """Get statistics for all circuit breakers"""
        return {name: breaker.get_stats() for name, breaker in self.breakers.items()}
    
    async def reset_all(self):
        """Reset all circuit breakers"""
        for breaker in self.breakers.values():
            await breaker.reset()
    
    def get_summary(self) -> Dict[str, str]:
        """Get a summary of all circuit breaker states"""
        summary = {}
        for name, breaker in self.breakers.items():
            stats = breaker.get_stats()
            summary[name] = {
                "state": stats.state.value,
                "failure_rate": f"{breaker.get_failure_rate():.1f}%",
                "total_requests": stats.total_requests,
                "available": breaker.is_available()
            }
        return summary


# Global circuit breaker manager
circuit_breaker_manager = CircuitBreakerManager()
