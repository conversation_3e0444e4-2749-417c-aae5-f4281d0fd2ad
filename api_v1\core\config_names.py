"""
API v1 Configuration Name Constants

This module provides standardized configuration names for API v1 services.
Use these constants throughout the codebase for consistency.

Example:
    from api_v1.core.config_names import API_V1_BASE1
    
    config = await config_service.get_api_config_by_name(API_V1_BASE1)
"""

from typing import Dict

# Primary API configurations (use these!)
API_V1_BASE1 = "api_v1_base1"  # Primary BIN cards API (formerly "api1")
API_V1_CART = "api_v1_cart"    # Cart operations API (formerly "api1_external_cart")
API_V1_DUMPS = "api_v1_dumps"  # DUMPS v1 API (formerly "dump_api1")
API_V2_BASE2 = "api_v2_base2"  # Secondary BIN API (formerly "api2")
API_V3_BROWSE = "api_v3_browse"  # API v3 browse service

# Legacy name mappings (for backward compatibility)
# DO NOT USE THESE DIRECTLY - they will be deprecated
LEGACY_ALIASES: Dict[str, str] = {
    "api1": API_V1_BASE1,
    "api": API_V1_BASE1,
    "api_v1": API_V1_BASE1,
    "api1_external_cart": API_V1_CART,
    "api2": API_V2_BASE2,
    "dump_api1": API_V1_DUMPS,
}


def normalize_config_name(name: str) -> str:
    """
    Convert legacy config names to standardized format.
    
    This function provides backward compatibility by mapping old
    configuration names to new standardized names.
    
    Args:
        name: Configuration name (can be legacy or new format)
        
    Returns:
        Standardized configuration name
        
    Example:
        >>> normalize_config_name("api1")
        'api_v1_base1'
        
        >>> normalize_config_name("api_v1_base1")
        'api_v1_base1'
    """
    name_lower = name.lower()
    return LEGACY_ALIASES.get(name_lower, name)


def is_legacy_name(name: str) -> bool:
    """Check if a name is a legacy format"""
    return name.lower() in LEGACY_ALIASES


def get_all_config_names() -> Dict[str, str]:
    """
    Get all configuration names with their descriptions.
    
    Returns:
        Dict mapping config names to descriptions
    """
    return {
        API_V1_BASE1: "API v1 - BASE 1 (Primary BIN Cards)",
        API_V1_CART: "API v1 - Cart Operations",
        API_V1_DUMPS: "API v1 - DUMPS v1",
        API_V2_BASE2: "API v2 - BASE 2 (Secondary BIN)",
        API_V3_BROWSE: "API v3 - Browse Service",
    }


# Product ID to config name mapping
PRODUCT_CONFIG_MAP: Dict[str, str] = {
    "bin_base_1": API_V1_BASE1,
    "bin_base_2": API_V2_BASE2,
    "bin_base_3": API_V3_BROWSE,
    "dump_base_1": API_V1_DUMPS,
}


def get_config_for_product(product_id: str) -> str:
    """Get configuration name for a product ID"""
    return PRODUCT_CONFIG_MAP.get(product_id, product_id)


# Export all constants
__all__ = [
    "API_V1_BASE1",
    "API_V1_CART",
    "API_V1_DUMPS",
    "API_V2_BASE2",
    "API_V3_BROWSE",
    "LEGACY_ALIASES",
    "normalize_config_name",
    "is_legacy_name",
    "get_all_config_names",
    "PRODUCT_CONFIG_MAP",
    "get_config_for_product",
]
