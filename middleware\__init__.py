"""
Middleware setup for the bot
"""

from __future__ import annotations

from aiogram import Di<PERSON>atcher, Router

from middleware.rate_limiting import RateLimitMiddleware
from middleware.user_context import UserContextMiddleware
from middleware.error_handling import ErrorHandlingMiddleware

from utils.central_logger import get_logger

logger = get_logger()


def setup_middleware(dp: Dispatcher) -> None:
    """Setup all middleware"""
    try:
        # Attach only global error handling at dispatcher level
        dp.message.middleware(ErrorHandlingMiddleware())
        dp.callback_query.middleware(ErrorHandlingMiddleware())

        logger.info("All middleware setup completed")

    except Exception as e:
        logger.error(f"Failed to setup middleware: {e}")
        raise


def attach_common_middlewares(router: Router) -> None:
    """Attach commonly used middlewares to router (per-router scope)."""
    # Rate limiting and user context per-router to avoid duplicate runs
    router.message.middleware(RateLimitMiddleware())
    router.callback_query.middleware(RateLimitMiddleware())

    router.message.middleware(UserContextMiddleware())
    router.callback_query.middleware(UserContextMiddleware())
