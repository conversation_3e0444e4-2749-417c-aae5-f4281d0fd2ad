"""
Synchronous database operations for payment processing.
This module provides synchronous database operations for the Flask server
to avoid event loop issues.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure, ServerSelectionTimeoutError

logger = logging.getLogger(__name__)

# Global client and database connections
_client = None
_db = None
_connection_failed = False  # Track if connection has failed to avoid repeated attempts

def _reset_connection():
    """Reset the global database connection."""
    global _client, _db, _connection_failed
    if _client:
        try:
            _client.close()
        except:
            pass
    _client = None
    _db = None
    _connection_failed = False
    logger.info("Database connection reset")


def _get_database_connection(retry=True):
    """Get synchronous database connection with robust error handling."""
    global _client, _db, _connection_failed
    
    # If connection previously failed and we're not retrying, raise immediately
    if _connection_failed and not retry:
        raise RuntimeError("Database connection previously failed")
    
    if _client is None:
        try:
            from config.settings import get_settings
            settings = get_settings()
            
            logger.info(f"Attempting to connect to MongoDB...")
            
            # Create synchronous MongoDB client with increased timeouts
            # and connection pooling to handle DNS/network issues
            _client = MongoClient(
                settings.MONGODB_URL,
                serverSelectionTimeoutMS=30000,  # 30 seconds for slow networks
                connectTimeoutMS=20000,  # 20 seconds connection timeout
                socketTimeoutMS=20000,   # 20 seconds socket timeout
                maxPoolSize=10,          # Connection pool
                minPoolSize=1,
                maxIdleTimeMS=45000,     # Keep connections alive
                retryWrites=True,        # Auto-retry writes
                retryReads=True,         # Auto-retry reads
            )
            _db = _client[settings.DATABASE_NAME]
            
            # Test connection with timeout
            _client.admin.command('ping', maxTimeMS=10000)
            logger.info("✅ Synchronous database connection established for payment module")
            _connection_failed = False
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to database - DNS or network issue: {e}")
            logger.error("Possible causes: DNS servers not responding, network connectivity issues, or MongoDB server unreachable")
            _client = None
            _db = None
            _connection_failed = True
            raise RuntimeError(f"Payment module requires database connection: {e}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            _client = None
            _db = None
            _connection_failed = True
            raise RuntimeError(f"Payment module requires database connection: {e}")
    
    return _client, _db

def save_payment_details_sync(**kwargs) -> Optional[Dict[str, Any]]:
    """
    Synchronously saves payment details. Returns payment dict on success.
    """
    try:
        # Extract the amount from kwargs
        amount = float(kwargs.get("amount", 0))

        # Create the payment record
        payment = {
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "status": "pending",
            "requested_amount": amount,
            "actual_paid_amount": 0,
            "payment_verified": False,
            **kwargs,
        }

        # Log the payment creation
        logger.info(f"Creating payment record for track_id {kwargs.get('track_id')}: ${amount:.2f}")

        # Get database connection
        client, db = _get_database_connection()
        payments_collection = db.payments
        
        # Insert payment
        result = payments_collection.insert_one(payment)
        
        if result.acknowledged:
            payment["_id"] = result.inserted_id
            logger.info(f"✅ Payment saved to database: track_id={kwargs.get('track_id')}")
            return payment
        else:
            logger.error(f"Payment insert not acknowledged for track_id {kwargs.get('track_id')}")
            return None
                
    except Exception as e:
        logger.error(f"Error saving payment details: {e}")
        return None

def get_payment_by_track_id_sync(track_id: str) -> Optional[Dict[str, Any]]:
    """Synchronously gets payment details by track_id."""
    try:
        # Ensure track_id is a string
        track_id = str(track_id) if track_id else None
        
        if not track_id:
            logger.error("No track_id provided")
            return None

        logger.info(f"Looking up payment for track_id: {track_id}")

        # Get database connection
        client, db = _get_database_connection()
        payments_collection = db.payments
        
        # Find payment by track_id
        payment = payments_collection.find_one({"track_id": track_id})
        
        if payment:
            # Convert ObjectId to string for JSON serialization
            if '_id' in payment:
                payment['_id'] = str(payment['_id'])
            logger.info(f"✅ Payment found in database: {track_id}")
            return payment
        else:
            logger.warning(f"Payment not found for track_id: {track_id}")
            return None
            
    except Exception as e:
        logger.error(f"Error getting payment by track_id ({track_id}): {e}")
        return None

def update_payment_status_sync(track_id: str, new_status: str, **kwargs) -> bool:
    """Synchronously updates payment status."""
    try:
        # Get database connection
        client, db = _get_database_connection()
        payments_collection = db.payments
        
        # Update payment
        update_data = {
            "status": new_status,
            "updated_at": datetime.now(),
            **kwargs
        }
        
        result = payments_collection.update_one(
            {"track_id": track_id},
            {"$set": update_data}
        )
        
        if result.modified_count > 0:
            logger.info(f"✅ Payment status updated to {new_status} for track_id: {track_id}")
            return True
        else:
            logger.warning(f"No payment updated for track_id: {track_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error updating payment status for track_id ({track_id}): {e}")
        return False

def get_wallet_balance_sync(user_id: int) -> float:
    """Synchronously gets wallet balance (single source of truth)."""
    try:
        # Get database connection
        client, db = _get_database_connection()
        users_collection = db.users
        wallets_collection = db.wallets
        
        # Get user document to find user_id
        user_doc = users_collection.find_one({"telegram_id": user_id})
        if user_doc and user_doc.get("_id"):
            # Get wallet balance
            wallet_doc = wallets_collection.find_one({"user_id": str(user_doc.get("_id"))})
            if wallet_doc:
                balance = wallet_doc.get("balance", 0.0)
                logger.info(f"User {user_id} wallet balance: ${balance:.2f}")
                return float(balance)
            else:
                logger.warning(f"Wallet not found for user {user_id}")
                return 0.0
        else:
            logger.warning(f"User {user_id} not found")
            return 0.0
            
    except Exception as e:
        logger.error(f"Error getting wallet balance for user_id ({user_id}): {e}")
        return 0.0

def get_user_balance_sync(user_id: int) -> float:
    """DEPRECATED: Use get_wallet_balance_sync instead. This is kept for backward compatibility."""
    return get_wallet_balance_sync(user_id)

def update_user_balance_sync(user_id: int, new_balance: float) -> bool:
    """Synchronously updates user balance."""
    try:
        # Get database connection
        client, db = _get_database_connection()
        users_collection = db.users
        
        # Update user balance (create user if not exists)
        result = users_collection.update_one(
            {"telegram_id": user_id},
            {
                "$set": {"balance": new_balance, "updated_at": datetime.now()},
                "$setOnInsert": {"telegram_id": user_id, "created_at": datetime.now(), "last_seen": datetime.now()}
            },
            upsert=True
        )
        
        if result.modified_count > 0:
            logger.info(f"✅ User {user_id} balance updated to ${new_balance:.2f}")
            return True
        else:
            logger.warning(f"No user updated for user_id: {user_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error updating user balance for user_id ({user_id}): {e}")
        return False

def add_transaction_sync(user_id: int, amount: float, transaction_type: str, **kwargs) -> bool:
    """Synchronously adds a transaction record."""
    try:
        # Get database connection
        client, db = _get_database_connection()
        transactions_collection = db.transactions
        
        # Create transaction record with proper hash generation
        import hashlib
        import time
        
        # Generate unique hash to avoid duplicate key errors
        timestamp = time.time()
        random_salt = hashlib.sha256(f"{timestamp}:{user_id}:{amount}".encode()).hexdigest()[:8]
        hash_value = hashlib.sha256(f"{transaction_type}:{user_id}:{amount}:{timestamp}:{random_salt}".encode()).hexdigest()
        
        transaction = {
            "user_id": user_id,
            "amount": amount,
            "transaction_type": transaction_type,
            "created_at": datetime.now(),
            "hash": hash_value,
            **kwargs
        }
        
        # Insert transaction
        result = transactions_collection.insert_one(transaction)
        
        if result.acknowledged:
            logger.info(f"✅ Transaction recorded for user {user_id}: {transaction_type} ${amount:.2f}")
            return True
        else:
            logger.error(f"Transaction insert not acknowledged for user {user_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error adding transaction for user_id ({user_id}): {e}")
        return False
