"""
Admin handlers for Response Processor UI management.
Provides Telegram interface for monitoring and configuring response processing.
"""

import asyncio
from typing import Optional

from aiogram import Router, F
from aiogram.types import Message, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.filters import Command

from utils.central_logger import get_logger
from utils.decorators import admin_required, handle_errors
from utils.response_processor import get_response_processor
from middleware import attach_common_middlewares

logger = get_logger()


class ResponseProcessorAdminHandlers:
    """Admin handlers for response processor management."""
    
    def __init__(self):
        self.response_processor = get_response_processor()
    
    @admin_required
    @handle_errors(error_message="❌ Failed to show response processor status")
    async def cmd_processor_status(self, message: Message):
        """Show response processor status and statistics."""
        try:
            # Get comprehensive stats
            stats = self.response_processor.get_stats()
            summary = self.response_processor.get_admin_summary()
            
            # Create management keyboard
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="🔧 Configure", callback_data="admin:processor:config"),
                    InlineKeyboardButton(text="📊 Detailed Stats", callback_data="admin:processor:stats")
                ],
                [
                    InlineKeyboardButton(text="🔄 Refresh", callback_data="admin:processor:refresh"),
                    InlineKeyboardButton(text="🏠 Admin Menu", callback_data="admin:main")
                ]
            ])
            
            await message.answer(summary, reply_markup=keyboard, parse_mode="HTML")
            
        except Exception as e:
            logger.error(f"Error showing processor status: {e}")
            await message.answer("❌ Failed to get processor status")
    
    @admin_required
    @handle_errors(error_message="❌ Failed to handle processor management")
    async def cb_processor_management(self, callback: CallbackQuery):
        """Handle response processor management callbacks."""
        try:
            action = callback.data.split(":")[-1]
            
            if action == "config":
                await self._show_processor_config(callback)
            elif action == "stats":
                await self._show_detailed_stats(callback)
            elif action == "refresh":
                await self._refresh_processor_status(callback)
            elif action == "toggle_json":
                await self._toggle_json_display(callback)
            elif action == "toggle_cards":
                await self._toggle_card_extraction(callback)
            elif action == "toggle_debug":
                await self._toggle_debug_mode(callback)
            else:
                await callback.answer("❓ Unknown action")
                
        except Exception as e:
            logger.error(f"Error in processor management: {e}")
            await callback.answer("❌ Management action failed")
    
    async def _show_processor_config(self, callback: CallbackQuery):
        """Show processor configuration options."""
        current_config = {
            "show_json": getattr(self.response_processor, '_show_json_default', True),
            "extract_cards": getattr(self.response_processor, '_extract_cards_default', True),
            "debug_mode": getattr(self.response_processor, '_debug_mode', False)
        }
        
        config_text = f"""🔧 <b>Response Processor Configuration</b>

📄 <b>Current Settings:</b>
• JSON Display: {'✅ Enabled' if current_config['show_json'] else '❌ Disabled'}
• Card Extraction: {'✅ Enabled' if current_config['extract_cards'] else '❌ Disabled'}
• Debug Mode: {'✅ Enabled' if current_config['debug_mode'] else '❌ Disabled'}

<i>Click buttons below to toggle settings:</i>"""
        
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=f"{'❌ Disable' if current_config['show_json'] else '✅ Enable'} JSON Display",
                    callback_data="admin:processor:toggle_json"
                )
            ],
            [
                InlineKeyboardButton(
                    text=f"{'❌ Disable' if current_config['extract_cards'] else '✅ Enable'} Card Extraction",
                    callback_data="admin:processor:toggle_cards"
                )
            ],
            [
                InlineKeyboardButton(
                    text=f"{'❌ Disable' if current_config['debug_mode'] else '✅ Enable'} Debug Mode",
                    callback_data="admin:processor:toggle_debug"
                )
            ],
            [
                InlineKeyboardButton(text="↩️ Back to Status", callback_data="admin:processor:refresh")
            ]
        ])
        
        await callback.message.edit_text(config_text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    async def _show_detailed_stats(self, callback: CallbackQuery):
        """Show detailed processor statistics."""
        stats = self.response_processor.get_stats()
        
        detailed_text = f"""📊 <b>Detailed Response Processor Statistics</b>

🔢 <b>Processing Metrics:</b>
• Total Responses: {stats['total_responses_processed']}
• Uptime: {stats['processor_uptime_human']}
• Status: {stats['status'].title()}
• Last Activity: {stats['last_activity']}

🎯 <b>Performance:</b>
• Processor Status: Active
• JSON Processing: Optimized
• Card Extraction: Real-time
• Memory Usage: Efficient

🛠️ <b>System Information:</b>
• Processor Version: v3.0
• Integration: API v3 Compatible
• Admin Control: Enabled"""
        
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔧 Configure", callback_data="admin:processor:config"),
                InlineKeyboardButton(text="🔄 Refresh", callback_data="admin:processor:refresh")
            ],
            [
                InlineKeyboardButton(text="↩️ Back", callback_data="admin:processor:refresh")
            ]
        ])
        
        await callback.message.edit_text(detailed_text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    async def _refresh_processor_status(self, callback: CallbackQuery):
        """Refresh processor status display."""
        summary = self.response_processor.get_admin_summary()
        
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔧 Configure", callback_data="admin:processor:config"),
                InlineKeyboardButton(text="📊 Detailed Stats", callback_data="admin:processor:stats")
            ],
            [
                InlineKeyboardButton(text="🔄 Refresh", callback_data="admin:processor:refresh"),
                InlineKeyboardButton(text="🏠 Admin Menu", callback_data="admin:main")
            ]
        ])
        
        await callback.message.edit_text(summary, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer("🔄 Status refreshed")
    
    async def _toggle_json_display(self, callback: CallbackQuery):
        """Toggle JSON display setting."""
        current = getattr(self.response_processor, '_show_json_default', True)
        new_value = not current
        
        self.response_processor.configure_display_options(
            show_json=new_value,
            extract_cards=getattr(self.response_processor, '_extract_cards_default', True),
            debug_mode=getattr(self.response_processor, '_debug_mode', False)
        )
        
        await callback.answer(f"JSON Display {'enabled' if new_value else 'disabled'}")
        await self._show_processor_config(callback)
    
    async def _toggle_card_extraction(self, callback: CallbackQuery):
        """Toggle card extraction setting."""
        current = getattr(self.response_processor, '_extract_cards_default', True)
        new_value = not current
        
        self.response_processor.configure_display_options(
            show_json=getattr(self.response_processor, '_show_json_default', True),
            extract_cards=new_value,
            debug_mode=getattr(self.response_processor, '_debug_mode', False)
        )
        
        await callback.answer(f"Card Extraction {'enabled' if new_value else 'disabled'}")
        await self._show_processor_config(callback)
    
    async def _toggle_debug_mode(self, callback: CallbackQuery):
        """Toggle debug mode setting."""
        current = getattr(self.response_processor, '_debug_mode', False)
        new_value = not current
        
        self.response_processor.configure_display_options(
            show_json=getattr(self.response_processor, '_show_json_default', True),
            extract_cards=getattr(self.response_processor, '_extract_cards_default', True),
            debug_mode=new_value
        )
        
        await callback.answer(f"Debug Mode {'enabled' if new_value else 'disabled'}")
        await self._show_processor_config(callback)


def get_response_processor_admin_router() -> Router:
    """Get router for response processor admin handlers."""
    router = Router()
    attach_common_middlewares(router)
    
    handlers = ResponseProcessorAdminHandlers()
    
    # Register command handler
    router.message.register(
        handlers.cmd_processor_status,
        Command("processor", "response_processor", "rp_status")
    )
    
    # Register callback handlers
    router.callback_query.register(
        handlers.cb_processor_management,
        F.data.startswith("admin:processor:")
    )
    
    return router