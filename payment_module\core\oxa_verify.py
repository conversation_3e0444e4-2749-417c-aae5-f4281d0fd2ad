import aiohttp
import asyncio
import json
import logging
import time
from typing import Dict, <PERSON>, Op<PERSON>, Tuple, Union, List
from collections import defaultdict

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Payment verification cache
# Structure: {track_id: {"data": result_data, "timestamp": cache_time}}
_payment_cache: Dict[str, Dict[str, Any]] = {}

# Cache settings
CACHE_EXPIRY_SECONDS = 30  # Short cache time to avoid stale data
# Statuses that should not be cached (or should expire quickly)
VOLATILE_STATUSES = ["pending", "waiting", "confirming"]


async def verify_payment(merchant_api_key: str, track_id: str, max_retries: int = 2) -> dict:
    """
    Verify a payment with OXA Pay API v1 asynchronously with retry logic.

    Args:
        merchant_api_key (str): The merchant API key.
        track_id (str): The transaction track ID to verify.
        max_retries (int): Maximum number of retry attempts.

    Returns:
        dict: API response with status and data or error message.
    """
    if not track_id:
        return {"status": "error", "message": "No track ID provided"}

    url = f"https://api.oxapay.com/v1/payment/{track_id}"
    headers = {"merchant_api_key": merchant_api_key}

    logger.info(f"Verifying payment for track_id: {track_id}")

    # Try with progressively longer timeouts
    timeouts = [5, 8, 12]  # Start with 5 seconds, then 8, then 12

    for attempt in range(max_retries + 1):
        timeout = timeouts[min(attempt, len(timeouts) - 1)]

        try:
            logger.debug(f"Attempt {attempt + 1}/{max_retries + 1} for {track_id} with {timeout}s timeout")

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=timeout) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        try:
                            data = await response.json()
                            logger.info(
                                f"Payment verified for {track_id} on attempt {attempt + 1}: {json.dumps(data)}"
                            )
                            return {"status": "success", "data": data}
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON decode error for {track_id}: {str(e)}")
                            return {
                                "status": "error",
                                "message": f"Invalid JSON: {response_text}",
                            }
                    else:
                        logger.warning(
                            f"API returned error {response.status} for {track_id} on attempt {attempt + 1}: {response_text}"
                        )
                        # Don't return immediately on non-200 status, let it retry
                        if attempt == max_retries:  # Last attempt
                            return {
                                "status": "error",
                                "message": f"API error {response.status}: {response_text}",
                            }

        except asyncio.TimeoutError as e:
            logger.warning(f"Timeout on attempt {attempt + 1}/{max_retries + 1} for {track_id} (timeout: {timeout}s): {str(e)}")
            if attempt == max_retries:  # Last attempt
                return {"status": "timeout", "message": "Payment gateway timeout after retries"}
            # Continue to next attempt
            await asyncio.sleep(1)  # Brief delay before retry

        except aiohttp.ClientError as e:
            logger.warning(f"Client error on attempt {attempt + 1}/{max_retries + 1} for {track_id}: {str(e)}")
            if attempt == max_retries:  # Last attempt
                return {"status": "error", "message": f"Client error: {str(e)}"}
            # Continue to next attempt
            await asyncio.sleep(1)  # Brief delay before retry

        except Exception as e:
            logger.error(f"Unexpected error on attempt {attempt + 1}/{max_retries + 1} for {track_id}: {str(e)}")
            if attempt == max_retries:  # Last attempt
                return {"status": "error", "message": "Unexpected error occurred"}
            # Continue to next attempt
            await asyncio.sleep(1)  # Brief delay before retry

    # If we get here, all attempts failed
    logger.error(f"All {max_retries + 1} attempts failed for {track_id}")
    return {"status": "error", "message": "All verification attempts failed"}


def _is_cache_valid(track_id: str) -> bool:
    """
    Check if the cached payment data is still valid.

    Args:
        track_id: The payment track ID

    Returns:
        bool: True if cache is valid, False otherwise
    """
    if track_id not in _payment_cache:
        return False

    cache_entry = _payment_cache[track_id]
    current_time = time.time()
    cache_time = cache_entry.get("timestamp", 0)

    # Check if cache has expired
    time_diff = current_time - cache_time

    # Get payment status to determine cache validity
    payment_data = cache_entry.get("data", {})
    payment_status = str(payment_data.get("status", "")).lower()

    # For completed/failed payments, use longer cache time
    if payment_status in VOLATILE_STATUSES:
        # For pending/waiting payments, use shorter cache time
        return time_diff < (CACHE_EXPIRY_SECONDS / 2)
    elif payment_status in ["completed", "success"]:
        # Completed payments can be cached longer (2 minutes)
        return time_diff < 120
    else:
        # Default cache time
        return time_diff < CACHE_EXPIRY_SECONDS


async def check_oxapay_payment(track_id: str, api_key: str) -> dict:
    """
    Check the payment status using OXA Pay verification.
    Uses caching to reduce API calls for the same track_id.

    Args:
        track_id (str): Transaction track ID.
        api_key (str): OXA Pay API key.

    Returns:
        dict: The final processed result.
    """
    # Check cache first
    if track_id in _payment_cache and _is_cache_valid(track_id):
        cache_hit = _payment_cache[track_id]
        logger.info(f"Using cached payment data for track_id: {track_id}")
        return cache_hit["data"]

    logger.info(f"Checking OXA Pay payment status for track_id: {track_id}")

    if not api_key:
        logger.error("Missing OXA_PAY_API_KEY in configuration")
        return {"status": "error", "message": "Missing OXA Pay API key"}

    result = await verify_payment(api_key, track_id)

    if result.get("status") == "success":
        # Cache the successful result
        _payment_cache[track_id] = {"data": result["data"], "timestamp": time.time()}
        return result["data"]

    return result