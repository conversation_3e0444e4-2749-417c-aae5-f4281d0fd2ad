"""
API v1 Cart and Orders Service

Implements cart and order-related endpoints for API v1:
- view_cart: Get cart items
- orders: List user orders
- order_view: View single order details
- check: Mark order as checked
- download: Download order data
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio

from ..core.base import BaseService, BaseResponse, retry_on_failure
from ..core.exceptions import APIv1Exception
from ..services.api_config import get_unified_api_config_service, UnifiedAPIConfiguration
from ..services.checkout_processor_service import get_checkout_processor, CheckoutProcessorError
from ..utils.authentication import AuthenticationHelper
from shared_api.http.client import ConfigurableHTTPClient, HTTPResponse
from shared_api.config.api_config import APIConfiguration, TimeoutConfiguration, RetryConfiguration
from shared_api.core.constants import HTTPMethod

from utils.central_logger import get_logger

logger = get_logger()


class CartServiceError(APIv1Exception):
    """Cart service specific errors"""
    pass


class CartService(BaseService):
    """
    Service for cart and order operations
    
    Provides methods for:
    - Viewing cart contents
    - Listing orders
    - Viewing order details
    - Checking orders
    - Downloading order data
    """
    
    def __init__(self, config_name: str = "api_v1_base"):
        super().__init__(service_name="cart_service")
        self.config_name = config_name
        self.config_service = get_unified_api_config_service()
        self.checkout_processor = get_checkout_processor()
        self.auth_helper = AuthenticationHelper()
        self._http_client: Optional[ConfigurableHTTPClient] = None
        self._api_config: Optional[UnifiedAPIConfiguration] = None
    
    async def _get_api_config(self) -> UnifiedAPIConfiguration:
        """Get API configuration from config service"""
        if self._api_config is None:
            self._api_config = await self.config_service.get_api_config(
                self.config_name,
                decrypt_sensitive=True,
                as_unified=True
            )
            if not self._api_config:
                raise CartServiceError(
                    f"API configuration '{self.config_name}' not found",
                    error_code="CONFIG_NOT_FOUND"
                )
        return self._api_config
    
    async def _get_http_client(self) -> ConfigurableHTTPClient:
        """Get or create HTTP client with current configuration"""
        api_config = await self._get_api_config()
        
        # Convert unified config to shared_api APIConfiguration
        shared_config = APIConfiguration(
            base_url=api_config.base_url,
            timeout_config=TimeoutConfiguration(
                total=30,
                connect=10,
                read=30
            ),
            retry_config=RetryConfiguration(
                max_attempts=3,
                delay=1.0,
                backoff_factor=2.0,
                retry_on_status=[500, 502, 503, 504]
            ),
            default_headers=api_config.credentials.headers or {}
        )
        
        return ConfigurableHTTPClient(config=shared_config)
    
    def _build_request_headers(self, api_config: UnifiedAPIConfiguration, content_type: Optional[str] = None) -> Dict[str, str]:
        """Build request headers with authentication"""
        headers = self.auth_helper.build_default_headers()
        
        # Add custom headers from config
        if api_config.credentials.headers:
            headers.update(api_config.credentials.headers)
        
        # Add content type if specified
        if content_type:
            headers["content-type"] = content_type
        
        return headers
    
    def _build_cookies(self, api_config: UnifiedAPIConfiguration) -> Dict[str, str]:
        """Build cookies for authentication"""
        cookies = {}
        
        # Add session cookies
        if api_config.credentials.session_cookies:
            cookies.update(api_config.credentials.session_cookies)
        
        # Add login token
        if api_config.credentials.login_token:
            cookies["loginToken"] = api_config.credentials.login_token
        
        return cookies
    
    @retry_on_failure(max_retries=3, delay=1.0)
    async def view_cart(self, user_id: Optional[str] = None) -> BaseResponse:
        """
        Get cart items for current user
        
        Endpoint: GET /api/cart/
        
        Args:
            user_id: Optional user ID for tracking
            
        Returns:
            BaseResponse with cart data
        """
        try:
            api_config = await self._get_api_config()
            
            # Build URL
            url = f"{api_config.base_url.rstrip('/')}/api/cart/"
            
            # Build headers and cookies
            headers = self._build_request_headers(api_config)
            cookies = self._build_cookies(api_config)
            
            # Make request
            client = await self._get_http_client()
            
            self.logger.info(f"Fetching cart for user: {user_id or 'current'}")
            
            try:
                response = await client._make_request(
                    method=HTTPMethod.GET,
                    url=url,
                    headers=headers,
                    cookies=cookies
                )
                
                # Parse response
                if isinstance(response, dict):
                    success = response.get("success", False)
                    data = response.get("data", [])
                    total_price = response.get("totalCartPrice", 0)
                    
                    return BaseResponse(
                        success=success,
                        message="Cart retrieved successfully",
                        data={
                            "items": data,
                            "total_price": total_price,
                            "item_count": len(data) if isinstance(data, list) else 0
                        }
                    )
                else:
                    return BaseResponse(
                        success=False,
                        message="Invalid response format",
                        error_code="INVALID_RESPONSE"
                    )
                    
            except Exception as e:
                self.logger.error(f"Failed to fetch cart: {e}")
                raise CartServiceError(
                    f"Failed to fetch cart: {str(e)}",
                    error_code="CART_FETCH_ERROR"
                )
                
        except Exception as e:
            return await self._handle_error(e, "view_cart")
    
    @retry_on_failure(max_retries=3, delay=1.0)
    async def list_orders(
        self,
        category: str = "hq",
        page: int = 1,
        limit: int = 10,
        user_id: Optional[str] = None
    ) -> BaseResponse:
        """
        List orders for current user
        
        Endpoint: GET /api/cards/{category}/orders?page=1&limit=10
        
        Args:
            category: Product category (e.g., "hq")
            page: Page number (default: 1)
            limit: Items per page (default: 10)
            user_id: Optional user ID for tracking
            
        Returns:
            BaseResponse with orders list
        """
        try:
            api_config = await self._get_api_config()
            
            # Build URL with query params
            url = f"{api_config.base_url.rstrip('/')}/api/cards/{category}/orders"
            params = {
                "page": page,
                "limit": limit
            }
            
            # Build headers and cookies
            headers = self._build_request_headers(api_config)
            cookies = self._build_cookies(api_config)
            
            # Make request
            client = await self._get_http_client()
            
            self.logger.info(f"Fetching orders for user: {user_id or 'current'} (page={page}, limit={limit})")
            
            try:
                response = await client._make_request(
                    method=HTTPMethod.GET,
                    url=url,
                    headers=headers,
                    params=params,
                    cookies=cookies
                )
                
                # Parse response
                if isinstance(response, dict):
                    success = response.get("success", False)
                    data = response.get("data", [])
                    total_count = response.get("totalCount", 0)
                    limit_val = response.get("limit", limit)
                    
                    return BaseResponse(
                        success=success,
                        message="Orders retrieved successfully",
                        data={
                            "orders": data,
                            "total_count": total_count,
                            "page": page,
                            "limit": limit_val,
                            "total_pages": (total_count + limit_val - 1) // limit_val if limit_val > 0 else 0
                        }
                    )
                else:
                    return BaseResponse(
                        success=False,
                        message="Invalid response format",
                        error_code="INVALID_RESPONSE"
                    )
                    
            except Exception as e:
                self.logger.error(f"Failed to fetch orders: {e}")
                raise CartServiceError(
                    f"Failed to fetch orders: {str(e)}",
                    error_code="ORDERS_FETCH_ERROR"
                )
                
        except Exception as e:
            return await self._handle_error(e, "list_orders")
    
    @retry_on_failure(max_retries=3, delay=1.0)
    async def view_order(
        self,
        order_id: int,
        category: str = "hq",
        user_id: Optional[str] = None
    ) -> BaseResponse:
        """
        View single order details
        
        Endpoint: POST /api/cards/{category}/order/view
        Body: {"id": order_id}
        
        Args:
            order_id: Order ID to view
            category: Product category (e.g., "hq")
            user_id: Optional user ID for tracking
            
        Returns:
            BaseResponse with order details
        """
        try:
            api_config = await self._get_api_config()
            
            # Build URL
            url = f"{api_config.base_url.rstrip('/')}/api/cards/{category}/order/view"
            
            # Build headers and cookies
            headers = self._build_request_headers(api_config, content_type="application/json")
            cookies = self._build_cookies(api_config)
            
            # Build request body
            data = {"id": order_id}
            
            # Make request
            client = await self._get_http_client()
            
            self.logger.info(f"Viewing order {order_id} for user: {user_id or 'current'}")
            
            try:
                response = await client._make_request(
                    method=HTTPMethod.POST,
                    url=url,
                    headers=headers,
                    data=data,
                    cookies=cookies
                )
                
                # Parse response
                if isinstance(response, dict):
                    success = response.get("success", False)
                    order_data = response.get("data", {})
                    
                    return BaseResponse(
                        success=success,
                        message="Order details retrieved successfully",
                        data={
                            "order": order_data
                        }
                    )
                else:
                    return BaseResponse(
                        success=False,
                        message="Invalid response format",
                        error_code="INVALID_RESPONSE"
                    )
                    
            except Exception as e:
                self.logger.error(f"Failed to view order: {e}")
                raise CartServiceError(
                    f"Failed to view order: {str(e)}",
                    error_code="ORDER_VIEW_ERROR"
                )
                
        except Exception as e:
            return await self._handle_error(e, "view_order")
    
    @retry_on_failure(max_retries=3, delay=1.0)
    async def check_order(
        self,
        order_id: int,
        category: str = "hq",
        user_id: Optional[str] = None
    ) -> BaseResponse:
        """
        Mark order as checked (non-refundable)
        
        Endpoint: POST /api/cards/{category}/check
        Body: {"id": order_id}
        
        Args:
            order_id: Order ID to check
            category: Product category (e.g., "hq")
            user_id: Optional user ID for tracking
            
        Returns:
            BaseResponse with updated order data
        """
        try:
            api_config = await self._get_api_config()
            
            # Build URL
            url = f"{api_config.base_url.rstrip('/')}/api/cards/{category}/check"
            
            # Build headers and cookies
            headers = self._build_request_headers(api_config, content_type="application/json")
            cookies = self._build_cookies(api_config)
            
            # Build request body
            data = {"id": order_id}
            
            # Make request
            client = await self._get_http_client()
            
            self.logger.info(f"Checking order {order_id} for user: {user_id or 'current'}")
            
            try:
                response = await client._make_request(
                    method=HTTPMethod.POST,
                    url=url,
                    headers=headers,
                    data=data,
                    cookies=cookies
                )
                
                # Parse response
                if isinstance(response, dict):
                    success = response.get("success", False)
                    order_data = response.get("data", {})
                    
                    return BaseResponse(
                        success=success,
                        message="Order marked as checked successfully",
                        data={
                            "order": order_data
                        }
                    )
                else:
                    return BaseResponse(
                        success=False,
                        message="Invalid response format",
                        error_code="INVALID_RESPONSE"
                    )
                    
            except Exception as e:
                self.logger.error(f"Failed to check order: {e}")
                raise CartServiceError(
                    f"Failed to check order: {str(e)}",
                    error_code="ORDER_CHECK_ERROR"
                )
                
        except Exception as e:
            return await self._handle_error(e, "check_order")
    
    @retry_on_failure(max_retries=3, delay=1.0)
    async def download_order(
        self,
        order_id: int,
        category: str = "hq",
        user_id: Optional[str] = None
    ) -> BaseResponse:
        """
        Download order data as text file
        
        Endpoint: POST /api/cards/{category}/download/single
        Body: {"_id": order_id}
        
        Args:
            order_id: Order ID to download
            category: Product category (e.g., "hq")
            user_id: Optional user ID for tracking
            
        Returns:
            BaseResponse with order data in text format
        """
        try:
            api_config = await self._get_api_config()
            
            # Build URL
            url = f"{api_config.base_url.rstrip('/')}/api/cards/{category}/download/single"
            
            # Build headers and cookies
            headers = self._build_request_headers(api_config, content_type="application/json")
            cookies = self._build_cookies(api_config)
            
            # Build request body (note: uses "_id" instead of "id")
            data = {"_id": order_id}
            
            # Make request
            client = await self._get_http_client()
            
            self.logger.info(f"Downloading order {order_id} for user: {user_id or 'current'}")
            
            try:
                # This endpoint returns text/plain instead of JSON
                session = await client._ensure_session()
                
                async with session.request(
                    HTTPMethod.POST.value,
                    url,
                    headers=headers,
                    json=data,
                    cookies=cookies
                ) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        # Parse the pipe-delimited text response
                        lines = response_text.strip().split('\n')
                        
                        if len(lines) >= 2:
                            # First line is headers
                            headers_line = lines[0]
                            # Second line is data (if present)
                            data_line = lines[1] if len(lines) > 1 else ""
                            
                            return BaseResponse(
                                success=True,
                                message="Order downloaded successfully",
                                data={
                                    "raw_text": response_text,
                                    "headers": headers_line,
                                    "data": data_line,
                                    "format": "pipe-delimited"
                                }
                            )
                        else:
                            return BaseResponse(
                                success=True,
                                message="Order downloaded successfully",
                                data={
                                    "raw_text": response_text,
                                    "format": "text"
                                }
                            )
                    else:
                        raise CartServiceError(
                            f"Download failed with status {response.status}",
                            error_code="DOWNLOAD_FAILED"
                        )
                    
            except Exception as e:
                self.logger.error(f"Failed to download order: {e}")
                raise CartServiceError(
                    f"Failed to download order: {str(e)}",
                    error_code="ORDER_DOWNLOAD_ERROR"
                )
                
        except Exception as e:
            return await self._handle_error(e, "download_order")
    
    @retry_on_failure(max_retries=2, delay=1.0)
    async def checkout_cart(
        self,
        user_id: str,
        clear_cart_on_success: bool = True
    ) -> BaseResponse:
        """
        Checkout cart and create purchases
        
        This endpoint:
        1. Calls the API checkout endpoint
        2. Retrieves the cart items
        3. Creates purchase records in database
        4. Deducts from wallet balance
        5. Clears the cart (optional)
        
        Endpoint: GET /api/cart/checkout
        
        Args:
            user_id: User ID for database operations
            clear_cart_on_success: Whether to clear cart after successful checkout
            
        Returns:
            BaseResponse with checkout result
        """
        try:
            api_config = await self._get_api_config()
            
            self.logger.info(f"🛒 Starting checkout for user {user_id}")
            
            # Step 1: Get current cart items before checkout
            cart_result = await self.view_cart(user_id=user_id)
            if not cart_result.success:
                return BaseResponse(
                    success=False,
                    message="Failed to retrieve cart",
                    error_code="CART_FETCH_FAILED"
                )
            
            cart_items = cart_result.data.get('items', [])
            if not cart_items:
                return BaseResponse(
                    success=False,
                    message="Cart is empty",
                    error_code="EMPTY_CART"
                )
            
            # Step 2: Call API checkout endpoint
            url = f"{api_config.base_url.rstrip('/')}/api/cart/checkout"
            headers = self._build_request_headers(api_config)
            cookies = self._build_cookies(api_config)
            
            client = await self._get_http_client()
            
            self.logger.info(f"📞 Calling API checkout endpoint")
            
            try:
                response = await client._make_request(
                    method=HTTPMethod.GET,
                    url=url,
                    headers=headers,
                    cookies=cookies
                )
                
                if not isinstance(response, dict) or not response.get("success"):
                    return BaseResponse(
                        success=False,
                        message="API checkout failed",
                        error_code="API_CHECKOUT_FAILED",
                        data={"api_response": response}
                    )
                
                # Extract order information
                api_order_data = response.get("data", [])
                api_order_id = None
                
                # Try to extract order ID if available
                if api_order_data:
                    first_item = api_order_data[0] if isinstance(api_order_data, list) else api_order_data
                    if isinstance(first_item, dict):
                        api_order_id = first_item.get('order_id') or first_item.get('external_order_id')
                
                self.logger.info(f"✅ API checkout successful, processing database operations")
                
                # Step 3: Process checkout in database (create purchases, deduct wallet)
                try:
                    checkout_result = await self.checkout_processor.process_checkout(
                        user_id=user_id,
                        cart_items=cart_items,
                        api_order_id=api_order_id,
                        api_version="v1"
                    )
                    
                    self.logger.info(f"💾 Database operations completed successfully")
                    
                    # Step 4: Clear cart if requested (best effort, don't fail if it fails)
                    if clear_cart_on_success:
                        try:
                            # Note: API might have already cleared the cart
                            self.logger.info(f"🗑️  Cart cleared (API-side)")
                        except Exception as clear_error:
                            self.logger.warning(f"⚠️  Cart clearing issue (non-critical): {clear_error}")
                    
                    return BaseResponse(
                        success=True,
                        message=f"Checkout completed successfully! {checkout_result['items_count']} items purchased",
                        data={
                            "purchases": [
                                {
                                    "_id": str(p['_id']),
                                    "sku": p['sku'],
                                    "price": p['price'],
                                    "product_type": p['product_type'],
                                    "external_product_id": p['external_product_id']
                                }
                                for p in checkout_result['purchases']
                            ],
                            "total_price": checkout_result['total_price'],
                            "new_balance": checkout_result['new_balance'],
                            "items_count": checkout_result['items_count'],
                            "transaction_id": checkout_result['transaction_id'],
                            "api_order_id": api_order_id,
                            "api_response": api_order_data
                        }
                    )
                    
                except CheckoutProcessorError as cpe:
                    self.logger.error(f"❌ Checkout processing failed: {cpe.message}")
                    return BaseResponse(
                        success=False,
                        message=cpe.message,
                        error_code=cpe.error_code,
                        data={"api_response": api_order_data}
                    )
                    
            except Exception as e:
                self.logger.error(f"❌ API checkout request failed: {e}")
                raise CartServiceError(
                    f"API checkout failed: {str(e)}",
                    error_code="API_CHECKOUT_ERROR"
                )
                
        except CartServiceError:
            raise
        except Exception as e:
            return await self._handle_error(e, "checkout_cart")
    
    async def health_check(self) -> BaseResponse:
        """Perform health check for the cart service"""
        try:
            # Check if we can get the API config
            api_config = await self._get_api_config()
            
            return BaseResponse(
                success=True,
                message="Cart service is healthy",
                data={
                    "service": self.service_name,
                    "config_loaded": api_config is not None,
                    "base_url": api_config.base_url if api_config else None
                }
            )
        except Exception as e:
            return BaseResponse(
                success=False,
                message=f"Cart service health check failed: {str(e)}",
                error_code="HEALTH_CHECK_FAILED"
            )


# Singleton instance
_cart_service_instance = None


def get_cart_service(config_name: str = "api_v1_base") -> CartService:
    """Get singleton instance of the cart service"""
    global _cart_service_instance
    if _cart_service_instance is None:
        _cart_service_instance = CartService(config_name=config_name)
    return _cart_service_instance

