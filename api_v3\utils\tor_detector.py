"""
Tor Port Detection Utility

Automatically detects which Tor SOCKS port is available and provides
the correct configuration for API v3 connections.
"""

import socket
from typing import Optional, Tuple, Dict
import time

from utils.central_logger import get_logger

logger = get_logger()


class TorPortDetector:
    """
    Detects available Tor SOCKS ports and provides configuration recommendations.
    
    This utility checks common Tor ports and determines which one is accessible,
    allowing the API v3 client to automatically use the correct port.
    """
    
    # Common Tor SOCKS ports and their descriptions
    TOR_PORTS = {
        9150: "Tor Browser SOCKS proxy",
        9050: "System Tor SOCKS proxy", 
        9051: "Tor control port (not SOCKS)",
    }
    
    # SOCKS ports only (exclude control port)
    SOCKS_PORTS = {9150, 9050}
    
    def __init__(self, timeout: float = 2.0):
        """
        Initialize Tor port detector.
        
        Args:
            timeout: Connection timeout for port checks
        """
        self.timeout = timeout
        self._cache: Optional[Tuple[int, str]] = None
        self._cache_time: float = 0
        self._cache_ttl: float = 30.0  # Cache results for 30 seconds
    
    def check_port(self, host: str = "127.0.0.1", port: int = 9150) -> bool:
        """
        Check if a specific port is open and accepting connections.
        
        Args:
            host: Host to check (default: localhost)
            port: Port to check
            
        Returns:
            True if port is accessible, False otherwise
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception as e:
            logger.debug(f"Error checking port {port}: {e}")
            return False
    
    def detect_available_port(self, host: str = "127.0.0.1") -> Optional[int]:
        """
        Detect which Tor SOCKS port is available.
        
        Args:
            host: Host to check (default: localhost)
            
        Returns:
            Available port number or None if no Tor ports are accessible
        """
        # Check cache first
        current_time = time.time()
        if (self._cache and 
            current_time - self._cache_time < self._cache_ttl):
            return self._cache[0]
        
        # Check ports in order of preference (Tor Browser first, then system Tor)
        accessible_ports = []
        for port in [9150, 9050]:
            if self.check_port(host, port):
                logger.info(f"✅ Found Tor on port {port} ({self.TOR_PORTS[port]})")
                accessible_ports.append(port)
        
        if accessible_ports:
            self._cache = accessible_ports
            self._cache_time = current_time
            return accessible_ports[0]
        
        logger.warning("❌ No Tor SOCKS ports are accessible")
        self._cache = None
        return None
    
    def get_socks_url(self, host: str = "127.0.0.1") -> Optional[str]:
        """
        Get the correct SOCKS URL for the available Tor port.
        
        Args:
            host: Host to use (default: localhost)
            
        Returns:
            SOCKS URL string or None if no Tor ports are accessible
        """
        port = self.detect_available_port(host)
        if port:
            return f"socks5h://{host}:{port}"
        return None
    
    def get_port_status(self, host: str = "127.0.0.1") -> Dict[int, bool]:
        """
        Get status of all common Tor ports.
        
        Args:
            host: Host to check (default: localhost)
            
        Returns:
            Dictionary mapping port numbers to their accessibility status
        """
        status = {}
        for port in self.TOR_PORTS.keys():
            status[port] = self.check_port(host, port)
        return status
    
    def print_status_report(self, host: str = "127.0.0.1") -> None:
        """
        Print a detailed status report of Tor port availability.
        
        Args:
            host: Host to check (default: localhost)
        """
        logger.info("🔍 Tor Port Detection Report")
        logger.info("=" * 50)
        
        status = self.get_port_status(host)
        available_socks_ports = []
        
        for port, description in self.TOR_PORTS.items():
            is_open = status[port]
            status_icon = "✅" if is_open else "❌"
            status_text = "OPEN" if is_open else "CLOSED"
            
            logger.info(f"{status_icon} Port {port}: {status_text} ({description})")
            
            if is_open and port in self.SOCKS_PORTS:
                available_socks_ports.append(port)
        
        logger.info("")
        
        if available_socks_ports:
            recommended_port = available_socks_ports[0]  # Prefer 9150 (Tor Browser)
            logger.info(f"🎯 Recommended Configuration:")
            logger.info(f"   SOCKS_URL=socks5h://{host}:{recommended_port}")
            logger.info(f"   Using: {self.TOR_PORTS[recommended_port]}")
        else:
            logger.info("⚠️  No Tor SOCKS ports are accessible!")
            logger.info("")
            logger.info("🔧 To fix this:")
            logger.info("   Option 1: Start Tor Browser")
            logger.info("     - Download from: https://www.torproject.org/download/")
            logger.info("     - Open Tor Browser and wait for connection")
            logger.info("     - Uses port 9150")
            logger.info("")
            logger.info("   Option 2: Install system Tor")
            logger.info("     - Linux: sudo apt install tor && sudo systemctl start tor")
            logger.info("     - macOS: brew install tor && brew services start tor")
            logger.info("     - Uses port 9050")


def auto_detect_tor_port() -> Optional[str]:
    """
    Convenience function to automatically detect and return the best Tor SOCKS URL.
    
    Returns:
        SOCKS URL string or None if no Tor is available
    """
    detector = TorPortDetector()
    return detector.get_socks_url()


def is_tor_available() -> bool:
    """
    Quick check if any Tor SOCKS port is available.
    
    Returns:
        True if Tor is accessible, False otherwise
    """
    detector = TorPortDetector()
    return detector.detect_available_port() is not None


if __name__ == "__main__":
    # Command-line usage
    detector = TorPortDetector()
    detector.print_status_report()
