"""
Base API Client Implementation

Provides the base implementation for API clients in the shared API system.
This module contains the concrete implementation of the BaseAPIClient
abstract class defined in interfaces.py.
"""

import async<PERSON>
from typing import Dict, Any, Optional

from .interfaces import BaseAPIClient as BaseAPIClientInterface, APIConfigProtocol
from .constants import HTTPMethod
from .exceptions import Shared<PERSON>IException

from utils.central_logger import get_logger

logger = get_logger()


class BaseAPIClient(BaseAPIClientInterface):
    """
    Base implementation of API client
    
    This class provides a concrete implementation of the BaseAPIClient
    interface with common functionality that can be extended by
    specific client implementations.
    """
    
    def __init__(self, config: APIConfigProtocol):
        super().__init__(config)
        self.logger = get_logger()
    
    async def _make_request(
        self,
        method: HTTPMethod,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Base implementation of HTTP request
        
        This is a basic implementation that should be overridden
        by concrete client implementations.
        """
        raise NotImplementedError(
            "Subclasses must implement _make_request method"
        )
    
    async def health_check(self) -> bool:
        """
        Base implementation of health check
        
        This is a basic implementation that should be overridden
        by concrete client implementations.
        """
        try:
            # Try to find a health check endpoint
            health_endpoints = ["health", "status", "ping"]
            
            for endpoint_name in health_endpoints:
                if hasattr(self.config, 'endpoints') and endpoint_name in self.config.endpoints:
                    try:
                        await self.get(endpoint_name)
                        return True
                    except Exception as e:
                        self.logger.debug(f"Health check failed for {endpoint_name}: {e}")
                        continue
            
            # If no specific health endpoint, try the first available endpoint
            if hasattr(self.config, 'endpoints') and self.config.endpoints:
                first_endpoint = next(iter(self.config.endpoints.keys()))
                try:
                    await self.get(first_endpoint)
                    return True
                except Exception as e:
                    self.logger.debug(f"Health check failed for {first_endpoint}: {e}")
            
            return False
        
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            return False
