"""
API v3 Authentication Module

Session-based authentication for API v3 with form login and CSRF tokens.
"""

from .login import LoginSession
from .session_manager import SessionManager, get_authenticated_session
from .session_handler import APIV3<PERSON>ession<PERSON>and<PERSON>, SessionHandler

__all__ = [
    "LoginSession",
    "SessionManager",
    "get_authenticated_session",
    "APIV3SessionHandler",
    "SessionHandler",
]
