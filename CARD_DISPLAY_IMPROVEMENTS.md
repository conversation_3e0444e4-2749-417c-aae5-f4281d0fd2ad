# Card Display Improvements - Unmask & Check Status

## Overview
Enhanced card viewing to prioritize displaying cached unmasked/checked data from database instead of showing masked data or making redundant API calls.

## Changes Made

### 1. Database Data Priority in `cb_view_purchased_card()` (Line ~2307)

**Problem**: When clicking on a card, the system would always make an API call to fetch fresh data, even if the card was already unmasked or checked and stored in the database.

**Solution**: 
- Added checks to detect if card has unmasked data (presence of `cc` or `cvv` fields)
- Added checks to detect if card has been checked (presence of `is_checked` flag)
- Only makes API calls if the card doesn't have cached unmasked/checked data
- Logs which type of cached data is being used

**Code Changes**:
```python
# Check if card already has unmasked or checked data in database
has_unmasked_data = False
has_checked_data = False
if "extracted_cards" in order and order["extracted_cards"]:
    card_data = order["extracted_cards"][0]
    # Check if card has full unmasked data (cc, cvv)
    if card_data.get("cc") or card_data.get("cvv"):
        has_unmasked_data = True
        logger.info(f"✅ [View Card] Card has unmasked data in database - using cached data")
    # Check if card has been checked
    if order.get("is_checked") or card_data.get("is_checked"):
        has_checked_data = True
        checked_status = card_data.get("status", "").lower()
        logger.info(f"✅ [View Card] Card has been checked in database (status: {checked_status}) - using cached data")

# Only make API call if card doesn't have unmasked/checked data
should_fetch_api = api_version == "v1" and api_order_id and str(api_order_id) != "unknown"
should_fetch_api = should_fetch_api and not (has_unmasked_data or has_checked_data)
```

### 2. Database Data Priority in `cb_view_card_details()` (Line ~3477)

**Problem**: Same issue in the "My Orders" view - always fetched fresh data even when cached data was available.

**Solution**: Applied the same logic as above to check for cached unmasked/checked data before making API calls.

### 3. Enhanced Check Button Logic - API v1 (Line ~2505)

**Problem**: Check button would show even after a card was checked, and didn't account for check expiration times.

**Solution**:
- Parse `checked_at` timestamp to determine if check has expired (30 second window)
- For "NonRefundable" or "Refunded" status:
  - If check not expired: Hide check button (can_check = False)
  - If check expired: Show check button, allow rechecking
- For "Started" or "Pending" status:
  - If check in progress: Show remaining time
  - Otherwise: Show full 30-second timer
- Logs check expiration status and remaining time

**Code Changes**:
```python
# Check if card has been checked and if check has expired
checked_at = card_data.get("checked_at") or card_data.get("checkedAt") or order.get("checked_at")
check_expired = True
remaining_time = 0

if checked_at:
    try:
        # Parse checked_at timestamp
        if isinstance(checked_at, str):
            from dateutil import parser
            checked_dt = parser.parse(checked_at)
        else:
            checked_dt = checked_at
        
        # Calculate time since check (30 second window)
        now = datetime.now(timezone.utc)
        time_since_check = (now - checked_dt).total_seconds()
        remaining_time = max(0, 30 - time_since_check)
        check_expired = remaining_time <= 0
        
        if not check_expired:
            logger.info(f"⏰ [View Card] Check still valid - {remaining_time:.0f} seconds remaining")
        else:
            logger.info(f"⏰ [View Card] Check expired ({time_since_check:.0f} seconds ago)")
    except Exception as e:
        logger.warning(f"⚠️ [View Card] Failed to parse checked_at timestamp: {e}")

# Cards with "NonRefundable" or "Refunded" status are already checked - don't allow rechecking
if card_status in ["nonrefundable", "refunded"]:
    # Only disable if check is still valid (not expired)
    if not check_expired:
        can_check = False
        check_status = "completed"
        logger.info(f"🔒 [View Card] Card already checked ({card_status} status) - check not expired")
    else:
        # Check expired - allow rechecking
        can_check = True
        check_status = "active"
        expiry_timestamp = int(datetime.now(timezone.utc).timestamp()) + 30
        logger.info(f"🔄 [View Card] Check expired for {card_status} card - allowing recheck")
```

### 4. Enhanced Check Button Logic - API v3 (Line ~2587)

**Problem**: Same as API v1 - check button logic didn't account for expiration.

**Solution**: Applied similar check expiration logic as API v1:
- Parse `checked_at` timestamp
- Check if card is already checked and if check has expired
- Show/hide check button based on check status and expiration
- Display remaining time for checks in progress

## Benefits

### 1. **Performance Improvement**
- Reduces unnecessary API calls when data is already in database
- Faster card viewing experience for users
- Lower load on external API

### 2. **Consistent Data Display**
- Always shows the most recent unmasked/checked data
- No flickering between masked and unmasked states
- User sees their card's actual status immediately

### 3. **Smart Check Button Display**
- Check button only shows when checking is actually possible
- Respects check cooldown/expiration times
- Shows remaining time for checks in progress
- Allows rechecking after expiration

### 4. **Better User Experience**
- No confusion from seeing masked data after unmasking
- Clear indication of card check status
- Transparent logging for debugging

## Testing Checklist

- [ ] Click on unmasked card - should show unmasked data immediately (no API call)
- [ ] Click on checked card - should show checked data immediately (no API call)
- [ ] Check button should hide for recently checked cards (within 30 seconds)
- [ ] Check button should show for expired checks (after 30 seconds)
- [ ] Check button should show remaining time for in-progress checks
- [ ] Verify logs show "Using cached data" messages when appropriate
- [ ] Test with API v1 cards
- [ ] Test with API v3 cards
- [ ] Test from "My Orders" menu
- [ ] Test from inline card views

## Log Messages

### New Log Messages Added:
1. `✅ [View Card] Card has unmasked data in database - using cached data`
2. `✅ [View Card] Card has been checked in database (status: {status}) - using cached data`
3. `💾 [View Card] Using cached {type} data from database - skipping API call`
4. `⏰ [View Card] Check still valid - {remaining} seconds remaining`
5. `⏰ [View Card] Check expired ({time} seconds ago)`
6. `🔒 [View Card] Card already checked ({status} status) - check not expired`
7. `🔄 [View Card] Check expired for {status} card - allowing recheck`
8. `⏰ [View Card] Check in progress for '{status}' card: {remaining} seconds remaining`

Same messages also available for `[My Orders]` context.

## Files Modified

- `handlers/orders_handlers.py`:
  - `cb_view_purchased_card()` - Lines ~2307-2425
  - `cb_view_card_details()` - Lines ~3477-3611
  - Check button logic for API v1 - Lines ~2505-2585
  - Check button logic for API v3 - Lines ~2587-2635

## Implementation Date
October 25, 2025

