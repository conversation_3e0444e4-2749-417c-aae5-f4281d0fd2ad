"""
Dump Service for managing dumps v1 and vdumps v2 API operations
"""

from typing import Dict, Any, Optional
from services.external_api_service import (
    get_external_api_service,
    ExternalAPIService,
    APIResponse,
    APIOperation,
)

from utils.central_logger import get_logger

logger = get_logger()


class DumpService:
    """Service for handling dump-related API operations (v1 and v2)"""

    def __init__(self, external_api_service: Optional[ExternalAPIService] = None):
        """Initialize dump service"""
        self.external_api = external_api_service or get_external_api_service()

    async def close(self):
        """Close service sessions"""
        if hasattr(self.external_api, "close"):
            await self.external_api.close()

    # Browse/Filter methods that match BIN API interface
    async def browse_dumps(
        self,
        page: int = 1,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        api_version: str = "v1",
    ) -> Dict[str, Any]:
        """
        Browse dumps with filtering - unified interface for both v1 and v2
        This method provides the same interface as BIN APIs for consistency
        """
        logger.info(
            f"Browsing dumps {api_version}", extra={"page": page, "limit": limit}
        )

        if api_version == "v2":
            return await self._browse_vdumps_internal(page, limit, filters or {})
        else:
            return await self._browse_dumps_internal(page, limit, filters or {})

    async def _browse_dumps_internal(
        self, page: int, limit: int, filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Internal method for browsing dumps v1"""
        return await self.list_dumps(
            page=page,
            limit=limit,
            base=filters.get("base", ""),
            bank=filters.get("bank", ""),
            bin=filters.get("bin", ""),
            country=filters.get("country", ""),
            state=filters.get("state", ""),
            city=filters.get("city", ""),
            brand=filters.get("brand", ""),
            type=filters.get("type", ""),
            zip=filters.get("zip", ""),
            price_from=filters.get("price_from", 0),
            price_to=filters.get("price_to", 500),
            zip_check=filters.get("zip_check", False),
            track1=filters.get("track1", False),
            track2=filters.get("track2", False),
            pin=filters.get("pin", False),
        )

    async def _browse_vdumps_internal(
        self, page: int, limit: int, filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Internal method for browsing vdumps v2"""
        return await self.list_vdumps(
            page=page,
            limit=limit,
            base=filters.get("base", ""),
            type=filters.get("type", ""),
            bank=filters.get("bank", ""),
            bin=filters.get("bin", ""),
            country=filters.get("country", ""),
            state=filters.get("state", ""),
            city=filters.get("city", ""),
            brand=filters.get("brand", ""),
            zip=filters.get("zip", ""),
            name=filters.get("name", ""),
            price_from=filters.get("price_from", 0),
            price_to=filters.get("price_to", 500),
            zip_check=filters.get("zip_check", False),
            track1=filters.get("track1", False),
            track2=filters.get("track2", False),
            pin=filters.get("pin", False),
        )

    # DUMPS V1 API Methods
    async def list_dumps(
        self,
        page: int = 1,
        limit: int = 10,
        base: str = "",
        bank: str = "",
        bin: str = "",
        country: str = "",
        state: str = "",
        city: str = "",
        brand: str = "",
        type: str = "",
        zip: str = "",
        price_from: float = 0,
        price_to: float = 500,
        zip_check: bool = False,
        track1: bool = False,
        track2: bool = False,
        pin: bool = False,
    ) -> Dict[str, Any]:
        """
        List dumps v1 - equivalent to /api/cards/dumps/list
        Uses query parameters with POST method (no body) as per demo
        """
        logger.info("Fetching dumps v1 list", extra={"page": page, "limit": limit})

        try:
            # Get API configuration
            config = await self.external_api._get_api_config()
            if not config:
                return {"success": False, "error": "API configuration not available"}

            # Build query parameters as per demo
            query_params = {
                "page": page,
                "limit": limit,
                "base": base,
                "bank": bank,
                "bin": bin,
                "country": country,
                "state": state,
                "city": city,
                "brand": brand,
                "type": type,
                "zip": zip,
                "priceFrom": price_from,
                "priceTo": price_to,
                "zipCheck": str(zip_check).lower(),
                "track1": str(track1).lower(),
                "track2": str(track2).lower(),
                "pin": str(pin).lower(),
            }

            # Filter out empty string values
            filtered_params = {k: v for k, v in query_params.items() if v != ""}

            # Build URL with query parameters
            from urllib.parse import urlencode

            base_url = f"{config.base_url}/cards/dumps/list"
            url = f"{base_url}?{urlencode(filtered_params)}"

            # Build request components
            headers = self.external_api._build_headers(config, APIOperation.LIST_ITEMS)
            cookies = self.external_api._build_cookies(config)

            # Make authenticated request with POST method but no body (as per demo)
            response = await self.external_api._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=None,  # No body as per demo
                operation=APIOperation.LIST_ITEMS,
            )

            if response and response.success:
                logger.debug("Successfully fetched dumps v1 list")
                # Return the response.data directly as it contains the full JSON response
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error(
                    "Failed to fetch dumps v1 list", extra={"error": error_msg}
                )
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error fetching dumps v1 list: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def get_dump_orders(self, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """Get dump orders history"""
        logger.info("Fetching dump orders", extra={"page": page, "limit": limit})

        payload = {"page": page, "limit": limit}

        try:
            config = await self.external_api._get_api_config()
            if not config:
                return {"success": False, "error": "API configuration not available"}

            url = f"{config.base_url}/cards/dumps/orders"
            headers = self.external_api._build_headers(config, APIOperation.LIST_ORDERS)
            cookies = self.external_api._build_cookies(config)

            response = await self.external_api._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.LIST_ORDERS,
            )

            if response and response.success:
                logger.debug("Successfully fetched dump orders")
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error("Failed to fetch dump orders", extra={"error": error_msg})
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error fetching dump orders: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def get_dump_downloads(
        self, page: int = 1, limit: int = 10
    ) -> Dict[str, Any]:
        """Get dump downloads history"""
        logger.info("Fetching dump downloads", extra={"page": page, "limit": limit})

        payload = {"page": page, "limit": limit}

        try:
            config = await self.external_api._get_api_config()
            if not config:
                return {"success": False, "error": "API configuration not available"}

            url = f"{config.base_url}/cards/dumps/downloads"
            headers = self.external_api._build_headers(config, APIOperation.LIST_ITEMS)
            cookies = self.external_api._build_cookies(config)

            response = await self.external_api._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.LIST_ITEMS,
            )

            if response and response.success:
                logger.info("Successfully fetched dump downloads")
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error(
                    "Failed to fetch dump downloads", extra={"error": error_msg}
                )
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error fetching dump downloads: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    # VDUMPS V2 API Methods
    async def list_vdumps(
        self,
        page: int = 1,
        limit: int = 10,
        base: str = "",
        type: str = "",
        bank: str = "",
        bin: str = "",
        country: str = "",
        state: str = "",
        city: str = "",
        brand: str = "",
        zip: str = "",
        name: str = "",
        price_from: float = 0,
        price_to: float = 500,
        zip_check: bool = False,
        track1: bool = False,
        track2: bool = False,
        pin: bool = False,
    ) -> Dict[str, Any]:
        """
        List vdumps v2 - equivalent to /api/cards/vdumps/list
        Uses query parameters with POST method (no body) as per demo
        """
        logger.info("Fetching vdumps v2 list", extra={"page": page, "limit": limit})

        try:
            config = await self.external_api._get_api_config()
            if not config:
                return {"success": False, "error": "API configuration not available"}

            # Build query parameters as per demo (same format as dumps v1)
            query_params = {
                "page": page,
                "limit": limit,
                "base": base,
                "bank": bank,
                "bin": bin,
                "country": country,
                "state": state,
                "city": city,
                "brand": brand,
                "type": type,
                "zip": zip,
                "priceFrom": price_from,
                "priceTo": price_to,
                "zipCheck": str(zip_check).lower(),
                "track1": str(track1).lower(),
                "track2": str(track2).lower(),
                "pin": str(pin).lower(),
            }

            # Filter out empty string values
            filtered_params = {k: v for k, v in query_params.items() if v != ""}

            # Build URL with query parameters
            from urllib.parse import urlencode

            base_url = f"{config.base_url}/cards/vdumps/list"
            url = f"{base_url}?{urlencode(filtered_params)}"

            # Build request components
            headers = self.external_api._build_headers(config, APIOperation.LIST_ITEMS)
            cookies = self.external_api._build_cookies(config)

            # Make authenticated request with POST method but no body (as per demo)
            response = await self.external_api._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=None,  # No body as per demo
                operation=APIOperation.LIST_ITEMS,
            )

            if response and response.success:
                logger.info("Successfully fetched vdumps v2 list")
                # Return the response.data directly as it contains the full JSON response
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error(
                    "Failed to fetch vdumps v2 list", extra={"error": error_msg}
                )
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error fetching vdumps v2 list: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def buy_vdump(self, item_id: int) -> Dict[str, Any]:
        """Buy a specific vdump"""
        logger.info(f"Buying vdump item {item_id}")

        payload = {"id": item_id}

        try:
            config = await self.external_api._get_api_config()
            if not config:
                return {"success": False, "error": "API configuration not available"}

            url = f"{config.base_url}/cards/vdumps/buy"
            headers = self.external_api._build_headers(config, APIOperation.ADD_TO_CART)
            cookies = self.external_api._build_cookies(config)

            response = await self.external_api._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.ADD_TO_CART,
            )

            if response and response.success:
                logger.info(f"Successfully bought vdump item {item_id}")
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error(
                    f"Failed to buy vdump item {item_id}", extra={"error": error_msg}
                )
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error buying vdump item {item_id}: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    # Cart Management Methods
    async def add_dump_to_cart(self, item_id: int) -> Dict[str, Any]:
        """Add dump to cart - uses existing ExternalAPIService add_to_cart method"""
        logger.info(f"Adding dump {item_id} to cart")

        try:
            # Use the existing add_to_cart method with correct product table name
            response = await self.external_api.add_to_cart(
                item_id=item_id, product_table_name="dumps"
            )

            if response and response.success:
                logger.debug(f"Successfully added dump {item_id} to cart")
                # Return the response.data directly as it contains the full JSON response
                return response.data if response.data else {"success": True}
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error(
                    f"Failed to add dump {item_id} to cart", extra={"error": error_msg}
                )
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error adding dump {item_id} to cart: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def view_cart(self) -> Dict[str, Any]:
        """View cart contents"""
        logger.info("Viewing cart contents")

        try:
            # Use the existing cart method from ExternalAPIService
            response = await self.external_api.view_cart()

            if response and response.success:
                logger.debug("Successfully retrieved cart contents")
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error("Failed to view cart", extra={"error": error_msg})
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error viewing cart: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def checkout_cart(self) -> Dict[str, Any]:
        """Checkout cart"""
        logger.info("Checking out cart")

        try:
            # Use the existing checkout method from ExternalAPIService
            response = await self.external_api.checkout()

            if response and response.success:
                logger.info("Successfully checked out cart")
                return response.data
            else:
                error_msg = response.error if response else "Unknown error"
                logger.error("Failed to checkout cart", extra={"error": error_msg})
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"Error checking out cart: {e}", exc_info=True)
            return {"success": False, "error": str(e)}


# Global instance
_dump_service_instance = None


def get_dump_service() -> DumpService:
    """Get global dump service instance"""
    global _dump_service_instance
    if _dump_service_instance is None:
        _dump_service_instance = DumpService()
    return _dump_service_instance


async def close_dump_service():
    """Close dump service"""
    global _dump_service_instance
    if _dump_service_instance:
        await _dump_service_instance.close()
        _dump_service_instance = None
