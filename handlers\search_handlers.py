"""
Enhanced search functionality handlers
"""
from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from utils.central_logger import get_logger
from utils.ui_components import create_message, MessageType
from utils.enhanced_keyboards import EnhancedKeyboardBuilder, KeyboardStyle, ButtonPriority
from utils.ui_manager import ui_manager
from utils.keyboards import back_keyboard
from utils.handler_helpers import <PERSON>rror<PERSON>and<PERSON>, user_validator

logger = get_logger()


class SearchStates(StatesGroup):
    """FSM states for search operations"""
    waiting_search_input = State()


class SearchHandlers:
    """Enhanced search functionality handlers"""
    
    def __init__(self):
        self.user_service = user_validator.user_service
        # Store search history per user (in production, use Redis or database)
        self.user_search_history = {}
        self.popular_searches = ["BIN", "Visa", "Mastercard", "American Express", "High Balance"]
    
    async def cb_search_input(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle search input callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            await state.set_state(SearchStates.waiting_search_input)
            
            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Enter Search Term", "🔍")
            message_builder.add_content("Type your search term below:")
            message_builder.add_section("Tips", 
                "• Use BIN numbers (e.g., 411111)\n"
                "• Card brands (Visa, Mastercard)\n"
                "• Countries or states\n"
                "• Bank names", 
                "💡")
            
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="🔙 Back", callback_data="catalog:search")]
                ]
            )
            
            await ui_manager.edit_message_safely(
                callback,
                message_builder.build(),
                keyboard
            )
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error in search input: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
    
    async def cb_search_repeat(self, callback: CallbackQuery) -> None:
        """Handle repeat search callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Extract search term from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid search term", show_alert=True)
                return
            
            search_term = ":".join(parts[2:])
            await self._perform_search(callback, search_term)
            
        except Exception as e:
            logger.error(f"Error in search repeat: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
    
    async def cb_search_popular(self, callback: CallbackQuery) -> None:
        """Handle popular search callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Extract search term from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid search term", show_alert=True)
                return
            
            search_term = ":".join(parts[2:])
            await self._perform_search(callback, search_term)
            
        except Exception as e:
            logger.error(f"Error in popular search: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
    
    async def cb_search_smart(self, callback: CallbackQuery) -> None:
        """Handle smart search callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Get user's search history for smart suggestions
            user_id = user.id
            search_history = self.user_search_history.get(user_id, [])
            
            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Smart Search", "🧠")
            message_builder.add_content("Choose from smart search options:")
            
            # Create keyboard with smart suggestions
            builder = EnhancedKeyboardBuilder().set_style(KeyboardStyle.LIST, 1)
            
            # Recent searches
            if search_history:
                builder.add_separator("🕒 Recent Searches")
                for term in search_history[:3]:
                    builder.add_button(f"🔍 {term}", f"search:repeat:{term}", ButtonPriority.SECONDARY)
            
            # Popular searches
            builder.add_separator("🔥 Popular Searches")
            for term in self.popular_searches[:3]:
                builder.add_button(f"🔥 {term}", f"search:popular:{term}", ButtonPriority.SUCCESS)
            
            # Quick filters
            builder.add_separator("⚡ Quick Filters")
            builder.add_button("💳 High Balance Cards", "search:popular:High Balance", ButtonPriority.PRIMARY)
            builder.add_button("🇺🇸 US Cards", "search:popular:US", ButtonPriority.PRIMARY)
            builder.add_button("💎 Premium Cards", "search:popular:Premium", ButtonPriority.PRIMARY)
            
            builder.add_navigation_row("🔙 Back to Search", "catalog:search")
            
            await ui_manager.edit_message_safely(
                callback,
                message_builder.build(),
                builder.build()
            )
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error in smart search: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
    
    async def msg_search_input(self, message: Message, state: FSMContext) -> None:
        """Handle search input message"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found")
                return
            
            search_term = (message.text or "").strip()
            if not search_term:
                await message.answer("❌ Please enter a search term")
                return
            
            # Store in search history
            user_id = user.id
            if user_id not in self.user_search_history:
                self.user_search_history[user_id] = []
            
            # Add to history (keep last 10)
            if search_term not in self.user_search_history[user_id]:
                self.user_search_history[user_id].insert(0, search_term)
                self.user_search_history[user_id] = self.user_search_history[user_id][:10]
            
            await state.clear()
            
            # Perform search
            await self._perform_search_from_message(message, search_term)
            
        except Exception as e:
            logger.error(f"Error in search input message: {e}")
            await message.answer("❌ Error occurred")
    
    async def _perform_search(self, callback: CallbackQuery, search_term: str) -> None:
        """Perform search and redirect to catalog with filters"""
        try:
            # For now, redirect to catalog search with a message
            # In a full implementation, this would set search filters and redirect
            
            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Search Results", "🔍")
            message_builder.add_content(f"Searching for: <b>{search_term}</b>")
            message_builder.add_section("Next Steps",
                "Use the filters below to refine your search or browse all results.",
                "💡")
            
            # Create keyboard to continue with search
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔍 Search Cards", 
                            callback_data="catalog:search"
                        ),
                        InlineKeyboardButton(
                            text="📄 Browse All", 
                            callback_data="catalog:browse_all"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", 
                            callback_data="catalog:search"
                        )
                    ]
                ]
            )
            
            await ui_manager.edit_message_safely(
                callback,
                message_builder.build(),
                keyboard
            )
            await callback.answer(f"🔍 Searching for: {search_term}")
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
    
    async def _perform_search_from_message(self, message: Message, search_term: str) -> None:
        """Perform search from message input"""
        try:
            # Similar to _perform_search but for message context
            await message.answer(
                f"🔍 <b>Search Results</b>\n\n"
                f"Searching for: <b>{search_term}</b>\n\n"
                f"Use the buttons below to continue:",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔍 Search Cards", 
                                callback_data="catalog:search"
                            ),
                            InlineKeyboardButton(
                                text="📄 Browse All", 
                                callback_data="catalog:browse_all"
                            )
                        ]
                    ]
                )
            )
            
        except Exception as e:
            logger.error(f"Error performing search from message: {e}")
            await message.answer("❌ Error occurred")


def get_search_router() -> Router:
    """Create and return search router"""
    router = Router()
    from middleware import attach_common_middlewares
    attach_common_middlewares(router)
    handlers = SearchHandlers()
    
    # Search callback handlers
    router.callback_query.register(
        handlers.cb_search_input, F.data == "search:input"
    )
    router.callback_query.register(
        handlers.cb_search_repeat, F.data.startswith("search:repeat:")
    )
    router.callback_query.register(
        handlers.cb_search_popular, F.data.startswith("search:popular:")
    )
    router.callback_query.register(
        handlers.cb_search_smart, F.data == "search:smart"
    )
    
    # Search message handler
    router.message.register(
        handlers.msg_search_input,
        SearchStates.waiting_search_input
    )
    
    logger.debug("Search handlers registered")
    return router
