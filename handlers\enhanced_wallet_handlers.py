"""
Enhanced Wallet Handlers with Payment Module Integration
"""

from __future__ import annotations

import re
from typing import Optional

from aiogram import Router, F
from aiogram.filters import Command
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from services.user_service import UserService
from services.export_service import ExportService
from services.payment_service import get_payment_service
from utils.keyboards import wallet_menu_keyboard, back_keyboard
from utils.texts import (
    SUCCESS_FUNDS_ADDED,
    ERROR_INVALID_AMOUNT,
    ERROR_USER_NOT_FOUND,
    ERROR_INSUFFICIENT_FUNDS,
    ERROR_WALLET_LOCKED,
    ERROR_VALIDATION_FAILED,
    INFO_WALLET_BALANCE,
    VALIDATION_AMOUNT_FORMAT,
    VALIDATION_AMOUNT_RANGE,
    DEMO_WATERMARK,
)
from utils.validation import validate_amount, ValidationError
from utils.loading_animations import LoadingStages, WALLET_STAGES
from middleware import attach_common_middlewares

from utils.central_logger import get_logger

logger = get_logger()


class AddFundsStates(StatesGroup):
    """States for add funds flow"""

    waiting_for_amount = State()


class EnhancedWalletHandlers:
    """Enhanced wallet handlers with payment module integration"""

    def __init__(self):
        self.user_service = None
        self.export_service = None
        self.payment_service = get_payment_service()

    def _get_user_service(self):
        """Lazy initialization of user service"""
        if self.user_service is None:
            from services.user_service import UserService
            self.user_service = UserService()
        return self.user_service

    def _get_export_service(self):
        """Lazy initialization of export service"""
        if self.export_service is None:
            from services.export_service import ExportService
            self.export_service = ExportService()
        return self.export_service

    async def cmd_balance(self, message: Message) -> None:
        """Handle /balance command - shows external API balance"""
        try:
            user = message.from_user
            if not user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            # Get user and wallet for currency info
            db_user = await self._get_user_service().get_user_by_telegram_id(user.id)
            if not db_user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            wallet = await self._get_user_service().get_wallet_by_user_id(str(db_user.id))
            if not wallet:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            # Get external balance instead of local wallet balance
            external_balance = await self._get_external_balance()

            if external_balance is not None:
                balance_text = (
                    f"💰 Your Balance: ${external_balance:.2f} {wallet.currency}\n"
                    f"📡 Source: External API (Live Balance)"
                    + DEMO_WATERMARK
                )
            else:
                # Try to get updated balance from payment module first
                try:
                    from payment_module.database.sync_operations import get_user_balance_sync
                    payment_balance = get_user_balance_sync(user.id)
                    if payment_balance is not None and payment_balance > 0:
                        balance_text = (
                            f"💰 Your Balance: ${payment_balance:.2f} {wallet.currency}\n"
                            f"📡 Source: Payment System (Updated)"
                            + DEMO_WATERMARK
                        )
                    else:
                        # Fallback to local wallet balance
                        balance_text = (
                            f"💰 Your Balance: ${wallet.balance:.2f} {wallet.currency}\n"
                            f"⚠️ Source: Local Wallet (External API unavailable)"
                            + DEMO_WATERMARK
                        )
                except Exception as e:
                    logger.error(f"Error getting payment balance: {e}")
                    # Fallback to local wallet balance
                    balance_text = (
                        f"💰 Your Balance: ${wallet.balance:.2f} {wallet.currency}\n"
                        f"⚠️ Source: Local Wallet (External API unavailable)"
                        + DEMO_WATERMARK
                    )

            await message.answer(balance_text, reply_markup=wallet_menu_keyboard())

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await message.answer("❌ Error retrieving balance" + DEMO_WATERMARK)

    async def _get_external_balance(self) -> Optional[float]:
        """Get balance from external API"""
        try:
            from services.external_api_service import ExternalAPIService

            api_service = ExternalAPIService()
            balance_data = await api_service.get_balance()
            
            if balance_data and "balance" in balance_data:
                return float(balance_data["balance"])
            
            return None
            
        except Exception as e:
            logger.debug(f"External balance API unavailable: {e}")
            return None

    async def cmd_deposit(self, message: Message, state: FSMContext) -> None:
        """Handle /deposit command - start deposit process"""
        try:
            user = message.from_user
            if not user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            # Check if payment service is available
            if not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK
                )
                return

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            deposit_keyboard = keyboards.get('deposit_amount')
            
            if not deposit_keyboard:
                await message.answer(
                    "🚫 Payment system configuration error.\n"
                    "Please contact support." + DEMO_WATERMARK
                )
                return

            # Show deposit amount selection
            deposit_text = (
                "💰 <b>Add Funds to Your Wallet</b>\n\n"
                "Select an amount to deposit or enter a custom amount.\n\n"
                "💳 <b>Payment Methods:</b>\n"
                "• Cryptocurrency (BTC, ETH, USDT, etc.)\n"
                "• Automatic conversion to USD\n"
                "• Secure processing via OXA Pay\n\n"
                "⚠️ <b>Important:</b>\n"
                "• All payments are processed securely\n"
                "• Funds are added instantly after verification\n"
                "• Minimum deposit: $10, Maximum: $1000"
            )

            await message.answer(
                deposit_text,
                reply_markup=deposit_keyboard(),
                parse_mode="HTML"
            )

        except Exception as e:
            logger.error(f"Error in deposit command: {e}")
            await message.answer("❌ Error starting deposit process" + DEMO_WATERMARK)

    async def cb_select_amount(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle amount selection callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract amount from callback data
            amount_str = callback.data.split(":")[-1]
            try:
                amount = float(amount_str)
            except ValueError:
                await callback.answer("❌ Invalid amount", show_alert=True)
                return

            # Validate amount
            if amount < 10.0 or amount > 1000.0:
                await callback.answer("❌ Amount must be between $10 and $1000", show_alert=True)
                return

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            pay_keyboard = keyboards.get('deposit_pay')
            
            if not pay_keyboard:
                await callback.answer("❌ Payment system error", show_alert=True)
                return

            # Show payment confirmation
            confirm_text = (
                f"💳 <b>Confirm Payment</b>\n\n"
                f"💰 Amount: <b>${amount:.2f}</b>\n"
                f"🔄 Currency: Automatic conversion to USD\n"
                f"⚡ Processing: Instant via OXA Pay\n\n"
                f"⚠️ <b>Important:</b>\n"
                f"• Click 'Pay Now' to generate payment link\n"
                f"• Complete payment in your crypto wallet\n"
                f"• Funds will be added after verification\n\n"
                f"🔒 <b>Secure Payment Processing</b>"
            )

            await callback.message.edit_text(
                confirm_text,
                reply_markup=pay_keyboard(amount),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in amount selection: {e}")
            await callback.answer("❌ Error processing amount selection", show_alert=True)

    async def cb_custom_amount(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle custom amount input"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            cancel_keyboard = keyboards.get('custom_amount_cancel')
            
            if not cancel_keyboard:
                await callback.answer("❌ Payment system error", show_alert=True)
                return

            # Show custom amount input
            input_text = (
                "💰 <b>Enter Custom Amount</b>\n\n"
                "Please enter the amount you want to deposit:\n\n"
                "📝 <b>Format:</b> Enter a number (e.g., 25.50)\n"
                "💵 <b>Range:</b> $10.00 - $1000.00\n\n"
                "⚠️ <b>Note:</b> Amount will be converted from cryptocurrency to USD"
            )

            await callback.message.edit_text(
                input_text,
                reply_markup=cancel_keyboard(),
                parse_mode="HTML"
            )
            
            # Set state for amount input
            await state.set_state(AddFundsStates.waiting_for_amount)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in custom amount: {e}")
            await callback.answer("❌ Error processing custom amount", show_alert=True)

    async def process_custom_amount(self, message: Message, state: FSMContext) -> None:
        """Process custom amount input"""
        try:
            user = message.from_user
            if not user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            # Extract amount from message
            amount_text = message.text.strip()
            
            # Remove common currency symbols
            amount_text = re.sub(r'[$€£¥]', '', amount_text)
            
            try:
                amount = float(amount_text)
            except ValueError:
                await message.answer(
                    f"❌ {ERROR_INVALID_AMOUNT}\n\n"
                    f"Please enter a valid number between $10 and $1000."
                )
                return

            # Validate amount
            try:
                validate_amount(amount, min_amount=10.0, max_amount=1000.0)
            except ValidationError as e:
                await message.answer(f"❌ {e.message}")
                return

            # Clear state
            await state.clear()

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            pay_keyboard = keyboards.get('deposit_pay')
            
            if not pay_keyboard:
                await message.answer("❌ Payment system error" + DEMO_WATERMARK)
                return

            # Show payment confirmation
            confirm_text = (
                f"💳 <b>Confirm Payment</b>\n\n"
                f"💰 Amount: <b>${amount:.2f}</b>\n"
                f"🔄 Currency: Automatic conversion to USD\n"
                f"⚡ Processing: Instant via OXA Pay\n\n"
                f"⚠️ <b>Important:</b>\n"
                f"• Click 'Pay Now' to generate payment link\n"
                f"• Complete payment in your crypto wallet\n"
                f"• Funds will be added after verification\n\n"
                f"🔒 <b>Secure Payment Processing</b>"
            )

            await message.answer(
                confirm_text,
                reply_markup=pay_keyboard(amount),
                parse_mode="HTML"
            )

        except Exception as e:
            logger.error(f"Error processing custom amount: {e}")
            await message.answer("❌ Error processing amount" + DEMO_WATERMARK)
            await state.clear()

    async def cb_pay_deposit(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle payment processing"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract amount from callback data
            amount_str = callback.data.split(":")[-1]
            try:
                amount = float(amount_str)
            except ValueError:
                await callback.answer("❌ Invalid amount", show_alert=True)
                return

            # Create payment link
            success, payment_data, error_msg = await self.payment_service.create_payment_link(
                amount=amount,
                user_id=user.id,
                description="Wallet Deposit"
            )

            if not success:
                await callback.answer(f"❌ {error_msg}", show_alert=True)
                return

            # Payment saved to DB - callback will handle verification automatically
            track_id = payment_data.get('trackId')
            logger.info(f"✅ Payment created for user {user.id}, track_id={track_id} - awaiting callback")

            # Show payment link with information
            payment_text = (
                f"💳 <b>Payment Link Generated</b>\n\n"
                f"💰 <b>Amount:</b> ${amount:.2f}\n"
                f"🆔 <b>Order ID:</b> <code>{payment_data['orderId']}</code>\n"
                f"🔗 <b>Track ID:</b> <code>{track_id}</code>\n\n"
                f"📱 <b>Next Steps:</b>\n"
                f"1. Click the payment link below\n"
                f"2. Complete payment in your crypto wallet\n"
                f"3. Payment will be credited instantly via webhook\n"
                f"4. Funds will be added instantly\n\n"
                f"⚠️ <b>Important:</b>\n"
                f"• Payment includes automatic verification\n"
                f"• Underpayment/overpayment detection enabled"
            )

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            verify_keyboard = keyboards.get('payment_verification')
            
            if not verify_keyboard:
                await callback.answer("❌ Payment system error", show_alert=True)
                return

            await callback.message.edit_text(
                payment_text,
                reply_markup=verify_keyboard(),
                parse_mode="HTML"
            )

            # Send payment link separately
            await callback.message.answer(
                f"🔗 <b>Payment Link:</b>\n{payment_data['payLink']}",
                parse_mode="HTML"
            )

            await callback.answer("✅ Payment link generated with enhanced features!")

        except Exception as e:
            logger.error(f"Error in payment processing: {e}")
            await callback.answer("❌ Error processing payment", show_alert=True)

    async def cb_cancel_deposit(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle deposit cancellation"""
        try:
            await state.clear()
            
            await callback.message.edit_text(
                "❌ Deposit cancelled.\n\n"
                "You can start a new deposit anytime using /deposit" + DEMO_WATERMARK,
                reply_markup=wallet_menu_keyboard()
            )
            await callback.answer("Deposit cancelled")

        except Exception as e:
            logger.error(f"Error cancelling deposit: {e}")
            await callback.answer("❌ Error cancelling deposit", show_alert=True)

    async def cb_wallet_menu(self, callback: CallbackQuery) -> None:
        """Handle wallet menu callback - show wallet menu with all options"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Create work coroutine for wallet operations
            async def wallet_work():
                # Get wallet balance
                db_user = await self._get_user_service().get_user_by_telegram_id(user.id)
                if not db_user:
                    return None, None, None

                wallet = await self._get_user_service().get_wallet_by_user_id(str(db_user.id))
                if not wallet:
                    return None, None, None

                # Get external balance instead of local wallet balance
                external_balance = await self._get_external_balance()
                return db_user, wallet, external_balance
            
            # Run loading stages concurrently with wallet operations
            result = await LoadingStages.run_concurrent_loading(
                callback,
                WALLET_STAGES,
                wallet_work(),
                operation_name="Wallet Balance Check"
            )
            
            db_user, wallet, external_balance = result
            
            if not db_user or not wallet:
                await callback.answer("❌ Wallet not found", show_alert=True)
                return

            if external_balance is not None:
                # Enhanced balance display with visual elements
                balance_emoji = "💎" if external_balance >= 100 else "💰" if external_balance >= 10 else "💸"
                balance_text = (
                    f"<b>💼 WALLET DASHBOARD</b>\n\n"
                    f"{balance_emoji} <b>Current Balance: ${external_balance:.2f}</b>\n"
                    f"📡 <i>Live Balance • External API</i>\n"
                    f"🔄 <i>Last Updated: Just now</i>\n\n"
                    f"<b>📋 Available Actions:</b>\n"
                    f"• Add funds to your wallet\n"
                    f"• Verify payments manually\n"
                    f"• View wallet statistics\n\n"
                    f"<i>Choose an option below:</i>" + DEMO_WATERMARK
                )
            else:
                # Fallback to local balance if external API is unavailable
                balance_emoji = "💎" if wallet.balance >= 100 else "💰" if wallet.balance >= 10 else "💸"
                balance_text = (
                    f"<b>💼 WALLET DASHBOARD</b>\n\n"
                    f"{balance_emoji} <b>Current Balance: ${wallet.balance:.2f}</b>\n"
                    f"⚠️ <i>Local Wallet • External API unavailable</i>\n"
                    f"🔄 <i>Last Updated: Just now</i>\n\n"
                    f"<b>📋 Available Actions:</b>\n"
                    f"• Add funds to your wallet\n"
                    f"• Verify payments manually\n"
                    f"• View wallet statistics\n\n"
                    f"<i>Choose an option below:</i>" + DEMO_WATERMARK
                )

            await callback.message.edit_text(
                balance_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in wallet menu callback: {e}")
            await callback.answer("❌ Error retrieving balance", show_alert=True)

    async def cb_add_funds(self, callback: CallbackQuery) -> None:
        """Handle add funds callback - redirect to amount selection flow"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract amount from callback data
            amount_map = {
                "wallet:add10": 10.0,
                "wallet:add25": 25.0,
                "wallet:add50": 50.0,
            }
            
            amount = amount_map.get(callback.data)
            if not amount:
                await callback.answer("❌ Invalid amount", show_alert=True)
                return

            # Check if payment service is available
            if not self.payment_service.is_available():
                await callback.message.edit_text(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK,
                    reply_markup=wallet_menu_keyboard()
                )
                await callback.answer("Payment system unavailable", show_alert=True)
                return

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            pay_keyboard = keyboards.get('deposit_pay')
            
            if not pay_keyboard:
                await callback.answer("❌ Payment system error", show_alert=True)
                return

            # Show payment confirmation (same as /deposit flow)
            confirm_text = (
                f"💳 <b>Confirm Payment</b>\n\n"
                f"💰 Amount: <b>${amount:.2f}</b>\n"
                f"🔄 Currency: Automatic conversion to USD\n"
                f"⚡ Processing: Instant via OXA Pay\n\n"
                f"⚠️ <b>Important:</b>\n"
                f"• Click 'Pay Now' to generate payment link\n"
                f"• Complete payment in your crypto wallet\n"
                f"• Funds will be added after verification\n\n"
                f"🔒 <b>Secure Payment Processing</b>"
            )

            await callback.message.edit_text(
                confirm_text,
                reply_markup=pay_keyboard(amount),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in add funds callback: {e}")
            await callback.answer("❌ Error processing payment", show_alert=True)

    async def cb_add_funds_menu(self, callback: CallbackQuery) -> None:
        """Handle add funds menu callback - show deposit amount selection"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Check if payment service is available
            if not self.payment_service.is_available():
                await callback.message.edit_text(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK,
                    reply_markup=wallet_menu_keyboard()
                )
                await callback.answer("Payment system unavailable", show_alert=True)
                return

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            deposit_keyboard = keyboards.get('deposit_amount')
            
            if not deposit_keyboard:
                await callback.message.edit_text(
                    "🚫 Payment system configuration error.\n"
                    "Please contact support." + DEMO_WATERMARK,
                    reply_markup=wallet_menu_keyboard()
                )
                await callback.answer("Payment system error", show_alert=True)
                return

            # Show deposit amount selection
            deposit_text = (
                "💰 <b>Add Funds to Your Wallet</b>\n\n"
                "Select an amount to deposit or enter a custom amount.\n\n"
                "💳 <b>Payment Methods:</b>\n"
                "• Cryptocurrency (BTC, ETH, USDT, etc.)\n"
                "• Automatic conversion to USD\n"
                "• Secure processing via OXA Pay\n\n"
                "⚠️ <b>Important:</b>\n"
                "• All payments are processed securely\n"
                "• Funds are added instantly after verification\n"
                "• Minimum deposit: $10, Maximum: $1000"
            )

            await callback.message.edit_text(
                deposit_text,
                reply_markup=deposit_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in add funds menu callback: {e}")
            await callback.answer("❌ Error processing request", show_alert=True)

    async def cb_add_custom(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle add custom amount callback - use same flow as /deposit"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Check if payment service is available
            if not self.payment_service.is_available():
                await callback.message.edit_text(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK,
                    reply_markup=wallet_menu_keyboard()
                )
                await callback.answer("Payment system unavailable", show_alert=True)
                return

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            cancel_keyboard = keyboards.get('custom_amount_cancel')
            
            if not cancel_keyboard:
                await callback.answer("❌ Payment system error", show_alert=True)
                return

            # Show custom amount input (same as /deposit flow)
            input_text = (
                "💰 <b>Enter Custom Amount</b>\n\n"
                "Please enter the amount you want to deposit:\n\n"
                "📝 <b>Format:</b> Enter a number (e.g., 25.50)\n"
                "💵 <b>Range:</b> $10.00 - $1000.00\n\n"
                "⚠️ <b>Note:</b> Amount will be converted from cryptocurrency to USD"
            )

            await callback.message.edit_text(
                input_text,
                reply_markup=cancel_keyboard(),
                parse_mode="HTML"
            )
            
            # Set state for amount input
            await state.set_state(AddFundsStates.waiting_for_amount)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in add custom callback: {e}")
            await callback.answer("❌ Error processing custom amount", show_alert=True)

    async def cb_wallet_stats(self, callback: CallbackQuery) -> None:
        """Handle wallet statistics callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user and wallet
            db_user = await self._get_user_service().get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer(ERROR_USER_NOT_FOUND, show_alert=True)
                return

            wallet = await self._get_user_service().get_wallet_by_user_id(str(db_user.id))
            if not wallet:
                await callback.answer(ERROR_USER_NOT_FOUND, show_alert=True)
                return

            # Create statistics text with important data only
            created_date = wallet.created_at.strftime('%Y-%m-%d') if hasattr(wallet, 'created_at') and wallet.created_at else 'N/A'
            
            # Balance emoji based on amount
            balance_emoji = "💎" if wallet.balance >= 100 else "💰" if wallet.balance >= 10 else "💸"
            
            stats_text = (
                f"📊 <b>Wallet Statistics</b>\n\n"
                f"{balance_emoji} <b>Current Balance: ${wallet.balance:.2f} {wallet.currency}</b>\n\n"
                f"📈 <b>Account Information:</b>\n"
                f"• User ID: {db_user.id}\n"
                f"• Currency: {wallet.currency}\n"
                f"• Account Created: {created_date}\n\n"
                f"💳 <b>Available Features:</b>\n"
                f"• Cryptocurrency deposits\n"
                f"• Real-time payment verification\n"
                f"• Secure wallet management"
            )

            await callback.message.edit_text(
                stats_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in wallet stats callback: {e}")
            await callback.answer("❌ Error retrieving statistics", show_alert=True)

    async def cb_export_csv(self, callback: CallbackQuery) -> None:
        """Handle CSV export callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user data
            user_obj = await self._get_user_service().get_user_by_telegram_id(user.id)
            if not user_obj:
                await callback.answer(ERROR_USER_NOT_FOUND, show_alert=True)
                return

            # Generate CSV export
            csv_content = await self._get_export_service().export_transactions_csv(
                str(user_obj.id)
            )

            if not csv_content:
                await callback.answer(ERROR_USER_NOT_FOUND, show_alert=True)
                return

            # For demo purposes, show a preview instead of sending file
            lines = csv_content.split("\n")
            preview = "\n".join(lines[:6])  # Show first 5 rows + header

            preview_text = f"""
📄 <b>Transaction Export Preview</b>

<pre>{preview}</pre>

<i>In a production environment, this would be sent as a downloadable CSV file.</i>
"""

            await callback.message.edit_text(
                preview_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in CSV export callback: {e}")
            await callback.answer("❌ Error generating export", show_alert=True)

    async def cb_view_balance(self, callback: CallbackQuery) -> None:
        """Handle view balance button from payment completion"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user and wallet for currency info
            db_user = await self._get_user_service().get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            wallet = await self._get_user_service().get_wallet_by_user_id(str(db_user.id))
            if not wallet:
                await callback.answer("❌ Wallet not found", show_alert=True)
                return

            # Get external balance instead of local wallet balance
            external_balance = await self._get_external_balance()

            if external_balance is not None:
                balance_text = (
                    f"💰 <b>Your Balance</b>\n\n"
                    f"💵 <b>Amount:</b> ${external_balance:.2f} {wallet.currency}\n"
                    f"📡 <b>Source:</b> External API (Live Balance)\n\n"
                    f"✅ <b>Balance Updated!</b>\n"
                    f"Your recent payment has been added to your balance."
                    + DEMO_WATERMARK
                )
            else:
                # Try to get updated balance from payment module first
                try:
                    from payment_module.database.sync_operations import get_user_balance_sync
                    payment_balance = get_user_balance_sync(user.id)
                    if payment_balance is not None and payment_balance > 0:
                        balance_text = (
                            f"💰 <b>Your Balance</b>\n\n"
                            f"💵 <b>Amount:</b> ${payment_balance:.2f} {wallet.currency}\n"
                            f"📡 <b>Source:</b> Payment System (Updated)\n\n"
                            f"✅ <b>Balance Updated!</b>\n"
                            f"Your recent payment has been added to your balance."
                            + DEMO_WATERMARK
                        )
                    else:
                        # Fallback to local wallet balance
                        balance_text = (
                            f"💰 <b>Your Balance</b>\n\n"
                            f"💵 <b>Amount:</b> ${wallet.balance:.2f} {wallet.currency}\n"
                            f"⚠️ <b>Source:</b> Local Wallet (External API unavailable)\n\n"
                            f"✅ <b>Balance Updated!</b>\n"
                            f"Your recent payment has been added to your balance."
                            + DEMO_WATERMARK
                        )
                except Exception as e:
                    logger.error(f"Error getting payment balance: {e}")
                    # Fallback to local wallet balance
                    balance_text = (
                        f"💰 <b>Your Balance</b>\n\n"
                        f"💵 <b>Amount:</b> ${wallet.balance:.2f} {wallet.currency}\n"
                        f"⚠️ <b>Source:</b> Local Wallet (External API unavailable)\n\n"
                        f"✅ <b>Balance Updated!</b>\n"
                        f"Your recent payment has been added to your balance."
                        + DEMO_WATERMARK
                    )

            await callback.message.edit_text(
                balance_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer("✅ Balance updated!")

        except Exception as e:
            logger.error(f"Error in view balance callback: {e}")
            await callback.answer("❌ Error retrieving balance", show_alert=True)

    async def cb_transaction_history(self, callback: CallbackQuery) -> None:
        """Handle transaction history button from payment completion"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user's transaction history
            db_user = await self._get_user_service().get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get recent transactions
            transactions = await self._get_user_service().get_user_transactions(str(db_user.id), limit=10)
            
            if not transactions:
                history_text = (
                    "📋 <b>Transaction History</b>\n\n"
                    "No transactions found.\n"
                    "Your recent payment will appear here once processed."
                    + DEMO_WATERMARK
                )
            else:
                history_text = "📋 <b>Recent Transactions</b>\n\n"
                for i, transaction in enumerate(transactions[:5], 1):
                    amount = transaction.get('amount', 0)
                    transaction_type = transaction.get('transaction_type', 'unknown')
                    created_at = transaction.get('created_at', 'Unknown')
                    
                    # Format transaction type
                    type_emoji = "💰" if transaction_type == "deposit" else "💸"
                    type_text = "Deposit" if transaction_type == "deposit" else transaction_type.title()
                    
                    history_text += (
                        f"{i}. {type_emoji} <b>{type_text}</b>\n"
                        f"   💵 ${amount:.2f}\n"
                        f"   📅 {created_at}\n\n"
                    )
                
                history_text += f"<i>Showing {min(len(transactions), 5)} of {len(transactions)} transactions</i>"
                history_text += DEMO_WATERMARK

            await callback.message.edit_text(
                history_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer("📋 Transaction history loaded")

        except Exception as e:
            logger.error(f"Error in transaction history callback: {e}")
            await callback.answer("❌ Error retrieving transaction history", show_alert=True)

    async def cb_deposit_funds(self, callback: CallbackQuery) -> None:
        """Handle deposit funds button from payment completion"""
        try:
            # Redirect to add funds menu
            await self.cb_add_funds_menu(callback)
            await callback.answer("💰 Add funds menu opened")

        except Exception as e:
            logger.error(f"Error in deposit funds callback: {e}")
            await callback.answer("❌ Error opening deposit menu", show_alert=True)




def get_enhanced_wallet_router() -> Router:
    """Get enhanced wallet router with payment integration"""
    router = Router()
    handlers = EnhancedWalletHandlers()
    
    # Attach common middlewares
    attach_common_middlewares(router)
    
    # Commands
    router.message.register(handlers.cmd_balance, Command("balance"))
    router.message.register(handlers.cmd_deposit, Command("deposit"))
    
    # Payment callbacks
    router.callback_query.register(
        handlers.cb_select_amount, 
        F.data.startswith("select_amount:")
    )
    router.callback_query.register(
        handlers.cb_custom_amount, 
        F.data == "custom_amount"
    )
    router.callback_query.register(
        handlers.cb_pay_deposit, 
        F.data.startswith("pay_deposit:")
    )
    router.callback_query.register(
        handlers.cb_cancel_deposit, 
        F.data == "cancel_deposit"
    )
    
    # Legacy wallet callbacks (for backward compatibility)
    router.callback_query.register(handlers.cb_wallet_menu, F.data == "menu:wallet")
    router.callback_query.register(
        handlers.cb_add_funds,
        F.data.in_(["wallet:add10", "wallet:add25", "wallet:add50"]),
    )
    router.callback_query.register(
        handlers.cb_add_custom, F.data == "wallet:add_custom"
    )
    router.callback_query.register(handlers.cb_wallet_stats, F.data == "wallet:stats")
    router.callback_query.register(
        handlers.cb_export_csv, F.data == "wallet:export_csv"
    )
    
    # New clean wallet callbacks
    router.callback_query.register(
        handlers.cb_add_funds_menu, F.data == "wallet:add_funds"
    )
    
    # Payment completion callbacks
    router.callback_query.register(
        handlers.cb_view_balance, F.data == "view_balance"
    )
    router.callback_query.register(
        handlers.cb_transaction_history, F.data == "transaction_history"
    )
    router.callback_query.register(
        handlers.cb_deposit_funds, F.data == "deposit_funds"
    )
    
    
    # State handlers
    router.message.register(
        handlers.process_custom_amount,
        AddFundsStates.waiting_for_amount
    )
    
    return router
