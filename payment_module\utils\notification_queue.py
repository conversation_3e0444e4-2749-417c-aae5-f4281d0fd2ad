"""
Notification queue for sending Telegram notifications from Flask callbacks.

This module provides a thread-safe queue system for sending Telegram notifications
from the Flask callback server (which runs in a separate thread) to users via the bot.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from queue import Queue
from datetime import datetime

logger = logging.getLogger(__name__)

# Global notification queue
_notification_queue = Queue()

def add_notification(
    user_id: int,
    notification_type: str,
    track_id: str,
    received_amount: float,
    required_amount: float,
    bonus_amount: float = 0.0,
    **kwargs
) -> None:
    """
    Add a notification to the queue for processing.
    
    Args:
        user_id: Telegram user ID
        notification_type: Type of notification ("completed", "underpayment", "overpayment")
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        bonus_amount: Bonus amount (if any)
        **kwargs: Additional metadata
    """
    notification_data = {
        "user_id": user_id,
        "type": notification_type,
        "track_id": track_id,
        "received_amount": received_amount,
        "required_amount": required_amount,
        "bonus_amount": bonus_amount,
        "timestamp": datetime.now(),
        "metadata": kwargs
    }
    
    _notification_queue.put(notification_data)
    logger.info(f"📬 Notification queued: type={notification_type}, user={user_id}, track_id={track_id}")


def get_pending_notifications() -> list:
    """
    Get all pending notifications from the queue.
    
    Returns:
        List of notification dictionaries
    """
    notifications = []
    while not _notification_queue.empty():
        try:
            notification = _notification_queue.get_nowait()
            notifications.append(notification)
        except:
            break
    
    return notifications


async def process_notifications(bot) -> int:
    """
    Process all pending notifications and send them to users.
    
    Args:
        bot: Telegram Bot instance
        
    Returns:
        Number of notifications processed
    """
    from ..utils.payment_amount_handler import (
        handle_payment_completion,
        handle_underpayment,
        handle_overpayment
    )
    
    notifications = get_pending_notifications()
    
    if not notifications:
        return 0
    
    logger.info(f"📨 Processing {len(notifications)} pending notifications...")
    
    processed = 0
    for notification in notifications:
        try:
            user_id = notification["user_id"]
            notification_type = notification["type"]
            track_id = notification["track_id"]
            received_amount = notification["received_amount"]
            required_amount = notification["required_amount"]
            if notification_type == "completed":
                await handle_payment_completion(
                    bot=bot,
                    user_id=user_id,
                    track_id=track_id,
                    received_amount=received_amount,
                    required_amount=required_amount
                )
                processed += 1
                
            elif notification_type == "underpayment":
                await handle_underpayment(
                    bot=bot,
                    user_id=user_id,
                    track_id=track_id,
                    received_amount=received_amount,
                    required_amount=required_amount
                )
                processed += 1
                
            elif notification_type == "overpayment":
                await handle_overpayment(
                    bot=bot,
                    user_id=user_id,
                    track_id=track_id,
                    received_amount=received_amount,
                    required_amount=required_amount
                )
                processed += 1
            
            logger.info(f"✅ Notification sent: type={notification_type}, user={user_id}, track_id={track_id}")
            
        except Exception as e:
            logger.error(f"❌ Error processing notification for user {user_id}: {e}")
    
    logger.info(f"📨 Processed {processed}/{len(notifications)} notifications successfully")
    return processed

