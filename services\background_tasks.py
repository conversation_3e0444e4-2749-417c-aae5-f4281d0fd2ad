"""
Background task service for running periodic tasks like health monitoring,
metrics collection, and data cleanup.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any

# Legacy API health monitor removed - now handled by admin/services/api_health_monitoring.py
from config.settings import get_settings
from admin.services.api_health_monitoring import get_api_health_monitoring_service

from utils.central_logger import get_logger

logger = get_logger()


class BackgroundTaskService:
    """Service for managing background tasks"""
    
    def __init__(self):
        self.settings = get_settings()
        self._tasks: Dict[str, asyncio.Task] = {}
        self._is_running = False
    
    async def start(self) -> None:
        """Start all background tasks"""
        if self._is_running:
            logger.warning("Background tasks are already running")
            return
        
        self._is_running = True
        logger.debug("Starting background tasks")
        
        # Health monitoring now handled by admin/services/api_health_monitoring.py
        # No need to start legacy health monitor
        
        # Start periodic tasks
        self._tasks["metrics_collector"] = asyncio.create_task(self._metrics_collection_loop())
        self._tasks["data_cleanup"] = asyncio.create_task(self._data_cleanup_loop())
        
        logger.debug("Background tasks ready")
    
    async def stop(self) -> None:
        """Stop all background tasks"""
        self._is_running = False
        
        # Legacy health monitoring removed
        # Health monitoring now handled by admin/services/api_health_monitoring.py
        
        # Cancel all tasks
        for task_name, task in self._tasks.items():
            logger.info(f"Stopping background task: {task_name}")
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        self._tasks.clear()
        logger.info("All background tasks stopped")
    
    async def _metrics_collection_loop(self) -> None:
        """Loop for collecting usage metrics"""
        while self._is_running:
            try:
                current_time = datetime.now()

                # Collect hourly metrics at the top of each hour
                if current_time.minute == 0:
                    await health_monitor.collect_usage_metrics("hourly")

                # Collect daily metrics at midnight
                if current_time.hour == 0 and current_time.minute == 0:
                    await health_monitor.collect_usage_metrics("daily")

                # Wait for next check (check every minute)
                await asyncio.sleep(60)

            except asyncio.CancelledError:
                # Exit promptly on cancellation (e.g., Ctrl+C)
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                # Short backoff so shutdown isn't delayed
                try:
                    await asyncio.sleep(5)
                except asyncio.CancelledError:
                    break
    
    async def _data_cleanup_loop(self) -> None:
        """Loop for cleaning up old data"""
        while self._is_running:
            try:
                # Run cleanup once per day at 2 AM
                current_time = datetime.now()
                if current_time.hour == 2 and current_time.minute == 0:
                    await health_monitor.cleanup_old_data(days_to_keep=90)

                # Wait for next check (check every hour)
                await asyncio.sleep(3600)

            except asyncio.CancelledError:
                # Exit promptly on cancellation
                break
            except Exception as e:
                logger.error(f"Error in data cleanup loop: {e}")
                try:
                    await asyncio.sleep(10)
                except asyncio.CancelledError:
                    break


# Local API health monitor instance (compatibility with legacy background tasks)
health_monitor = get_api_health_monitoring_service()

# Global background task service instance
background_service = BackgroundTaskService()


async def start_background_tasks() -> None:
    """Start all background tasks"""
    await background_service.start()


async def stop_background_tasks() -> None:
    """Stop all background tasks"""
    await background_service.stop()
