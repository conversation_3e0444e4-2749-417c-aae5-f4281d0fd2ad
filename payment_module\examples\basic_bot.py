"""
Example: Basic Bot Integration

This example shows how to integrate the payment module into a basic Telegram bot.
"""

import asyncio
import logging
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.filters import CommandStart
from aiogram.types import Message

from payment_module import create_payment_module

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot configuration
BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"
OXA_PAY_API_KEY = "YOUR_OXA_PAY_API_KEY_HERE"
CALLBACK_URL = "https://yourdomain.com/callback"  # Optional

# Initialize bot and dispatcher
bot = Bot(token=BOT_TOKEN)
dp = Dispatcher()

# Initialize payment module
payment_module = create_payment_module(
    api_key=OXA_PAY_API_KEY,
    callback_url=CALLBACK_URL
)


@dp.message(CommandStart())
async def cmd_start(message: Message):
    """Handle /start command."""
    welcome_text = (
        "🤖 <b>Welcome to Payment Bot!</b>\n\n"
        "This bot demonstrates the payment module integration.\n\n"
        "Available commands:\n"
        "• /deposit - Add funds to your account\n"
        "• /balance - Check your balance\n"
        "• /help - Show help information\n\n"
        "<i>Use /deposit to start adding funds to your account.</i>"
    )
    
    await message.answer(welcome_text, parse_mode="HTML")


@dp.message(lambda message: message.text == "/balance")
async def cmd_balance(message: Message):
    """Handle /balance command."""
    # In a real implementation, you would check the user's balance from database
    balance_text = (
        "💰 <b>Your Balance</b>\n\n"
        f"<b>Current Balance:</b> <code>$0.00</code> USDT\n\n"
        "<i>Use /deposit to add funds to your account.</i>"
    )
    
    await message.answer(balance_text, parse_mode="HTML")


@dp.message(lambda message: message.text == "/help")
async def cmd_help(message: Message):
    """Handle /help command."""
    help_text = (
        "❓ <b>Help Information</b>\n\n"
        "<b>Available Commands:</b>\n"
        "• /start - Welcome message\n"
        "• /deposit - Add funds to your account\n"
        "• /balance - Check your balance\n"
        "• /help - Show this help message\n\n"
        "<b>Payment Information:</b>\n"
        "• We accept cryptocurrency payments via OXA Pay\n"
        "• All payments are converted to USDT\n"
        "• Payments are processed securely\n"
        "• Balance updates are instant\n\n"
        "<i>For support, contact the bot administrator.</i>"
    )
    
    await message.answer(help_text, parse_mode="HTML")


async def main():
    """Main function to start the bot."""
    try:
        # Register payment handlers
        handlers = payment_module.get_handlers()
        for handler in handlers:
            dp.include_router(handler.router)
            logger.info(f"Registered handler: {handler.router.name}")
        
        logger.info("Starting bot...")
        await dp.start_polling(bot)
        
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
    finally:
        await bot.session.close()


if __name__ == "__main__":
    asyncio.run(main())

