"""
Common utilities shared across all modules.
This eliminates the duplicated _response_to_jsonable function across multiple files.
"""

import json
from typing import Dict, Any
import requests


def response_to_jsonable(r: requests.Response) -> Dict[str, Any]:
    """
    Convert a requests Response object to a JSON-serializable dict.
    This function is used across multiple modules for consistent response handling.
    """
    return {
        "status": r.status_code,
        "url": r.url,
        "headers": dict(r.headers),
        "body_preview": (r.text or "")[:800],
    }


def save_json_result(data: Dict[str, Any], filepath: str) -> str:
    """
    Save dictionary data to JSON file with consistent formatting.
    """
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return filepath
    except Exception as e:
        raise Exception(f"Failed to save JSON to {filepath}: {e}")


def load_json_file(filepath: str) -> Dict[str, Any]:
    """
    Load JSON file and return as dictionary.
    """
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        raise Exception(f"Failed to load JSON from {filepath}: {e}")
