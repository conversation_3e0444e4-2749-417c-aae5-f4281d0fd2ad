"""
API v1 Checkout Processor Service

Handles database operations for checkout process:
- Purchase record creation
- Wallet balance deduction
- Transaction logging
- Cart clearing
- Atomicity via transactions
"""

from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from bson import ObjectId

from database.connection import get_collection, database_transaction
from models.transaction import Purchase, PurchaseStatus, PurchaseProductType
from models.user import Wallet
from models.base import now_utc

from utils.central_logger import get_logger

logger = get_logger()


class CheckoutProcessorError(Exception):
    """Checkout processor specific errors"""
    def __init__(self, message: str, error_code: str = "CHECKOUT_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class CheckoutProcessorService:
    """
    Service for processing checkout and database operations
    
    Handles:
    - Purchase record creation from cart items
    - Wallet balance deduction
    - Transaction audit logging
    - Cart clearing
    - Rollback on failure
    """
    
    def __init__(self):
        self.purchases_collection = get_collection("purchases")
        self.wallets_collection = get_collection("wallets")
        self.transactions_collection = get_collection("wallet_transactions")
        self.logger = get_logger()
    
    async def process_checkout(
        self,
        user_id: str,
        cart_items: List[Dict[str, Any]],
        api_order_id: Optional[str] = None,
        api_version: str = "v1"
    ) -> Dict[str, Any]:
        """
        Process complete checkout with database operations
        
        Args:
            user_id: User ID
            cart_items: List of items from cart
            api_order_id: External API order ID (if available)
            api_version: API version used
            
        Returns:
            Result dictionary with purchase details
        """
        try:
            self.logger.info(f"🛒 Processing checkout for user {user_id} ({len(cart_items)} items)")
            
            # Validate cart items
            if not cart_items:
                raise CheckoutProcessorError(
                    "Cart is empty",
                    error_code="EMPTY_CART"
                )
            
            # Calculate total price accounting for quantity
            total_price = sum(
                float(item.get('price', 0)) * (1 - float(item.get('discount', 0)) / 100) * int(item.get('quantity', 1))
                for item in cart_items
            )
            
            if total_price <= 0:
                raise CheckoutProcessorError(
                    "Invalid cart total",
                    error_code="INVALID_TOTAL"
                )
            
            # Check wallet balance
            wallet = await self._get_user_wallet(user_id)
            if wallet['balance'] < total_price:
                raise CheckoutProcessorError(
                    f"Insufficient balance: have ${wallet['balance']:.2f}, need ${total_price:.2f}",
                    error_code="INSUFFICIENT_BALANCE"
                )
            
            # Process checkout in transaction
            result = await self._process_checkout_transaction(
                user_id=user_id,
                cart_items=cart_items,
                total_price=total_price,
                wallet_balance=wallet['balance'],
                api_order_id=api_order_id,
                api_version=api_version
            )
            
            self.logger.info(f"✅ Checkout completed successfully for user {user_id}")
            return result
            
        except CheckoutProcessorError:
            raise
        except Exception as e:
            self.logger.error(f"❌ Checkout processing failed: {e}")
            raise CheckoutProcessorError(
                f"Checkout processing failed: {str(e)}",
                error_code="PROCESSING_ERROR"
            )
    
    async def _process_checkout_transaction(
        self,
        user_id: str,
        cart_items: List[Dict[str, Any]],
        total_price: float,
        wallet_balance: float,
        api_order_id: Optional[str],
        api_version: str
    ) -> Dict[str, Any]:
        """
        Process checkout within a transaction for atomicity
        
        This ensures all operations (purchase creation, wallet deduction) 
        happen atomically or not at all.
        """
        try:
            # Create purchases
            purchases = await self._create_purchase_records(
                user_id=user_id,
                cart_items=cart_items,
                api_order_id=api_order_id,
                api_version=api_version
            )
            
            # Deduct from wallet
            new_balance = await self._deduct_wallet_balance(
                user_id=user_id,
                amount=total_price,
                old_balance=wallet_balance,
                transaction_ref=api_order_id or str(ObjectId())
            )
            
            # Log transaction
            transaction_log = await self._log_wallet_transaction(
                user_id=user_id,
                amount=total_price,
                old_balance=wallet_balance,
                new_balance=new_balance,
                transaction_type="purchase",
                reference=api_order_id,
                purchase_ids=[str(p['_id']) for p in purchases]
            )
            
            return {
                "success": True,
                "purchases": purchases,
                "total_price": total_price,
                "new_balance": new_balance,
                "transaction_id": str(transaction_log['_id']),
                "api_order_id": api_order_id,
                "items_count": len(purchases)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Transaction failed, rolling back: {e}")
            raise
    
    async def _create_purchase_records(
        self,
        user_id: str,
        cart_items: List[Dict[str, Any]],
        api_order_id: Optional[str],
        api_version: str
    ) -> List[Dict[str, Any]]:
        """
        Create purchase records for each cart item
        
        Args:
            user_id: User ID
            cart_items: Cart items to create purchases for
            api_order_id: External API order ID
            api_version: API version
            
        Returns:
            List of created purchase documents
        """
        purchases = []
        created_at = now_utc()
        failed_items = 0
        
        for item in cart_items:
            try:
                # Extract item details
                product_id = item.get('product_id') or item.get('_id')
                if not product_id:
                    self.logger.warning(f"Skipping item without product_id: {item}")
                    failed_items += 1
                    continue
                
                # Get quantity (default to 1 if not specified)
                quantity = int(item.get('quantity', 1))
                if quantity <= 0:
                    self.logger.warning(f"Skipping item with invalid quantity: {quantity}")
                    failed_items += 1
                    continue
                
                # Calculate final price with discount and quantity
                base_price = float(item.get('price', 0))
                discount = float(item.get('discount', 0))
                final_price = base_price * (1 - discount / 100) * quantity
                
                if final_price <= 0:
                    self.logger.warning(f"Skipping item with invalid price: {item}")
                    failed_items += 1
                    continue
                
                # Determine product type
                product_table = item.get('product_table_name', 'Cards')
                product_type = PurchaseProductType.DUMP if 'dump' in product_table.lower() else PurchaseProductType.CARD
                
                # Generate SKU
                sku = f"{product_type.value}_{product_id}_{int(created_at.timestamp())}"
                
                # Create purchase document
                purchase = Purchase(
                    user_id=user_id,
                    sku=sku,
                    price=final_price,
                    currency="USD",
                    status=PurchaseStatus.SUCCESS,
                    api_version=api_version,
                    product_type=product_type,
                    external_order_id=api_order_id,
                    external_product_id=str(product_id),
                    metadata={
                        "brand": item.get('brand'),
                        "bin": item.get('bin'),
                        "country": item.get('country'),
                        "state": item.get('state'),
                        "city": item.get('city'),
                        "exp": item.get('exp'),
                        "level": item.get('level'),
                        "bank": item.get('bank'),
                        "base": item.get('base'),
                        "discount": discount,
                        "original_price": base_price,
                        "quantity": quantity,
                        "unit_price": base_price * (1 - discount / 100)
                    },
                    order_items=[item],
                    created_at=created_at,
                    updated_at=created_at
                )
                
                # Insert purchase
                result = await self.purchases_collection.insert_one(purchase.to_mongo())
                purchase_doc = purchase.to_mongo()
                purchase_doc['_id'] = result.inserted_id
                
                purchases.append(purchase_doc)
                self.logger.debug(f"✅ Created purchase for product {product_id}")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to create purchase for item {item.get('product_id')}: {e}")
                # Continue with other items but log the error
                failed_items += 1
                continue
        
        # Check if we created any purchases
        if not purchases:
            self.logger.error(f"❌ Failed to create any purchase records out of {len(cart_items)} items")
            raise CheckoutProcessorError(
                f"Failed to create any purchase records (total items: {len(cart_items)}, failed: {failed_items})",
                error_code="NO_PURCHASES_CREATED"
            )
        
        # Log summary
        if failed_items > 0:
            self.logger.warning(f"⚠️ Created {len(purchases)} purchase records, {failed_items} items failed")
        else:
            self.logger.info(f"✅ Created {len(purchases)} purchase records successfully")
        
        return purchases
    
    async def _get_user_wallet(self, user_id: str) -> Dict[str, Any]:
        """Get user's wallet"""
        wallet_doc = await self.wallets_collection.find_one({"user_id": user_id})
        
        if not wallet_doc:
            raise CheckoutProcessorError(
                "Wallet not found",
                error_code="WALLET_NOT_FOUND"
            )
        
        return wallet_doc
    
    async def _deduct_wallet_balance(
        self,
        user_id: str,
        amount: float,
        old_balance: float,
        transaction_ref: str
    ) -> float:
        """
        Deduct amount from user's wallet
        
        Args:
            user_id: User ID
            amount: Amount to deduct
            old_balance: Current balance for verification
            transaction_ref: Transaction reference
            
        Returns:
            New balance after deduction
        """
        new_balance = round(old_balance - amount, 2)
        
        if new_balance < 0:
            raise CheckoutProcessorError(
                "Insufficient balance after calculation",
                error_code="INSUFFICIENT_BALANCE"
            )
        
        # Update wallet with optimistic locking
        result = await self.wallets_collection.update_one(
            {
                "user_id": user_id,
                "balance": {"$gte": amount}  # Ensure sufficient funds
            },
            {
                "$inc": {"balance": -amount},
                "$set": {"updated_at": now_utc()}
            }
        )
        
        if result.matched_count == 0:
            raise CheckoutProcessorError(
                "Failed to deduct balance (insufficient funds or concurrent modification)",
                error_code="BALANCE_DEDUCTION_FAILED"
            )
        
        self.logger.info(f"💳 Deducted ${amount:.2f} from wallet, new balance: ${new_balance:.2f}")
        return new_balance
    
    async def _log_wallet_transaction(
        self,
        user_id: str,
        amount: float,
        old_balance: float,
        new_balance: float,
        transaction_type: str,
        reference: Optional[str],
        purchase_ids: List[str]
    ) -> Dict[str, Any]:
        """
        Log wallet transaction for audit trail
        
        Args:
            user_id: User ID
            amount: Transaction amount
            old_balance: Balance before transaction
            new_balance: Balance after transaction
            transaction_type: Type of transaction
            reference: External reference (order ID, etc.)
            purchase_ids: List of related purchase IDs
            
        Returns:
            Transaction log document
        """
        transaction_log = {
            "user_id": user_id,
            "amount": amount,
            "old_balance": old_balance,
            "new_balance": new_balance,
            "transaction_type": transaction_type,
            "reference": reference,
            "purchase_ids": purchase_ids,
            "status": "completed",
            "created_at": now_utc(),
            "metadata": {
                "items_count": len(purchase_ids),
                "api_version": "v1"
            }
        }
        
        result = await self.transactions_collection.insert_one(transaction_log)
        transaction_log['_id'] = result.inserted_id
        
        self.logger.debug(f"📝 Logged transaction: {transaction_log['_id']}")
        return transaction_log
    
    async def rollback_checkout(
        self,
        purchase_ids: List[str],
        user_id: str,
        refund_amount: float
    ) -> bool:
        """
        Rollback a failed checkout
        
        Args:
            purchase_ids: IDs of purchases to cancel
            user_id: User ID
            refund_amount: Amount to refund
            
        Returns:
            True if rollback successful
        """
        try:
            self.logger.warning(f"⚠️ Rolling back checkout for user {user_id}")
            
            # Mark purchases as failed
            if purchase_ids:
                await self.purchases_collection.update_many(
                    {"_id": {"$in": [ObjectId(pid) for pid in purchase_ids]}},
                    {
                        "$set": {
                            "status": PurchaseStatus.FAILED.value,
                            "updated_at": now_utc()
                        }
                    }
                )
            
            # Refund to wallet
            if refund_amount > 0:
                await self.wallets_collection.update_one(
                    {"user_id": user_id},
                    {
                        "$inc": {"balance": refund_amount},
                        "$set": {"updated_at": now_utc()}
                    }
                )
                
                # Log refund transaction
                await self._log_wallet_transaction(
                    user_id=user_id,
                    amount=refund_amount,
                    old_balance=0,  # Not critical for rollback
                    new_balance=0,  # Not critical for rollback
                    transaction_type="refund",
                    reference=f"rollback_{int(now_utc().timestamp())}",
                    purchase_ids=purchase_ids
                )
            
            self.logger.info(f"✅ Rollback completed for user {user_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Rollback failed: {e}")
            return False


# Singleton instance
_checkout_processor_instance = None


def get_checkout_processor() -> CheckoutProcessorService:
    """Get singleton instance of the checkout processor"""
    global _checkout_processor_instance
    if _checkout_processor_instance is None:
        _checkout_processor_instance = CheckoutProcessorService()
    return _checkout_processor_instance

