"""Configuration factory for API v2 (BASE 2)."""

from __future__ import annotations

from typing import Optional, Dict

from models.api import APIEnvironment

from shared_api.config.api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from shared_api.core.constants import HTTPMethod, AuthenticationType


DEFAULT_BASE_URL = "https://ronaldo-club.to/api/cards/vhq/"


def _derive_origin(base_url: str) -> str:
    """Derive site origin from API base URL."""
    if "/api" in base_url:
        return base_url.split("/api", 1)[0]
    return base_url.rstrip("/")


def create_api_v2_configuration(
    *,
    base_url: str = DEFAULT_BASE_URL,
    login_token: Optional[str] = None,
    session_cookies: Optional[Dict[str, str]] = None,
    environment: str | APIEnvironment = "production",
    default_headers: Optional[Dict[str, str]] = None,
    inherit_auth_from_api1: bool = True,
    api1_config: Optional[Dict[str, Any]] = None,
) -> APIConfiguration:
    """Create the shared API configuration for API v2/BASE 2.

    Args:
        base_url: Base URL for API v2
        login_token: Bearer token for authentication
        session_cookies: Session cookies for authentication
        environment: API environment
        default_headers: Additional headers
        inherit_auth_from_api1: Whether to inherit authentication from API v1
        api1_config: API v1 configuration to inherit from
    """
    normalized_base = base_url.rstrip("/")
    origin = _derive_origin(normalized_base)
    env_value = environment.value if isinstance(environment, APIEnvironment) else str(environment)

    # Handle authentication inheritance
    inherited_auth = None
    if inherit_auth_from_api1 and api1_config:
        inherited_auth = _inherit_authentication_from_api1(api1_config)
        if inherited_auth and not login_token:
            login_token = inherited_auth.get("login_token")

    endpoints = {
        "list_items": EndpointConfiguration(
            name="list_items",
            path="/list",
            method=HTTPMethod.POST,
            description="List BIN cards with filtering, search, and pagination (BASE 2)",
        ),
        "filters": EndpointConfiguration(
            name="filters",
            path="/filters",
            method=HTTPMethod.GET,
            description="Retrieve filter metadata for browse UI (BASE 2)",
        ),
        "orders": EndpointConfiguration(
            name="orders",
            path="/orders",
            method=HTTPMethod.GET,
            description="Fetch paginated purchase history scoped to BASE 2",
        ),
        "check_order": EndpointConfiguration(
            name="check_order",
            path="/check",
            method=HTTPMethod.POST,
            description="Verify order details within BASE 2",
        ),
    }

    # Use shared authentication configuration
    from services.shared_auth import get_shared_auth_config

    shared_auth = get_shared_auth_config()
    if shared_auth:
        # Use shared authentication - this ensures API v1 and v2 use identical credentials
        login_token = shared_auth.login_token
        session_cookies = shared_auth.session_cookies
        auth = AuthenticationConfiguration(
            type=AuthenticationType.NONE,  # Use session cookies, not bearer tokens
            custom_headers={}
        )
    elif inherit_auth_from_api1 and api1_config:
        # Fallback to legacy inheritance for backward compatibility
        inherited_auth = _inherit_authentication_from_api1(api1_config)
        if inherited_auth and inherited_auth.get("auth_type") == "session_cookies":
            auth = AuthenticationConfiguration(
                type=AuthenticationType.NONE,
                custom_headers=inherited_auth.get("headers", {})
            )
            if not login_token:
                login_token = inherited_auth.get("login_token")
        else:
            auth = AuthenticationConfiguration(type=AuthenticationType.NONE)
    elif login_token:
        # Default to session cookies if we have a login token
        auth = AuthenticationConfiguration(
            type=AuthenticationType.NONE,  # Use cookies, not bearer token
        )
    else:
        # Log warning when no authentication is configured
        from utils.central_logger import get_logger
        logger = get_logger()
        logger.warning("API v2 configuration created without authentication - this may cause 403 errors")
        auth = AuthenticationConfiguration(
            type=AuthenticationType.NONE,
        )

    # Use shared authentication headers as base, then add API v2 specific headers
    from services.shared_auth import build_default_headers_with_auth

    headers = build_default_headers_with_auth()

    # Add API v2 specific headers
    headers.update({
        "origin": origin,
        "referer": f"{origin}/store/cards/vhq",
    })

    # Override with any provided default headers
    if default_headers:
        headers.update(default_headers)

    # Add session cookies from shared auth (this ensures consistency with API v1)
    if shared_auth and shared_auth.session_cookies:
        cookie_header = "; ".join(f"{k}={v}" for k, v in shared_auth.session_cookies.items() if v)
        headers["cookie"] = cookie_header
    elif session_cookies:
        # Fallback for backward compatibility
        cookie_header = "; ".join(f"{k}={v}" for k, v in session_cookies.items() if v)
        headers["cookie"] = cookie_header

    timeout = TimeoutConfiguration(connect=10, read=30, total=60)
    retry = RetryConfiguration(
        max_attempts=3,
        delay=1.0,
        backoff_factor=1.0,
        retry_on_status=[500, 502, 503, 504, 429],
    )

    return APIConfiguration(
        name="api2",
        base_url=normalized_base,
        endpoints=endpoints,
        authentication=auth,
        default_headers=headers,
        timeout=timeout,
        retry=retry,
        description="API v2/BASE 2 - Secondary BIN browse API (VHQ endpoint)",
        version="2.0",
        environment=env_value,
    )


def _inherit_authentication_from_api1(api1_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Extract authentication configuration from API v1 for inheritance.

    Args:
        api1_config: API v1 configuration dictionary

    Returns:
        Dictionary with inherited authentication data or None
    """
    try:
        inherited = {}

        # Extract authentication from various possible locations
        auth_config = api1_config.get("authentication", {})
        credentials = api1_config.get("credentials", {})
        shared_config = api1_config.get("shared_config", {})

        # Get authentication type and credentials
        auth_type = auth_config.get("type", "none")

        if auth_type == "bearer_token":
            token = (
                auth_config.get("bearer_token") or
                credentials.get("login_token") or
                shared_config.get("authentication", {}).get("bearer_token")
            )
            if token:
                inherited["login_token"] = token
                inherited["auth_type"] = "bearer_token"
        elif auth_type == "session_cookies":
            # Handle session cookie authentication (like API v1)
            token = (
                auth_config.get("login_token") or
                credentials.get("login_token") or
                shared_config.get("authentication", {}).get("login_token")
            )
            if token:
                inherited["login_token"] = token
                inherited["auth_type"] = "session_cookies"

        # Extract session cookies from various sources
        session_cookies = {}

        # From credentials
        cred_cookies = credentials.get("session_cookies", {})
        if cred_cookies:
            session_cookies.update(cred_cookies)

        # From shared config
        shared_cookies = shared_config.get("session_cookies", {})
        if shared_cookies:
            session_cookies.update(shared_cookies)

        if session_cookies:
            inherited["session_cookies"] = session_cookies

        elif auth_type == "api_key":
            api_key = auth_config.get("api_key")
            api_key_header = auth_config.get("api_key_header", "X-API-Key")
            if api_key:
                inherited["login_token"] = api_key  # Use as login token for compatibility
                inherited["auth_type"] = "api_key"
                inherited["api_key"] = api_key
                inherited["api_key_header"] = api_key_header

        elif auth_type == "basic_auth":
            username = auth_config.get("username")
            password = auth_config.get("password")
            if username and password:
                inherited["auth_type"] = "basic_auth"
                inherited["username"] = username
                inherited["password"] = password

        # Extract headers
        headers = {}

        # From credentials
        if credentials.get("headers"):
            headers.update(credentials["headers"])

        # From auth config custom headers
        if auth_config.get("custom_headers"):
            headers.update(auth_config["custom_headers"])

        # From shared config
        if shared_config.get("default_headers"):
            headers.update(shared_config["default_headers"])

        if headers:
            inherited["headers"] = headers

        # Extract session cookies
        session_cookies = (
            credentials.get("session_cookies") or
            shared_config.get("session_cookies")
        )

        if session_cookies:
            inherited["session_cookies"] = session_cookies

        return inherited if inherited else None

    except Exception as e:
        # Log error but don't fail configuration creation
        from utils.central_logger import get_logger
        logger = get_logger()
        logger.warning(f"Failed to inherit authentication from API v1: {e}")
        return None


async def get_api1_configuration_for_inheritance() -> Optional[Dict[str, Any]]:
    """Get API v1 configuration for authentication inheritance.

    DEPRECATED: Use services.shared_auth.get_api_v2_compatible_config() instead.
    This function is kept for backward compatibility.

    Returns:
        API v1 configuration dictionary or None if not available
    """
    try:
        from services.shared_auth import get_api_v2_compatible_config
        return get_api_v2_compatible_config()
    except Exception as e:
        from utils.central_logger import get_logger
        logger = get_logger()
        logger.warning(f"Failed to get API v1 configuration for inheritance: {e}")
        return None
