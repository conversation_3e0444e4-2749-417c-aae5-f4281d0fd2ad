"""
API Registry

Manages multiple API configurations and provides a centralized way
to access and manage different API clients.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime

from ..core.interfaces import APIClientProtocol
from ..core.exceptions import ConfigurationError
from .api_config import APIConfiguration
from .client_factory import APIClientFactory

from utils.central_logger import get_logger

logger = get_logger()


class APIRegistry:
    """
    Central registry for managing multiple API configurations and clients

    Features:
    - Register and manage multiple API configurations
    - Create and cache client instances
    - Health monitoring for registered APIs
    - Configuration validation and management
    """

    def __init__(self):
        self._configurations: Dict[str, APIConfiguration] = {}
        self._clients: Dict[str, APIClientProtocol] = {}
        self._factory = APIClientFactory()
        self._health_status: Dict[str, Dict[str, Any]] = {}

    def register_api(self, config: APIConfiguration) -> None:
        """Register an API configuration"""
        existing_config = self._configurations.get(config.name)

        if existing_config:
            # Check if the configuration is substantially the same
            if self._configs_are_equivalent(existing_config, config):
                logger.debug(
                    f"Skipping registration of equivalent configuration for API: {config.name}"
                )
                return
            else:
                # Log differences for debugging
                differences = self._get_config_differences(existing_config, config)
                logger.warning(
                    f"Overwriting existing configuration for API: {config.name} (differences: {differences})"
                )

        # Validate configuration
        config.validate()

        self._configurations[config.name] = config
        self._health_status[config.name] = {
            "last_check": None,
            "is_healthy": None,
            "error_message": None,
        }

        logger.info(f"Registered API configuration: {config.name}")

    def register_api_safe(
        self, config: APIConfiguration, allow_overwrite: bool = True
    ) -> bool:
        """
        Register an API configuration with option to skip if equivalent exists

        Args:
            config: The API configuration to register
            allow_overwrite: Whether to allow overwriting existing configurations

        Returns:
            True if configuration was registered, False if skipped
        """
        existing_config = self._configurations.get(config.name)

        if existing_config:
            if self._configs_are_equivalent(existing_config, config):
                logger.debug(
                    f"Skipping registration of equivalent configuration for API: {config.name}"
                )
                return False
            elif not allow_overwrite:
                logger.debug(
                    f"Skipping registration - configuration exists for API: {config.name}"
                )
                return False

        # Use regular registration
        self.register_api(config)
        return True

    def _configs_are_equivalent(
        self, config1: APIConfiguration, config2: APIConfiguration
    ) -> bool:
        """
        Check if two API configurations are substantially equivalent

        This helps reduce noise from redundant registrations during startup.
        """
        try:
            # Check basic properties with safe attribute access
            name1 = getattr(config1, "name", None)
            name2 = getattr(config2, "name", None)
            if name1 != name2:
                return False

            base_url1 = getattr(config1, "base_url", None)
            base_url2 = getattr(config2, "base_url", None)
            if base_url1 != base_url2:
                return False

            version1 = getattr(config1, "version", None)
            version2 = getattr(config2, "version", None)
            if version1 != version2:
                return False

            # Check authentication type (simplified comparison)
            try:
                auth1 = getattr(config1, "authentication", None)
                auth2 = getattr(config2, "authentication", None)
                auth1_type = getattr(auth1, "type", None) if auth1 else None
                auth2_type = getattr(auth2, "type", None) if auth2 else None
                if auth1_type != auth2_type:
                    return False
            except Exception:
                # If auth comparison fails, assume they might be different
                pass

            # Check endpoints count (detailed comparison would be too expensive)
            try:
                endpoints1 = getattr(config1, "endpoints", None) or {}
                endpoints2 = getattr(config2, "endpoints", None) or {}
                if len(endpoints1) != len(endpoints2):
                    return False

                # Check if endpoint names match
                if set(endpoints1.keys()) != set(endpoints2.keys()):
                    return False
            except Exception:
                # If endpoints comparison fails, assume they might be different
                pass

            return True

        except Exception as e:
            logger.debug(
                f"Config comparison failed for {getattr(config1, 'name', 'unknown')}: {e}"
            )
            # If comparison fails, assume they're different
            return False

    def _get_config_differences(
        self, config1: APIConfiguration, config2: APIConfiguration
    ) -> str:
        """
        Get a summary of differences between two configurations for logging
        """
        try:
            differences = []

            # Safe attribute comparison
            base_url1 = getattr(config1, "base_url", None)
            base_url2 = getattr(config2, "base_url", None)
            if base_url1 != base_url2:
                differences.append("base_url")

            version1 = getattr(config1, "version", None)
            version2 = getattr(config2, "version", None)
            if version1 != version2:
                differences.append("version")

            env1 = getattr(config1, "environment", None)
            env2 = getattr(config2, "environment", None)
            if env1 != env2:
                differences.append("environment")

            # Check authentication type safely
            try:
                auth1 = getattr(config1, "authentication", None)
                auth2 = getattr(config2, "authentication", None)
                auth1_type = getattr(auth1, "type", None) if auth1 else None
                auth2_type = getattr(auth2, "type", None) if auth2 else None
                if auth1_type != auth2_type:
                    differences.append("auth_type")
            except Exception:
                differences.append("auth_check_failed")

            # Check endpoints safely
            try:
                endpoints1 = getattr(config1, "endpoints", None) or {}
                endpoints2 = getattr(config2, "endpoints", None) or {}
                if len(endpoints1) != len(endpoints2):
                    differences.append("endpoints_count")
                elif set(endpoints1.keys()) != set(endpoints2.keys()):
                    differences.append("endpoint_names")
            except Exception:
                differences.append("endpoints_check_failed")

            # Check headers count (simplified)
            try:
                headers1 = getattr(config1, "default_headers", None) or {}
                headers2 = getattr(config2, "default_headers", None) or {}
                if len(headers1) != len(headers2):
                    differences.append("headers_count")
            except Exception:
                differences.append("headers_check_failed")

            return ", ".join(differences) if differences else "configs_appear_identical"

        except Exception as e:
            return f"comparison_error:{str(e)[:50]}"

    def unregister_api(self, api_name: str) -> None:
        """Unregister an API configuration"""
        if api_name in self._configurations:
            # Close client if it exists
            if api_name in self._clients:
                client = self._clients[api_name]
                if hasattr(client, "close"):
                    # Note: This is sync, but close() is async
                    # In a real implementation, you'd handle this properly
                    pass
                del self._clients[api_name]

            del self._configurations[api_name]
            if api_name in self._health_status:
                del self._health_status[api_name]

            logger.info(f"Unregistered API configuration: {api_name}")
        else:
            logger.warning(f"Attempted to unregister unknown API: {api_name}")

    def get_client(
        self, api_name: str, client_type: str = "default"
    ) -> APIClientProtocol:
        """Get or create a client for the specified API"""
        if api_name not in self._configurations:
            raise ConfigurationError(f"API '{api_name}' not found in registry")

        # Use cached client if available
        client_key = f"{api_name}:{client_type}"
        if client_key in self._clients:
            return self._clients[client_key]

        # Create new client
        config = self._configurations[api_name]
        client = self._factory.create_client(config, client_type)
        self._clients[client_key] = client

        logger.info(f"Created client for API: {api_name} (type: {client_type})")
        return client

    def get_configuration(self, api_name: str) -> APIConfiguration:
        """Get configuration for the specified API"""
        if api_name not in self._configurations:
            raise ConfigurationError(f"API '{api_name}' not found in registry")
        return self._configurations[api_name]

    def list_apis(self) -> List[Dict[str, Any]]:
        """List all registered APIs with their basic information"""
        apis = []
        for name, config in self._configurations.items():
            health = self._health_status.get(name, {})
            apis.append(
                {
                    "name": name,
                    "base_url": config.base_url,
                    "description": config.description,
                    "version": config.version,
                    "environment": config.environment,
                    "endpoints_count": len(config.endpoints),
                    "is_healthy": health.get("is_healthy"),
                    "last_health_check": health.get("last_check"),
                }
            )
        return apis

    def get_api_info(self, api_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific API"""
        if api_name not in self._configurations:
            raise ConfigurationError(f"API '{api_name}' not found in registry")

        config = self._configurations[api_name]
        health = self._health_status.get(api_name, {})

        return {
            "name": config.name,
            "base_url": config.base_url,
            "description": config.description,
            "version": config.version,
            "environment": config.environment,
            "authentication_type": config.auth_config.type.value,
            "endpoints": {
                name: {
                    "path": ep.path,
                    "method": ep.method.value,
                    "description": ep.description,
                }
                for name, ep in config.endpoints.items()
            },
            "timeout_config": {
                "connect": config.timeout_config.connect,
                "read": config.timeout_config.read,
                "total": config.timeout_config.total,
            },
            "retry_config": {
                "max_attempts": config.retry_config.max_attempts,
                "delay": config.retry_config.delay,
                "backoff_factor": config.retry_config.backoff_factor,
            },
            "health_status": health,
            "created_at": config.created_at.isoformat(),
        }

    async def check_api_health(self, api_name: str) -> bool:
        """Check health of a specific API"""
        try:
            client = self.get_client(api_name)
            is_healthy = await client.health_check()

            self._health_status[api_name] = {
                "last_check": datetime.utcnow().isoformat(),
                "is_healthy": is_healthy,
                "error_message": None,
            }

            logger.info(
                f"Health check for {api_name}: {'healthy' if is_healthy else 'unhealthy'}"
            )
            return is_healthy

        except Exception as e:
            error_msg = str(e)
            self._health_status[api_name] = {
                "last_check": datetime.utcnow().isoformat(),
                "is_healthy": False,
                "error_message": error_msg,
            }

            logger.error(f"Health check failed for {api_name}: {error_msg}")
            return False

    async def check_all_health(self) -> Dict[str, bool]:
        """Check health of all registered APIs"""
        results = {}
        for api_name in self._configurations.keys():
            results[api_name] = await self.check_api_health(api_name)
        return results

    def get_health_status(self, api_name: Optional[str] = None) -> Dict[str, Any]:
        """Get health status for one or all APIs"""
        if api_name:
            if api_name not in self._health_status:
                raise ConfigurationError(f"API '{api_name}' not found in registry")
            return {api_name: self._health_status[api_name]}
        else:
            return self._health_status.copy()

    async def close_all_clients(self) -> None:
        """Close all active clients"""
        for client_key, client in self._clients.items():
            try:
                await client.close()
                logger.info(f"Closed client: {client_key}")
            except Exception as e:
                logger.error(f"Error closing client {client_key}: {e}")

        self._clients.clear()

    def export_configurations(self) -> Dict[str, Dict[str, Any]]:
        """Export all configurations as dictionaries"""
        return {name: config.to_dict() for name, config in self._configurations.items()}

    def import_configurations(self, configs_dict: Dict[str, Dict[str, Any]]) -> None:
        """Import configurations from dictionaries"""
        for name, config_dict in configs_dict.items():
            try:
                config = APIConfiguration.from_dict(config_dict)
                self.register_api(config)
                logger.info(f"Imported configuration: {name}")
            except Exception as e:
                logger.error(f"Failed to import configuration {name}: {e}")


# Global registry instance
api_registry = APIRegistry()


def register_api_v2(
    configuration_kwargs: Optional[Dict[str, Any]] = None,
) -> APIConfiguration:
    """Helper to register API v2/BASE 2 configuration with the global registry."""

    from api_v2.config.api_config import create_api_v2_configuration

    config = create_api_v2_configuration(**(configuration_kwargs or {}))
    api_registry.register_api(config)
    return config
