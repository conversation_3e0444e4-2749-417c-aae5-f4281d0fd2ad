"""
Centralized Authentication Helper

Consolidates duplicate authentication logic from:
- services/card_service.py (lines 140-190)
- services/shared_auth.py (lines 40-80)  
- services/external_api_service.py (lines 680-700)

Single source of truth for token and cookie handling.
"""

import os
from typing import Dict, Optional, Any
from dotenv import load_dotenv
from utils.central_logger import get_logger

logger = get_logger()


class AuthenticationHelper:
    """Centralized authentication helper to eliminate duplicate auth code"""
    
    @staticmethod
    def get_login_token() -> Optional[str]:
        """
        Get login token from environment
        
        Returns:
            Login token or None if not found
        """
        load_dotenv()
        token = os.getenv("EXTERNAL_LOGIN_TOKEN", "")
        
        if not token:
            logger.warning("No EXTERNAL_LOGIN_TOKEN found - authentication may fail")
            return None
            
        return token
    
    @staticmethod
    def get_session_cookies() -> Dict[str, str]:
        """
        Build session cookies from environment variables
        
        Returns:
            Dictionary of session cookies
        """
        load_dotenv()
        
        # Start with login token if available
        session_cookies = {}
        login_token = AuthenticationHelper.get_login_token()
        if login_token:
            session_cookies["loginToken"] = login_token
        
        # Parse EXTERNAL_COOKIES string (preferred format)
        external_cookies = os.getenv("EXTERNAL_COOKIES", "")
        if external_cookies:
            logger.debug("Loading cookies from EXTERNAL_COOKIES environment variable")
            for cookie_pair in external_cookies.split(';'):
                if '=' in cookie_pair:
                    key, value = cookie_pair.strip().split('=', 1)
                    if value:  # Only add non-empty values
                        session_cookies[key] = value
        else:
            # Fallback to individual environment variables (legacy support)
            logger.debug("Loading cookies from individual EXTERNAL_DDG* environment variables")
            legacy_cookies = {
                "__ddg1_": os.getenv("EXTERNAL_DDG1", ""),
                "__ddg8_": os.getenv("EXTERNAL_DDG8", ""),
                "__ddg9_": os.getenv("EXTERNAL_DDG9", ""),
                "__ddg10_": os.getenv("EXTERNAL_DDG10", ""),
                "_ga": os.getenv("EXTERNAL_GA", ""),
                "_ga_KZWCRF57VT": os.getenv("EXTERNAL_GA_KZWCRF57VT", ""),
                "testcookie": "1",
            }
            # Only add non-empty cookies
            session_cookies.update({k: v for k, v in legacy_cookies.items() if v})
        
        return session_cookies
    
    @staticmethod
    def get_auth_headers() -> Dict[str, str]:
        """
        Build authentication headers
        
        Returns:
            Dictionary of authentication headers
        """
        headers = {}
        login_token = AuthenticationHelper.get_login_token()
        
        if login_token:
            headers["Authorization"] = f"Bearer {login_token}"
        
        return headers
    
    @staticmethod
    def get_api_v1_config() -> Optional[Dict[str, Any]]:
        """
        Build API v1 configuration dictionary
        
        Returns:
            API v1 config dictionary or None if no token available
        """
        login_token = AuthenticationHelper.get_login_token()
        if not login_token:
            return None
        
        session_cookies = AuthenticationHelper.get_session_cookies()
        auth_headers = AuthenticationHelper.get_auth_headers()
        
        return {
            "authentication": {
                "type": "bearer_token",
                "bearer_token": login_token,
            },
            "credentials": {
                "login_token": login_token,
                "session_cookies": session_cookies,
                "headers": auth_headers,
            },
            "shared_config": {
                "authentication": {"bearer_token": login_token},
                "default_headers": auth_headers,
                "session_cookies": session_cookies,
            },
        }
    
    @staticmethod
    def is_authentication_available() -> bool:
        """
        Check if authentication credentials are available
        
        Returns:
            True if authentication is available, False otherwise
        """
        return AuthenticationHelper.get_login_token() is not None


# Singleton instance for backward compatibility
_auth_helper_instance = None

def get_auth_helper() -> AuthenticationHelper:
    """Get singleton instance of authentication helper"""
    global _auth_helper_instance
    if _auth_helper_instance is None:
        _auth_helper_instance = AuthenticationHelper()
    return _auth_helper_instance