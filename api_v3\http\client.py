"""
API v3 HTTP Client

Handles HTTP requests for API v3 using session-based authentication.
Adapted from demo/api3_demo operations to work with the bot's architecture.
"""

from __future__ import annotations

import asyncio
from typing import Any, Dict, Optional, List
from urllib.parse import urljoin, urlencode
import json
import time

import requests
from bs4 import BeautifulSoup, SoupStrainer

from ..auth.shared_session import get_shared_session_manager
from ..utils.tor_detector import auto_detect_tor_port
from utils.multi_file_logger import log_api_request, log_api_response
from config.settings import get_settings

# Import unified API logging system
from utils.api_logging import get_api_logger, LogLevel

from utils.central_logger import get_logger
from utils.card_data_extractor import get_card_data_extractor

from utils.central_logger import get_logger
from utils.card_data_extractor import get_card_data_extractor

logger = get_logger()
# Use central logger instead of separate api.requests logger
api_request_logger = logger

# Get unified API logger for v3
api_logger = get_api_logger("api_v3_http_client", LogLevel.DEBUG)

# Global session cache to prevent multiple sessions for same user
_global_session_cache: Dict[str, requests.Session] = {}
_global_session_cache_times: Dict[str, float] = {}
_global_session_cache_ttl = 300  # 5 minutes

# Global response cache to prevent redundant API calls
_global_response_cache: Dict[str, tuple[float, Dict[str, Any]]] = {}
_global_response_cache_ttl = 30  # 30 seconds for API responses


class APIV3HTTPClient:
    """
    HTTP client for API v3 with session-based authentication.

    This client:
    - Uses SharedSessionManager for authentication (singleton pattern)
    - Shares session across all service instances (Browse, Cart, Order)
    - Maintains session cookies across requests
    - Supports SOCKS proxy for .onion domains
    - Parses HTML responses into structured data
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url.rstrip("/")
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy or ".onion" in base_url
        self.logger = get_logger()

        # Auto-detect Tor port if using SOCKS proxy and no specific URL provided
        if self.use_socks_proxy and socks_url == "socks5h://127.0.0.1:9150":
            auto_detected_url = auto_detect_tor_port()
            if auto_detected_url:
                self.socks_url = auto_detected_url
                # Auto-detected Tor SOCKS proxy
            else:
                self.socks_url = socks_url
                self.logger.warning(
                    f"⚠️  Could not auto-detect Tor, using default: {self.socks_url}"
                )
        else:
            self.socks_url = socks_url

        # Use shared session manager (singleton) with advanced configuration
        self.shared_session_manager = get_shared_session_manager()
        self.settings = get_settings()
        # Cache the session manager for reuse

        # Advanced HTTP optimization settings (enhanced for better performance)
        self._connection_pool_size = 20  # Increased pool size for concurrent requests
        self._connection_pool_maxsize = 30  # Max connections per host
        self._connection_timeout = 6  # Faster connection timeout for speed
        self._read_timeout = 20  # Optimized read timeout
        self._max_retries = 2  # Request retries
        self._backoff_factor = 0.1  # Fastest retry backoff
        
        # Response optimization (enhanced)
        self._stream_threshold = 512 * 1024  # 512KB streaming threshold (reduced for faster processing)
        self._compression_enabled = True
        self._keep_alive = True  # Enable keep-alive for connection reuse
        
        # Performance tracking
        self._request_count = 0
        self._total_response_time = 0.0
        self._start_time = time.time()
        self.cache_hits = 0  # Track cache hits for performance metrics

        # Note: Session and response caching is now handled globally, not per-instance

    def _truncate_response_body(self, body: str, max_length: int = 1000) -> str:
        """
        Truncate response body for logging based on settings.
        
        Args:
            body: Full response body
            max_length: Maximum length before truncation
            
        Returns:
            Truncated or full body based on LOG_FULL_API_RESPONSES setting
        """
        if self.settings.LOG_FULL_API_RESPONSES:
            return body
        
        if len(body) <= max_length:
            return body
        
        # For console output, be more aggressive with truncation
        if max_length <= 200:  # Console output
            # Show only first line + summary
            first_line = body.split('\n')[0][:100]
            return f"{first_line}... [HTML truncated, {len(body)} chars total]"
        
        # For API logging, show first and last parts
        half_length = max_length // 2
        return f"{body[:half_length]}...{body[-half_length:]} [truncated, {len(body)} chars total]"

    @property
    def session(self) -> requests.Session:
        """
        Get the shared session (optimized with global caching across all client instances).

        Returns:
            Authenticated requests.Session shared across all services and client instances
        """
        current_time = time.time()
        session_key = f"{self.base_url}:{self.username}"
        
        # Check global session cache first
        if (session_key in _global_session_cache and 
            session_key in _global_session_cache_times and
            (current_time - _global_session_cache_times[session_key]) < _global_session_cache_ttl):
            # Refresh cache time and return existing session
            _global_session_cache_times[session_key] = current_time
            return _global_session_cache[session_key]
            
        # Create new session if not in cache or expired
        session = self.shared_session_manager.get_or_create_session(
            base_url=self.base_url,
            username=self.username,
            password=self.password,
            use_socks_proxy=self.use_socks_proxy,
            socks_url=self.socks_url,
        )
        
        # Cache the session globally
        _global_session_cache[session_key] = session
        _global_session_cache_times[session_key] = current_time
        
        self.logger.info(f"🆕 SESSION CREATED: New session for {self.username}")
        return session

    def _format_applied_filters(self, params: Optional[Dict[str, Any]]) -> str:
        """
        Format applied filters into a human-readable string for logging.
        
        Args:
            params: Dictionary of request parameters
            
        Returns:
            Human-readable string describing applied filters
        """
        if not params:
            return "No filters applied"
            
        filter_descriptions = []
        
        # Map API v3 parameter names to readable descriptions
        filter_mapping = {
            "country[]": "Country",
            "scheme[]": "Brand",
            "type[]": "Card Type", 
            "level[]": "Level",
            "base_id[]": "Base ID",
            "continent[]": "Continent",
            "with_billing": "With Address",
            "with_phone": "With Phone",
            "with_dob": "With DOB",
            "searched_bank": "Bank Search",
            "selected_bank": "Selected Bank",
            "bins": "BIN Numbers",
            "postal_code": "Postal Code",
            "region": "Region/State",
            "city": "City",
            "ethnicity": "Ethnicity",
            "price_from": "Min Price",
            "price_to": "Max Price",
            "expiry_month": "Expiry Month",
            "expiry_year": "Expiry Year",
            "quality": "Quality",
            "show_medium_valid": "Medium Valid",
            "expiring_soon": "Expiring Soon",
            "expiring_next": "Expiring Next Month",
            "cc_per_bin": "Cards per BIN"
        }
        
        for param_key, param_value in params.items():
            if param_value:  # Only include non-empty values
                readable_name = filter_mapping.get(param_key, param_key)
                
                # Handle special cases
                if param_key in ["show_medium_valid", "expiring_soon", "expiring_next", "cc_per_bin"]:
                    if param_value == "on":
                        filter_descriptions.append(f"{readable_name}: Yes")
                else:
                    filter_descriptions.append(f"{readable_name}: {param_value}")
        
        if not filter_descriptions:
            return "No active filters"
            
        return "; ".join(filter_descriptions)

    def _build_url(self, endpoint: str) -> str:
        """Build complete URL for endpoint (optimized)"""
        if endpoint.startswith("/"):
            endpoint = endpoint[1:]
        return f"{self.base_url}/{endpoint}"
    
    def _fast_process_params(self, params: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Fast parameter processing - remove empty values and optimize for API calls
        
        Args:
            params: Request parameters
            
        Returns:
            Optimized parameters dictionary
        """
        if not params:
            return {}
        
        # Fast filtering - only include non-empty values
        return {k: v for k, v in params.items() if v not in (None, "", [])}
    
    def _is_cacheable_endpoint(self, endpoint: str) -> bool:
        """
        Determine if endpoint response can be cached
        
        Args:
            endpoint: API endpoint
            
        Returns:
            True if endpoint is cacheable
        """
        # Cache GET requests for orders, shop, filters, categories
        # Don't cache POST/PUT requests (unmask, download operations) 
        cacheable_endpoints = {"shop", "filters", "categories", "orders/"}
        return any(cache_ep in endpoint for cache_ep in cacheable_endpoints)
    
    def _get_cache_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """
        Generate cache key for request
        
        Args:
            endpoint: API endpoint
            params: Request parameters
            
        Returns:
            Cache key string
        """
        # Create deterministic cache key
        param_str = "&".join(f"{k}={v}" for k, v in sorted(params.items()) if v)
        return f"{endpoint}:{hash(param_str)}"
    
    def _get_cached_response(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Get cached response if available and valid (using global cache)
        
        Args:
            cache_key: Cache key
            
        Returns:
            Cached response or None
        """
        if cache_key in _global_response_cache:
            timestamp, cached_data = _global_response_cache[cache_key]
            if time.time() - timestamp < _global_response_cache_ttl:
                self.cache_hits += 1
                return cached_data
            else:
                # Remove expired cache entry
                del _global_response_cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response_data: Dict[str, Any]) -> None:
        """
        Cache response data (using global cache)
        
        Args:
            cache_key: Cache key
            response_data: Response to cache
        """
        _global_response_cache[cache_key] = (time.time(), response_data)
        
        # Simple cache cleanup - remove old entries if cache gets too large (global level)
        if len(_global_response_cache) > 100:  # Keep cache reasonable size
            # Remove oldest entries
            sorted_items = sorted(_global_response_cache.items(), key=lambda x: x[1][0])
            for key, _ in sorted_items[:20]:  # Remove 20 oldest entries
                del _global_response_cache[key]
        if len(self._response_cache) > 100:
            # Remove oldest entries
            sorted_items = sorted(self._response_cache.items(), key=lambda x: x[1][0])
            for key, _ in sorted_items[:20]:  # Remove 20 oldest
                del self._response_cache[key]
    
    async def warmup_connection(self) -> bool:
        """
        Warmup connection pool for better performance.
        
        Returns:
            True if warmup successful, False otherwise
        """
        try:
            session = self.session
            warmup_url = self._build_url("shop")
            
            # Make a lightweight HEAD request to establish connection
            response = session.head(warmup_url, timeout=5)
            self.logger.info(f"🔥 CONNECTION WARMUP: Connection pool warmed up ({response.status_code})")
            return response.status_code < 400
        except Exception as e:
            self.logger.warning(f"⚠️ CONNECTION WARMUP: Failed to warmup connection: {e}")
            return False
    
    def optimize_session_for_batch(self) -> None:
        """
        Optimize session settings for batch operations.
        Increases connection pool and reduces timeouts for faster processing.
        """
        try:
            session = self.session
            
            # Apply batch optimization settings
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=self._connection_pool_size,
                pool_maxsize=self._connection_pool_maxsize,
                max_retries=self._max_retries
            )
            
            session.mount('http://', adapter)
            session.mount('https://', adapter)
            
            # Enable keep-alive and compression
            session.headers.update({
                'Connection': 'keep-alive',
                'Accept-Encoding': 'gzip, deflate' if self._compression_enabled else 'identity'
            })
            
            self.logger.info(f"⚡ BATCH OPTIMIZATION: Session optimized for batch operations (pool: {self._connection_pool_size})")
            
        except Exception as e:
            self.logger.warning(f"⚠️ BATCH OPTIMIZATION: Failed to optimize session: {e}")
    
    async def batch_get(
        self, 
        requests: List[tuple[str, Optional[Dict[str, Any]]]]
    ) -> List[Dict[str, Any]]:
        """
        Execute multiple GET requests in an optimized batch.
        
        Args:
            requests: List of (endpoint, params) tuples
            
        Returns:
            List of response dictionaries
        """
        if not requests:
            return []
        
        # Optimize session for batch processing
        self.optimize_session_for_batch()
        
        results = []
        start_time = time.time()
        
        logger.info(f"⚡ BATCH REQUEST: Processing {len(requests)} requests")
        
        for endpoint, params in requests:
            try:
                # Use appropriate timeout based on domain type (shorter for batch operations)
                timeout = 20 if ".onion" in self.base_url else 10
                result = await self.get(endpoint, params, timeout=timeout)
                results.append(result)
            except Exception as e:
                logger.warning(f"Batch request failed for {endpoint}: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "endpoint": endpoint,
                    "params": params
                })
        
        batch_time = (time.time() - start_time) * 1000
        logger.info(f"⚡ BATCH COMPLETE: {len(results)} responses in {batch_time:.1f}ms")
        
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get HTTP client performance statistics"""
        uptime = time.time() - self._start_time
        avg_response_time = (self._total_response_time / max(self._request_count, 1)) * 1000  # ms
        
        return {
            "total_requests": self._request_count,
            "average_response_time_ms": avg_response_time,
            "requests_per_second": self._request_count / max(uptime, 1),
            "total_response_time_s": self._total_response_time,
            "uptime_seconds": uptime,
            "connection_pool_size": self._connection_pool_size,
            "optimization_level": "advanced",
        }

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        timeout: int = 60,
        allow_redirects: bool = True,
    ) -> Dict[str, Any]:
        """
        Make optimized GET request with unified API logging.

        Args:
            endpoint: API endpoint
            params: Query parameters
            timeout: Request timeout in seconds
            allow_redirects: Whether to follow redirects automatically

        Returns:
            Response dictionary with parsed data
        """
        try:
            # Fast path optimizations
            start_time = time.time()
            session = self.session  # Cached session access
            url = self._build_url(endpoint)  # Optimized URL building
            
            # Fast parameter processing
            processed_params = self._fast_process_params(params)
            
            # Check cache for cacheable endpoints
            cache_key = None
            if self._is_cacheable_endpoint(endpoint):
                cache_key = self._get_cache_key(endpoint, processed_params)
                cached_response = self._get_cached_response(cache_key)
                if cached_response:
                    logger.info(f"🚀 CACHE HIT: {endpoint} ({len(processed_params)} params)")
                    return cached_response
            
            # Minimal logging for performance
            context = api_logger.create_context(
                user_id=self.username,
                operation=f"api_v3_{endpoint}"
            )
            
            # Only log in debug mode to reduce overhead
            if self.settings.LOG_LEVEL == "DEBUG":
                api_logger.log_request(
                    context=context,
                    method="GET",
                    url=url,
                    headers=dict(session.headers),
                    query_params=processed_params,
                    timeout=float(timeout)
                )
            
            # Performance tracking (minimal overhead)
            self._request_count += 1
            
            # Optimized logging - only compute expensive operations when needed
            if self.settings.LOG_LEVEL == "DEBUG":
                try:
                    # Only compute expensive logging data in debug mode
                    applied_filters = self._format_applied_filters(params) if params else "None"
                    query_string = "&".join([f"{k}={v}" for k, v in (params or {}).items()]) if params else ""
                    full_url_with_params = f"{url}?{query_string}" if query_string else url
                    
                    log_api_request(
                        method="GET",
                        url=url,
                        headers=dict(session.headers),
                        params=params,
                        data=params,
                        duration=None,
                        applied_filters=applied_filters,
                        query_string=query_string,
                        full_url=full_url_with_params
                    )
                    
                    # Simplified parameter logging
                    if params:
                        logger.debug(f"📦 REQUEST PARAMS: {len(params)} parameters")
                except Exception as e:
                    logger.warning(f"Failed to log API request: {e}")

            # Optimized request execution
            response = session.get(
                url, 
                params=processed_params, 
                timeout=timeout, 
                allow_redirects=allow_redirects,
                stream=False  # Disable streaming for faster processing of small responses
            )
            
            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000
            response_time_seconds = response_time_ms / 1000
            
            # Log response using unified system
            api_logger.log_response(
                context=context,
                status_code=response.status_code,
                status_message=response.reason or "OK",
                headers=dict(response.headers),
                body=self._truncate_response_body(response.text, 2000),  # Truncate for API logging

            )
            
            # Optimized response logging - minimize overhead
            logger.info(f"📥 {response.status_code} ({response_time_ms:.1f}ms)")
            
            # Only detailed logging in debug mode
            if self.settings.LOG_LEVEL == "DEBUG":
                try:
                    applied_filters = self._format_applied_filters(params) if params else "None"
                    
                    log_api_response(
                        status_code=response.status_code,
                        url=url,
                        response_size=len(response.text) if response.text else 0,
                        duration=response_time_seconds,
                        success=response.status_code < 400,
                        headers=dict(response.headers),
                        response_body=self._truncate_response_body(response.text, 1000) if response.text else None,
                        applied_filters=applied_filters,
                        request_params=params
                    )
                    
                    # Show processing type
                    content_type = response.headers.get('content-type', '').lower()
                    if 'text/html' in content_type:
                        logger.debug(f"🔄 Processing HTML ({len(response.text)} chars)")
                    elif 'application/json' in content_type:
                        logger.debug(f"📋 Processing JSON")
                except Exception as e:
                    logger.warning(f"Failed to log response details: {e}")

            # If redirects not auto-followed, follow manually
            if not allow_redirects:
                response = self._follow_redirect_if_any(session, response, timeout)

            response.raise_for_status()

            # Automatically convert response to JSON based on content type
            response_data = self._process_response_content(response)
            
            # Show final response structure in terminal with card extraction
            self._display_final_response(response_data, endpoint)

            return {
                "success": True,
                "status_code": response.status_code,
                "data": response_data,
                "url": str(response.url),
                "content_type": "application/json",  # Always JSON output
                "original_format": response.headers.get('content-type', '').lower()
            }

        except Exception as e:
            # Create context for error logging if it doesn't exist
            try:
                error_context = context
            except NameError:
                error_context = api_logger.create_context(
                    user_id=self.username,
                    operation=f"api_v3_{endpoint}"
                )
            
            # Log error using unified system
            api_logger.log_response(
                context=error_context,
                status_code=500,
                status_message="Request Failed",
                headers={},
                body=str(e),
                error_type="http_error",
                error_message=str(e)
            )
            
            # Also print to console for immediate debugging
            logger.error(f"❌ API v3 ERROR: GET {url} - {str(e)}")

            # SOCKS RECOVERY: Attempt recovery for SOCKS-related errors
            if "SOCKS" in str(e) and self.use_socks_proxy:
                self.logger.warning(
                    f"🔄 SOCKS error detected, attempting recovery: {e}"
                )
                try:
                    from ..utils.socks_recovery import handle_socks_error

                    if handle_socks_error(session, self.base_url, self.socks_url):
                        # Retry the request once after recovery
                        response = session.get(
                            url, params=params, timeout=timeout, allow_redirects=allow_redirects
                        )
                        if not allow_redirects:
                            response = self._follow_redirect_if_any(
                                session, response, timeout
                            )
                        response.raise_for_status()
                        response_data = self._process_response_content(response)
                        return {
                            "success": True,
                            "status_code": response.status_code,
                            "data": response_data,
                            "url": str(response.url),
                            "content_type": "application/json",
                            "original_format": response.headers.get('content-type', '').lower()
                        }
                except Exception as recovery_error:
                    self.logger.error(f"❌ SOCKS recovery failed: {recovery_error}")

            # Provide specific error context
            error_context = ""
            if "timeout" in str(e).lower():
                error_context = " (Request timed out - check network connection)"
            elif "connection" in str(e).lower():
                error_context = " (Connection failed - check URL and proxy settings)"
            elif "ssl" in str(e).lower() or "certificate" in str(e).lower():
                error_context = " (SSL/Certificate error - check domain and certificates)"
            elif "proxy" in str(e).lower() or "socks" in str(e).lower():
                error_context = " (Proxy error - check SOCKS proxy configuration)"
            
            self.logger.error(f"GET request failed: {e}{error_context}")
            return {
                "success": False,
                "error": str(e),
                "data": None,
            }

    async def post(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        timeout: int = 60,
        allow_redirects: bool = False,
    ) -> Dict[str, Any]:
        """
        Make optimized POST request with unified API logging.

        Args:
            endpoint: API endpoint
            data: Form data to send
            json_data: JSON data to send
            timeout: Request timeout in seconds
            allow_redirects: Whether to follow redirects automatically

        Returns:
            Response dictionary with parsed data
        """
        try:
            # Get shared session (cached across all services)
            session = self.session
            url = self._build_url(endpoint)

            # Create unified logging context
            context = api_logger.create_context(
                user_id=self.username,
                operation=f"api_v3_{endpoint}"
            )

            # Prepare request body
            request_body = json_data if json_data else data

            # Log request using unified system
            start_time = time.time()
            api_logger.log_request(
                context=context,
                method="POST",
                url=url,
                headers=dict(session.headers),
                body=request_body,
                timeout=float(timeout)
            )
            
            # Log API request to multi-file logger with full details
            try:
                # Create readable applied filters summary for POST data
                applied_filters = self._format_applied_filters(request_body) if request_body else "None"
                
                log_api_request(
                    method="POST",
                    url=url,
                    headers=dict(session.headers),  # Include all headers
                    data=request_body,
                    duration=None,  # Will be updated after response
                    payload=json.dumps(request_body) if request_body else None,  # Full payload as string
                    applied_filters=applied_filters,  # Human-readable filters
                    full_url=url  # POST requests don't have query params
                )
            except Exception as e:
                logger.warning(f"Failed to log API request to multi-file logger: {e}")
            
            # API request logging removed for cleaner output
            if request_body:
                logger.info(f"📦 REQUEST BODY: {json.dumps(request_body, indent=2) if json_data else str(data)}")

            # Make request with appropriate content type
            if json_data:
                response = session.post(
                    url,
                    json=json_data,
                    timeout=timeout,
                    allow_redirects=allow_redirects,
                )
            else:
                response = session.post(
                    url,
                    data=data,
                    timeout=timeout,
                    allow_redirects=allow_redirects,
                )
            
            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000
            response_time_seconds = response_time_ms / 1000
            
            # Log response using unified system
            api_logger.log_response(
                context=context,
                status_code=response.status_code,
                status_message=response.reason or "OK",
                headers=dict(response.headers),
                body=self._truncate_response_body(response.text, 2000),  # Truncate for API logging

            )
            
            # Log API response to multi-file logger with full details
            try:
                # Create readable applied filters summary for POST data
                applied_filters = self._format_applied_filters(request_body) if request_body else "None"
                
                log_api_response(
                    status_code=response.status_code,
                    url=url,
                    response_size=len(response.text) if response.text else 0,
                    duration=response_time_seconds,
                    success=response.status_code < 400,
                    headers=dict(response.headers),  # Include all response headers
                    response_body=self._truncate_response_body(response.text, 5000) if response.text else None,  # Truncate for logging
                    applied_filters=applied_filters,  # Human-readable filters
                    request_params=request_body  # Original request data
                )
            except Exception as e:
                logger.warning(f"Failed to log API response to multi-file logger: {e}")
            
            # Also log to console for immediate debugging
            logger.info(f"📥 API v3 RESPONSE: {response.status_code} {response.reason or 'OK'} ({response_time_ms:.1f}ms)")
            
            # Check if response is HTML and will be converted to JSON
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' in content_type:
                logger.info(f"🔄 Converting HTML to JSON... ({len(response.text)} chars)")
            else:
                console_body = self._truncate_response_body(response.text, 150)  # Very short for console
                logger.info(f"📋 RESPONSE BODY: {console_body}")

            # Handle redirects and return result
            if not allow_redirects:
                response = self._follow_redirect_if_any(session, response, timeout)

            response.raise_for_status()

            # Automatically convert response to JSON based on content type
            response_data = self._process_response_content(response)
            
            # Show final response structure in terminal with card extraction
            self._display_final_response(response_data, endpoint)

            return {
                "success": True,
                "status_code": response.status_code,
                "data": response_data,
                "url": str(response.url),
                "content_type": "application/json",  # Always JSON output
                "original_format": response.headers.get('content-type', '').lower(),
                "raw_text": response.text  # Include raw text for download operations
            }

        except Exception as e:
            # Create context for error logging if it doesn't exist
            try:
                error_context = context
            except NameError:
                error_context = api_logger.create_context(
                    user_id=self.username,
                    operation="api_v3_post"
                )
            
            # Log error using unified system
            api_logger.log_response(
                context=error_context,
                status_code=500,
                status_message="Request Failed",
                headers={},
                body=str(e),
                error_type="http_error",
                error_message=str(e)
            )
            
            # Also print to console for immediate debugging
            logger.error(f"❌ API v3 ERROR: POST - {str(e)}")
            
            self.logger.error(f"POST request failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None,
            }

    def _follow_redirect_if_any(
        self, session: requests.Session, response: requests.Response, timeout: int = 60
    ) -> requests.Response:
        """
        Follow redirect if response has one (like demo/api3_demo/add_to_cart.py).
        This is critical for cart operations to work properly.
        """
        try:
            status = response.status_code
            location = response.headers.get("Location")

            if location and 300 <= status < 400:
                # Build absolute URL from relative location
                from urllib.parse import urljoin

                target = urljoin(str(response.url), location)

                self.logger.info(f"Following redirect to: {target}")

                # Follow the redirect
                redirect_response = session.get(
                    target,
                    allow_redirects=True,
                    timeout=timeout,
                    headers={
                        "Referer": str(response.url)
                    },  # Important for some redirects
                )

                return redirect_response

            return response

        except Exception as e:
            self.logger.warning(f"Error following redirect: {e}")
            return response

    def _parse_table_data_fast(self, html_content: str) -> Dict[str, Any]:
        """
        Fast parsing of HTML content into structured format.
        Handles both table data (for browsing) and order views (for individual orders).
        """
        try:
            if not html_content or len(html_content) < 100:
                return {}

            # Use the same HTML-to-JSON conversion logic as the demo
            structured_data = self._convert_html_to_jsonable(html_content)
            
            # Add metadata for easier consumption
            return {
                **structured_data,
                "metadata": {
                    "content_type": "application/json",
                    "original_format": "text/html",
                    "parsed_at": time.time(),
                    "parser_version": "1.0"
                }
            }

        except Exception as e:
            self.logger.warning(f"Error parsing table data: {e}")
            return {}



    def _convert_html_to_jsonable(self, html_content: str) -> Dict[str, Any]:
        """
        Convert HTML response to structured JSON format.
        This matches the _response_to_jsonable function from the demo code.
        Optimized for performance with selective parsing.
        """
        try:
            from bs4 import BeautifulSoup, SoupStrainer
            
            # Performance optimization: Use selective parsing
            strainer = SoupStrainer(["table", "thead", "tbody", "tr", "th", "td", "input", "form"])
            
            # Fast parser selection with error handling
            try:
                # lxml is faster but may not be available
                soup = BeautifulSoup(html_content, "lxml", parse_only=strainer)
            except:
                try:
                    # html5lib is more accurate but slower
                    soup = BeautifulSoup(html_content, "html5lib", parse_only=strainer)
                except:
                    # Fallback to built-in parser
                    soup = BeautifulSoup(html_content, "html.parser", parse_only=strainer)
            
            # Extract CSRF token quickly
            token = self._extract_csrf_token_fast(soup)
            
            # Use sections-based parsing like the demo
            sections = self._extract_sections_fast(soup)
            
            return {
                "sections": sections,
                "_token": token,
                "payload": {"_token": token}  # For compatibility
            }
            
        except Exception as e:
            self.logger.warning(f"Error converting HTML to JSON: {e}")
            return {"sections": []}
    
    def _extract_csrf_token_fast(self, soup: BeautifulSoup) -> Optional[str]:
        """Fast CSRF token extraction"""
        # Check input fields first (most common)
        token_input = soup.find("input", {"name": "_token"})
        if token_input:
            value = token_input.get("value")
            if value:
                return value.strip()
        return None
    
    def _extract_sections_fast(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Fast section extraction focusing only on tables"""
        sections = []
        
        # Directly extract all tables (skip complex section parsing)
        tables = self._extract_all_tables_fast(soup)
        if tables:
            section_data = {
                "heading": "",
                "messages": [],
                "tables": tables,
                "paragraphs": []
            }
            sections.append(section_data)
        
        return sections
    
    def _extract_all_tables_fast(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Fast table extraction with minimal processing"""
        tables = []
        
        for table_elem in soup.find_all("table", limit=5):  # Limit to first 5 tables
            table_data = self._parse_table_fast(table_elem)
            if table_data:
                tables.append(table_data)
        
        return tables
    
    def _parse_table_fast(self, table_elem) -> Dict[str, Any]:
        """Fast table parsing with minimal validation"""
        try:
            headers = []
            rows = []
            
            # Extract headers quickly
            header_row = table_elem.find("tr")
            if header_row:
                for th in header_row.find_all(["th", "td"]):
                    headers.append({"text": th.get_text(strip=True)})
            
            # Extract data rows (skip header row)
            for tr in table_elem.find_all("tr")[1:]:  # Skip first row (headers)
                row_data = []
                for td in tr.find_all(["td", "th"]):
                    cell_data = {"text": td.get_text(strip=True)}
                    # Check for input fields
                    input_field = td.find("input")
                    if input_field:
                        cell_data["input_value"] = input_field.get("value", "")
                    row_data.append(cell_data)
                
                if row_data:  # Only add non-empty rows
                    rows.append(row_data)
            
            return {"header": headers, "rows": rows} if rows else None
            
        except Exception as e:
            logger.debug(f"Error parsing table: {e}")
            return None
    
    def _parse_section_element(self, sec) -> Dict[str, Any]:
        """Parse a single section element"""
        # Extract heading within section
        heading = ""
        for tag in ["h1", "h2", "h3", "h4"]:
            h = sec.find(tag)
            if h:
                heading = h.get_text(separator=" ", strip=True)
                break
        
        # Extract messages within section
        messages = []
        for el in sec.find_all(attrs={"role": "alert"}):
            text = el.get_text(separator=" ", strip=True)
            if text:
                messages.append(text)
        
        # Extract tables within section
        tables = self._extract_tables_from_element(sec)
        
        # Extract paragraphs within section
        paragraphs = []
        for p in sec.find_all("p"):
            text = p.get_text(separator=" ", strip=True)
            if text:
                paragraphs.append(text)
        
        return {
            "heading": heading,
            "messages": messages,
            "tables": tables,
            "paragraphs": paragraphs[:5]  # Limit to first 5 paragraphs
        }
    
    def _extract_main_heading(self, soup: BeautifulSoup) -> str:
        """Extract main heading from page"""
        for tag in ["h1", "h2", "h3"]:
            h = soup.find(tag)
            if h:
                return h.get_text(separator=" ", strip=True)
        return ""
    
    def _extract_flash_messages(self, soup: BeautifulSoup) -> List[str]:
        """Extract flash messages from page"""
        messages = []
        
        # Look for alert elements
        for el in soup.find_all(attrs={"role": "alert"}):
            text = el.get_text(separator=" ", strip=True)
            if text:
                messages.append(text)
        
        # Look for common alert classes
        for cls in ["alert", "alert-success", "alert-danger", "alert-warning", "alert-info", "error", "success", "notice"]:
            for el in soup.select(f".{cls}"):
                text = el.get_text(separator=" ", strip=True)
                if text and text not in messages:
                    messages.append(text)
        
        return messages
    
    def _extract_all_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract all tables from soup"""
        return self._extract_tables_from_element(soup)
    
    def _extract_tables_from_element(self, element) -> List[Dict[str, Any]]:
        """Extract tables from a specific element"""
        tables = []
        
        for table in element.find_all("table"):
            table_data = self._parse_table(table)
            if table_data:
                tables.append(table_data)
        
        return tables
    
    def _parse_table(self, table) -> Dict[str, Any]:
        """Parse a single table element using demo logic"""
        header = []
        rows = []
        
        # Extract header
        thead = table.find("thead")
        if thead:
            head_rows = thead.find_all("tr")
            if head_rows:
                header = [self._parse_cell(c) for c in head_rows[0].find_all(["th", "td"])]
        
        # If no thead, try first tr with th elements
        if not header:
            first_tr = table.find("tr")
            if first_tr:
                ths = first_tr.find_all("th")
                if ths:
                    header = [self._parse_cell(th) for th in ths]
        
        # Extract rows
        bodies = table.find_all("tbody")
        if bodies:
            for body in bodies:
                for tr in body.find_all("tr"):
                    cells = tr.find_all(["th", "td"])
                    if cells:
                        rows.append([self._parse_cell(c) for c in cells])
        else:
            # No tbody, parse all tr elements (skip header if it was th elements)
            all_trs = table.find_all("tr")
            skip_first = bool(header)  # Skip first row if we extracted header from it
            
            for tr in all_trs[1 if skip_first else 0:]:
                cells = tr.find_all(["th", "td"])
                if cells:
                    rows.append([self._parse_cell(c) for c in cells])
        
        return {"header": header, "rows": rows}
    
    def _parse_cell(self, cell) -> Dict[str, Any]:
        """Parse a table cell (matches demo logic exactly)"""
        text = cell.get_text(separator=" ", strip=True)
        inputs = list(cell.find_all("input"))
        
        obj = {"text": text}
        
        if len(inputs) == 1:
            itype = (inputs[0].get("type") or "text").lower()
            val = inputs[0].get("value")
            if val is None and itype in ("checkbox", "radio"):
                val = "on"
            obj["input_type"] = itype
            obj["input_value"] = "" if val is None else val
        elif len(inputs) > 1:
            for idx, inp in enumerate(inputs, start=1):
                itype = (inp.get("type") or "text").lower()
                val = inp.get("value")
                if val is None and itype in ("checkbox", "radio"):
                    val = "on"
                obj[f"input_type_{idx}"] = itype
                obj[f"input_value_{idx}"] = "" if val is None else val
        
        return obj
    
    def _extract_paragraphs(self, soup: BeautifulSoup) -> List[str]:
        """Extract paragraphs from soup"""
        paragraphs = []
        for p in soup.find_all("p"):
            text = p.get_text(separator=" ", strip=True)
            if text:
                paragraphs.append(text)
        return paragraphs[:5]  # Limit to first 5 paragraphs

    def _process_response_content(self, response: requests.Response) -> Dict[str, Any]:
        """
        Automatically process response content and convert to JSON if needed.
        This makes the API client completely transparent - always returns JSON.
        
        Args:
            response: Raw HTTP response
            
        Returns:
            Processed response data (always JSON format)
        """
        content_type = response.headers.get('content-type', '').lower()
        
        # Handle JSON responses (pass through)
        if 'application/json' in content_type:
            try:
                return response.json()
            except Exception as e:
                self.logger.warning(f"Failed to parse JSON response: {e}")
                return {"error": "Invalid JSON response", "raw_text": response.text}
        
        # Handle HTML responses (convert to JSON)
        elif 'text/html' in content_type:
            logger.info(f"🔄 Auto-converting HTML to JSON... ({len(response.text)} chars)")
            
            # Convert HTML to structured JSON
            json_data = self._parse_table_data_fast(response.text)
            
            # Show conversion success
            sections_count = len(json_data.get('sections', []))
            logger.info(f"✅ HTML→JSON conversion complete ({sections_count} sections parsed)")
            
            return json_data
        
        # Handle plain text responses
        elif 'text/plain' in content_type:
            return {"text": response.text, "format": "plain_text"}
        
        # Handle other content types
        else:
            self.logger.warning(f"Unknown content type: {content_type}")
            return {
                "raw_text": response.text,
                "content_type": content_type,
                "format": "unknown"
            }

    def _process_response_content(self, response: requests.Response) -> Dict[str, Any]:
        """
        Automatically process response content and convert to JSON if needed.
        This makes the API client completely transparent - always returns JSON.
        
        Args:
            response: Raw HTTP response
            
        Returns:
            Processed response data (always JSON format)
        """
        content_type = response.headers.get('content-type', '').lower()
        
        # Handle JSON responses (pass through)
        if 'application/json' in content_type:
            try:
                return response.json()
            except Exception as e:
                self.logger.warning(f"Failed to parse JSON response: {e}")
                return {"error": "Invalid JSON response", "raw_text": response.text}
        
        # Handle HTML responses (convert to JSON)
        elif 'text/html' in content_type:
            logger.info(f"🔄 Auto-converting HTML to JSON... ({len(response.text)} chars)")
            
            # Convert HTML to structured JSON
            json_data = self._parse_table_data_fast(response.text)
            
            # Show conversion success
            sections_count = len(json_data.get('sections', []))
            logger.info(f"✅ HTML→JSON conversion complete ({sections_count} sections parsed)")
            
            return json_data
        
        # Handle plain text responses
        elif 'text/plain' in content_type:
            return {"text": response.text, "format": "plain_text"}
        
        # Handle other content types
        else:
            self.logger.warning(f"Unknown content type: {content_type}")
            return {
                "raw_text": response.text,
                "content_type": content_type,
                "format": "unknown"
            }

    async def close(self):
        """Close the HTTP client and clean up resources."""
        # The HTTP client uses the shared session manager
        # so we don't need to close anything specific here
        self.logger.info(f"🔐 HTTP client closed for {self.username}")
        pass

    def _is_download_response(self, response_data: Dict[str, Any], endpoint: str = "unknown") -> bool:
        """
        Detect if this response is from a download operation.
        
        Download operations return plain text card data and should NOT be processed
        by the card extractor. Instead, the raw text should be used directly.
        
        Args:
            response_data: The processed response data
            endpoint: The API endpoint being called
            
        Returns:
            True if this is a download response, False otherwise
        """
        try:
            # Check endpoint pattern - downloads are typically orders/{order_id}  
            if endpoint and endpoint.startswith("orders/") and len(endpoint) > 7:
                # Check if response contains text format typical of downloads
                if isinstance(response_data, dict):
                    # Download responses typically have "text" field with card data
                    if "text" in response_data and "format" in response_data:
                        text_content = response_data.get("text", "")
                        # Check if it looks like card data (pipe-separated format)
                        if isinstance(text_content, str) and "|" in text_content:
                            # Additional check - card data typically has multiple pipe separators
                            pipe_count = text_content.count("|")
                            if pipe_count >= 5:  # Typical card format has many fields
                                return True
                    
                    # Alternative: Check if response lacks typical API v3 structure (sections/tables)
                    if "text" in response_data and "sections" not in response_data:
                        return True
            
            return False
        except Exception as e:
            logger.debug(f"Error detecting download response: {e}")
            return False

    def _display_final_response(self, response_data: Dict[str, Any], endpoint: str = "unknown") -> Dict[str, Any]:
        """
        Display the complete final JSON response and extract card data.
        
        Args:
            response_data: The processed response data
            endpoint: The API endpoint being called
            
        Returns:
            Dictionary with extracted cards
        """
        try:
            # Display full API response JSON (like api3_demo) - only if enabled
            logger.info(f"[API-CALL] Processing response from endpoint: {endpoint}")
            
            # Check if this is a download operation (skip card extraction for downloads)
            is_download_operation = self._is_download_response(response_data, endpoint)
            
            # Check if full response logging is enabled
            if self.settings.LOG_FULL_API_RESPONSES:
                logger.info("=" * 80)
                logger.info(f"📤 FULL API RESPONSE ({endpoint}):")
                logger.info("=" * 80)
                
                # Log complete JSON response
                import json
                complete_json = json.dumps(response_data, indent=2, ensure_ascii=False)
                
                # Truncate if too large to avoid massive logs
                MAX_LOG_SIZE = 2000  # Reduced from 10KB to 2KB for less spam
                if len(complete_json) > MAX_LOG_SIZE:
                    logger.info(complete_json[:MAX_LOG_SIZE])
                    logger.info(f"... (truncated {len(complete_json) - MAX_LOG_SIZE:,} characters)")
                else:
                    logger.info(complete_json)
                
                logger.info("=" * 80)
            else:
                # Just show a summary when full logging is disabled
                logger.info(f"📦 API response received from {endpoint} (full logging disabled)")
                if isinstance(response_data, dict) and "sections" in response_data:
                    sections = response_data.get("sections", [])
                    if sections and "tables" in sections[0]:
                        rows = sections[0]["tables"][0].get("rows", [])
                        logger.info(f"📊 Response contains {len(rows)} items")
                    else:
                        logger.info("📊 Response structure analysis complete")
            
            # Skip card extraction for download operations - use plain text directly
            if is_download_operation:
                logger.info(f"[CARD-EXTRACTION] ⏩ Skipping card extraction for download operation from {endpoint}")
                logger.info(f"[CARD-EXTRACTION] 📄 Using plain text format directly")
                return {"extracted_cards": [], "is_download": True}
            
            # Extract cards using the centralized card extractor (SIMPLIFIED)
            try:
                extractor = get_card_data_extractor()
                extracted_cards = extractor.extract_from_api_response(response_data)
                
                if extracted_cards:
                    logger.info(f"[CARD-EXTRACTION] ✅ Extracted {len(extracted_cards)} cards from API response")
                    # Add extracted cards to the response data for compatibility
                    response_data["cards"] = extracted_cards
                    response_data["extracted_cards"] = extracted_cards
                    logger.info(f"✅ Added {len(extracted_cards)} extracted cards to response data")
                else:
                    logger.info(f"[CARD-EXTRACTION] ℹ️ No cards extracted from {endpoint} response")
                    extracted_cards = []
                    
            except Exception as e:
                logger.error(f"Card extraction error: {e}")
                extracted_cards = []
                
            return {"extracted_cards": extracted_cards}
            
        except Exception as e:
            # Fallback to simple display if processing fails
            logger.warning(f"Response processing failed, using fallback display: {e}")
            
            # Only log full response if enabled in settings
            if self.settings.LOG_FULL_API_RESPONSES:
                import json
                logger.info(f"📤 FINAL JSON RESPONSE ({endpoint}):")
                logger.info("=" * 60)
                
                if isinstance(response_data, dict):
                    complete_json = json.dumps(response_data, indent=2, ensure_ascii=False)
                    # Truncate to prevent massive logs
                    MAX_LOG_SIZE = 1000  # Reduced to 1KB maximum
                    if len(complete_json) > MAX_LOG_SIZE:
                        complete_json = complete_json[:MAX_LOG_SIZE] + f"\n... (truncated {len(complete_json) - MAX_LOG_SIZE:,} characters)"
                    logger.info(complete_json)
                else:
                    logger.info(f"Response type: {type(response_data).__name__}")
            else:
                # Just log a summary
                logger.info(f"📦 Fallback response processing complete for {endpoint} (full logging disabled)")
                logger.info(f"Content: {str(response_data)[:500]}...")  # Limit to 500 chars
            
            logger.info("=" * 60)
            logger.info("✅ Response ready for code consumption")
            
            return {"extracted_cards": []}
