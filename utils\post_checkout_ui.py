"""
Enhanced Post-Checkout UI Components

This module provides modern, user-friendly UI components for post-checkout
experiences including order completion, card viewing, status checking, and
order management functionality.
"""

from __future__ import annotations

from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from utils.ui_components import create_message, MessageType
from utils.enhanced_keyboards import (
    create_enhanced_keyboard,
    KeyboardStyle,
    ButtonPriority,
)
from utils.data_display import (
    create_card_display,
    is_valid_value,
    format_date,
    format_currency,
    format_boolean,
)
from utils.card_formatter import (
    CentralizedCardFormatter,
    CardFormattingOptions,
    CardDisplayMode,
    format_card_full,
    format_card_masked,
)

from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class OrderSummary:
    """Order summary information for UI display"""

    order_id: str
    transaction_id: str
    total_amount: float
    item_count: int
    remaining_balance: float
    created_at: datetime
    status: str = "completed"


@dataclass
class CardDetails:
    """Card details for UI display - Enhanced with all API fields"""

    # Core identification
    card_id: str
    bank: str
    brand: str
    level: str
    country: str
    price: float
    status: str
    last_checked: Optional[datetime] = None

    # Additional card information
    card_type: Optional[str] = None  # CREDIT, DEBIT, etc.
    base: Optional[str] = None  # Base/quality information

    # Location information
    state: Optional[str] = None
    city: Optional[str] = None
    zip_code: Optional[str] = None
    address: Optional[str] = None

    # Cardholder information (masked)
    cardholder_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None

    # Timestamps
    created_at: Optional[datetime] = None
    start_date: Optional[datetime] = None
    viewed_at: Optional[datetime] = None
    check_date: Optional[datetime] = None
    refund_at: Optional[datetime] = None
    checked_at: Optional[datetime] = None

    # Status flags
    can_check: Optional[bool] = None
    refundable: Optional[bool] = None
    is_viewed: Optional[bool] = None

    # IDs
    order_id: Optional[str] = None
    product_id: Optional[str] = None

    # API v3 specific fields
    partial_card_number: Optional[str] = (
        None  # Masked card number (e.g., "555426**********")
    )
    expiry_date: Optional[str] = None  # Card expiry (e.g., "10/25")
    bin_info: Optional[str] = None  # Full BIN information text

    # Sensitive data fields (available when show_sensitive=True)
    full_card_number: Optional[str] = (
        None  # Full card number (e.g., "****************")
    )
    cvv: Optional[str] = None  # CVV code (e.g., "123")
    
    # Unmasking status
    unmasked_but_requires_download: Optional[bool] = None  # Card is unmasked but needs download for full data


@dataclass
class PaginationInfo:
    """Pagination information for UI display"""

    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int
    has_next: bool
    has_previous: bool
    start_index: int
    end_index: int

    @classmethod
    def create(
        cls, current_page: int, total_items: int, items_per_page: int = 10
    ) -> "PaginationInfo":
        """Create pagination info with calculated values"""
        if current_page < 1:
            current_page = 1

        total_pages = max(1, (total_items + items_per_page - 1) // items_per_page)

        if current_page > total_pages:
            current_page = total_pages

        start_index = (current_page - 1) * items_per_page
        end_index = min(start_index + items_per_page, total_items)

        return cls(
            current_page=current_page,
            total_pages=total_pages,
            items_per_page=items_per_page,
            total_items=total_items,
            has_next=current_page < total_pages,
            has_previous=current_page > 1,
            start_index=start_index,
            end_index=end_index,
        )


class PostCheckoutUI:
    """Enhanced UI components for post-checkout experiences"""

    @staticmethod
    def create_order_completion_message(
        order_summary: OrderSummary, purchased_cards: List[Dict[str, Any]] = None
    ) -> str:
        """
        Create a beautiful order completion message

        Args:
            order_summary: Order summary information
            purchased_cards: List of purchased card data

        Returns:
            Formatted completion message
        """
        msg = create_message(MessageType.SUCCESS)
        msg.set_title("🎉 Order Complete!", "✅")

        # Essential order info only
        msg.add_content(
            f"📦 <b>Order:</b> #{order_summary.order_id[:8]}... • "
            f"<b>${order_summary.total_amount:.2f}</b>\n"
            f"💳 <b>Cards:</b> {order_summary.item_count} ready • "
            f"<b>Balance:</b> ${order_summary.remaining_balance:.2f}"
        )

        # Quick card preview (simplified)
        if purchased_cards:
            card_preview = purchased_cards[0]  # Show only first card
            bank = card_preview.get("bank", "Unknown")
            brand = card_preview.get("brand", "")
            card_name = f"{bank} {brand}".strip()
            if not card_name or card_name == "Unknown":
                card_name = "Premium Card"
            
            msg.add_section(
                "🃏 Your Card",
                f"<b>{card_name}</b>\n"
                f"<code>Ready to view and use</code>",
                "💎"
            )

        # Essential next steps only
        msg.add_list_section(
            "🚀 Quick Actions",
            [
                "🃏 View your cards",
                "🔍 Check card status",
                "📋 View order details"
            ],
            "⚡"
        )

        return msg.build(add_watermark=False)

    @staticmethod
    def create_order_completion_keyboard(
        order_id: str,
        has_cards: bool = True,
        show_receipt: bool = True,
        transaction_id: str = None,
        card_id: str = None,
        api_version: str = None,
    ):
        """
        Create an enhanced keyboard for order completion with optimized callbacks

        Args:
            order_id: Order ID for callbacks
            has_cards: Whether order has cards to view
            show_receipt: Whether to show receipt option
            transaction_id: Transaction ID for better data matching
            card_id: First card ID for direct unmask action
            api_version: API version to determine which callback to use

        Returns:
            Enhanced keyboard with optimized callback data
        """
        kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)

        # Primary action - view cards
        if has_cards:
            if card_id:
                # For API v1, use view_details to show same view as My Orders
                # For other APIs, use view_card for direct view
                if api_version == 'v1':
                    kb.add_button(
                        "🃏 View Card",
                        f"orders:view_details:{card_id}",
                        ButtonPriority.PRIMARY,
                    )
                    logger.info(f"✅ [Post Checkout] Using view_details for API v1 card: {card_id}")
                else:
                    # Use full card ID to ensure proper lookup
                    kb.add_button(
                        "🃏 View Card",
                        f"orders:view_card:{card_id}",
                        ButtonPriority.PRIMARY,
                    )
                    logger.info(f"✅ [Post Checkout] Using view_card for API {api_version} card: {card_id}")
            else:
                short_order_id = order_id[:16] if len(order_id) > 16 else order_id
                kb.add_button(
                    "🃏 View Cards",
                    f"orders:view_recent_cards:{short_order_id}",
                    ButtonPriority.PRIMARY,
                )

        # Essential secondary actions
        kb.add_button("📜 Orders", "menu:orders", ButtonPriority.SECONDARY)
        kb.add_button("🛒 Browse", "menu:browse", ButtonPriority.SECONDARY)

        # Navigation
        kb.add_navigation_row(back_text="🏠 Main Menu", back_callback="menu:main")

        return kb

    @staticmethod
    def create_card_view_message(
        card_details: CardDetails, show_sensitive: bool = True, compact: bool = False
    ) -> str:
        """
        Create an enhanced card viewing message with comprehensive data display.
        
        🎨 Now delegates to the centralized formatter for consistency.

        Args:
            card_details: Card information
            show_sensitive: Whether to show sensitive information (default True)
            compact: If True, creates a shorter version to avoid MESSAGE_TOO_LONG errors

        Returns:
            Formatted card view message
        """
        try:
            # Convert CardDetails dataclass to dict for formatter
            card_dict = {
                "card_number": card_details.full_card_number if show_sensitive else card_details.partial_card_number,
                "expiry_date": card_details.expiry_date,
                "cvv": card_details.cvv,
                "bank": card_details.bank,
                "brand": card_details.brand,
                "level": card_details.level,
                "country": card_details.country,
                "type": card_details.card_type,
                "base": card_details.base,
                "state": card_details.state,
                "city": card_details.city,
                "zip": card_details.zip_code,
                "address": card_details.address,
                "cardholder_name": card_details.cardholder_name,
                "email": card_details.email,
                "phone": card_details.phone,
                "status": card_details.status,
                "price": card_details.price,
                "order_id": card_details.order_id,
                "product_id": card_details.product_id,
                "unmasked_but_requires_download": card_details.unmasked_but_requires_download,
                "bin": card_details.bin_info,
            }
            
            # Use centralized formatter
            options = CardFormattingOptions(
                mode=CardDisplayMode.COMPACT if compact else CardDisplayMode.FULL,
                show_sensitive=show_sensitive,
                compact=compact,
                show_order_info=True,
                show_timestamps=False,  # CardDetails doesn't have all timestamp fields yet
                show_technical_details=not compact,
                show_location=not compact
            )
            
            return CentralizedCardFormatter.format_card_details(card_dict, options)
            
        except Exception as e:
            logger.error(f"Error in create_card_view_message: {e}", exc_info=True)
            # Fallback to legacy implementation
        msg = create_message(MessageType.INFO)

        # Card header with enhanced visual appeal
        card_name = (
            f"{card_details.bank} {card_details.brand} {card_details.level}".strip()
        )
        if not card_name or "Unknown" in card_name:
            card_name = f"Card #{card_details.card_id}"

        # Add status badge to title for quick recognition
        status_badge = PostCheckoutUI._get_status_badge(card_details.status)
        msg.set_title(f'💳 {card_name} {status_badge}', '🃏')
        return msg.build(add_watermark=False)

    @staticmethod
    def create_card_view_keyboard(
        card_id: str,
        order_id: str,
        can_check: bool = True,
        expiry_timestamp: Optional[int] = None,
        is_unmasked: bool = False,
        check_status: Optional[str] = None,
        user_id: Optional[str] = None,
        cooldown_handler: Optional[callable] = None,
        show_loading_states: bool = True,
    ):
        """
        Create optimized keyboard for card viewing with enhanced loading states and visual improvements.

        Args:
            card_id: Card ID
            order_id: Order ID
            can_check: Whether check functionality is available
            expiry_timestamp: Check expiry timestamp
            is_unmasked: Whether card details are already unmasked
            check_status: Current check status (active, available, processing, etc.)
            user_id: User ID for cooldown checking
            cooldown_handler: Function to check cooldown status
            show_loading_states: Whether to show loading state indicators

        Returns:
            Enhanced keyboard with loading states and visual improvements
        """
        kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)

        # Primary action - MOST IMPORTANT with enhanced visual feedback
        if not is_unmasked:
            # Show unmask button if card is not yet unmasked
            kb.add_button(
                "🔓 Unmask Card",
                f"orders:unmask:{order_id}:{card_id}",
                ButtonPriority.PRIMARY,
            )
        else:
            # Show download button if card is already unmasked
            kb.add_button(
                "📥 Download Card Data",
                f"orders:download:{order_id}:{card_id}",
                ButtonPriority.PRIMARY,
            )
            # Also allow re-unmasking to test the full flow again
            # kb.add_button(
            #     "🔄 Re-Unmask (Test)",
            #     f"orders:unmask:{order_id}:{short_card_id}",
            #     ButtonPriority.SECONDARY,
            # )

        # Secondary action - Check status with enhanced visual indicators
        # Show check button based on status and timer conditions
        if can_check:
            current_time = int(datetime.now(timezone.utc).timestamp())
            
            # If status is checking, always show recheck button without timer
            if check_status == "checking":
                kb.add_button(
                    "🔄 Recheck Status",
                    f"orders:check:{order_id}:{card_id}",
                    ButtonPriority.SECONDARY,
                )
            # If status is active/available and timer is valid, show timer-based check
            elif check_status in ["active", "available"] and expiry_timestamp:
                # Check if timer is still valid
                if current_time <= expiry_timestamp:
                    remaining_time = expiry_timestamp - current_time
                    
                    # Optimized cooldown check - only if handler provided
                    cooldown_active = False
                    if cooldown_handler and user_id:
                        cooldown_active = cooldown_handler(user_id)
                    
                    if not cooldown_active:
                        # Enhanced button with visual timer indicator
                        timer_emoji = "⏰" if remaining_time <= 10 else "🔍"
                        kb.add_button(
                            f"{timer_emoji} Check Status ({remaining_time}s)",
                            f"orders:check:{order_id}:{card_id}:{expiry_timestamp}",
                            ButtonPriority.SECONDARY,
                        )
            # If no timer but status allows checking, show regular check button
            elif check_status in ["active", "available"] and not expiry_timestamp:
                kb.add_button(
                    "🔍 Check Status",
                    f"orders:check:{order_id}:{card_id}",
                    ButtonPriority.SECONDARY,
                )

        # Enhanced status indicators
        if show_loading_states:
            if check_status == "processing":
                kb.add_button(
                    "🔄 Processing...",
                    "noop:loading",
                    ButtonPriority.TERTIARY,
                )
            elif check_status == "completed":
                kb.add_button(
                    "✅ Status Checked",
                    "noop:completed",
                    ButtonPriority.SUCCESS,
                )

        # Essential navigation with enhanced styling
        kb.add_button("📋 Order History", "menu:orders", ButtonPriority.SECONDARY)
        kb.add_button("🛒 Browse More", "menu:browse", ButtonPriority.SUCCESS)
        kb.add_navigation_row(back_text="🏠 Main Menu", back_callback="menu:main")

        return kb.build()

    @staticmethod
    def create_check_status_message(
        card_status: str, check_details: Dict[str, Any] = None, is_live: bool = False
    ) -> str:
        """
        Create enhanced check status message

        Args:
            card_status: Raw status from API
            check_details: Additional check details
            is_live: Whether card is live/valid

        Returns:
            Formatted status message
        """
        msg = create_message(MessageType.SUCCESS if is_live else MessageType.WARNING)

        status_display = PostCheckoutUI._get_status_display(card_status)
        status_icon = PostCheckoutUI._get_status_icon(card_status)

        msg.set_title(f"Card Status: {status_display}", status_icon)

        # Status details
        if is_live:
            msg.add_section(
                "✅ Card Status",
                f"<b>Status:</b> {status_icon} {status_display}\n"
                f"<b>Verified:</b> {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}\n"
                f"<b>Validity:</b> Card is active and usable",
                "🎯",
            )
        else:
            msg.add_section(
                "⚠️ Card Status",
                f"<b>Status:</b> {status_icon} {status_display}\n"
                f"<b>Checked:</b> {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}\n"
                f"<b>Note:</b> Card may not be active or has issues",
                "📊",
            )

        # Additional details if provided - Enhanced to show more API data
        if check_details:
            # Card information section
            card_info = []
            if "bank" in check_details and check_details["bank"]:
                card_info.append(f"🏦 Bank: {check_details['bank']}")
            if "brand" in check_details and check_details["brand"]:
                card_info.append(f"🏷️ Brand: {check_details['brand']}")
            if "level" in check_details and check_details["level"]:
                card_info.append(f"⭐ Level: {check_details['level']}")
            if "type" in check_details and check_details["type"]:
                card_info.append(f"📋 Type: {check_details['type']}")
            if "country" in check_details and check_details["country"]:
                card_info.append(f"🌍 Country: {check_details['country']}")

            if card_info:
                msg.add_list_section("💳 Card Information", card_info, "📊")

            # Technical details section
            tech_details = []
            if "response_code" in check_details:
                tech_details.append(f"Response: {check_details['response_code']}")
            if "auth_code" in check_details:
                tech_details.append(f"Auth Code: {check_details['auth_code']}")
            if "gateway" in check_details:
                tech_details.append(f"Gateway: {check_details['gateway']}")
            if "base" in check_details and check_details["base"]:
                tech_details.append(f"Base: {check_details['base']}")
            if "_id" in check_details:
                tech_details.append(f"Order ID: #{check_details['_id']}")
            if "product_id" in check_details:
                tech_details.append(f"Product ID: #{check_details['product_id']}")

            if tech_details:
                msg.add_list_section("🔍 Technical Details", tech_details, "⚙️")

            # Timestamps section
            timestamp_info = []
            if "createdAt" in check_details and check_details["createdAt"]:
                timestamp_info.append(f"Created: {check_details['createdAt']}")
            if "viewedAt" in check_details and check_details["viewedAt"]:
                timestamp_info.append(f"First Viewed: {check_details['viewedAt']}")
            if "check_Date" in check_details and check_details["check_Date"]:
                timestamp_info.append(f"Last Check: {check_details['check_Date']}")

            if timestamp_info:
                msg.add_list_section("📅 Timeline", timestamp_info, "⏰")

        # Recommendations
        if is_live:
            msg.add_list_section(
                "💡 Recommendations",
                [
                    "Card is ready for use",
                    "Keep card details secure",
                    "Use responsibly and legally",
                    "Check status periodically",
                ],
                "⭐",
            )
        else:
            msg.add_list_section(
                "💡 Next Steps",
                [
                    "Contact support if needed",
                    "Try checking again later",
                    "Consider replacement if faulty",
                    "Review purchase terms",
                ],
                "🔧",
            )

        return msg.build(add_watermark=False)

    @staticmethod
    def create_order_receipt_message(
        order_summary: OrderSummary,
        items: List[Dict[str, Any]],
        transaction_details: Dict[str, Any] = None,
    ) -> str:
        """
        Create detailed order receipt message

        Args:
            order_summary: Order summary information
            items: List of purchased items
            transaction_details: Additional transaction details

        Returns:
            Formatted receipt message
        """
        msg = create_message(MessageType.INFO)
        msg.set_title("🧾 Order Receipt", "📄")

        # Itemized list
        item_lines = []
        subtotal = 0.0

        for i, item in enumerate(items, 1):
            name = item.get("name", f"Card #{item.get('card_id', i)}")
            price = float(item.get("price", 0.0))
            qty = int(item.get("quantity", 1))
            total_item_price = price * qty

            subtotal += total_item_price

            if qty > 1:
                item_lines.append(f"{i}. {name} (x{qty}) - ${total_item_price:.2f}")
            else:
                item_lines.append(f"{i}. {name} - ${price:.2f}")

        msg.add_list_section("🛒 Items Purchased", item_lines, "💳")

        # Payment summary
        tax = 0.0  # Assuming no tax for now
        total = order_summary.total_amount

        payment_text = (
            f"<b>Subtotal:</b> ${subtotal:.2f}\n"
            f"<b>Tax:</b> ${tax:.2f}\n"
            f"<b>Total:</b> ${total:.2f}\n"
            f"<b>Payment Method:</b> Wallet Balance\n"
            f"<b>Remaining Balance:</b> ${order_summary.remaining_balance:.2f}"
        )

        msg.add_section("💰 Payment Summary", payment_text, "💳")

        # Additional transaction details
        if transaction_details:
            details = []
            if "payment_processor" in transaction_details:
                details.append(f"Processor: {transaction_details['payment_processor']}")
            if "confirmation_code" in transaction_details:
                details.append(
                    f"Confirmation: {transaction_details['confirmation_code']}"
                )

            if details:
                msg.add_list_section("📊 Transaction Details", details, "🔍")

        # Footer
        msg.add_section(
            "ℹ️ Important Notes",
            "Keep this receipt for your records. Contact support if you have any questions about this order.",
            "📝",
        )

        return msg.build(add_watermark=False)

    @staticmethod
    def _get_status_icon(status: str) -> str:
        """Get appropriate icon for card status"""
        # Ensure status is a string
        status_str = str(status) if status is not None else "unknown"
        status_lower = status_str.lower()

        if status_lower in ["live", "active", "valid", "approved"]:
            return "✅"
        elif status_lower in ["refunded", "dead", "invalid", "declined"]:
            return "❌"
        elif status_lower in ["pending", "checking", "processing"]:
            return "⏳"
        elif status_lower in ["unknown", "timeout", "error"]:
            return "❓"
        else:
            return "🔍"

    @staticmethod
    def _get_status_display(status: str) -> str:
        """Get display-friendly status text"""
        # Ensure status is a string
        status_str = str(status) if status is not None else "unknown"
        status_lower = status_str.lower()

        status_map = {
            "live": "Live",
            "active": "Active",
            "valid": "Valid",
            "approved": "Approved",
            "refunded": "Refunded",
            "dead": "Dead",
            "invalid": "Invalid",
            "declined": "Declined",
            "pending": "Pending",
            "checking": "Checking",
            "processing": "Processing",
            "unknown": "Unknown",
            "timeout": "Timeout",
            "error": "Error",
        }

        return status_map.get(status_lower, status_str.title())

    @staticmethod
    def _get_status_badge(status: str) -> str:
        """Get visual badge for card status"""
        # Ensure status is a string
        status_str = str(status) if status is not None else "unknown"
        status_lower = status_str.lower()

        if status_lower in ["live", "active", "valid", "approved"]:
            return "🟢"  # Green circle for active
        elif status_lower in [
            "refunded",
            "dead",
            "invalid",
            "declined",
            "nonrefundable",
        ]:
            return "🔴"  # Red circle for inactive
        elif status_lower in ["pending", "checking", "processing"]:
            return "🟡"  # Yellow circle for pending
        else:
            return "⚪"  # White circle for unknown

    @staticmethod
    def _mask_email(email: str) -> str:
        """Mask email address for privacy"""
        if not email or "@" not in email:
            return email or "N/A"

        parts = email.split("@")
        username = parts[0]
        domain = parts[1]

        if len(username) <= 2:
            masked_username = username[0] + "*"
        else:
            masked_username = username[0] + "*" * (len(username) - 2) + username[-1]

        return f"{masked_username}@{domain}"

    @staticmethod
    def _mask_phone(phone: str) -> str:
        """Mask phone number for privacy"""
        if not phone:
            return "N/A"

        # Remove non-digits
        digits = "".join(c for c in phone if c.isdigit())

        if len(digits) < 4:
            return "*" * len(digits)

        # Show last 4 digits
        return "*" * (len(digits) - 4) + digits[-4:]

    @staticmethod
    def create_loading_message(action: str = "Processing") -> str:
        """Create enhanced loading message for async operations"""
        msg = create_message(MessageType.LOADING)
        msg.set_title(f"⏳ {action}...", "🔄")
        msg.add_content(
            "Please wait while we process your request. This may take a few moments."
        )
        msg.add_section(
            "🔄 Processing",
            "• Validating request\n• Connecting to secure servers\n• Preparing response",
            "⚡"
        )
        return msg.build(add_watermark=False)

    @staticmethod
    def create_enhanced_loading_message(
        action: str = "Processing",
        stages: List[str] = None,
        show_progress: bool = True
    ) -> str:
        """Create sophisticated loading message with stages and progress"""
        msg = create_message(MessageType.LOADING)
        msg.set_title(f"⏳ {action}...", "🔄")
        
        if stages:
            msg.add_content("Please wait while we complete the following steps:")
            msg.add_list_section("📋 Progress", stages, "⚡")
        else:
            msg.add_content("Please wait while we process your request.")
        
        if show_progress:
            msg.add_section(
                "📊 Status",
                "🔄 Processing • ⏳ Please wait • ⚡ Almost ready",
                "📈"
            )
        
        return msg.build(add_watermark=False)

    @staticmethod
    def create_error_message(
        error_title: str, error_description: str, suggestions: List[str] = None
    ) -> str:
        """Create user-friendly error message with recovery options"""
        msg = create_message(MessageType.ERROR)
        msg.set_title(error_title, "❌")
        msg.add_content(error_description)

        if suggestions:
            msg.add_list_section("💡 Try This", suggestions, "🔧")
        
        # Add helpful recovery section
        recovery_options = [
            "🔄 Try the operation again",
            "📞 Contact support if the issue persists",
            "🏠 Return to main menu to continue"
        ]
        msg.add_list_section("🛠️ Recovery Options", recovery_options, "⚡")

        return msg.build(add_watermark=False)

    @staticmethod
    def create_success_message(
        title: str, 
        description: str, 
        next_steps: List[str] = None,
        show_celebration: bool = True
    ) -> str:
        """Create enhanced success message with next steps"""
        msg = create_message(MessageType.SUCCESS)
        
        if show_celebration:
            msg.set_title(f"🎉 {title}", "✅")
        else:
            msg.set_title(title, "✅")
            
        msg.add_content(description)

        if next_steps:
            msg.add_list_section("🚀 What's Next?", next_steps, "⭐")

        return msg.build(add_watermark=False)

    @staticmethod
    def format_countdown_timer(expiry_timestamp: int) -> str:
        """Format countdown timer for UI"""
        now = int(datetime.now(timezone.utc).timestamp())
        remaining = max(0, expiry_timestamp - now)

        if remaining <= 0:
            return "⏰ <i>Expired</i>"

        minutes = remaining // 60
        seconds = remaining % 60

        if minutes > 0:
            return f"⏱️ <i>{minutes}m {seconds}s remaining</i>"
        else:
            return f"⏱️ <i>{seconds}s remaining</i>"

    @staticmethod
    def create_paginated_list_message(
        title: str,
        items: List[Dict[str, Any]],
        pagination: PaginationInfo,
        item_formatter: callable,
        subtitle: str = "",
        show_stats: bool = True,
    ) -> str:
        """
        Create a paginated list message with navigation info

        Args:
            title: Main title for the message
            items: All items to paginate
            pagination: Pagination information
            item_formatter: Function to format each item
            subtitle: Optional subtitle
            show_stats: Whether to show pagination stats

        Returns:
            Formatted paginated message
        """
        msg = create_message(MessageType.INFO)
        # Use provided title styling without forcing an extra emoji
        msg.set_title(title, "")

        if subtitle:
            msg.add_content(subtitle)

        # Pagination info section
        if show_stats:
            page_info = (
                f"<b>Page:</b> {pagination.current_page} of {pagination.total_pages}\n"
                f"<b>Showing:</b> {pagination.start_index + 1}-{pagination.end_index} of {pagination.total_items} items"
            )
            msg.add_section("Page Info", page_info, "📊")

        # Paginated items
        if items:
            current_page_items = items[pagination.start_index : pagination.end_index]
            formatted_items = []

            for i, item in enumerate(current_page_items, pagination.start_index + 1):
                formatted_item = item_formatter(item, i)
                formatted_items.append(formatted_item)

            list_title = f"Items {pagination.start_index + 1}-{pagination.end_index}"
            msg.add_list_section(list_title, formatted_items, "📝")
        else:
            msg.add_section("📭 No Items", "No items found to display.", "ℹ️")

        # Navigation hints
        nav_hints = []
        if pagination.has_previous:
            nav_hints.append("⬅️ Previous page available")
        if pagination.has_next:
            nav_hints.append("➡️ Next page available")
        if nav_hints:
            msg.add_list_section("🧭 Navigation", nav_hints, "🔄")

        return msg.build(add_watermark=False)

    @staticmethod
    def create_paginated_keyboard(
        pagination: PaginationInfo,
        base_callback: str,
        additional_buttons: List[Tuple[str, str, ButtonPriority]] = None,
        back_callback: str = "menu:main",
        show_quick_nav: bool = True,
    ):
        """
        Create enhanced keyboard with pagination controls and visual improvements

        Args:
            pagination: Pagination information
            base_callback: Base callback pattern (will append :page:N)
            additional_buttons: Additional buttons as (text, callback, priority) tuples
            back_callback: Back button callback
            show_quick_nav: Whether to show quick navigation buttons

        Returns:
            Enhanced keyboard with pagination and visual improvements
        """
        kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 3)

        # Enhanced pagination row with visual improvements
        pagination_row = []

        if pagination.has_previous:
            prev_page = pagination.current_page - 1
            kb.add_button(
                "⬅️ Previous",
                f"{base_callback}:page:{prev_page}",
                ButtonPriority.SECONDARY,
            )

        # Enhanced page indicator with visual styling
        page_indicator = f"📄 {pagination.current_page}/{pagination.total_pages}"
        kb.add_button(
            page_indicator,
            "noop:page_info",
            ButtonPriority.TERTIARY,
        )

        if pagination.has_next:
            next_page = pagination.current_page + 1
            kb.add_button(
                "Next ➡️", 
                f"{base_callback}:page:{next_page}", 
                ButtonPriority.SECONDARY
            )

        # Quick navigation for longer lists with enhanced visual feedback
        if show_quick_nav and pagination.total_pages > 3:
            # First/Last page buttons with enhanced styling
            if pagination.current_page > 2:
                kb.add_button(
                    "⏮️ First", 
                    f"{base_callback}:page:1", 
                    ButtonPriority.TERTIARY
                )

            if pagination.current_page < pagination.total_pages - 1:
                kb.add_button(
                    "Last ⏭️",
                    f"{base_callback}:page:{pagination.total_pages}",
                    ButtonPriority.TERTIARY,
                )

        # Add additional buttons if provided with enhanced styling
        if additional_buttons:
            for text, callback, priority in additional_buttons:
                kb.add_button(text, callback, priority)

        # Enhanced navigation with visual improvements
        kb.add_navigation_row(
            back_text="⬅️ Back", 
            back_callback=back_callback,
            additional_buttons=[
                {"text": "🔄 Refresh", "callback_data": f"{base_callback}:refresh"},
                {"text": "🏠 Main Menu", "callback_data": "menu:main"}
            ]
        )

        return kb

    @staticmethod
    def format_order_item_paginated(order: Dict[str, Any], index: int) -> str:
        """Format a single order item for paginated display with enhanced visual indicators"""
        meta = order.get("metadata", {}) or {}
        cd = meta.get("card_data", {}) or {}

        price = order.get("price", 0.0)
        status = order.get("status", "")
        bank = cd.get("bank", "Unknown Bank")
        brand = cd.get("brand", "")
        level = cd.get("level", "")
        created = order.get("created_at")

        # Enhanced status icons with better visual hierarchy
        status_icons = {
            "completed": "✅",
            "active": "🟢",
            "pending": "🟡",
            "processing": "🔄",
            "failed": "❌",
            "refunded": "💰",
            "expired": "⏰",
            "cancelled": "🚫",
            "queued": "⏳",
        }
        status_icon = status_icons.get(str(status).lower(), "📋")

        # Enhanced card name with better formatting
        card_name = f"{bank}"
        if brand and brand != bank:
            card_name += f" {brand}"
        if level:
            card_name += f" ({level})"

        # Format date with enhanced styling
        if created:
            if isinstance(created, datetime):
                date_str = created.strftime("%m/%d %H:%M")
            else:
                date_str = str(created)[:16]
        else:
            date_str = "Unknown"

        # Enhanced formatting with visual improvements
        return (
            f"<b>{index}. {card_name}</b>\n"
            f"   {status_icon} <b>${price:.2f}</b> • {status.title()} • 📅 {date_str}"
        )

    @staticmethod
    def format_transaction_item_paginated(
        transaction: Dict[str, Any], index: int
    ) -> str:
        """Format a single transaction item for paginated display"""
        ttype = transaction.get("type", "")
        amount = float(transaction.get("amount", 0))
        created = transaction.get("created_at")
        reference = transaction.get("reference", "")

        # Transaction type formatting
        type_info = {
            "deposit": ("💰", "Deposit", "+"),
            "add_funds": ("💰", "Add Funds", "+"),
            "credit": ("💰", "Credit", "+"),
            "purchase": ("🛒", "Purchase", "-"),
            "debit": ("🛒", "Debit", "-"),
            "withdrawal": ("💸", "Withdrawal", "-"),
            "refund": ("🔄", "Refund", "+"),
        }

        emoji, type_name, sign = type_info.get(str(ttype).lower(), ("📄", str(ttype).title(), ""))

        # Format amount
        amount_str = f"{sign}${abs(amount):.2f}"

        # Format date
        if created:
            if isinstance(created, datetime):
                date_str = created.strftime("%m/%d %H:%M")
            else:
                date_str = str(created)[:16]
        else:
            date_str = "Unknown"

        # Reference info (truncated)
        ref_info = (
            f" • {reference[:20]}..."
            if reference and len(reference) > 20
            else f" • {reference}" if reference else ""
        )

        return (
            f"<b>{index}. {emoji} {amount_str}</b> - {type_name}\n"
            f"   📅 {date_str}{ref_info}"
        )

    @staticmethod
    def format_card_item_paginated(card: Dict[str, Any], index: int) -> str:
        """Format a single card item for paginated display"""
        bank = card.get("bank", "Unknown Bank")
        brand = card.get("brand", "")
        level = card.get("level", "")
        country = card.get("country", "")
        price = float(card.get("price", 0.0))
        status = card.get("status", "active")

        # Status icon
        status_icons = {
            "active": "🟢",
            "live": "🟢",
            "valid": "✅",
            "checked": "✅",
            "pending": "🟡",
            "invalid": "❌",
            "expired": "⏰",
            "dead": "💀",
        }
        status_icon = status_icons.get(str(status).lower(), "📋")

        # Card name
        card_name = f"{bank}"
        if brand and brand != bank:
            card_name += f" {brand}"
        if level:
            card_name += f" ({level})"

        # Country info
        country_info = f" • {country}" if country and country != "Unknown" else ""

        return (
            f"<b>{index}. {card_name}</b>\n"
            f"   {status_icon} ${price:.2f} • {status}{country_info}"
        )


class PaginationHelpers:
    """Helper utilities for common pagination scenarios"""

    @staticmethod
    def parse_page_from_callback(callback_data: str, default: int = 1) -> int:
        """Extract page number from callback data like 'orders:all:page:3'"""
        try:
            parts = callback_data.split(":")
            if len(parts) >= 3 and parts[-2] == "page":
                return max(1, int(parts[-1]))
        except (ValueError, IndexError):
            pass
        return default

    @staticmethod
    def create_orders_page(
        orders: List[Dict[str, Any]],
        current_page: int = 1,
        items_per_page: int = 10,
        title: str = "📦 Order History",
    ) -> Tuple[str, Any]:
        """
        Create paginated orders display

        Returns:
            Tuple of (message_text, keyboard)
        """
        pagination = PaginationInfo.create(current_page, len(orders), items_per_page)

        # Calculate statistics for subtitle
        total_spent = sum(float(order.get("price", 0)) for order in orders)
        active_count = len(
            [
                o
                for o in orders
                if str(o.get("status", "")).lower() in ["active", "completed", "valid"]
            ]
        )

        subtitle = (
            f"💳 <b>{len(orders)}</b> total orders • "
            f"<b>{active_count}</b> active • "
            f"<b>${total_spent:.2f}</b> total spent"
        )

        message = PostCheckoutUI.create_paginated_list_message(
            title=title,
            items=orders,
            pagination=pagination,
            item_formatter=PostCheckoutUI.format_order_item_paginated,
            subtitle=subtitle,
        )

        # Additional buttons for orders
        additional_buttons = [
            ("🔄 Refresh", "orders:refresh", ButtonPriority.SECONDARY),
            ("🛒 New Order", "menu:browse", ButtonPriority.SECONDARY),
        ]

        keyboard = PostCheckoutUI.create_paginated_keyboard(
            pagination=pagination,
            base_callback="orders:all",
            additional_buttons=additional_buttons,
            back_callback="menu:main",
        )

        return message, keyboard

    @staticmethod
    def create_transactions_page(
        transactions: List[Dict[str, Any]],
        current_page: int = 1,
        items_per_page: int = 10,
        title: str = "💳 Transaction History",
    ) -> Tuple[str, Any]:
        """
        Create paginated transactions display

        Returns:
            Tuple of (message_text, keyboard)
        """
        pagination = PaginationInfo.create(
            current_page, len(transactions), items_per_page
        )

        # Calculate statistics
        total_in = sum(
            float(t.get("amount", 0))
            for t in transactions
            if t.get("type") in ["deposit", "add_funds", "credit"]
        )
        total_out = sum(
            abs(float(t.get("amount", 0)))
            for t in transactions
            if t.get("type") in ["purchase", "withdrawal", "debit"]
        )
        net_flow = total_in - total_out

        subtitle = (
            f"📊 <b>{len(transactions)}</b> transactions • "
            f"<b>+${total_in:.2f}</b> in • "
            f"<b>-${total_out:.2f}</b> out • "
            f"<b>${net_flow:.2f}</b> net"
        )

        message = PostCheckoutUI.create_paginated_list_message(
            title=title,
            items=transactions,
            pagination=pagination,
            item_formatter=PostCheckoutUI.format_transaction_item_paginated,
            subtitle=subtitle,
        )

        # Additional buttons for transactions
        additional_buttons = [
            ("💰 Add Funds", "wallet:deposit", ButtonPriority.SECONDARY),
        ]

        keyboard = PostCheckoutUI.create_paginated_keyboard(
            pagination=pagination,
            base_callback="history:all",
            additional_buttons=additional_buttons,
            back_callback="menu:main",
        )

        return message, keyboard

    @staticmethod
    def create_cards_page(
        cards: List[Dict[str, Any]],
        current_page: int = 1,
        items_per_page: int = 8,
        title: str = "🃏 Available Cards",
    ) -> Tuple[str, Any]:
        """
        Create paginated cards browse display

        Returns:
            Tuple of (message_text, keyboard)
        """
        pagination = PaginationInfo.create(current_page, len(cards), items_per_page)

        # Calculate statistics
        price_range = "$0.00"
        if cards:
            try:
                prices = [float(c.get('price', 0)) for c in cards if c.get('price') is not None]
                if prices:
                    price_range = f"${min(prices):.2f}-${max(prices):.2f}"
            except (ValueError, TypeError):
                price_range = "$0.00"
        countries = len(
            set(
                c.get("country", "Unknown")
                for c in cards
                if c.get("country", "Unknown") != "Unknown"
            )
        )

        subtitle = (
            f"🌍 <b>{len(cards)}</b> available • "
            f"<b>{countries}</b> countries • "
            f"{price_range} range"
        )

        message = PostCheckoutUI.create_paginated_list_message(
            title=title,
            items=cards,
            pagination=pagination,
            item_formatter=PostCheckoutUI.format_card_item_paginated,
            subtitle=subtitle,
        )

        # Additional buttons for cards
        additional_buttons = [
            ("🔍 Filter", "browse:filter", ButtonPriority.PRIMARY),
            ("🛒 View Cart", "cart:view", ButtonPriority.SECONDARY),
            ("🔄 Refresh", "browse:refresh", ButtonPriority.SECONDARY),
        ]

        keyboard = PostCheckoutUI.create_paginated_keyboard(
            pagination=pagination,
            base_callback="browse:cards",
            additional_buttons=additional_buttons,
            back_callback="menu:main",
        )

        return message, keyboard
