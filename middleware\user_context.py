"""
User context middleware
"""

from __future__ import annotations

from typing import Any, Awaitable, Callable, Dict

from aiogram.dispatcher.middlewares.base import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery

from utils.central_logger import get_logger

logger = get_logger()


class UserContextMiddleware(BaseMiddleware):
    """Middleware to add user context to handlers"""

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any],
    ) -> Any:
        """Add user context to handler data"""
        # Only apply to messages and callback queries
        if not isinstance(event, (Message, CallbackQuery)):
            return await handler(event, data)

        user = event.from_user
        if user:
            # Add user context to data
            data["user_context"] = {
                "user_id": user.id,
                "username": user.username,
                "first_name": user.first_name,
                "language_code": user.language_code,
                "is_bot": user.is_bot,
                "is_premium": getattr(user, "is_premium", False),
            }

            # Add to logger context
            logger.debug(
                f"Processing request from user {user.id}",
                extra={"user_id": user.id, "username": user.username},
            )

        return await handler(event, data)
