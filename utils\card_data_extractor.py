"""
CENTRALIZED Card Data Extractor - SINGLE SOURCE OF TRUTH

This is the ONLY file that should handle card data extraction across the entire codebase.
All other extraction logic has been consolidated here to maintain consistency and ease of maintenance.

IMPORTANT: NO OTHER FILES SHOULD PERFORM DATA EXTRACTION
- response_processor.py: Delegates to this file only
- orders_handlers.py: All extraction methods deprecated, delegates to this file
- All services: Must use methods from this file only
- All endpoints: Must route through this file for extraction

Key Features:
- 100% automatic extraction based on filter_response.json
- High-speed cached lookups and pattern matching  
- Intelligent field detection and validation
- Zero hardcoded values or manual patterns
- Handles ALL API versions (v1, v2, v3) consistently
- Processes ALL response formats (sections, tables, text, legacy)

Centralized Methods for ALL Endpoints:
- extract_comprehensive_card_data(): Master method for any response format
- extract_from_table_format(): For headers+rows table data
- extract_from_content_text(): For text-based card content
- extract_from_sections_response(): For API v3 sections format
- extract_from_api_response(): For standard API responses
- extract_from_order(): For order data structures

Usage Examples:
    # For any API response (recommended)
    from utils.card_data_extractor import extract_comprehensive_card_data
    card_data = extract_comprehensive_card_data(api_response, card_id)
    
    # For specific formats (when you know the format)
    from utils.card_data_extractor import extract_from_table_format
    card_data = extract_from_table_format(headers, rows, card_id)
    
    # For instance-based extraction
    from utils.card_data_extractor import get_card_data_extractor
    extractor = get_card_data_extractor()
    cards = extractor.extract_from_api_response(api_data)

MAINTAINER NOTE: When adding new extraction logic, add it to this file only.
Never create extraction methods in other files to maintain centralization.
"""

import re
import json
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple
from pathlib import Path
from functools import lru_cache

from utils.central_logger import get_logger

logger = get_logger()

# Performance-optimized regex patterns (minimal, essential only)
PRICE_PATTERN = re.compile(r"(\d+(?:\.\d+)?)\$", re.IGNORECASE)
DISCOUNT_PATTERN = re.compile(r"(\d+)%\s*off", re.IGNORECASE)
PHONE_PATTERN = re.compile(r"Phone\s*:\s*([+\d\s\-\.]+)", re.IGNORECASE)
WHITESPACE_PATTERN = re.compile(r'\s+')

# Fast field extraction indexes (column positions for 9-column format)
FIELD_INDEXES = {
    "checkbox": 0, "bin": 1, "expiry": 2, "base": 3, "cardholder": 4,
    "country_continent": 5, "scheme_info": 6, "contact_info": 7, "price": 8
}

# Global extraction cache to prevent redundant processing
_extraction_cache: Dict[str, List[Dict[str, Any]]] = {}
_extraction_cache_times: Dict[str, float] = {}
_extraction_cache_ttl = 60  # 1 minute cache for extraction results


class AutomaticCardDataExtractor:
    """
    Fully automatic, high-performance card data extractor.
    
    Uses filter_response.json for all validation and extraction rules.
    Zero hardcoded data, maximum automation and speed.
    """
    
    def __init__(self):
        """Initialize automatic extractor with filter-based validation."""
        # Load and cache all filter data once
        self.filter_data = self._load_filter_data()
        
        # Build fast lookup structures from filter data
        self._build_fast_lookups()
        
        # Performance counters
        self.extraction_count = 0
        self.cache_hits = 0
        
        logger.debug(f"AutomaticCardDataExtractor initialized: "
                    f"{len(self.schemes)} schemes, {len(self.types)} types, "
                    f"{len(self.levels)} levels, {len(self.banks)} banks, "
                    f"{len(self.countries)} countries, {len(self.continents)} continents")
    
    @lru_cache(maxsize=1)
    def _load_filter_data(self) -> List[Dict[str, Any]]:
        """Load and cache filter validation data from filter_response.json."""
        try:
            filter_path = (
                Path(__file__).parent.parent 
                / "data" 
                / "filters" 
                / "filter_response.json"
            )
            
            if filter_path.exists():
                with open(filter_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"Filter file not found: {filter_path}")
                
        except Exception as e:
            logger.error(f"Error loading filter data: {e}")
        
        return []
    
    def _build_fast_lookups(self) -> None:
        """Build optimized lookup structures from filter data for maximum speed."""
        try:
            # Extract all filter options into fast sets and dicts
            self.schemes = set(self._extract_options("scheme[]"))
            self.types = set(self._extract_options("type[]"))
            self.levels = set(self._extract_options("level[]"))
            self.banks = set(self._extract_options("selected_bank"))
            self.countries = set(self._extract_options("country[]"))
            self.continents = set(self._extract_options("continent[]"))
            
            # Build fast normalized lookups (uppercase -> original)
            self.scheme_lookup = {s.upper(): s for s in self.schemes}
            self.type_lookup = {t.upper(): t for t in self.types}
            self.level_lookup = {l.upper(): l for l in self.levels}
            self.bank_lookup = {b.upper(): b for b in self.banks}
            self.country_lookup = {c.upper(): c for c in self.countries}
            self.continent_lookup = {c.upper(): c for c in self.continents}
            
            # Build fast keyword matching for schemes (for substring matching)
            self.scheme_keywords = sorted(self.schemes, key=len, reverse=True)  # Longest first
            self.type_keywords = sorted(self.types, key=len, reverse=True)
            self.level_keywords = sorted(self.levels, key=len, reverse=True)
            
            # Cache for repeated lookups
            self._lookup_cache = {}
            
        except Exception as e:
            logger.error(f"Error building fast lookups: {e}")
            # Fallback to empty sets
            self.schemes = self.types = self.levels = self.banks = set()
            self.countries = self.continents = set()

    def _extract_options(self, filter_name: str) -> List[str]:
        """Fast extraction of options from filter data."""
        try:
            for filter_group in self.filter_data:
                if filter_group.get("name") == filter_name:
                    return [opt.get("value", "") for opt in filter_group.get("options", []) if opt.get("value")]
        except Exception as e:
            logger.error(f"Error extracting options for {filter_name}: {e}")
        return []
    
    def _extract_valid_options(self, filter_name: str) -> List[str]:
        """Extract valid options for a specific filter type."""
        try:
            for filter_group in self.filter_data:
                if filter_group.get("name") == filter_name:
                    return [opt.get("value", "") for opt in filter_group.get("options", [])]
        except Exception as e:
            logger.error(f"Error extracting valid options for {filter_name}: {e}")
        
        return []
    
    def _create_normalized_lookup(self, options: List[str]) -> Dict[str, str]:
        """Create normalized lookup dictionary for better matching."""
        normalized = {}
        try:
            for option in options:
                if not option or not isinstance(option, str):
                    continue
                    
                option_stripped = option.strip()
                if not option_stripped:
                    continue
                
                # Create multiple normalized keys for flexible matching
                option_upper = option_stripped.upper()
                normalized[option_upper] = option_stripped
                normalized[option_upper.replace(" ", "")] = option_stripped
                normalized[option_upper.replace("-", "")] = option_stripped
                normalized[option_upper.replace(".", "")] = option_stripped
                
                # Handle common abbreviations
                if "LIMITED" in option_upper:
                    short_form = option_upper.replace(" LIMITED", "").replace(" LTD", "")
                    if short_form:  # Ensure it's not empty
                        normalized[short_form] = option_stripped
                if "CORPORATION" in option_upper:
                    short_form = option_upper.replace(" CORPORATION", "").replace(" CORP", "")
                    if short_form:  # Ensure it's not empty
                        normalized[short_form] = option_stripped
                
                # Add common variations
                if " & " in option_upper:
                    normalized[option_upper.replace(" & ", " AND ")] = option_stripped
                    normalized[option_upper.replace(" & ", "")] = option_stripped
                if " AND " in option_upper:
                    normalized[option_upper.replace(" AND ", " & ")] = option_stripped
                    
        except Exception as e:
            logger.error(f"Error creating normalized lookup: {e}")
            
        return normalized
    
    def extract_from_api_response(self, api_data: Dict[str, Any], endpoint: str = None) -> List[Dict[str, Any]]:
        """
        Extract card data from API v3 response with caching to prevent redundant processing.
        
        Args:
            api_data: Complete API response with sections/tables structure
            endpoint: API endpoint being processed (for logging)
            
        Returns:
            List of extracted card data dictionaries
        """
        try:
            endpoint_info = f" from {endpoint}" if endpoint else ""
            
            # Generate hash for cache key to detect identical data
            import time
            try:
                # Try to serialize to JSON for cache key
                data_str = json.dumps(api_data, sort_keys=True)
                data_hash = hashlib.md5(data_str.encode()).hexdigest()
            except (TypeError, ValueError) as e:
                # If JSON serialization fails (e.g., circular references), use a simpler cache key
                logger.debug(f"⚠️ Cannot serialize API data for cache key (circular refs or unsupported type): {e}")
                # Use id() of the api_data object as a simple cache key
                # This means each unique object gets cached, but we won't cache identical data from different objects
                data_hash = str(id(api_data))
                logger.debug(f"Using object ID for cache key: {data_hash}")
            
            cache_key = f"{endpoint or 'unknown'}:{data_hash}"
            
            # Check if we've already processed this exact data recently
            current_time = time.time()
            if (cache_key in _extraction_cache and 
                cache_key in _extraction_cache_times and
                (current_time - _extraction_cache_times[cache_key]) < _extraction_cache_ttl):
                cached_cards = _extraction_cache[cache_key]
                logger.info(f"🚀 EXTRACTION CACHE HIT: Reusing {len(cached_cards)} cached cards{endpoint_info}")
                return cached_cards
            
            logger.info(f"🔍 CARD EXTRACTION START{endpoint_info}")
            extracted_cards = []
            
            # Debug: Log the structure of api_data
            logger.debug(f"[CARD-EXTRACTION-DEBUG] API data keys: {list(api_data.keys())}")
            
            # Handle direct headers/rows format (API v3 style)
            if "headers" in api_data and "rows" in api_data:
                headers = api_data.get("headers", [])
                rows = api_data.get("rows", [])
                logger.debug(f"[CARD-EXTRACTION-DEBUG] Direct format: {len(headers)} headers, {len(rows)} rows")
                
                if headers and rows:
                    # Extract each card from table rows efficiently
                    for row in rows:
                        card_data = self._extract_card_from_row(row, headers)
                        if card_data:
                            extracted_cards.append(card_data)
            
            # Navigate to sections -> tables (legacy format)
            sections = api_data.get("sections", [])
            logger.debug(f"[CARD-EXTRACTION-DEBUG] Found {len(sections)} sections in API response")
            
            if not sections:
                # Check if this is an empty cart response (which is normal)
                if "cart" in str(api_data).lower() or "empty" in str(api_data).lower():
                    logger.debug("[CARD-EXTRACTION-DEBUG] Empty cart response detected - this is normal")
                else:
                    logger.debug("[CARD-EXTRACTION-DEBUG] No sections found in API response - may be empty or different format")
            
            # Debug: Show section structure
            for section_idx, section in enumerate(sections):
                if isinstance(section, dict):
                    logger.debug(f"[CARD-EXTRACTION-DEBUG] Section {section_idx} keys: {list(section.keys())}")
                    tables = section.get("tables", [])
                    logger.debug(f"[CARD-EXTRACTION-DEBUG] Section {section_idx} has {len(tables)} tables")
                    
                    if not tables:
                        logger.warning(f"[CARD-EXTRACTION-DEBUG] Section {section_idx} has no tables!")
                else:
                    logger.warning(f"[CARD-EXTRACTION-DEBUG] Section {section_idx} is not a dict: {type(section)}")
                    continue
                    
                for table_idx, table in enumerate(tables):
                    if isinstance(table, dict):
                        logger.debug(f"[CARD-EXTRACTION-DEBUG] Table {table_idx} keys: {list(table.keys())}")
                        # Try both 'header' and 'headers' keys for compatibility
                        headers = table.get("headers", []) or table.get("header", [])
                        rows = table.get("rows", [])
                        logger.debug(f"[CARD-EXTRACTION-DEBUG] Table {table_idx}: {len(headers)} headers, {len(rows)} rows")
                        
                        if not headers:
                            logger.warning(f"[CARD-EXTRACTION-DEBUG] Table {table_idx} has no headers!")
                        if not rows:
                            logger.warning(f"[CARD-EXTRACTION-DEBUG] Table {table_idx} has no rows!")
                    else:
                        logger.warning(f"[CARD-EXTRACTION-DEBUG] Table {table_idx} is not a dict: {type(table)}")
                        continue
                    
                    if not headers or not rows:
                        logger.warning(f"[CARD-EXTRACTION-DEBUG] Skipping table {table_idx}: headers={bool(headers)}, rows={bool(rows)}")
                        continue
                    
                    # Extract each card from table rows efficiently
                    for row in rows:
                        card_data = self._extract_card_from_row(row, headers)
                        if card_data:
                            extracted_cards.append(card_data)
            
            # Always log first 3 items from API response and extracted cards automatically
            self._log_first_three_items(api_data, extracted_cards, enable_debug=True)
            
            # Summary logging
            if extracted_cards:
                logger.info(f"[CARD-EXTRACTION] ✅ Extracted {len(extracted_cards)} cards from API response{endpoint_info}")
            else:
                logger.info(f"[CARD-EXTRACTION] ⚠️ No cards extracted from API response{endpoint_info}")
            
            # Cache the extraction results for future use
            _extraction_cache[cache_key] = extracted_cards
            _extraction_cache_times[cache_key] = current_time
            
            # Validate and set correct card_number_status for all extracted cards
            for card in extracted_cards:
                card_number = card.get("card_number", "")
                current_status = card.get("card_number_status", "")
                
                # Determine correct status based on actual card number content
                if card_number is None or "[PAN_REDACTED]" in str(card_number):
                    correct_status = "requires_download"
                    # Ensure we don't show PAN_REDACTED
                    if card_number == "[PAN_REDACTED]":
                        card["card_number"] = None
                        card["display_note"] = "Card data available via download"
                elif "*" in str(card_number):
                    correct_status = "masked"
                elif card_number and len(str(card_number).replace(" ", "")) >= 13:
                    # Check if it's all digits (unmasked)
                    card_digits = re.sub(r"[^\d]", "", str(card_number))
                    if len(card_digits) >= 13 and card_digits.isdigit():
                        correct_status = "unmasked"
                    else:
                        correct_status = "partial"
                else:
                    correct_status = "requires_download"  # Default for empty/invalid data
                
                # Set the correct status (only log if there's an actual issue, not normal corrections)
                if current_status != correct_status:
                    # Only log as warning if there's a significant discrepancy or missing status
                    if not current_status or (current_status and current_status not in ["masked", "unmasked", "partial", "requires_download"]):
                        logger.debug(f"🔧 Setting card status: {card.get('_id', 'unknown')} - {current_status} → {correct_status}")
                    card["card_number_status"] = correct_status
                else:
                    # Ensure status is set even if it matches
                    if not current_status:
                        card["card_number_status"] = correct_status
            
            # Simple cache cleanup - remove old entries if cache gets too large
            if len(_extraction_cache) > 50:  # Keep extraction cache reasonable size
                sorted_items = sorted(_extraction_cache_times.items(), key=lambda x: x[1])
                for key, _ in sorted_items[:10]:  # Remove 10 oldest entries
                    _extraction_cache.pop(key, None)
                    _extraction_cache_times.pop(key, None)
            
            return extracted_cards
            
        except Exception as e:
            logger.error(f"Error extracting from API response: {e}")
            return []
    
    def _extract_card_from_row(self, row: List[Dict[str, Any]], headers: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Extract card data from a single table row.
        
        Supports multiple formats:
        - 9 columns (browse/cart): [checkbox, BIN, Expiry, Base, F.Name, Country/Ethnicity/Continent, Scheme/Type/Level, Address/Phone/DOB, Price]
        - 6 columns (orders): [checkbox, Card, Base, F.Name, Bin Info, Address/Phone/DOB]
        """
        try:
            if len(row) == 9:
                # Browse/Cart format (9 columns)
                return self._extract_browse_format_row(row, headers)
            elif len(row) == 6:
                # Order format (6 columns)
                return self._extract_order_format_row(row, headers)
            else:
                logger.warning(f"Unsupported row length: {len(row)}, expected 6 or 9 columns")
                # Return None for unsupported formats
                return None
                
        except Exception as e:
            logger.error(f"Error extracting card from row: {e}")
            return None
            
            # Extract basic fields
            card_data = {
                "card_id": self._extract_card_id(row[0]),
                "bin": self._clean_text(row[1]),
                "expiry": self._clean_text(row[2]),
                "cardholder_name": self._clean_text(row[4]),
                "raw_country": self._clean_text(row[5]),
                "raw_scheme": self._clean_text(row[6]),
                "raw_contact": self._clean_text(row[7]),
                "raw_price": self._clean_text(row[8])
            }
            
            # Parse and validate complex fields
            self._parse_country_info(card_data)
            self._parse_scheme_info(card_data)
            self._parse_contact_info(card_data)
            self._parse_price_info(card_data)
            
            # Clean up raw fields (keep only processed data)
            for key in list(card_data.keys()):
                if key.startswith("raw_"):
                    del card_data[key]
            
            return card_data
            
        except Exception as e:
            logger.error(f"Error extracting card from row: {e}")
            return None
    
    def _extract_card_id(self, checkbox_cell: Dict[str, Any]) -> str:
        """Extract card ID from checkbox input value."""
        try:
            # Try different possible keys for card ID
            return (checkbox_cell.get("value", "") or 
                   checkbox_cell.get("input_value", "") or 
                   checkbox_cell.get("id", ""))
        except Exception:
            return ""
    
    def _get_cell_text(self, cell: Dict[str, Any]) -> str:
        """Fast text extraction from table cell with minimal processing."""
        try:
            text = cell.get("text", "")
            return WHITESPACE_PATTERN.sub(' ', text).strip() if text else ""
        except Exception:
            return ""
    
    def _clean_text(self, cell: Dict[str, Any]) -> str:
        """Extract and clean text from table cell."""
        return self._get_cell_text(cell)
    
    def _parse_country_info(self, card_data: Dict[str, Any]) -> None:
        """
        Parse country and continent information from raw_country field using filter validation.
        Also extracts bank/scheme information when present in the same field.
        
        Expected formats:
        - "GERMANY , Europe"
        - "UNITED STATES , North America"
        - "UNITED STATES,North AmericaVISA DEBIT PREPAIDSUTTON BANK"
        
        This method separates country and continent, validates both against filter_response.json,
        and attempts intelligent matching with normalization for better accuracy.
        """
        try:
            raw_country = card_data.get("raw_country", "").strip()  # Trim input first
            country_extracted = ""
            continent_extracted = ""
            bank_scheme_info = ""
            
            # Parse the raw country field - handle various formats with spaces
            # Examples: "SPAIN,Europe", "SPAIN, Europe", " SPAIN , Europe ", etc.
            if "," in raw_country:
                parts = raw_country.split(",", 1)
                country_extracted = parts[0].strip()  # Remove spaces from country
                remaining = parts[1].strip() if len(parts) > 1 else ""
                
                # Check if remaining text contains both continent and bank/scheme info
                # Look for patterns like "North AmericaVISA DEBIT PREPAIDSUTTON BANK"
                continent_bank_parts = self._separate_continent_and_bank_info(remaining)
                continent_extracted = continent_bank_parts["continent"]
                bank_scheme_info = continent_bank_parts["bank_scheme"]
            else:
                country_extracted = raw_country.strip()  # Handle single value (just country)
                continent_extracted = ""
            
            # Validate and normalize country (only if not empty after trimming)
            validated_country = self._validate_and_normalize_country(country_extracted) if country_extracted else ""
            card_data["country"] = validated_country
            
            # Validate and normalize continent (only if not empty after trimming)
            validated_continent = self._validate_and_normalize_continent(continent_extracted) if continent_extracted else ""
            card_data["continent"] = validated_continent
            
            # If we found bank/scheme info in the country field, parse it
            if bank_scheme_info and not card_data.get("bank"):
                self._parse_bank_scheme_from_country_field(card_data, bank_scheme_info)
            
            # Debug logging for validation results
            if country_extracted and not validated_country:
                logger.debug(f"Country validation failed: '{country_extracted}' not found in filter data")
            if continent_extracted and not validated_continent:
                logger.debug(f"Continent validation failed: '{continent_extracted}' not found in filter data")
                
            logger.debug(f"Country/Continent extraction: '{raw_country}' → country='{validated_country}', continent='{validated_continent}', bank_scheme='{bank_scheme_info}'")
            
        except Exception as e:
            logger.error(f"Error parsing country info: {e}")
            logger.error(f"Raw country data was: '{card_data.get('raw_country', 'NOT_FOUND')}'")
            import traceback
            logger.error(f"Country parsing traceback: {traceback.format_exc()}")
            card_data["country"] = ""
            card_data["continent"] = ""
    
    def _separate_continent_and_bank_info(self, text: str) -> Dict[str, str]:
        """Separate continent and bank/scheme information from combined text."""
        try:
            text_upper = text.upper()
            continent = ""
            bank_scheme = ""
            
            # Try to find continent first
            for continent_name in self.continents:
                continent_upper = continent_name.upper()
                if continent_upper in text_upper:
                    continent = continent_name
                    # Find where continent ends to extract bank/scheme info
                    continent_end = text_upper.find(continent_upper) + len(continent_upper)
                    bank_scheme = text[continent_end:].strip()
                    break
            
            # If no continent found, assume all text is bank/scheme info
            if not continent:
                bank_scheme = text.strip()
            
            return {"continent": continent, "bank_scheme": bank_scheme}
            
        except Exception as e:
            logger.error(f"Error separating continent and bank info: {e}")
            return {"continent": "", "bank_scheme": ""}
    
    def _parse_bank_scheme_from_country_field(self, card_data: Dict[str, Any], bank_scheme_text: str) -> None:
        """Parse bank and scheme information from country field when present."""
        try:
            if not bank_scheme_text:
                return
            
            # Extract brand, type, and bank from the bank_scheme_text
            brand = self._find_best_match(bank_scheme_text, self.scheme_keywords, self.scheme_lookup)
            card_type = self._find_best_match(bank_scheme_text, self.type_keywords, self.type_lookup)
            bank = self._extract_bank_auto(bank_scheme_text, brand, card_type, "")
            
            # Update card_data with extracted information if not already present
            if brand and not card_data.get("brand"):
                card_data["brand"] = brand
            if card_type and not card_data.get("type"):
                card_data["type"] = card_type
            if bank and not card_data.get("bank"):
                card_data["bank"] = bank
            
            logger.debug(f"Bank/scheme from country field: '{bank_scheme_text}' → brand='{brand}', type='{card_type}', bank='{bank}'")
            
        except Exception as e:
            logger.error(f"Error parsing bank/scheme from country field: {e}")
    
    def _parse_country_info_from_text(self, card_data: Dict[str, Any], country_text: str) -> None:
        """Parse country info from text and update card_data directly."""
        try:
            # Set up temporary structure for parsing
            temp_data = {"raw_country": country_text}
            self._parse_country_info(temp_data)
            
            # Update the main card_data with parsed results
            card_data.update({
                "country": temp_data.get("country", ""),
                "continent": temp_data.get("continent", ""),
                "brand": temp_data.get("brand", ""),
                "type": temp_data.get("type", ""),
                "bank": temp_data.get("bank", "")
            })
            
        except Exception as e:
            logger.error(f"Error parsing country info from text: {e}")
    
    def _parse_scheme_info(self, card_data: Dict[str, Any]) -> None:
        """
        Automatic scheme parsing using filter_response.json validation.
        Fast, accurate, zero hardcoded values.
        """
        try:
            raw_scheme = card_data.get("raw_scheme", "").upper()
            if not raw_scheme:
                card_data.update({"brand": "", "type": "", "level": "", "bank": ""})
                return
            
            # Check cache first for performance
            cache_key = f"scheme_{hash(raw_scheme)}"
            if cache_key in self._lookup_cache:
                self.cache_hits += 1
                card_data.update(self._lookup_cache[cache_key])
                return
            
            # Fast automatic extraction using filter keywords
            brand = self._find_best_match(raw_scheme, self.scheme_keywords, self.scheme_lookup)
            card_type = self._find_best_match(raw_scheme, self.type_keywords, self.type_lookup)  
            level = self._find_best_match(raw_scheme, self.level_keywords, self.level_lookup)
            bank = self._extract_bank_auto(raw_scheme, brand, card_type, level)
            
            # Cache result for future use
            result = {"brand": brand, "type": card_type, "level": level, "bank": bank}
            self._lookup_cache[cache_key] = result
            card_data.update(result)
            
        except Exception as e:
            logger.error(f"Error in automatic scheme parsing: {e}")
            card_data.update({"brand": "", "type": "", "level": "", "bank": ""})
    
    def _find_best_match(self, text: str, keywords: List[str], lookup: Dict[str, str]) -> str:
        """Find best matching keyword from filter data using fast substring search."""
        text_upper = text.upper()
        
        # Try direct lookup first (fastest)
        if text_upper in lookup:
            return lookup[text_upper]
        
        # Find longest matching keyword (more accurate)
        for keyword in keywords:
            if keyword.upper() in text_upper:
                return keyword
                
        return ""
    
    def _extract_bank_auto(self, raw_scheme: str, brand: str, card_type: str, level: str) -> str:
        """Automatic bank extraction using filter validation with smart PREPAID handling."""
        try:
            # Remove known components to isolate bank name
            bank_text = raw_scheme
            for component in [brand, card_type, level]:
                if component:
                    bank_text = bank_text.replace(component.upper(), "")
            
            # Clean and normalize
            bank_text = WHITESPACE_PATTERN.sub(' ', bank_text).strip()
            
            # Smart handling of PREPAID concatenated with bank names
            # Check if text contains PREPAID followed by a bank name (no space)
            bank_upper = bank_text.upper()
            
            # Handle cases like "PREPAIDSUTTON BANK" -> "SUTTON BANK"
            if "PREPAID" in bank_upper:
                # Remove PREPAID and check if remainder matches a known bank
                without_prepaid = bank_upper.replace("PREPAID", "").strip()
                if without_prepaid in self.bank_lookup:
                    return self.bank_lookup[without_prepaid]
                
                # Try partial matches for the remainder
                for bank in self.banks:
                    bank_upper_clean = bank.upper().strip()
                    if bank_upper_clean in without_prepaid or without_prepaid in bank_upper_clean:
                        return bank
                
                # If no match found, use the cleaned version
                bank_text = without_prepaid
            
            # Handle bank names with commas (like "PATHWARD, NATIONAL ASSOCIATION")
            if "," in bank_text:
                parts = [part.strip() for part in bank_text.split(",")]
                # Try to find a match for the full bank name first
                full_bank_name = ", ".join(parts)
                if full_bank_name.upper() in self.bank_lookup:
                    return self.bank_lookup[full_bank_name.upper()]
                
                # Try each part individually
                for part in reversed(parts):  # Start with the last part (usually the main bank name)
                    if len(part) > 2:
                        if part.upper() in self.bank_lookup:
                            return self.bank_lookup[part.upper()]
                        
                        # Try partial matches
                        for bank in self.banks:
                            bank_upper_clean = bank.upper().strip()
                            if bank_upper_clean in part.upper() or part.upper() in bank_upper_clean:
                                return bank
                
                # If no match found, use the full bank name
                bank_text = full_bank_name
            
            # Check against known banks from filter data
            if bank_upper in self.bank_lookup:
                return self.bank_lookup[bank_upper]
            
            # Try partial matches for longer bank names
            for bank in self.banks:
                bank_upper_clean = bank.upper().strip()
                if bank_upper_clean in bank_upper or bank_upper in bank_upper_clean:
                    return bank
            
            # Return cleaned text if no exact match but has content
            return bank_text.title() if len(bank_text) > 2 else ""
            
        except Exception as e:
            logger.error(f"Error in automatic bank extraction: {e}")
            return ""
    
    def _parse_contact_info(self, card_data: Dict[str, Any]) -> None:
        """
        Parse contact information from raw_contact field.
        
        Expected formats:
        - "No address DOB: YES"
        - "Taunus..., NeuIsenburg, 63263,"
        - "No address Phone : +1..."
        - "Sofia..., Sofia, 1111, Phone : 359..."
        """
        try:
            raw_contact = card_data.get("raw_contact", "")
            
            # Initialize contact fields
            card_data["address"] = ""
            card_data["phone"] = ""
            card_data["dob_available"] = False
            
            if "No address" in raw_contact:
                card_data["address"] = ""  # Set as blank instead of "No address"
            else:
                # Extract address (everything before phone)
                if "Phone :" in raw_contact:
                    address_part = raw_contact.split("Phone :")[0].strip()
                    card_data["address"] = address_part.rstrip(",").strip()
                else:
                    card_data["address"] = raw_contact.strip()
            
            # Extract phone using compiled pattern
            phone_match = PHONE_PATTERN.search(raw_contact)
            if phone_match:
                card_data["phone"] = phone_match.group(1).strip()
            
            # Check DOB availability
            if "DOB: YES" in raw_contact:
                card_data["dob_available"] = True
            
        except Exception as e:
            logger.error(f"Error parsing contact info: {e}")
            card_data.update({"address": "", "phone": "", "dob_available": False})
    
    def _parse_price_info(self, card_data: Dict[str, Any]) -> None:
        """
        Parse price information with discount handling.
        
        Expected formats:
        - "8.5$"
        - "EXPIRING NEXT MONTH! 20% off 13$ 10.4$"
        - "15$ 12$" (with discount)
        - "10$ 8$"
        """
        try:
            raw_price = card_data.get("raw_price", "")
            
            # Initialize price fields
            card_data["original_price"] = 0.0
            card_data["current_price"] = 0.0
            card_data["discount_percentage"] = 0
            card_data["is_expiring"] = False
            
            # Check for expiring status
            if "EXPIRING" in raw_price.upper():
                card_data["is_expiring"] = True
            
            # Extract discount percentage using compiled pattern
            discount_match = DISCOUNT_PATTERN.search(raw_price)
            if discount_match:
                discount_val = int(discount_match.group(1))
                # Validate discount percentage is reasonable
                if 0 < discount_val <= 100:
                    card_data["discount_percentage"] = discount_val
            
            # Extract all price values using compiled pattern
            price_matches = PRICE_PATTERN.findall(raw_price)
            
            if len(price_matches) >= 2:
                # Multiple prices: original and discounted
                try:
                    original = float(price_matches[0])
                    current = float(price_matches[-1])  # Last price is current
                    # Ensure both prices are positive
                    if original > 0 and current > 0:
                        card_data["original_price"] = original
                        card_data["current_price"] = current
                except ValueError:
                    logger.warning(f"Invalid price values in: {raw_price}")
            elif len(price_matches) == 1:
                # Single price
                try:
                    price_value = float(price_matches[0])
                    if price_value > 0:
                        card_data["original_price"] = price_value
                        card_data["current_price"] = price_value
                except ValueError:
                    logger.warning(f"Invalid price value in: {raw_price}")
            
            # Validate price consistency
            if card_data["current_price"] > card_data["original_price"] and card_data["original_price"] > 0:
                # Swap if current > original (data error)
                card_data["original_price"], card_data["current_price"] = \
                    card_data["current_price"], card_data["original_price"]
            
            # Set main price field for compatibility
            if card_data["current_price"] > 0:
                card_data["price"] = card_data["current_price"]
            elif card_data["original_price"] > 0:
                card_data["price"] = card_data["original_price"]
                logger.debug(f"Swapped price values for consistency: {raw_price}")
            
        except Exception as e:
            logger.error(f"Error parsing price info: {e}")
            card_data.update({
                "original_price": 0.0,
                "current_price": 0.0,
                "discount_percentage": 0,
                "is_expiring": False
            })
    
    def _validate_and_normalize_country(self, country_raw: str) -> str:
        """Fast country validation using cached lookups."""
        if not country_raw:
            return ""
        
        country_upper = country_raw.strip().upper()
        return self.country_lookup.get(country_upper, "")
    
    def _validate_and_normalize_continent(self, continent_raw: str) -> str:
        """Fast continent validation using cached lookups."""
        if not continent_raw:
            return ""
        
        continent_upper = continent_raw.strip().upper()
        return self.continent_lookup.get(continent_upper, "")
    
    def _log_first_three_items(self, api_data: Dict[str, Any], extracted_cards: List[Dict[str, Any]], enable_debug: bool = True) -> None:
        """
        Automatically log the first 3 items from API response and extracted card data.
        
        Args:
            api_data: Original API response data
            extracted_cards: List of extracted card data
            enable_debug: Whether to enable logging (default: True for automatic logging)
        """
        if not enable_debug:
            return
        try:
            import json
            
            logger.info("🔍 API RESPONSE → CARD EXTRACTION (First 3 Items)")
            
            # Log first 3 items from API response
            logger.info("📊 API RESPONSE (First 3):")
            if "headers" in api_data and "rows" in api_data:
                headers = api_data.get("headers", [])
                rows = api_data.get("rows", [])
                truncated_data = {
                    "headers": headers[:3],
                    "rows": rows[:3],
                    "total_headers": len(headers),
                    "total_rows": len(rows)
                }
                logger.info(json.dumps(truncated_data, indent=2, ensure_ascii=False))
            elif "sections" in api_data:
                sections = api_data.get("sections", [])
                if sections and isinstance(sections[0], dict) and "tables" in sections[0]:
                    tables = sections[0].get("tables", [])
                    if tables:
                        table = tables[0]
                        headers = table.get("headers", [])
                        rows = table.get("rows", [])
                        truncated_data = {
                            "format": "sections",
                            "headers": headers[:3],
                            "rows": rows[:3],
                            "total_headers": len(headers),
                            "total_rows": len(rows)
                        }
                        logger.info(json.dumps(truncated_data, indent=2, ensure_ascii=False))
            else:
                # Show first 3 keys for other formats
                if isinstance(api_data, dict):
                    keys = list(api_data.keys())
                    truncated_data = {
                        "type": type(api_data).__name__,
                        "first_3_keys": keys[:3],
                        "total_keys": len(keys)
                    }
                    logger.info(json.dumps(truncated_data, indent=2, ensure_ascii=False))
            
            # Log first 3 extracted cards
            logger.info("💳 CARD EXTRACTION (First 3):")
            if extracted_cards:
                card_summary = {
                    "total_cards": len(extracted_cards),
                    "first_3_cards": extracted_cards[:3]
                }
                logger.info(json.dumps(card_summary, indent=2, ensure_ascii=False))
            else:
                logger.info(json.dumps({"total_cards": 0, "message": "No cards extracted"}, indent=2))
            
            logger.info("─" * 60)
            
        except Exception as e:
            logger.error(f"Error logging first three items: {e}")
    
    def extract_single_card(self, api_data: Dict[str, Any], card_id: str) -> Optional[Dict[str, Any]]:
        """
        Extract data for a specific card ID from API response.
        
        Args:
            api_data: Complete API response
            card_id: Target card ID to extract
            
        Returns:
            Extracted card data or None if not found
        """
        try:
            all_cards = self.extract_from_api_response(api_data)
            
            for card in all_cards:
                if card.get("_id") == card_id:
                    logger.debug(f"Found card data for ID: {card_id}")
                    return card
            
            logger.warning(f"Card ID {card_id} not found in response")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting single card: {e}")
            return None
    
    def _extract_browse_format_row(self, row: List[Dict[str, Any]], headers: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Automatic extraction from 9-column browse format using fast field indexing.
        Format: [checkbox, BIN, Expiry, Base, F.Name, Country/Continent, Scheme/Type/Level, Contact, Price]
        """
        try:
            if len(row) != 9:
                return None
                
            self.extraction_count += 1
            
            # Fast field extraction using indexes
            card_data = {
                "_id": self._extract_card_id(row[FIELD_INDEXES["checkbox"]]),
                "bin": self._get_cell_text(row[FIELD_INDEXES["bin"]]),
                "expiry": self._get_cell_text(row[FIELD_INDEXES["expiry"]]),  
                "cardholder": self._get_cell_text(row[FIELD_INDEXES["cardholder"]]),
                "raw_country": self._get_cell_text(row[FIELD_INDEXES["country_continent"]]),
                "raw_scheme": self._get_cell_text(row[FIELD_INDEXES["scheme_info"]]),
                "raw_contact": self._get_cell_text(row[FIELD_INDEXES["contact_info"]]),
                "raw_price": self._get_cell_text(row[FIELD_INDEXES["price"]])
            }
            
            # Set initial card status based on BIN field (for browse format, BIN is usually the card identifier)
            bin_value = card_data.get("bin", "")
            if bin_value:
                # For browse format, BIN is typically just the first 6 digits, so status is requires_download
                card_data["card_number_status"] = "requires_download"
            else:
                card_data["card_number_status"] = "requires_download"
            
            # Automatic parsing with filter validation
            self._parse_country_info(card_data)
            self._parse_scheme_info(card_data)
            self._parse_contact_info(card_data)
            self._parse_price_info(card_data)
            
            # Remove raw fields (clean output)
            for key in list(card_data.keys()):
                if key.startswith("raw_"):
                    del card_data[key]
            
            return card_data
            
        except Exception as e:
            logger.error(f"Error in automatic browse format extraction: {e}")
            return None
    
    def _extract_order_format_row(self, row: List[Dict[str, Any]], headers: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Extract card data from 6-column order format.
        
        Format: [checkbox, Card, Base, F.Name, Bin Info, Address/Phone/DOB]
        """
        try:
            # Extract basic fields
            card_data = {
                "_id": self._extract_card_id(row[0]),
                "raw_card": self._clean_text(row[1]),  # Full card info: number, expiry, cvv, status
                "base": self._clean_text(row[2]),
                "cardholder_name": self._clean_text(row[3]),
                "raw_bin_info": self._clean_text(row[4]),  # Country, brand, type, level, bank
                "raw_contact": self._clean_text(row[5])   # Address, phone, DOB
            }
            
            # Set initial card status based on raw_card field content
            raw_card = card_data.get("raw_card", "")
            if "[PAN_REDACTED]" in raw_card:
                card_data["card_number_status"] = "requires_download"
            elif "*" in raw_card:
                card_data["card_number_status"] = "masked"
            elif raw_card and len(raw_card.replace(" ", "")) >= 13:
                # Check if it looks like a full card number
                card_digits = re.sub(r"[^\d]", "", raw_card.split(",")[0] if "," in raw_card else raw_card)
                if len(card_digits) >= 13 and card_digits.isdigit():
                    card_data["card_number_status"] = "unmasked"
                else:
                    card_data["card_number_status"] = "partial"
            else:
                card_data["card_number_status"] = "requires_download"
            
            # Parse order-specific fields
            self._parse_order_card_info(card_data)
            self._parse_order_bin_info(card_data)
            self._parse_contact_info(card_data)
            
            # Clean up raw fields
            for key in list(card_data.keys()):
                if key.startswith("raw_"):
                    del card_data[key]
            
            return card_data
            
        except Exception as e:
            logger.error(f"Error extracting order format row: {e}")
            return None
    
    def _parse_order_card_info(self, card_data: Dict[str, Any]) -> None:
        """
        Parse card information from order format "Card" column.
        
        Expected formats:
        - "[PAN_REDACTED], 10/25, 836 Check Expired!" (masked)
        - "****************, 10/25, 640 Check 178 seconds left to check" (unmasked)
        - "555426**********, 10/25 -" (partially masked)
        """
        try:
            raw_card = card_data.get("raw_card", "")
            
            if not raw_card:
                return
            
            # Split by commas to get different parts
            parts = [part.strip() for part in raw_card.split(",")]
            
            if len(parts) >= 1:
                # First part: Card number
                card_number = parts[0].strip()
                
                if "[PAN_REDACTED]" in card_number:
                    # Check if this card has been unmasked on the backend
                    if card_data.get("backend_unmasked") or card_data.get("unmask_operation_successful"):
                        # Card has been unmasked on backend - mark as unmasked even though view shows PAN_REDACTED
                        card_data["card_number_status"] = "unmasked"
                        card_data["display_note"] = "Card unmasked - data available via download"
                        # Set a placeholder card number to indicate it's unmasked but data needs to be downloaded
                        card_data["card_number"] = "[UNMASKED - DOWNLOAD REQUIRED]"
                    else:
                        # Skip PAN_REDACTED entirely - mark as requiring unmask first
                        card_data["card_number"] = None  # No placeholder
                        card_data["card_number_status"] = "requires_unmask"
                        card_data["display_note"] = "Card requires unmasking first"
                    logger.debug(f"🔒 SKIPPING [PAN_REDACTED]: Card {card_data.get('_id', 'unknown')} requires download for real data")
                elif "*" in card_number:
                    card_data["card_number"] = card_number
                    card_data["card_number_status"] = "masked"
                else:
                    # Check if this is a full card number
                    card_digits = re.sub(r"[^\d]", "", card_number)
                    if len(card_digits) >= 13 and card_digits.isdigit():
                        card_data["card_number"] = card_digits
                        card_data["card_number_status"] = "unmasked"
                        logger.debug(f"🔓 CARD STATUS DEBUG: Setting status='unmasked' for full card {card_digits[:4]}****{card_digits[-4:]} (card {card_data.get('_id', 'unknown')})")
                    else:
                        card_data["card_number"] = card_number
                        card_data["card_number_status"] = "partial"
            
            if len(parts) >= 2:
                # Second part: Expiry date
                expiry_part = parts[1].strip()
                # Remove common suffixes
                expiry_clean = re.sub(r"\s*-\s*$", "", expiry_part)
                if expiry_clean and expiry_clean != "-":
                    card_data["expiry_date"] = expiry_clean
                    card_data["expiry"] = expiry_clean
            
            if len(parts) >= 3:
                # Third part: CVV and status
                cvv_status_part = parts[2].strip()
                
                # Extract CVV (3-4 digits)
                cvv_match = re.search(r"(\d{3,4})", cvv_status_part)
                if cvv_match:
                    card_data["cvv"] = cvv_match.group(1)
                
                # Extract status information
                status_text = cvv_status_part.upper()
                if "REFUNDED" in status_text:
                    card_data["status"] = "Refunded"
                    card_data["refund_status"] = "refunded"
                    # Extract refund reason
                    reason_match = re.search(r"reason:\s*([^,]+)", status_text, re.IGNORECASE)
                    if reason_match:
                        card_data["refund_reason"] = reason_match.group(1).strip()
                    # Extract additional details like CVV failure
                    if "AUTH-FAILED" in status_text or "CVV" in status_text:
                        card_data["decline_reason"] = "CVV Auth Failed"
                elif "CHECK" in status_text:
                    if "EXPIRED" in status_text:
                        card_data["status"] = "Check Expired"
                        card_data["check_status"] = "expired"
                    elif "SECONDS LEFT" in status_text:
                        card_data["status"] = "Check Available"
                        card_data["check_status"] = "active"
                        # Extract timer
                        timer_match = re.search(r"(\d+)\s*seconds?\s*left", status_text.lower())
                        if timer_match:
                            card_data["check_timer"] = int(timer_match.group(1))
                    elif "HOLD" in status_text or "BEING CHECKED" in status_text:
                        card_data["status"] = "Checking"
                        card_data["check_status"] = "processing"
                    else:
                        card_data["status"] = "Check Available"
                        card_data["check_status"] = "available"
                elif "LIVE" in status_text:
                    card_data["status"] = "Live"
                elif "DEAD" in status_text:
                    card_data["status"] = "Dead"
                elif "DECLINED" in status_text:
                    card_data["status"] = "Declined"
                    # Extract decline reason
                    reason_match = re.search(r"reason:\s*([^,]+)", status_text, re.IGNORECASE)
                    if reason_match:
                        card_data["decline_reason"] = reason_match.group(1).strip()
                else:
                    card_data["status"] = "Unknown"
            
        except Exception as e:
            logger.error(f"Error parsing order card info: {e}")
    
    def _parse_order_bin_info(self, card_data: Dict[str, Any]) -> None:
        """
        Parse BIN information from order format "Bin Info" column.
        
        Expected formats:
        - "ITALY , Europe MASTERCARD DEBIT PREPAID INTESA SANPAOLO SPA"
        - "UNITED STATES,North AmericaMASTERCARD DEBIT ENHANCEDFISERV SOLUTIONS, LLC"
        - "UNITED STATES , North America MASTERCARD DEBIT PREPAID RELOADABLE PATHWARD, NATIONAL ASSOCIATION"
        """
        try:
            raw_bin_info = card_data.get("raw_bin_info", "")
            
            if not raw_bin_info:
                return
            
            text_upper = raw_bin_info.upper()
            
            # Step 1: Extract country (up to first comma)
            if "," in raw_bin_info:
                country_part = raw_bin_info.split(",")[0].strip()
                card_data["country"] = country_part
                # Remove country from further processing
                remaining_text = raw_bin_info[raw_bin_info.index(",") + 1:].strip()
            else:
                remaining_text = raw_bin_info
                # Try to extract country from beginning
                words = raw_bin_info.split()
                if words:
                    card_data["country"] = words[0]
            
            # Step 2: Extract continent (look for continent keywords)
            continent_patterns = [
                ("North America", ["NORTH AMERICA", "NORTHAMERICA"]),
                ("South America", ["SOUTH AMERICA", "SOUTHAMERICA"]),
                ("Europe", ["EUROPE"]),
                ("Asia", ["ASIA"]),
                ("Africa", ["AFRICA"]),
                ("Oceania", ["OCEANIA"])
            ]
            
            remaining_upper = remaining_text.upper()
            for continent_name, patterns in continent_patterns:
                for pattern in patterns:
                    if pattern in remaining_upper:
                        card_data["continent"] = continent_name
                        # Remove continent from remaining text (case-insensitive)
                        import re
                        remaining_text = re.sub(pattern, "", remaining_text, flags=re.IGNORECASE)
                        remaining_text = re.sub(pattern.replace(" ", ""), "", remaining_text, flags=re.IGNORECASE)
                        break
                if "continent" in card_data:
                    break
            
            # Step 3: Extract brand (VISA, MASTERCARD, etc.)
            brand_patterns = [
                ("MASTERCARD", ["MASTERCARD"]),
                ("VISA", ["VISA"]),
                ("AMERICAN EXPRESS", ["AMERICAN EXPRESS", "AMEX"]),
                ("DISCOVER", ["DISCOVER"])
            ]
            
            remaining_upper = remaining_text.upper()
            for brand_name, patterns in brand_patterns:
                for pattern in patterns:
                    if pattern in remaining_upper:
                        card_data["brand"] = brand_name
                        # Remove brand from remaining text (case-insensitive)
                        import re
                        remaining_text = re.sub(pattern, "", remaining_text, flags=re.IGNORECASE)
                        remaining_text = re.sub(pattern.replace(" ", ""), "", remaining_text, flags=re.IGNORECASE)
                        break
                if "brand" in card_data:
                    break
            
            # Step 4: Extract type (DEBIT, CREDIT, PREPAID) - handle multiple types
            type_patterns = ["DEBIT", "CREDIT", "PREPAID"]
            remaining_upper = remaining_text.upper()
            card_types = []
            for card_type in type_patterns:
                if card_type in remaining_upper:
                    card_types.append(card_type)
                    # Remove type from remaining text (case-insensitive)
                    remaining_text = re.sub(card_type, "", remaining_text, flags=re.IGNORECASE)
            
            # Combine multiple types (e.g., "DEBIT PREPAID")
            if card_types:
                card_data["type"] = " ".join(card_types)
            
            # Step 5: Extract level (ENHANCED, PLATINUM, GOLD, etc.)
            level_keywords = [
                "ENHANCED", "RELOADABLE", "PLATINUM", "GOLD", "CLASSIC", "STANDARD", 
                "SIGNATURE", "WORLD", "INFINITE", "BLACK", "CORPORATE", "BUSINESS"
            ]
            remaining_upper = remaining_text.upper()
            for keyword in level_keywords:
                if keyword in remaining_upper:
                    card_data["level"] = keyword
                    # Remove level from remaining text (case-insensitive)
                    remaining_text = re.sub(keyword, "", remaining_text, flags=re.IGNORECASE)
                    break
            
            # Step 6: Extract bank name (what remains after removing all other components)
            # Clean up the remaining text
            bank_text = WHITESPACE_PATTERN.sub(" ", remaining_text).strip()
            bank_text = re.sub(r"^[,\s]+|[,\s]+$", "", bank_text)  # Remove leading/trailing commas and spaces
            
            # Handle bank names that contain commas (like "PATHWARD, NATIONAL ASSOCIATION")
            # For bank names with commas, we want to preserve the full bank name
            if "," in bank_text:
                parts = [part.strip() for part in bank_text.split(",")]
                # Join all parts to preserve the full bank name
                bank_text = ", ".join(parts)
            
            if len(bank_text) > 2:
                # Capitalize properly (each word)
                bank_text = bank_text.title()
                card_data["bank"] = bank_text
            else:
                card_data["bank"] = "Unknown Bank"
            
            logger.debug(f"BIN parsing result: country='{card_data.get('country', '')}', continent='{card_data.get('continent', '')}', brand='{card_data.get('brand', '')}', type='{card_data.get('type', '')}', level='{card_data.get('level', '')}', bank='{card_data.get('bank', '')}'")
            
        except Exception as e:
            logger.error(f"Error parsing order BIN info: {e}")
            card_data["bank"] = "Unknown Bank"
    
    def validate_card_data(self, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive validation and correction of card data against all filter rules.
        
        Args:
            card_data: Raw extracted card data
            
        Returns:
            Validated and corrected card data
        """
        try:
            validated_data = card_data.copy()
            
            # Create cache key for this validation
            cache_key = str(sorted([(k, v) for k, v in card_data.items() if isinstance(v, (str, int, float))]))
            if cache_key in self._validation_cache:
                return self._validation_cache[cache_key].copy()
            
            # Validate continent with enhanced matching
            continent = validated_data.get("continent", "")
            if continent and continent not in self.valid_continents:
                # Try to extract continent from mixed strings like "REPUBLIC OF,Europe"
                continent_upper = continent.upper().strip()
                for valid_continent in self.valid_continents:
                    if valid_continent.upper() in continent_upper:
                        logger.debug(f"Extracted continent from mixed string: {continent} -> {valid_continent}")
                        validated_data["continent"] = valid_continent
                        break
                else:
                    # Try fuzzy matching
                    corrected_continent = self._find_similar_value(continent, self.valid_continents)
                    if corrected_continent:
                        logger.debug(f"Corrected continent: {continent} -> {corrected_continent}")
                        validated_data["continent"] = corrected_continent
            
            # Validate country with enhanced matching
            country = validated_data.get("country", "")
            if country and country not in self.valid_countries:
                # Try enhanced matching for complex country names
                corrected_country = self._find_enhanced_match(country, self.valid_countries, self.normalized_countries)
                if corrected_country:
                    logger.debug(f"Corrected country: {country} -> {corrected_country}")
                    validated_data["country"] = corrected_country
                else:
                    # Try partial matching for cases like "KOSOVO" -> "KOSOVO, REPUBLIC OF"
                    country_upper = country.upper().strip()
                    for valid_country in self.valid_countries:
                        if country_upper in valid_country.upper() or valid_country.upper() in country_upper:
                            logger.debug(f"Partial match found: {country} -> {valid_country}")
                            validated_data["country"] = valid_country
                            break
                    else:
                        logger.debug(f"Country validation failed: '{country}' not found in filter data")
            
            # Validate brand/scheme with enhanced matching
            brand = validated_data.get("brand", "")
            if brand and brand not in self.valid_schemes:
                corrected_brand = self._find_enhanced_match(brand, self.valid_schemes, self.normalized_schemes)
                if corrected_brand:
                    logger.debug(f"Corrected brand: {brand} -> {corrected_brand}")
                    validated_data["brand"] = corrected_brand
            
            # Validate type
            card_type = validated_data.get("type", "")
            if card_type and card_type not in self.valid_types:
                corrected_type = self._find_similar_value(card_type, self.valid_types)
                if corrected_type:
                    logger.debug(f"Corrected type: {card_type} -> {corrected_type}")
                    validated_data["type"] = corrected_type
            
            # Validate level
            level = validated_data.get("level", "")
            if level and level not in self.valid_levels:
                corrected_level = self._find_similar_value(level, self.valid_levels)
                if corrected_level:
                    logger.debug(f"Corrected level: {level} -> {corrected_level}")
                    validated_data["level"] = corrected_level
            
            # Validate bank with enhanced matching
            bank = validated_data.get("bank", "")
            if bank and bank not in self.valid_banks:
                corrected_bank = self._find_enhanced_match(bank, self.valid_banks, self.normalized_banks)
                if corrected_bank:
                    logger.debug(f"Corrected bank: {bank} -> {corrected_bank}")
                    validated_data["bank"] = corrected_bank
                else:
                    # For banks, also try partial matching since many bank names are variations
                    corrected_bank = self._find_bank_partial_match(bank)
                    if corrected_bank:
                        logger.debug(f"Partial bank match: {bank} -> {corrected_bank}")
                        validated_data["bank"] = corrected_bank
            
            # Cache the validation result
            self._validation_cache[cache_key] = validated_data.copy()
            
            # Limit cache size to prevent memory issues
            if len(self._validation_cache) > 1000:
                # Remove oldest 20% of entries
                items_to_remove = len(self._validation_cache) // 5
                for _ in range(items_to_remove):
                    self._validation_cache.pop(next(iter(self._validation_cache)))
            
            return validated_data
            
        except Exception as e:
            logger.error(f"Error validating card data: {e}")
            return card_data
    
    def _find_similar_value(self, value: str, valid_options: List[str]) -> Optional[str]:
        """Find similar value in valid options (basic string matching)."""
        try:
            value_upper = value.upper().strip()
            if not value_upper:
                return None
            
            # Exact match
            for option in valid_options:
                if option.upper() == value_upper:
                    return option
            
            # Partial match
            for option in valid_options:
                if value_upper in option.upper() or option.upper() in value_upper:
                    return option
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding similar value: {e}")
            return None
    
    def _find_enhanced_match(self, value: str, valid_options: List[str], normalized_lookup: Dict[str, str]) -> Optional[str]:
        """Enhanced matching using normalized lookup tables."""
        try:
            if not value or not value.strip():
                return None
                
            value_clean = value.strip()
            
            # Direct exact match first
            if value_clean in valid_options:
                return value_clean
            
            # Try normalized lookup
            value_normalized = value_clean.upper()
            if value_normalized in normalized_lookup:
                return normalized_lookup[value_normalized]
            
            # Try without spaces, dashes, dots
            value_compact = value_normalized.replace(" ", "").replace("-", "").replace(".", "")
            if value_compact in normalized_lookup:
                return normalized_lookup[value_compact]
            
            # Fallback to basic similarity
            return self._find_similar_value(value_clean, valid_options)
            
        except Exception as e:
            logger.error(f"Error in enhanced matching: {e}")
            return None
    
    def _find_bank_partial_match(self, bank_name: str) -> Optional[str]:
        """Special handling for bank name partial matching with enhanced comma support."""
        try:
            if not bank_name or not bank_name.strip():
                return None
                
            bank_upper = bank_name.upper().strip()
            
            # Handle bank names with commas (like "PATHWARD, NATIONAL ASSOCIATION")
            if "," in bank_name:
                parts = [part.strip() for part in bank_name.split(",")]
                # Try the full bank name first
                full_bank_name = ", ".join(parts)
                if full_bank_name.upper() in self.bank_lookup:
                    return self.bank_lookup[full_bank_name.upper()]
                
                # Try each part individually
                for part in reversed(parts):  # Start with the last part (usually the main bank name)
                    if len(part) > 2:
                        if part.upper() in self.bank_lookup:
                            return self.bank_lookup[part.upper()]
                        
                        # Try partial matches for this part
                        matches = []
                        for valid_bank in self.valid_banks:
                            valid_upper = valid_bank.upper()
                            
                            # Skip empty or special banks
                            if not valid_bank or "EMPTY" in valid_upper or "SUSPENDED" in valid_upper:
                                continue
                            
                            # Check if part is contained in valid bank name
                            if part.upper() in valid_upper:
                                matches.append(valid_bank)
                            # Check if valid bank name is contained in part
                            elif len(valid_upper) > 5 and valid_upper in part.upper():
                                matches.append(valid_bank)
                        
                        if matches:
                            return min(matches, key=len)
            
            # Try to find banks containing the input as substring
            matches = []
            for valid_bank in self.valid_banks:
                valid_upper = valid_bank.upper()
                
                # Skip empty or special banks
                if not valid_bank or "EMPTY" in valid_upper or "SUSPENDED" in valid_upper:
                    continue
                
                # Check if input is contained in valid bank name
                if bank_upper in valid_upper:
                    matches.append(valid_bank)
                # Check if valid bank name is contained in input (for longer extractions)
                elif len(valid_upper) > 5 and valid_upper in bank_upper:
                    matches.append(valid_bank)
            
            # Return the shortest match (most specific)
            if matches:
                return min(matches, key=len)
            
            # Try word-based matching for complex bank names
            bank_words = bank_upper.split()
            if len(bank_words) >= 2:
                for valid_bank in self.valid_banks:
                    valid_upper = valid_bank.upper()
                    valid_words = valid_upper.split()
                    
                    # Check if at least 2 words match
                    common_words = set(bank_words) & set(valid_words)
                    if len(common_words) >= min(2, len(bank_words)):
                        return valid_bank
            
            return None
            
        except Exception as e:
            logger.error(f"Error in bank partial matching: {e}")
            return None

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        return {
            "cache_size": len(self._validation_cache),
            "valid_data_loaded": {
                "continents": len(self.valid_continents),
                "countries": len(self.valid_countries), 
                "schemes": len(self.valid_schemes),
                "types": len(self.valid_types),
                "levels": len(self.valid_levels),
                "banks": len(self.valid_banks)
            },
            "normalized_lookups": {
                "banks": len(self.normalized_banks),
                "countries": len(self.normalized_countries),
                "schemes": len(self.normalized_schemes)
            }
        }

    # Legacy compatibility methods (simplified versions)
    def extract_from_order(self, order_data: Dict[str, Any], card_id: Optional[str] = None) -> Dict[str, Any]:
        """Legacy compatibility: extract from order data (simplified)."""
        try:
            # Check if order contains raw API data
            raw_data = order_data.get("raw_data", {})
            if raw_data and card_id:
                card = self.extract_single_card(raw_data, card_id)
                if card:
                    return card
            
            # Return basic order data if no API data available
            return {
                "_id": card_id or "",
                "price": order_data.get("price", 0.0),
                "status": order_data.get("status", "active"),
                "created_at": order_data.get("created_at"),
                "api_version": order_data.get("api_version", "v3")
            }
            
        except Exception as e:
            logger.error(f"Error in legacy extract_from_order: {e}")
            return order_data

    def extract_from_row_data(self, row: List[Any], headers: List[str]) -> Dict[str, Any]:
        """Legacy compatibility: extract from row data (simplified)."""
        try:
            # Convert old format to new format
            if len(row) >= 9 and len(headers) >= 9:
                # Try to extract using new method
                formatted_row = []
                for i, cell in enumerate(row):
                    if isinstance(cell, dict):
                        formatted_row.append(cell)
                    else:
                        formatted_row.append({"text": str(cell)})
                
                return self._extract_card_from_row(formatted_row, [{"text": h} for h in headers]) or {}
            
            return {}
            
        except Exception as e:
            logger.error(f"Error in legacy extract_from_row_data: {e}")
            return {}


# Global instance for easy access
_automatic_extractor: Optional[AutomaticCardDataExtractor] = None


def get_optimized_extractor() -> AutomaticCardDataExtractor:
    """Get the global automatic card data extractor instance."""
    global _automatic_extractor
    if _automatic_extractor is None:
        _automatic_extractor = AutomaticCardDataExtractor()
    return _automatic_extractor


# Legacy compatibility
def get_card_data_extractor() -> AutomaticCardDataExtractor:
    """Legacy compatibility function."""
    return get_optimized_extractor()


# Main convenience functions
def extract_cards_from_api(api_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convenience function to extract all cards from API response.
    
    Args:
        api_data: Complete API response data
        
    Returns:
        List of extracted and validated card data
    """
    extractor = get_optimized_extractor()
    cards = extractor.extract_from_api_response(api_data)
    
    # Validate each card
    validated_cards = []
    for card in cards:
        validated_card = extractor.validate_card_data(card)
        validated_cards.append(validated_card)
    
    return validated_cards


def extract_single_card_from_api(api_data: Dict[str, Any], card_id: str) -> Optional[Dict[str, Any]]:
    """
    Convenience function to extract specific card from API response.
    
    Args:
        api_data: Complete API response data
        card_id: Target card ID
        
    Returns:
        Extracted and validated card data or None
    """
    extractor = get_optimized_extractor()
    card = extractor.extract_single_card(api_data, card_id)
    
    if card:
        return extractor.validate_card_data(card)
    
    return None


# Legacy compatibility functions
def extract_card_data_from_order(order_data: Dict[str, Any], card_id: Optional[str] = None) -> Dict[str, Any]:
    """Legacy compatibility function."""
    return get_optimized_extractor().extract_from_order(order_data, card_id)


def extract_card_data_from_api_response(raw_data: Dict[str, Any], card_id: Optional[str] = None) -> Dict[str, Any]:
    """Legacy compatibility function."""
    if card_id:
        return extract_single_card_from_api(raw_data, card_id) or {}
    else:
        cards = extract_cards_from_api(raw_data)
        return cards[0] if cards else {}


def parse_card_text(card_text: str) -> Dict[str, Any]:
    """Legacy compatibility function (simplified)."""
    # Basic parsing for legacy support
    result = {}
    if not card_text:
        return result
    
    # Extract basic card info
    parts = card_text.split(",")
    if len(parts) >= 1:
        result["card_info"] = parts[0].strip()
    if len(parts) >= 2:
        result["expiry"] = parts[1].strip()
    
    return result


def parse_bin_info(bin_text: str) -> Dict[str, Any]:
    """Legacy compatibility function (simplified)."""
    # Basic parsing for legacy support
    result = {}
    if not bin_text:
        return result
    
    text_upper = bin_text.upper()
    
    # Extract brand
    if "MASTERCARD" in text_upper:
        result["brand"] = "MASTERCARD"
    elif "VISA" in text_upper:
        result["brand"] = "VISA"
    elif "AMEX" in text_upper:
        result["brand"] = "AMERICAN EXPRESS"
    
    return result


def parse_contact_info(contact_text: str) -> Dict[str, Any]:
    """Legacy compatibility function (simplified)."""
    # Basic parsing for legacy support
    result = {}
    if not contact_text:
        return result
    
    if "Phone" in contact_text:
        result["phone"] = "Available"
    if "address" in contact_text:
        result["address"] = "Available"
    
    return result

    def extract_from_legacy_response(self, raw_data: Dict[str, Any], card_id: str) -> Dict[str, Any]:
        """Extract card data from legacy API response formats."""
        try:
            logger.debug(f"Extracting from legacy response format for card {card_id}")
            
            # If raw_data contains direct card fields, use them
            if raw_data.get("card_number") or raw_data.get("cc"):
                logger.debug("Found direct card fields in legacy response")
                return {
                    "card_id": card_id,
                    "card_number": raw_data.get("card_number") or raw_data.get("cc", ""),
                    "expiry": raw_data.get("expiry") or raw_data.get("exp", ""),
                    "cvv": raw_data.get("cvv", ""),
                    "cardholder_name": raw_data.get("name") or raw_data.get("cardholder_name", ""),
                    "bank": raw_data.get("bank", "Unknown Bank"),
                    "brand": raw_data.get("brand", ""),
                    "country": raw_data.get("country", ""),
                    "type": raw_data.get("type", ""),
                    "level": raw_data.get("level", ""),
                    "phone": raw_data.get("phone", ""),
                    "address": raw_data.get("address", ""),
                    "api_version": "v3"
                }
            
            # Try to extract from text content
            if isinstance(raw_data, dict):
                for key, value in raw_data.items():
                    if isinstance(value, str) and len(value) > 10:
                        # Look for card-like patterns in text
                        if any(pattern in value for pattern in ["Card Number", "CC:", "Expiry:", "CVV:"]):
                            logger.debug(f"Found card data in text field: {key}")
                            return self._extract_from_text_content(value, card_id)
            
            logger.warning(f"No extractable data found in legacy response for card {card_id}")
            return {
                "card_id": card_id,
                "bank": "Unknown Bank",
                "api_version": "v3"
            }
            
        except Exception as e:
            logger.error(f"Error extracting from legacy response: {e}")
            return {
                "card_id": card_id,
                "bank": "Unknown Bank",
                "api_version": "v3"
            }
    
    def _extract_from_text_content(self, text_content: str, card_id: str) -> Dict[str, Any]:
        """Extract card data from text content."""
        try:
            import re
            
            result = {
                "card_id": card_id,
                "api_version": "v3"
            }
            
            # Extract card number
            cc_match = re.search(r"(?:Card Number|CC):\s*([0-9\s]+)", text_content)
            if cc_match:
                result["card_number"] = cc_match.group(1).strip()
            
            # Extract expiry
            exp_match = re.search(r"Expiry:\s*([0-9/]+)", text_content)
            if exp_match:
                result["expiry"] = exp_match.group(1).strip()
            
            # Extract CVV
            cvv_match = re.search(r"CVV:\s*([0-9]+)", text_content)
            if cvv_match:
                result["cvv"] = cvv_match.group(1).strip()
            
            # Extract name
            name_match = re.search(r"Name:\s*([^\n]+)", text_content)
            if name_match:
                result["cardholder_name"] = name_match.group(1).strip()
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting from text content: {e}")
            return {
                "card_id": card_id,
                "bank": "Unknown Bank",
                "api_version": "v3"
            }
    
    def extract_from_order(self, order_data: Dict[str, Any], card_id: str = None) -> Dict[str, Any]:
        """Extract card data from order data structure."""
        try:
            logger.debug(f"Extracting card data from order for card {card_id}")
            
            # Check for raw_data containing API response
            raw_data = order_data.get("raw_data", {})
            if raw_data and "sections" in raw_data:
                logger.debug("Found raw_data with sections in order")
                extracted_cards = self.extract_from_api_response(raw_data)
                if extracted_cards:
                    # Find matching card or return first one
                    for card in extracted_cards:
                        if not card_id or str(card.get("card_id", "")) == str(card_id):
                            return card
                    return extracted_cards[0]
            
            # Check for unmasked_data
            unmasked_data = order_data.get("unmasked_data", {})
            if unmasked_data:
                logger.debug("Found unmasked_data in order")
                return self._normalize_order_card_data(unmasked_data, card_id)
            
            # Check metadata card_data
            card_data = order_data.get("metadata", {}).get("card_data", {})
            if card_data:
                logger.debug("Found card_data in order metadata")
                return self._normalize_order_card_data(card_data, card_id)
            
            # Extract basic fields from order
            return self._extract_basic_order_data(order_data, card_id)
            
        except Exception as e:
            logger.error(f"Error extracting from order: {e}")
            return self._get_fallback_card_data(card_id)
    
    def normalize_and_validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize and validate card data."""
        try:
            if not data:
                return data
            
            # Normalize field names
            normalized = {}
            
            # Map common field variations
            field_mapping = {
                "cc": "card_number",
                "full_card_number": "card_number",
                "exp": "expiry",
                "expiry_date": "expiry",
                "name": "cardholder_name",
                "cardholder": "cardholder_name",
                "scheme": "brand",
                "card_type": "type"
            }
            
            # Apply field mapping
            for key, value in data.items():
                mapped_key = field_mapping.get(key, key)
                normalized[mapped_key] = value
            
            # Ensure required fields exist
            if "bank" not in normalized:
                normalized["bank"] = "Unknown Bank"
            
            if "api_version" not in normalized:
                normalized["api_version"] = "v3"
            
            # Validate against filter data if available
            if normalized.get("country") and self.valid_countries:
                country = normalized["country"].upper()
                if country not in [c.upper() for c in self.valid_countries]:
                    logger.debug(f"Country validation failed: {country}")
            
            return normalized
            
        except Exception as e:
            logger.error(f"Error normalizing data: {e}")
            return data
    
    def extract_from_row_data(self, row: List[Any], headers: List[str]) -> Dict[str, Any]:
        """Extract card data from row and headers - optimized for performance."""
        try:
            if not row or not headers or len(row) == 0:
                return {}
            
            # Skip conversion to dict format - work directly with data
            result = {}
            
            # Fast header mapping
            for i, header in enumerate(headers):
                if i >= len(row):
                    break
                    
                # Get header text
                header_text = header.get("text", "") if isinstance(header, dict) else str(header)
                if not header_text:
                    continue
                
                # Get cell data
                cell = row[i]
                if isinstance(cell, dict):
                    text = cell.get("text", "")
                    if "input_value" in cell:
                        result["_id"] = cell["input_value"]
                        result["card_hash"] = cell["input_value"]
                else:
                    text = str(cell)
                
                if not text:
                    continue
                
                # Fast field mapping using header text
                header_lower = header_text.lower()
                if "bin" in header_lower:
                    result["bin"] = text
                    result["card_number"] = text
                elif "expiry" in header_lower or "exp" in header_lower:
                    result["expiry"] = text
                    result["exp"] = text
                elif "name" in header_lower and "f." in header_lower:
                    result["name"] = text
                    result["cardholder"] = text
                elif "country" in header_lower:
                    result["country"] = text.upper()
                elif "scheme" in header_lower or "type" in header_lower:
                    result["scheme_type_level"] = text
                    # Quick brand detection
                    text_upper = text.upper()
                    if "VISA" in text_upper:
                        result["brand"] = "VISA"
                    elif "MASTERCARD" in text_upper:
                        result["brand"] = "MASTERCARD"
                elif "address" in header_lower or "phone" in header_lower:
                    result["address_phone_dob"] = text
                elif "price" in header_lower:
                    result["price_text"] = text
                    # Quick price extraction
                    import re
                    price_match = PRICE_PATTERN.search(text)
                    if price_match:
                        try:
                            result["price"] = float(price_match.group(1))
                        except ValueError:
                            result["price"] = 0.0
            
            # Ensure essential fields
            if not result.get("_id") and result.get("bin"):
                result["_id"] = result["bin"]
            if not result.get("bin") and result.get("_id"):
                result["bin"] = result["_id"]
            if not result.get("name"):
                result["name"] = result.get("country", "Unknown")
            if "price" not in result:
                result["price"] = 0.0
                
            return result if result.get("_id") or result.get("bin") else {}
            
        except Exception as e:
            logger.debug(f"Error extracting from row data: {e}")
            return {}
    
    def parse_card_text(self, card_text: str) -> Dict[str, Any]:
        """Parse card text in format: 'card_number, expiry, cvv status'."""
        try:
            result = {}
            if not card_text:
                return result
            
            # Split by commas
            parts = [part.strip() for part in card_text.split(",")]
            
            if len(parts) >= 1 and parts[0]:
                # Card number
                card_num = parts[0].strip()
                if "*" in card_num:
                    result["partial_card_number"] = card_num
                else:
                    result["card_number"] = card_num
            
            if len(parts) >= 2 and parts[1]:
                # Expiry
                result["expiry"] = parts[1].strip()
            
            if len(parts) >= 3 and parts[2]:
                # CVV and status
                cvv_status = parts[2].strip()
                # Extract CVV (first 3-4 digits)
                import re
                cvv_match = re.search(r"(\d{3,4})", cvv_status)
                if cvv_match:
                    result["cvv"] = cvv_match.group(1)
                
                # Extract status
                if "Refunded" in cvv_status:
                    result["status"] = "Refunded"
                    result["refund_status"] = "refunded"
                    # Extract refund reason
                    reason_match = re.search(r"reason:\s*([^,]+)", cvv_status, re.IGNORECASE)
                    if reason_match:
                        result["refund_reason"] = reason_match.group(1).strip()
                    # Extract additional details like CVV failure
                    if "Auth-Failed" in cvv_status or "CVV" in cvv_status:
                        result["decline_reason"] = "CVV Auth Failed"
                elif "Live" in cvv_status:
                    result["status"] = "Live"
                elif "Dead" in cvv_status:
                    result["status"] = "Dead"
                elif "Check" in cvv_status:
                    result["status"] = "Check Available"
                elif "Declined" in cvv_status:
                    result["status"] = "Declined"
                    # Extract decline reason
                    reason_match = re.search(r"reason:\s*([^,]+)", cvv_status, re.IGNORECASE)
                    if reason_match:
                        result["decline_reason"] = reason_match.group(1).strip()
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing card text: {e}")
            return {}
    
    def parse_bin_info(self, bin_text: str) -> Dict[str, Any]:
        """Parse BIN information text."""
        try:
            result = {}
            if not bin_text:
                return result
            
            text_upper = bin_text.upper()
            
            # Extract country (usually first)
            words = text_upper.split()
            if words:
                potential_country = words[0].replace(",", "")
                if len(potential_country) > 2:
                    result["country"] = potential_country.title()
            
            # Extract brand
            if "MASTERCARD" in text_upper:
                result["brand"] = "MASTERCARD"
            elif "VISA" in text_upper:
                result["brand"] = "VISA"
            elif "AMEX" in text_upper or "AMERICAN EXPRESS" in text_upper:
                result["brand"] = "AMERICAN EXPRESS"
            
            # Extract type
            if "DEBIT" in text_upper:
                result["type"] = "DEBIT"
            elif "CREDIT" in text_upper:
                result["type"] = "CREDIT"
            elif "PREPAID" in text_upper:
                result["type"] = "PREPAID"
            
            # Extract bank (usually at the end)
            if "," in bin_text:
                parts = bin_text.split(",")
                bank_candidate = parts[-1].strip()
                if len(bank_candidate) > 3:
                    result["bank"] = bank_candidate.title()
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing BIN info: {e}")
            return {}
    
    def parse_contact_info(self, contact_text: str) -> Dict[str, Any]:
        """Parse contact information text."""
        try:
            result = {}
            if not contact_text:
                return result
            
            # Extract phone
            import re
            phone_match = re.search(r"Phone\s*:\s*([+\d\s\-\.]+)", contact_text)
            if phone_match:
                result["phone"] = phone_match.group(1).strip()
            
            # Extract address
            if "No address" in contact_text:
                result["address"] = ""  # Set as blank instead of "No address"
            else:
                # Address is usually before phone
                if "Phone :" in contact_text:
                    address_part = contact_text.split("Phone :")[0].strip()
                    if address_part:
                        result["address"] = address_part.rstrip(",").strip()
            
            # Check DOB
            if "DOB: YES" in contact_text:
                result["dob_available"] = True
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing contact info: {e}")
            return {}
    
    def is_unmask_data(self, order_data: Dict[str, Any]) -> bool:
        """Check if order contains unmasked card data."""
        try:
            # Check for unmasked_data field
            if order_data.get("unmasked_data"):
                return True
            
            # Check for is_unmasked flag
            if order_data.get("is_unmasked"):
                return True
            
            # Check extracted cards for unmasked data
            extracted_cards = order_data.get("extracted_cards", [])
            if extracted_cards:
                for card in extracted_cards:
                    # Check card_number_status first (most reliable)
                    if card.get("card_number_status") == "unmasked":
                        return True
                    
                    # Check if card has full card number (unmasked)
                    card_number = card.get("card_number", "")
                    if card_number and len(card_number.replace(" ", "")) > 10 and "*" not in card_number and "[PAN_REDACTED]" not in card_number:
                        return True
                    
                    # Check for CVV
                    if card.get("cvv"):
                        return True
            
            # Check for full card numbers in metadata (legacy)
            card_data = order_data.get("metadata", {}).get("card_data", {})
            cc = card_data.get("cc") or card_data.get("card_number", "")
            if cc and len(cc.replace(" ", "")) > 10 and "*" not in cc and "[PAN_REDACTED]" not in cc:
                return True
            
            # Check for CVV in metadata (legacy)
            if card_data.get("cvv"):
                return True
            
            # Check order_metadata for extracted cards (new storage location)
            order_metadata = order_data.get("order_metadata", {})
            metadata_extracted = order_metadata.get("extracted_cards", [])
            if metadata_extracted:
                for card in metadata_extracted:
                    # Check card_number_status first (most reliable)
                    if card.get("card_number_status") == "unmasked":
                        return True
                    
                    card_number = card.get("card_number", "")
                    if card_number and len(card_number.replace(" ", "")) > 10 and "*" not in card_number and "[PAN_REDACTED]" not in card_number:
                        return True
                    if card.get("cvv"):
                        return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Error checking unmask data: {e}")
            return False
    
    def _normalize_order_card_data(self, card_data: Dict[str, Any], card_id: str = None) -> Dict[str, Any]:
        """Normalize card data from order."""
        normalized = self.normalize_and_validate_data(card_data)
        if card_id:
            normalized["card_id"] = card_id
        return normalized
    
    def _extract_basic_order_data(self, order_data: Dict[str, Any], card_id: str = None) -> Dict[str, Any]:
        """Extract basic card data from order structure."""
        try:
            result = {
                "card_id": card_id or order_data.get("external_product_id", ""),
                "bank": "Unknown Bank",
                "api_version": order_data.get("api_version", "v3"),
                "status": order_data.get("status", "active"),
                "price": order_data.get("price", 0.0)
            }
            
            # Try to extract from direct fields
            for field in ["cc", "card_number", "expiry", "cvv", "name", "bank", "brand", "country"]:
                if field in order_data:
                    result[field] = order_data[field]
            
            return self.normalize_and_validate_data(result)
            
        except Exception as e:
            logger.error(f"Error extracting basic order data: {e}")
            return self._get_fallback_card_data(card_id)
    
    def _get_fallback_card_data(self, card_id: str = None) -> Dict[str, Any]:
        """Get fallback card data when extraction fails."""
        return {
            "card_id": card_id or "unknown",
            "bank": "Unknown Bank",
            "brand": "",
            "country": "",
            "api_version": "v3",
            "_extraction_failed": True
        }


# Global instance
_card_data_extractor = None


def get_card_data_extractor() -> AutomaticCardDataExtractor:
    """Get the global card data extractor instance."""
    global _card_data_extractor
    if _card_data_extractor is None:
        _card_data_extractor = AutomaticCardDataExtractor()
    return _card_data_extractor


# COMPREHENSIVE EXTRACTION METHODS FOR ALL ENDPOINTS
# These methods handle all extraction scenarios previously scattered across different files

def extract_from_table_format(headers: list, rows: list, card_id: str = None) -> Dict[str, Any]:
    """
    Extract card data from table format (headers + rows).
    CENTRALIZED method to replace all scattered table extraction logic.
    """
    try:
        extractor = get_card_data_extractor()
        logger.debug(f"Extracting from table format for card {card_id}")
        
        # Find matching row by card ID if provided
        target_row = None
        if card_id:
            for row in rows:
                if isinstance(row, list) and len(row) > 0:
                    first_cell = row[0]
                    if isinstance(first_cell, dict):
                        row_card_id = first_cell.get("input_value", "") or first_cell.get("text", "")
                        if str(row_card_id) == str(card_id):
                            target_row = row
                            break
                    elif isinstance(first_cell, str) and str(first_cell) == str(card_id):
                        target_row = row
                        break
        
        # Use first row if no specific card ID or single row response
        if not target_row and rows:
            target_row = rows[0]
        
        if not target_row:
            logger.warning(f"No matching row found for card {card_id}")
            return {}
        
        # Extract using centralized method
        return extractor.extract_from_row_data(target_row, headers)
        
    except Exception as e:
        logger.error(f"Error extracting from table format: {e}")
        return {}


def extract_from_content_text(content: str, card_id: str = None) -> Dict[str, Any]:
    """
    Extract card data from text content.
    CENTRALIZED method to replace all scattered text extraction logic.
    """
    try:
        extractor = get_card_data_extractor()
        logger.debug(f"Extracting from content text for card {card_id}")
        
        result = {
            "card_id": card_id or "unknown",
            "api_version": "v3"
        }
        
        # Use extractor's text parsing capabilities
        if hasattr(extractor, '_extract_from_text_content'):
            extracted = extractor._extract_from_text_content(content, card_id or "unknown")
            result.update(extracted)
        else:
            # Basic text extraction patterns
            import re
            
            # Extract card number patterns
            cc_patterns = [
                r"(?:Card Number|CC|Card):\s*([0-9\s\*]{10,})",
                r"([0-9]{4}[\s\*]*[0-9]{4}[\s\*]*[0-9]{4}[\s\*]*[0-9]{4})",
                r"BIN:\s*([0-9]{4,8})"
            ]
            
            for pattern in cc_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    result["card_number"] = match.group(1).strip()
                    result["bin"] = match.group(1).strip()[:6]
                    break
            
            # Extract expiry
            exp_match = re.search(r"(?:Expiry|Exp):\s*([0-9]{2}/[0-9]{2,4})", content, re.IGNORECASE)
            if exp_match:
                result["expiry"] = exp_match.group(1).strip()
            
            # Extract CVV
            cvv_match = re.search(r"CVV:\s*([0-9]{3,4})", content, re.IGNORECASE)
            if cvv_match:
                result["cvv"] = cvv_match.group(1).strip()
        
        # Validate and normalize using extractor
        if hasattr(extractor, 'normalize_and_validate_data'):
            return extractor.normalize_and_validate_data(result)
        else:
            return result
        
    except Exception as e:
        logger.error(f"Error extracting from content text: {e}")
        return {"card_id": card_id or "unknown", "api_version": "v3"}


def extract_from_sections_response(sections_data: Dict[str, Any], card_id: str = None) -> Dict[str, Any]:
    """
    Extract card data from API v3 sections format response.
    CENTRALIZED method to replace all scattered sections extraction logic.
    """
    try:
        extractor = get_card_data_extractor()
        logger.debug(f"Extracting from sections response for card {card_id}")
        
        # Use extractor's API response method
        all_cards = extractor.extract_from_api_response(sections_data)
        
        if not all_cards:
            return {}
        
        # Find specific card or return first one
        if card_id:
            for card in all_cards:
                if str(card.get("_id", "")) == str(card_id) or str(card.get("card_id", "")) == str(card_id):
                    return card
        
        return all_cards[0] if all_cards else {}
        
    except Exception as e:
        logger.error(f"Error extracting from sections response: {e}")
        return {}


def extract_from_table_format(headers: List[str], rows: List[List[Dict[str, Any]]], card_id: str = None) -> Dict[str, Any]:
    """Extract card data from table format (headers + rows)."""
    try:
        extractor = get_card_data_extractor()
        logger.debug(f"Extracting from table format for card {card_id}")
        
        if not rows:
            return {}
        
        # Use the first row for extraction
        first_row = rows[0]
        
        # Extract card data using basic extraction logic
        result = {
            "card_id": card_id or "unknown",
            "api_version": "v3"
        }
        
        # Extract basic fields from row cells
        if len(first_row) >= 6:
            # Cell 0: checkbox with card ID
            if first_row[0].get("input_value"):
                result["_id"] = first_row[0]["input_value"]
                result["card_id"] = first_row[0]["input_value"]
            
            # Cell 1: card number and expiry
            bin_text = first_row[1].get("text", "")
            if bin_text:
                # Extract card number and expiry
                if "," in bin_text:
                    parts = bin_text.split(",")
                    result["card_number"] = parts[0].strip()
                    if len(parts) > 1:
                        result["expiry"] = parts[1].strip().replace("-", "")
                else:
                    result["card_number"] = bin_text.strip()
            
            # Cell 2: base/scheme info
            base_text = first_row[2].get("text", "")
            if base_text:
                result["base"] = base_text
            
            # Cell 4: country, continent, and bank info
            country_text = first_row[4].get("text", "")
            if country_text:
                # Parse country info (this will also extract bank info)
                extractor._parse_country_info_from_text(result, country_text)
        
        return result
        
    except Exception as e:
        logger.error(f"Error extracting from table format: {e}")
        return {
            "card_id": card_id or "unknown",
            "api_version": "v3",
            "_extraction_error": str(e)
        }


def extract_comprehensive_card_data(api_response: Dict[str, Any], card_id: str = None, endpoint: str = None) -> Dict[str, Any]:
    """
    MASTER extraction method that handles ALL response formats.
    This replaces ALL scattered extraction logic across the codebase.
    
    Handles:
    - API v1/v2/v3 responses
    - Table formats (headers + rows)
    - Sections formats 
    - Text content
    - Order data
    - Legacy formats
    """
    try:
        extractor = get_card_data_extractor()
        endpoint_info = f" from {endpoint}" if endpoint else ""
        logger.debug(f"Comprehensive extraction for card {card_id}{endpoint_info}")
        
        # Check for pre-extracted cards first
        if "extracted_cards" in api_response and api_response["extracted_cards"]:
            cards = api_response["extracted_cards"]
            if card_id:
                for card in cards:
                    if str(card.get("_id", "")) == str(card_id) or str(card.get("card_id", "")) == str(card_id):
                        return card
            return cards[0] if cards else {}
        
        # Handle sections format (API v3)
        if "sections" in api_response:
            return extract_from_sections_response(api_response, card_id)
        
        # Handle direct headers/rows format
        elif "headers" in api_response and "rows" in api_response:
            headers = api_response["headers"]
            rows = api_response["rows"]
            return extract_from_table_format(headers, rows, card_id)
        
        # Handle order data format
        elif "raw_data" in api_response:
            return extractor.extract_from_order(api_response, card_id)
        
        # Handle text content
        elif isinstance(api_response, dict):
            for key, value in api_response.items():
                if isinstance(value, str) and len(value) > 50:
                    # Look for card-like content
                    if any(pattern in value.lower() for pattern in ["card", "bin", "expiry", "cvv"]):
                        return extract_from_content_text(value, card_id)
        
        # Use extractor's main method as fallback
        cards = extractor.extract_from_api_response(api_response)
        
        # If we have a specific card_id, find it
        if card_id and cards:
            for card in cards:
                if str(card.get("_id", "")) == str(card_id) or str(card.get("card_id", "")) == str(card_id):
                    return card
        
        # Return first card if available, or empty dict
        return cards[0] if cards else {}
        
    except Exception as e:
        logger.error(f"Error in comprehensive card extraction: {e}")
        return {
            "card_id": card_id or "unknown",
            "bank": "Unknown Bank",
            "api_version": "v3",
            "_extraction_error": str(e)
        }


# Legacy compatibility functions for existing code
def extract_cards_from_api(api_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Legacy function for extracting cards from API data."""
    return get_card_data_extractor().extract_from_api_response(api_data)


def extract_single_card_from_api(api_data: Dict[str, Any], card_id: str) -> Dict[str, Any]:
    """Legacy function for extracting a single card from API data."""
    cards = extract_cards_from_api(api_data)
    for card in cards:
        if str(card.get("card_id", "")) == str(card_id):
            return card
    return cards[0] if cards else {}


# Legacy parsing functions
def parse_card_text(card_text: str) -> Dict[str, Any]:
    """Legacy compatibility function."""
    return get_card_data_extractor().parse_card_text(card_text)


def parse_bin_info(bin_text: str) -> Dict[str, Any]:
    """Legacy compatibility function."""
    return get_card_data_extractor().parse_bin_info(bin_text)


def parse_contact_info(contact_text: str) -> Dict[str, Any]:
    """Legacy compatibility function."""
    return get_card_data_extractor().parse_contact_info(contact_text)