"""
API v3 Session Manager

Handles session persistence and validation for API v3.
Adapted from demo/api3_demo/session_manager.py
"""

from __future__ import annotations

import json
import os
import time
from pathlib import Path
from typing import Optional
from urllib.parse import urljoin

import requests

from .login import (
    LoginSession,
    cookies_to_serializable,
    load_cookies_into_jar,
    refresh_xsrf_headers_from_cookies,
    prune_cookie_duplicates,
)

from utils.central_logger import get_logger

logger = get_logger()

# Session validation cache time (30 minutes for better session reuse)
FAST_VALIDATION_CACHE_TIME = 1800  # 30 minutes

# Global session cache (shared across all service instances)
_cached_sessions: dict[str, requests.Session] = {}
_cached_session_times: dict[str, float] = {}

# Global validation cache
_last_validation_time = 0
_last_validation_result = False


def _check_basic_connectivity(base_url: str, use_socks_proxy: bool = False, socks_url: str = "socks5h://127.0.0.1:9150") -> bool:
    """
    Perform a basic connectivity check to see if we can reach the target.
    Returns True if basic connectivity works, False otherwise.
    """
    try:
        logger.info("🔍 Performing basic connectivity check...")
        
        # Create a minimal session for testing
        test_session = requests.Session()
        
        if use_socks_proxy:
            test_session.proxies.update({"http": socks_url, "https": socks_url})
            test_session.trust_env = False
        
        # Try to connect to the root URL with a very short timeout
        test_url = base_url.rstrip("/") + "/"
        response = test_session.get(test_url, timeout=10)
        
        # Any response (even 404) means connectivity is working
        logger.info(f"✅ Basic connectivity OK (status: {response.status_code})")
        return True
        
    except requests.exceptions.ConnectTimeout:
        logger.warning("❌ Connectivity check: Connection timeout")
        return False
    except requests.exceptions.ConnectionError as e:
        logger.warning(f"❌ Connectivity check: Connection error - {e}")
        return False
    except Exception as e:
        logger.warning(f"❌ Connectivity check failed: {e}")
        return False
    finally:
        try:
            test_session.close()
        except:
            pass


def get_session_cookies_path(base_url: str) -> str:
    """Get session cookies file path for a specific base URL"""
    # Create a safe filename from the base URL
    safe_name = base_url.replace("://", "_").replace("/", "_").replace(".", "_")
    storage_dir = Path("storage/api_v3")
    storage_dir.mkdir(parents=True, exist_ok=True)
    return str(storage_dir / f"session_{safe_name}.json")


def save_session_cookies(
    session: requests.Session,
    base_url: str,
    path: Optional[str] = None,
) -> Optional[str]:
    """Persist current session cookies to JSON for reuse"""
    global _last_validation_time
    try:
        out_path = path or get_session_cookies_path(base_url)
        data = {
            "cookies": cookies_to_serializable(session.cookies),
            "saved_at": time.time(),
            "validated_at": _last_validation_time,
            "base_url": base_url,
        }
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info("Saved session cookies to: %s", out_path)
        return out_path
    except Exception as e:
        logger.warning("Failed to save session cookies: %s", e)
        return None


def load_session_cookies(
    session: requests.Session,
    base_url: str,
    path: Optional[str] = None,
) -> int:
    """Load cookies from JSON into the session. Returns number of cookies loaded."""
    global _last_validation_time
    p = path or get_session_cookies_path(base_url)
    try:
        if not os.path.exists(p):
            return 0

        with open(p, "r", encoding="utf-8") as f:
            data = json.load(f) or {}

        items = data.get("cookies") or []

        # Check if session data is recent (with enhanced optimization)
        saved_at = data.get("saved_at", 0)
        current_time = time.time()
        session_age = current_time - saved_at
        
        # Enhanced performance optimization: Trust very recent sessions
        # Skip validation if session is less than 30 minutes old and has validated_at
        validated_at = data.get("validated_at", 0)
        if (session_age < 1800 and  # 30 minutes
            validated_at > 0 and 
            (current_time - validated_at) < 1800):
            logger.info(f"⚡ FAST LOAD: Using very recent session (age: {int(session_age)}s, last validated: {int(current_time - validated_at)}s ago)")
            _last_validation_time = validated_at
            _last_validation_result = True  # Trust recent validation
        
        # Check cookie expiry
        cookies_valid = True
        for cookie_data in items:
            expires = cookie_data.get("expires")
            if expires and expires < current_time:
                cookies_valid = False
                logger.warning(f"Cookie {cookie_data.get('name')} expired at {expires}")
                break
        
        if not cookies_valid:
            logger.warning("Session cookies expired, will need fresh login")
            return 0
            
        if current_time - saved_at < 14400:  # 4 hours
            _last_validation_time = data.get("validated_at", 0)
            logger.info(
                f"Loading recent session (saved {int(current_time - saved_at)}s ago)"
            )

        loaded = load_cookies_into_jar(session.cookies, items)
        if loaded:
            refresh_xsrf_headers_from_cookies(session)
            referer = base_url.rstrip("/") + "/"
            prune_cookie_duplicates(session.cookies, "XSRF-TOKEN", base_url=referer)
            prune_cookie_duplicates(session.cookies, "bbm_session", base_url=referer)

        return loaded
    except Exception as e:
        logger.warning("Failed to load session cookies: %s", e)
        return 0


def is_unauthenticated(resp: requests.Response) -> bool:
    """Check if a response indicates that the user is not authenticated"""
    try:
        url = getattr(resp, "url", "") or ""
        if "/login" in url:
            return True
        if resp.status_code in (401, 403):
            return True
        html = resp.text or ""
        # Heuristic: presence of login form fields
        if ('name="username"' in html and 'name="passwd"' in html) or (
            'name="password"' in html
        ):
            return True
    except Exception:
        pass
    return False


def validate_session(
    session: requests.Session,
    base_url: str,
    use_socks_proxy: bool = False,
    socks_url: str = "socks5h://127.0.0.1:9150",
    force_check: bool = False,
) -> bool:
    """
    Session validation with cookie expiry checking and caching.
    """
    global _last_validation_time, _last_validation_result
    current_time = time.time()

    # First check if essential cookies exist and are not expired
    if not force_check:
        has_session_cookie = any(cookie.name == "bbm_session" for cookie in session.cookies)
        has_xsrf_cookie = any(cookie.name == "XSRF-TOKEN" for cookie in session.cookies)
        
        if not has_session_cookie or not has_xsrf_cookie:
            logger.debug("❌ Missing essential cookies, session invalid")
            return False
            
        # Check cookie expiry
        for cookie in session.cookies:
            if cookie.name in ("bbm_session", "XSRF-TOKEN") and cookie.expires:
                if cookie.expires < current_time:
                    logger.debug(f"❌ Cookie {cookie.name} expired, session invalid")
                    return False

    # Enhanced validation cache with cookie-based optimization
    if (
        not force_check
        and (current_time - _last_validation_time) < FAST_VALIDATION_CACHE_TIME
        and _last_validation_result
    ):
        # Additional optimization: if cookies haven't changed and cache is still valid, skip validation
        logger.debug(f"✅ Using validation cache (age: {int(current_time - _last_validation_time)}s, still valid)")
        return _last_validation_result

    logger.info("Performing fresh session validation...")
    try:
        # Ensure proxy settings are applied
        if use_socks_proxy and not session.proxies:
            session.proxies.update({"http": socks_url, "https": socks_url})
            logger.info(f"Applied proxy settings for validation: {socks_url}")

        # Make a lightweight request to check authentication
        # Use longer timeout for .onion domains which can be slower
        timeout = 30 if ".onion" in base_url else 15
        shop_url = urljoin(base_url.rstrip("/") + "/", "shop")
        resp = session.get(shop_url, allow_redirects=True, timeout=timeout)

        # Check if the response indicates we are unauthenticated
        is_valid = not is_unauthenticated(resp)

        # Cache the result globally
        _last_validation_time = current_time
        _last_validation_result = is_valid

        if is_valid:
            logger.info("✅ Session is valid")
        else:
            logger.warning("❌ Session validation failed")

        return is_valid
    except requests.exceptions.ConnectTimeout as e:
        logger.warning("Session validation timed out (connection): %s", e)
        # For connection timeouts, be more conservative about invalidating sessions
        # If we have recent cookies and this is just a timeout, assume session is still valid
        has_recent_cookies = any(
            cookie.name in ("bbm_session", "XSRF-TOKEN") 
            for cookie in session.cookies
        )
        if has_recent_cookies:
            logger.info("Assuming session is still valid due to recent cookies despite timeout")
            return True
        return False
    except requests.exceptions.ReadTimeout as e:
        logger.warning("Session validation timed out (read): %s", e)
        # Similar to connection timeout
        has_recent_cookies = any(
            cookie.name in ("bbm_session", "XSRF-TOKEN") 
            for cookie in session.cookies
        )
        if has_recent_cookies:
            logger.info("Assuming session is still valid due to recent cookies despite timeout")
            return True
        return False
    except requests.RequestException as e:
        logger.error("Session validation failed due to network error: %s", e)
        # For other network errors, also check if we have valid cookies
        has_recent_cookies = any(
            cookie.name in ("bbm_session", "XSRF-TOKEN") 
            for cookie in session.cookies
        )
        if has_recent_cookies:
            logger.info("Assuming session is still valid due to recent cookies despite network error")
            return True
        return False


def get_authenticated_session(
    base_url: str,
    username: str,
    password: str,
    use_socks_proxy: bool = False,
    socks_url: str = "socks5h://127.0.0.1:9150",
) -> requests.Session:
    """
    Provides a guaranteed authenticated session with ultra-optimized caching.
    Uses aggressive caching and minimal validation to maximize performance.
    """
    global _last_validation_time, _last_validation_result, _cached_sessions, _cached_session_times

    # Create cache key from base_url + username
    cache_key = f"{base_url}:{username}"

    # Check if we have a cached session that's still valid
    if cache_key in _cached_sessions:
        cached_session = _cached_sessions[cache_key]
        cache_time = _cached_session_times.get(cache_key, 0)
        current_time = time.time()
        age_seconds = current_time - cache_time

        # Optimized cache time - extended validity with intelligent validation
        if age_seconds < 21600:  # 6 hours extended cache
            logger.debug(f"🔄 Reusing cached session (age: {int(age_seconds)}s)")

            # Skip validation for very recent sessions (2 hours)
            if age_seconds < 7200:  # 2 hours
                logger.debug("✅ Session is very recent, skipping validation")
                return cached_session
            
            # Lightweight validation for moderately old sessions (2-4 hours)
            elif age_seconds < 14400:  # 4 hours
                logger.debug("🔍 Performing lightweight validation")
                # Use cached validation result if recent
                global _last_validation_time, _last_validation_result
                if (current_time - _last_validation_time < 1800 and  # 30 min cache
                    _last_validation_result):
                    logger.debug("✅ Using cached validation result")
                    return cached_session
                    
            # Full validation for older sessions
            if validate_session(cached_session, base_url, use_socks_proxy, socks_url):
                logger.info("✅ Cached session validated successfully")
                # Update cache time to extend validity
                _cached_session_times[cache_key] = current_time
                _last_validation_time = current_time
                _last_validation_result = True
                return cached_session
            else:
                logger.info("❌ Cached session expired, will re-authenticate")
                cached_session.cookies.clear()
                _last_validation_result = False
                # Remove from cache
                del _cached_sessions[cache_key]
                del _cached_session_times[cache_key]
        else:
            logger.info(f"⏰ Cached session too old ({int(age_seconds)}s), will re-authenticate")
            # Remove from cache
            del _cached_sessions[cache_key]
            del _cached_session_times[cache_key]

    # Create NEW login session instance
    login_session = LoginSession(
        base_url=base_url,
        username=username,
        password=password,
        use_socks_proxy=use_socks_proxy,
        socks_url=socks_url,
    )

    # Load cookies and try fast validation only if cookies aren't expired
    cookies_loaded = load_session_cookies(login_session.session, base_url)
    if cookies_loaded > 0:
        logger.info(f"📁 Loaded {cookies_loaded} cookies from disk")
        
        # Check if we have essential cookies (bbm_session and XSRF-TOKEN)
        has_session_cookie = any(cookie.name == "bbm_session" for cookie in login_session.session.cookies)
        has_xsrf_cookie = any(cookie.name == "XSRF-TOKEN" for cookie in login_session.session.cookies)
        
        if has_session_cookie and has_xsrf_cookie:
            logger.info("✅ Essential cookies found, validating session...")
            if validate_session(login_session.session, base_url, use_socks_proxy, socks_url):
                logger.info("✅ Reusing valid session from cookies")
                # Cache the session
                _cached_sessions[cache_key] = login_session.session
                _cached_session_times[cache_key] = time.time()
                return login_session.session
            else:
                logger.info("❌ Loaded session is invalid, performing fresh login")
                login_session.session.cookies.clear()
        else:
            logger.info("❌ Missing essential cookies, performing fresh login")
            login_session.session.cookies.clear()
    else:
        pass  # No cookies loaded from disk

    # Perform fresh login if needed
    logger.info("🔐 Performing fresh login...")
    
    # First, do a basic connectivity check
    if not _check_basic_connectivity(base_url, use_socks_proxy, socks_url):
        logger.error("❌ Basic connectivity check failed - network may be unreachable")
        raise Exception("Network connectivity issues detected - cannot proceed with login")
    
    # Add retry logic for fresh login with exponential backoff
    max_retries = 3
    base_delay = 5  # Start with 5 seconds
    
    for attempt in range(max_retries):
        try:
            if login_session.login():
                # Mark as validated and cache
                _last_validation_time = time.time()
                _last_validation_result = True

                # Save the new session cookies with validation timestamp
                save_session_cookies(login_session.session, base_url)

                # Cache the session for reuse across all services
                _cached_sessions[cache_key] = login_session.session
                _cached_session_times[cache_key] = time.time()

                logger.info("✅ Fresh authentication completed and cached")
                return login_session.session
            else:
                logger.warning(f"Login attempt {attempt + 1} failed")
                
        except requests.exceptions.ConnectTimeout as e:
            logger.warning(f"Login attempt {attempt + 1} timed out: {e}")
        except requests.exceptions.ReadTimeout as e:
            logger.warning(f"Login attempt {attempt + 1} read timeout: {e}")
        except Exception as e:
            logger.error(f"Login attempt {attempt + 1} failed with error: {e}")
        
        # If not the last attempt, wait before retrying
        if attempt < max_retries - 1:
            delay = base_delay * (2 ** attempt)  # Exponential backoff
            logger.info(f"Waiting {delay} seconds before retry...")
            time.sleep(delay)
    
    # All attempts failed
    logger.error("❌ All login attempts failed")
    
    # Provide helpful diagnostics
    logger.error("Possible causes and solutions:")
    logger.error("1. Network connectivity issues - check internet connection")
    logger.error("2. Tor proxy issues - ensure Tor is running and accessible")
    logger.error("3. Invalid credentials - verify username/password")
    logger.error("4. Target server issues - server may be down or blocking requests")
    
    if use_socks_proxy:
        logger.error(f"5. SOCKS proxy configuration - current proxy: {socks_url}")
        logger.error("   Try checking if Tor is running on the correct port")
    
    raise Exception(f"Authentication failed after {max_retries} attempts - see logs for details")


class SessionManager:
    """
    Session manager for API v3.
    Handles session creation, validation, and persistence.

    Uses a global session cache so all instances with the same credentials
    share the same underlying session.
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy or ".onion" in base_url
        self.socks_url = socks_url
        self.cache_key = f"{base_url}:{username}"

    def get_session(self) -> requests.Session:
        """
        Get authenticated session (uses global cache for sharing across services).
        This ensures all services with the same credentials use the SAME session.
        """
        return get_authenticated_session(
            base_url=self.base_url,
            username=self.username,
            password=self.password,
            use_socks_proxy=self.use_socks_proxy,
            socks_url=self.socks_url,
        )

    def validate_session(self, force_check: bool = False) -> bool:
        """Validate current session"""
        session = self.get_session()
        return validate_session(
            session,
            self.base_url,
            self.use_socks_proxy,
            self.socks_url,
            force_check,
        )

    def refresh_session(self) -> requests.Session:
        """Force refresh the session by clearing cache"""
        global _cached_sessions, _cached_session_times
        # Clear from global cache
        if self.cache_key in _cached_sessions:
            del _cached_sessions[self.cache_key]
            del _cached_session_times[self.cache_key]
        # Get new session
        return self.get_session()

    def save_session(self) -> Optional[str]:
        """Save current session to disk"""
        session = self.get_session()
        return save_session_cookies(session, self.base_url)

    def close(self):
        """
        Close the session.
        Note: Since sessions are shared globally, this doesn't actually close
        the underlying session to avoid disrupting other services.
        """
        # Don't close the session as it may be used by other services
        pass


def clear_session_cache():
    """
    Clear the global session cache.
    Useful for forcing re-authentication across all services.
    """
    global _cached_sessions, _cached_session_times, _last_validation_time, _last_validation_result
    _cached_sessions.clear()
    _cached_session_times.clear()
    _last_validation_time = 0
    _last_validation_result = False
    logger.info("🧹 Cleared global session cache")
