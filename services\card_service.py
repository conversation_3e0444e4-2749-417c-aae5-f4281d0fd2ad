"""
Card service for fetching and managing card data from external API
Enhanced with comprehensive API request/response logging for 403 error diagnosis
"""

from __future__ import annotations

from typing import Dict, Any, Optional
import asyncio
import time

from config.settings import get_settings
from utils.performance import monitor_performance
from utils.api_logging import get_api_logger, LogLevel
from services.external_api_service import (
    ExternalAPIService,
    get_external_api_service,
    ListItemsParams,
)
from services.api_status_service import get_api_status_service

# Import API v2 services for enhanced functionality
from api_v2.services.browse_service import (
    APIV2BrowseService,
    get_api_v2_browse_service,
    APIV2BrowseParams,
    APIV2BrowseResponse,
)
# Static fallback removed - only real API calls allowed

from utils.central_logger import get_logger

logger = get_logger()
api_logger = get_api_logger("card_service", LogLevel.DEBUG)


class CardService:
    """Service for fetching card data from external API"""

    def __init__(
        self,
        external_api_service: Optional[ExternalAPIService] = None,
        use_api_v2: Optional[bool] = None,
    ):
        self.settings = get_settings()

        # Initialize API status service
        self.status_service = get_api_status_service()

        # Initialize API version flags
        self.use_api_v3 = False
        self.use_api_v2 = False

        # Check if external_api_service is an API v3 adapter
        if external_api_service is not None:
            service_type = type(external_api_service).__name__
            logger.debug(f"CardService received service type: {service_type}")
            logger.debug(f"Service has browse_cards: {hasattr(external_api_service, 'browse_cards')}")
            logger.debug(f"Service has browse_service: {hasattr(external_api_service, 'browse_service')}")
            
            # Check if it's an APIV3Adapter by checking for the browse_cards method
            if hasattr(external_api_service, "browse_cards") and hasattr(
                external_api_service, "browse_service"
            ):
                self.use_api_v3 = True
                self.api_v3_adapter = external_api_service
                self.external_api = None
                self.api_v2_service = None
                self.status_service.set_current_version("v3")
                logger.info(f"CardService using API v3 adapter (service type: {service_type})")
                return

            # Check if it's an APIV2ExternalBrowseAdapter
            if hasattr(external_api_service, "list_items") and hasattr(
                external_api_service, "_service"
            ):
                # This is likely an APIV2ExternalBrowseAdapter
                self.use_api_v2 = True
                self.api_v2_service = external_api_service
                self.external_api = None
                self.api_v3_adapter = None
                self.status_service.set_current_version("v2")
                logger.info(f"CardService using API v2 adapter (service type: {service_type})")
                return
            
            # If we reach here, it should be an ExternalAPIService (API v1)
            logger.info(f"CardService treating service as ExternalAPIService (service type: {service_type})")

        # Determine API version to use based on explicit parameter or service type
        if use_api_v2 is None:
            # If external_api_service is provided, check its API version
            if external_api_service is not None:
                # Get the API version from the external service
                service_api_version = getattr(external_api_service, 'api_version', 'v1')
                logger.debug(f"ExternalAPIService reports api_version: {service_api_version}")

                if service_api_version == "v3" or service_api_version == "base3":
                    # For v3, we need to use the external_api_service directly (not v2 service)
                    self.use_api_v2 = False
                    logger.info(
                        f"CardService using API v3 (ExternalAPIService with version {service_api_version})"
                    )
                elif service_api_version == "v2" or service_api_version == "base2":
                    self.use_api_v2 = True
                    logger.info(
                        f"CardService using API v2 (ExternalAPIService with version {service_api_version})"
                    )
                else:
                    self.use_api_v2 = False
                    logger.info(
                        f"CardService using API v1 (ExternalAPIService with version {service_api_version})"
                    )
            else:
                # Fall back to global setting when no service is provided
                api_version = getattr(self.settings, "EXTERNAL_API_VERSION", "v1")
                
                # Auto-detect API v3 if credentials are configured but version not explicitly set
                if api_version.lower() not in ("v3", "base3"):
                    # Check if API v3 credentials are configured
                    has_v3_config = (
                        (getattr(self.settings, 'EXTERNAL_V3_BASE_URL', '') or 
                         getattr(self.settings, 'API_V3_BASE_URL', '')) and
                        (getattr(self.settings, 'EXTERNAL_V3_USERNAME', '') or 
                         getattr(self.settings, 'API_V3_USERNAME', '')) and
                        (getattr(self.settings, 'EXTERNAL_V3_PASSWORD', '') or 
                         getattr(self.settings, 'API_V3_PASSWORD', ''))
                    )
                    
                    if has_v3_config:
                        logger.info(f"🔍 Auto-detected API v3 configuration in CardService (EXTERNAL_API_VERSION={api_version}, but v3 credentials found)")
                        api_version = "v3"

                if api_version.lower() == "v3" or api_version.lower() == "base3":
                    # For v3, we'll create an ExternalAPIService internally
                    self.use_api_v2 = False
                    logger.debug(
                        f"CardService auto-detected {api_version} -> using API v3"
                    )
                elif api_version.lower() == "v2" or api_version.lower() == "base2":
                    self.use_api_v2 = True
                    logger.debug(
                        f"CardService auto-detected {api_version} -> using API v2"
                    )
                else:
                    self.use_api_v2 = False
                    logger.debug(
                        f"CardService auto-detected {api_version} -> using API v1"
                    )
        else:
            self.use_api_v2 = use_api_v2
            logger.debug(f"CardService using explicit API v2 setting: {use_api_v2}")

        if self.use_api_v2:
            # Use new API v2 browse service with proper authentication
            # Get authentication from environment variables directly
            # Use centralized authentication helper to eliminate duplicate code
            from utils.auth_helper import AuthenticationHelper
            
            api1_config_dict = AuthenticationHelper.get_api_v1_config()
            
            if api1_config_dict:
                login_token = AuthenticationHelper.get_login_token()
                session_cookies = AuthenticationHelper.get_session_cookies()
                
                config_kwargs = {
                    "inherit_auth_from_api1": True,
                    "api1_config": api1_config_dict,
                    "login_token": login_token,
                    "session_cookies": session_cookies,
                }
            else:
                logger.warning(
                    "No EXTERNAL_LOGIN_TOKEN found in environment - API v2 may fail"
                )
                config_kwargs = {"inherit_auth_from_api1": True}

            self.api_v2_service = APIV2BrowseService(config_kwargs=config_kwargs)
            self.external_api = None
            self.status_service.set_current_version("v2")
        else:
            # Use main ExternalAPIService (API v1)
            self.external_api = external_api_service or get_external_api_service()
            self.api_v2_service = None
            self.status_service.set_current_version("v1")
            logger.debug("CardService using ExternalAPIService (API v1)")

        # No local headers/cookies; HTTP/auth handled by API services

    def _get_current_api_version(self) -> str:
        """Get the current API version being used by this CardService instance"""
        if self.use_api_v3:
            return "v3"
        elif self.use_api_v2:
            return "v2"
        else:
            # Check if we have an external_api service with v3 configuration
            if hasattr(self, 'external_api') and self.external_api:
                external_version = getattr(self.external_api, 'api_version', 'v1')
                if external_version == "v3" or external_version == "base3":
                    return "v3"
            return "v1"

    def get_api_status_message(self) -> str:
        """Get formatted API status message for display to users"""
        return self.status_service.get_status_message()

    def get_user_friendly_status_message(self) -> str:
        """Get user-friendly API status message without technical details"""
        return self.status_service.get_user_friendly_message()

    def get_api_status_details(self) -> Dict[str, Any]:
        """Get detailed API status information"""
        return self.status_service.get_detailed_status()

    async def close(self) -> None:
        """Nothing to close; external_api_service manages HTTP session"""
        return None

    def _apply_filters_to_params(
        self, params: Dict[str, str], filters: Dict[str, Any]
    ) -> None:
        """
        Apply filters to API parameters with proper validation and mapping

        Args:
            params: API parameters dictionary to modify
            filters: User filters to apply
        """
        logger.debug(f"_apply_filters_to_params called with filters: {filters}")
        
        # Filter mapping from UI filter names to API parameter names
        # Note: For API v3, some mappings differ (e.g., bin->bins)
        filter_mapping = {
            "bank": "bank",
            "country": "country",
            "brand": "brand",
            "scheme": "scheme",  # Added scheme mapping
            "type": "type",
            "level": "level",
            "priceFrom": "priceFrom",
            "priceTo": "priceTo",
            "base": "base",
            "bin": "bin",
            "bins": "bins",  # Added bins mapping for API v3
            "state": "state",
            "region": "region",  # Added region mapping for API v3
            "city": "city",
            "zip": "zip",
            "postal_code": "postal_code",  # Added postal_code mapping for API v3
            "continent": "continent",  # Added continent mapping for API v3
            "ethnicity": "ethnicity",  # Added ethnicity mapping for API v3
            "selected_bank": "selected_bank",  # Added selected_bank for API v3
            "searched_bank": "searched_bank",  # Added searched_bank for API v3
        }

        # Boolean filters mapping
        boolean_filters = {
            "zipCheck": "zipCheck",
            "address": "address",
            "phone": "phone",
            "email": "email",
            "withoutcvv": "withoutcvv",
            "refundable": "refundable",
            "expirethismonth": "expirethismonth",
            "dob": "dob",
            "ssn": "ssn",
            "mmn": "mmn",
            "ip": "ip",
            "dl": "dl",
            "ua": "ua",
            "discount": "discount",
            # API v3 specific boolean filters
            "with_billing": "with_billing",
            "with_phone": "with_phone",
            "with_dob": "with_dob",
            "show_medium_valid": "show_medium_valid",
            "expiring_soon": "expiring_soon",
            "expiring_next": "expiring_next",
            "cc_per_bin": "cc_per_bin",
        }

        # Apply string/value filters with API version awareness
        api_version = self._get_current_api_version()
        
        # For API v3, use different mappings
        if api_version == "v3":
            # API v3 specific mappings
            v3_filter_mapping = {
                "bank": "selected_bank",
                "country": "country",
                "brand": "scheme",
                "scheme": "scheme",
                "type": "type",
                "level": "level",
                "priceFrom": "priceFrom",
                "priceTo": "priceTo",
                "base": "base",
                "bin": "bins",  # Key mapping for API v3
                "bins": "bins",
                "state": "region",  # Key mapping for API v3
                "region": "region",
                "city": "city",
                "zip": "postal_code",  # Key mapping for API v3
                "postal_code": "postal_code",
                "continent": "continent",
                "ethnicity": "ethnicity",
                "selected_bank": "selected_bank",
                "searched_bank": "searched_bank",
            }
            current_mapping = v3_filter_mapping
        else:
            # Use standard mapping for v1/v2
            current_mapping = filter_mapping
            
        for filter_key, param_key in current_mapping.items():
            if filter_key in filters and filters[filter_key] is not None:
                value = filters[filter_key]
                # Clean and validate the value
                if isinstance(value, str):
                    value = value.strip()
                
                # Skip empty values
                if not value and value != 0:
                    continue
                    
                # For API v3, handle special boolean-to-string conversions
                if api_version == "v3":
                    # Handle checkbox parameters that need "on" values
                    if param_key in ["show_medium_valid", "expiring_soon", "expiring_next", "cc_per_bin"]:
                        if isinstance(value, bool):
                            params[param_key] = "on" if value else ""
                        elif str(value).lower() in ["true", "on", "1", "yes"]:
                            params[param_key] = "on"
                        else:
                            params[param_key] = ""
                        continue
                    # Handle with_* parameters that need "with" values  
                    elif param_key in ["with_billing", "with_phone", "with_dob"]:
                        if isinstance(value, bool):
                            params[param_key] = "with" if value else ""
                        elif str(value).lower() in ["true", "on", "1", "yes", "with"]:
                            params[param_key] = "with"
                        else:
                            params[param_key] = ""
                        continue
                
                # For all other cases, convert to string
                params[param_key] = str(value)
                logger.debug(
                    f"Applied filter {filter_key}={value} to param {param_key} (API {api_version})"
                )

        # Apply boolean filters with API version awareness
        # For API v3, some boolean filters may need different mappings
        if api_version == "v3":
            # API v3 specific boolean mappings
            v3_boolean_filters = {
                "zipCheck": "zipCheck",
                "address": "with_billing",  # Map to with_billing for API v3
                "phone": "with_phone",      # Map to with_phone for API v3
                "email": "email",
                "withoutcvv": "withoutcvv",
                "refundable": "refundable",
                "expirethismonth": "expiring_soon",  # Map to expiring_soon for API v3
                "dob": "with_dob",          # Map to with_dob for API v3
                "ssn": "ssn",
                "mmn": "mmn",
                "ip": "ip",
                "dl": "dl",
                "ua": "ua",
                "discount": "discount",
                # Direct API v3 boolean filters
                "with_billing": "with_billing",
                "with_phone": "with_phone",
                "with_dob": "with_dob",
                "show_medium_valid": "show_medium_valid",
                "expiring_soon": "expiring_soon",
                "expiring_next": "expiring_next",
                "cc_per_bin": "cc_per_bin",
            }
            current_boolean_mapping = v3_boolean_filters
        else:
            # Use standard mapping for v1/v2
            current_boolean_mapping = boolean_filters
            
        for filter_key, param_key in current_boolean_mapping.items():
            if filter_key in filters and filters[filter_key] is not None:
                value = filters[filter_key]
                
                # For API v3, handle boolean parameters differently
                if api_version == "v3":
                    # API v3 boolean parameters that need "on"/"with" values
                    if param_key in ["show_medium_valid", "expiring_soon", "expiring_next", "cc_per_bin"]:
                        if isinstance(value, bool):
                            params[param_key] = "on" if value else ""
                        elif str(value).lower() in ["true", "on", "1", "yes"]:
                            params[param_key] = "on"
                        else:
                            params[param_key] = ""
                    elif param_key in ["with_billing", "with_phone", "with_dob"]:
                        if isinstance(value, bool):
                            params[param_key] = "with" if value else ""
                        elif str(value).lower() in ["true", "on", "1", "yes", "with"]:
                            params[param_key] = "with"
                        else:
                            params[param_key] = ""
                    else:
                        # Other API v3 boolean fields use "true"/"false" strings
                        if isinstance(value, bool):
                            params[param_key] = "true" if value else "false"
                        elif isinstance(value, str) and value.lower() in ("true", "false"):
                            params[param_key] = value.lower()
                        else:
                            params[param_key] = "true" if value else "false"
                else:
                    # For API v1/v2, convert to string boolean
                    if isinstance(value, bool):
                        params[param_key] = "true" if value else "false"
                    elif isinstance(value, str) and value.lower() in ("true", "false"):
                        params[param_key] = value.lower()
                    else:
                        params[param_key] = "true" if value else "false"
                        
                logger.debug(
                    f"Applied boolean filter {filter_key}={params[param_key]} to param {param_key} (API {api_version})"
                )
        
        logger.debug(f"Final params after _apply_filters_to_params: {params}")

    def _build_default_params(self, page: int, limit: int) -> Dict[str, str]:
        """Create default API parameter payload matching demo defaults."""
        return {
            "page": str(page),
            "limit": str(limit),
            "base": "",
            "bank": "",
            "bin": "",
            "country": "",
            "state": "",
            "city": "",
            "brand": "",
            "type": "",
            "level": "",
            "zip": "",
            "priceFrom": "0",
            "priceTo": "500",
            "zipCheck": "false",
            "address": "false",
            "phone": "false",
            "email": "false",
            "withoutcvv": "false",
            "refundable": "false",
            "expirethismonth": "false",
            "dob": "false",
            "ssn": "false",
            "mmn": "false",
            "ip": "false",
            "dl": "false",
            "ua": "false",
            "discount": "false",
        }

    @staticmethod
    def _to_bool(value: Any) -> bool:
        if isinstance(value, bool):
            return value
        if value is None:
            return False
        return str(value).strip().lower() == "true"

    @staticmethod
    def _to_float(value: Any) -> float:
        if value in (None, ""):
            return 0.0
        try:
            return float(value)
        except (TypeError, ValueError):
            return 0.0

    def _params_to_list_items(self, params: Dict[str, str]) -> ListItemsParams:
        """Map parameter dictionary into the dataclass expected by external API."""
        # Import the parameter classes to avoid Union instantiation
        from services.external_api_service import ListItemsParamsV1V2, ListItemsParamsV3
        
        # Create the appropriate parameter class based on API version
        if self.use_api_v3:
            return ListItemsParamsV3(
                page=int(params.get("page", 1)),
                limit=int(params.get("limit", 10)),
                continent=params.get("continent", ""),
                country=params.get("country", ""),
                region=params.get("region", ""),
                city=params.get("city", ""),
                postal_code=params.get("zip", ""),
                bins=params.get("bin", ""),
                card_schemes=[],
                card_types=[],
                card_levels=[],
                banks=[],
                price_min=self._to_float(params.get("priceFrom")),
                price_max=self._to_float(params.get("priceTo")),
                include_refundable=self._to_bool(params.get("refundable")),
                include_non_refundable=True,
                include_base=params.get("base", ""),
                # Map other relevant fields for v3
            )
        else:
            # Use API v1/v2 parameters
            return ListItemsParamsV1V2(
                page=int(params.get("page", 1)),
                limit=int(params.get("limit", 10)),
                base=params.get("base", ""),
                bank=params.get("bank", ""),
                bin=params.get("bin", ""),
                country=params.get("country", ""),
                state=params.get("state", ""),
                city=params.get("city", ""),
                brand=params.get("brand", ""),
                type=params.get("type", ""),
                level=params.get("level", ""),
                zip=params.get("zip", ""),
                price_from=self._to_float(params.get("priceFrom")),
                price_to=self._to_float(params.get("priceTo")),
                zip_check=self._to_bool(params.get("zipCheck")),
                address=self._to_bool(params.get("address")),
                phone=self._to_bool(params.get("phone")),
                email=self._to_bool(params.get("email")),
                without_cvv=self._to_bool(params.get("withoutcvv")),
                refundable=self._to_bool(params.get("refundable")),
                expire_this_month=self._to_bool(params.get("expirethismonth")),
                dob=self._to_bool(params.get("dob")),
                ssn=self._to_bool(params.get("ssn")),
                mmn=self._to_bool(params.get("mmn")),
                ip=self._to_bool(params.get("ip")),
                dl=self._to_bool(params.get("dl")),
                ua=self._to_bool(params.get("ua")),
                discount=self._to_bool(params.get("discount")),
            )

    def _map_filters_to_api_v2(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Map filters from card service format to API v2 parameter names."""
        if not filters:
            return {}

        rename_map = {
            "priceFrom": "price_from",
            "priceTo": "price_to",
            "zipCheck": "zip_check",
            "withoutcvv": "without_cvv",
            "expirethismonth": "expire_this_month",
        }

        bool_fields = {
            "zip_check",
            "address",
            "phone",
            "email",
            "without_cvv",
            "refundable",
            "expire_this_month",
            "dob",
            "ssn",
            "mmn",
            "ip",
            "dl",
            "ua",
            "discount",
        }

        float_fields = {"price_from", "price_to"}

        mapped: Dict[str, Any] = {}
        for key, value in filters.items():
            target_key = rename_map.get(key, key)
            if target_key in bool_fields:
                mapped[target_key] = self._to_bool(value)
            elif target_key in float_fields:
                mapped[target_key] = self._to_float(value)
            else:
                mapped[target_key] = value

        return mapped

    @monitor_performance("fetch_cards")
    async def fetch_cards(
        self,
        page: int = 1,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Fetch cards from the external API with comprehensive logging

        Args:
            page: Page number (default: 1)
            limit: Number of items per page (default: 10)
            filters: Optional filters to apply
            user_id: User ID for logging context

        Returns:
            Dictionary containing success status, data, and metadata
        """
        # Create logging context
        context = api_logger.create_context(user_id=user_id, operation="fetch_cards")

        try:
            # Check if using API v3 adapter
            if self.use_api_v3 and hasattr(self, "api_v3_adapter"):
                logger.info(
                    f"Using API v3 adapter to fetch cards (page {page}, limit {limit})"
                )

                # Use API v3 adapter's browse_cards method
                request_start = time.time()
                api_resp = await self.api_v3_adapter.browse_cards(
                    filters=filters, page=page, limit=limit, user_id=user_id
                )
                response_time = time.time() - request_start

                if api_resp.get("success"):
                    cards_count = len(api_resp.get("data", []))
                    total_count = api_resp.get("totalCount", 0)
                    logger.info(
                        f"Successfully fetched {cards_count} cards from API v3 (page {page}, total: {total_count})"
                    )

                    # Record success in status service
                    self.status_service.record_success("v3", response_time, 200)

                    # Log successful API response
                    api_logger.log_response(
                        context=context,
                        status_code=200,
                        status_message="OK",
                        headers={},
                        body=api_resp,
                    )

                    return api_resp
                else:
                    error_msg = api_resp.get(
                        "error", "API v3 returned unsuccessful response"
                    )
                    logger.error(f"API v3 failed: {error_msg}")

                    # Record failure in status service
                    self.status_service.record_failure("v3", error_msg, 500)

                    api_logger.log_response(
                        context=context,
                        status_code=500,
                        status_message="API Error",
                        headers={},
                        body={"error": error_msg},
                    )
                    return {"success": False, "error": error_msg, "data": []}

            elif self.use_api_v2 and self.api_v2_service:
                # Use API v2 browse service with proper authentication
                api_v2_params = APIV2BrowseParams(
                    page=page,
                    limit=limit,
                    # Map filters to API v2 format if needed
                    **({} if not filters else self._map_filters_to_api_v2(filters)),
                )

                request_start = time.time()
                api_resp = await self.api_v2_service.list_items(
                    params=api_v2_params, user_id=user_id
                )
                response_time = time.time() - request_start

                # API v2 returns APIV2BrowseResponse object
                if api_resp.success and api_resp.data:
                    data = api_resp.data
                    cards_count = len(data.get("data", []))

                    logger.info(
                        f"Successfully fetched {cards_count} cards (page {page}, total: {data.get('totalCount', 0)})"
                    )

                    # Record success in status service
                    self.status_service.record_success("v2", response_time, 200)

                    # Log successful API response
                    api_logger.log_response(
                        context=context,
                        status_code=200,
                        status_message="OK",
                        headers={},
                        body=data,
                    )

                    return data
                else:
                    error_msg = (
                        api_resp.error or "API v2 returned unsuccessful response"
                    )
                    logger.error(f"API v2 failed: {error_msg}")

                    # Record failure in status service
                    self.status_service.record_failure(
                        "v2", error_msg, api_resp.status_code or 500
                    )

                    api_logger.log_response(
                        context=context,
                        status_code=api_resp.status_code or 500,
                        status_message="API Error",
                        headers={},
                        body={"error": error_msg},
                    )
                    return {"success": False, "error": error_msg, "data": []}

            else:
                # Fallback to old external API service
                # Build query parameters with defaults
                params = self._build_default_params(page, limit)

                # Apply filters if provided with proper validation and mapping
                if filters:
                    self._apply_filters_to_params(params, filters)
                    logger.debug(f"Applied filters: {filters}")

                # Delegate to external API service list_items
                list_params = self._params_to_list_items(params)

                # Use the correct API version for status tracking
                api_version = self._get_current_api_version()

                request_start = time.time()
                api_resp = await self.external_api.list_items(
                    params=list_params, user_id=user_id
                )
                response_time = time.time() - request_start

                if api_resp.success and isinstance(api_resp.data, dict):
                    data = api_resp.data
                    cards_count = len(data.get("data", []))
                    total_count = data.get("totalCount", 0)
                    logger.info(
                        f"Successfully fetched {cards_count} cards (page {page}, total: {total_count})"
                    )

                    # Record success in status service
                    self.status_service.record_success(api_version, response_time, 200)

                    return data
                else:
                    error_msg = api_resp.error or "List items failed"
                    logger.error(f"API returned error: {error_msg}")

                    # Record failure in status service
                    self.status_service.record_failure(api_version, error_msg)

                    return {
                        "success": False,
                        "error": error_msg,
                        "data": [],
                        "totalCount": 0,
                    }
        except asyncio.TimeoutError:
            logger.error("API request timed out")

            # Record timeout in status service
            api_version = self._get_current_api_version()
            self.status_service.record_failure(api_version, "Request timed out", 408)

            # Log timeout error
            api_logger.log_response(
                context=context,
                status_code=408,
                status_message="Request Timeout",
                headers={},
                body={"error": "Request timed out after 60 seconds"},
            )

            # For API v3, provide more helpful timeout message
            if api_version == "v3":
                error_msg = "API v3 request timed out. This can happen with .onion domains. Please try again."
            else:
                error_msg = "Request timed out"

            return {
                "success": False,
                "error": error_msg,
                "data": [],
                "totalCount": 0,
            }
        except Exception as e:
            logger.error(f"Error fetching cards: {e}")

            # Record exception in status service
            api_version = self._get_current_api_version()
            self.status_service.record_failure(api_version, str(e), 500)

            # Log general error
            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Error",
                headers={},
                body=None,
                error_type="exception",
                error_message=str(e),
            )

            return {"success": False, "error": str(e), "data": [], "totalCount": 0}

    async def search_cards(
        self,
        bank: Optional[str] = None,
        country: Optional[str] = None,
        card_type: Optional[str] = None,
        brand: Optional[str] = None,
        price_from: Optional[float] = None,
        price_to: Optional[float] = None,
        page: int = 1,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Search cards with specific criteria

        Args:
            bank: Bank name filter
            country: Country code filter
            card_type: Card type filter (CREDIT/DEBIT)
            brand: Card brand filter
            price_from: Minimum price filter
            price_to: Maximum price filter
            page: Page number
            limit: Items per page

        Returns:
            Dictionary containing search results
        """
        filters = {}

        if bank:
            filters["bank"] = bank
        if country:
            filters["country"] = country
        if card_type:
            filters["type"] = card_type
        if brand:
            filters["brand"] = brand
        if price_from is not None:
            filters["priceFrom"] = str(price_from)
        if price_to is not None:
            filters["priceTo"] = str(price_to)

        return await self.fetch_cards(page=page, limit=limit, filters=filters)

    async def fetch_filter_options(
        self,
        filter_name: str,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Retrieve dynamic filter options using the selected API version.

        API v1: Uses external_api.get_filters() -> /api/cards/hq/filters
        API v2: Uses api_v2_service.get_filters() -> /api/cards/vhq/filters
        API v3: Uses api_v3_adapter.get_filters() -> API v3 filters endpoint
        """
        try:
            # Check if using API v3 adapter
            if self.use_api_v3 and hasattr(self, "api_v3_adapter"):
                logger.info(f"Using API v3 adapter for filter '{filter_name}'")

                # Use API v3 adapter's get_filter_options method
                if hasattr(self.api_v3_adapter, "get_filter_options"):
                    api_resp = await self.api_v3_adapter.get_filter_options(
                        filter_name=filter_name, filters=filters, user_id=user_id
                    )

                    if api_resp.get("success"):
                        logger.info(f"API v3 filters successful for '{filter_name}'")
                        return api_resp
                    else:
                        error_msg = api_resp.get(
                            "error", "API v3 filter request failed"
                        )
                        logger.warning(
                            f"API v3 filter error for '{filter_name}': {error_msg}"
                        )
                        # No fallback - return failure for real API calls only
                        return {"success": False, "error": "API v3 filter request failed", "data": []}
                else:
                    logger.error(
                        f"API v3 adapter doesn't support get_filter_options"
                    )
                    return {"success": False, "error": "Filter options not supported by API v3", "data": []}

            elif self.use_api_v2 and self.api_v2_service:
                # API v2 Route: Use API v2 browse service for filters
                logger.info(f"Using API v2 filters endpoint for '{filter_name}'")

                api_v2_params = APIV2BrowseParams(
                    page=1,
                    limit=10,
                    **({} if not filters else self._map_filters_to_api_v2(filters)),
                )

                api_resp = await self.api_v2_service.get_filters(
                    filter_name=filter_name, params=api_v2_params, user_id=user_id
                )

                if api_resp.success and api_resp.data:
                    logger.info(f"API v2 filters successful for '{filter_name}'")
                    return api_resp.data
                else:
                    error_msg = api_resp.error or "API v2 filter request failed"
                    logger.warning(
                        f"API v2 filter error for '{filter_name}': {error_msg}"
                    )

                    # Check if it's a 404 error (endpoint doesn't exist)
                    if "404" in error_msg or "Cannot GET" in error_msg:
                        logger.info(
                            f"API v2 filters endpoint not available for '{filter_name}'"
                        )
                        return {"success": False, "error": f"API v2 filter '{filter_name}' not available", "data": []}
                    else:
                        return {"success": False, "error": error_msg, "data": []}

            else:
                # API v1 Route: Use external API service for filters
                logger.info(f"Using API v1 filters endpoint for '{filter_name}'")

                if not self.external_api:
                    error_msg = (
                        "API v1 external service not available for filter requests"
                    )
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg, "data": []}

                params = self._build_default_params(page=1, limit=10)
                if filters:
                    self._apply_filters_to_params(params, filters)
                list_params = self._params_to_list_items(params)

                api_resp = await self.external_api.get_filters(
                    filter_name=filter_name, params=list_params, user_id=user_id
                )

                if api_resp.success and isinstance(api_resp.data, dict):
                    logger.info(f"API v1 filters successful for '{filter_name}'")
                    return api_resp.data
                else:
                    error_msg = api_resp.error or "API v1 filter request failed"
                    logger.error(
                        f"API v1 filter error for '{filter_name}': {error_msg}"
                    )
                    return {"success": False, "error": error_msg, "data": []}

        except Exception as e:
            error_msg = f"Unexpected error fetching filter '{filter_name}': {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg, "data": []}

    # Static filter options removed - only real API calls allowed
    # All filter requests must come from actual external API endpoints

    def format_card_for_display(self, card: Dict[str, Any]) -> str:
        """
        Format a card object for display in Telegram

        Args:
            card: Card data dictionary

        Returns:
            Formatted string for display
        """
        try:
            # Extract key information
            card_id = card.get("_id", "N/A")
            bank = card.get("bank", "Unknown Bank")
            bin_number = card.get("bin", "N/A")
            card_type = card.get("type", "N/A")
            level = card.get("level", "N/A")
            brand = card.get("brand", "N/A")
            country = card.get("country", "N/A")
            price = card.get("price", "0.00")
            exp = card.get("exp", "N/A")
            refund_rate = card.get("refund_rate", "0.00")

            # Format the card information (hide internal card ID from user)
            import html
            formatted = (
                f"🔢 <b>BIN:</b> {html.escape(bin_number)} • <b>EXP:</b> {html.escape(exp)}\n"
                f"🏛️ <b>{html.escape(bank)}</b>\n"
                f"🎯 <b>{html.escape(brand)}</b> • 💳 <b>{html.escape(card_type)}</b> • 🏷️ <b>{html.escape(level)}</b>\n"
                f"🌍 <b>Country:</b> {html.escape(country)}\n"
                f"💰 <b>Price:</b> ${html.escape(price)}\n"
                f"📈 <b>Refund Rate:</b> {html.escape(refund_rate)}%\n"
            )

            # Add additional features if available
            features = []
            if card.get("address"):
                features.append("📍 Address")
            if card.get("phone"):
                features.append("📞 Phone")
            if card.get("email"):
                features.append("📧 Email")
            if card.get("ip"):
                features.append("🌐 IP")
            if card.get("refundable"):
                features.append("🔄 Refundable")

            if features:
                formatted += f"✨ <b>Features:</b> {', '.join(features)}\n"

            return formatted

        except Exception as e:
            logger.error(f"Error formatting card for display: {e}")
            return f"💳 Card #{card.get('_id', 'N/A')} - Error displaying details"

    async def get_card_summary(self, cards_data: Dict[str, Any]) -> str:
        """
        Generate a summary of the cards data

        Args:
            cards_data: Response from fetch_cards

        Returns:
            Formatted summary string
        """
        try:
            if not cards_data.get("success", False):
                return "❌ Failed to fetch card data"

            cards = cards_data.get("data", [])
            total_count = cards_data.get("totalCount", 0)

            if not cards:
                return "📭 No cards found matching your criteria"

            # Generate statistics
            card_types = {}
            banks = {}
            countries = {}

            for card in cards:
                # Count card types
                card_type = card.get("type", "Unknown")
                card_types[card_type] = card_types.get(card_type, 0) + 1

                # Count banks
                bank = card.get("bank", "Unknown")
                banks[bank] = banks.get(bank, 0) + 1

                # Count countries
                country = card.get("country", "Unknown")
                countries[country] = countries.get(country, 0) + 1

            summary = (
                f"📊 <b>Card Catalog Summary</b>\n\n"
                f"📈 <b>Total Available:</b> {total_count:,} cards\n"
                f"📄 <b>Showing:</b> {len(cards)} of {total_count:,}\n\n"
                f"🏦 <b>Top Banks:</b>\n"
            )

            # Show top 3 banks
            top_banks = sorted(banks.items(), key=lambda x: x[1], reverse=True)[:3]
            for bank, count in top_banks:
                bank_short = bank[:30] + "..." if len(bank) > 30 else bank
                summary += f"  • {bank_short}: {count}\n"

            summary += "\n💳 <b>Card Types:</b>\n"
            for card_type, count in sorted(card_types.items()):
                summary += f"  • {card_type}: {count}\n"

            summary += f"\n🌍 <b>Countries:</b> {', '.join(sorted(countries.keys()))}\n"

            return summary

        except Exception as e:
            logger.error(f"Error generating card summary: {e}")
            return "❌ Error generating summary"

    async def get_filter_options(
        self, filter_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get available filter options from the API.

        Args:
            filter_name: Optional specific filter name to get options for

        Returns:
            Dictionary containing filter options
        """
        try:
            if self.use_api_v3 and self.api_v3_adapter:
                # API v3 filter options
                response = await self.api_v3_adapter.get_filters()
                if response.success:
                    return response.data or {}
                else:
                    logger.error(f"API v3 filter options failed: {response.error}")
                    return {}

            elif self.use_api_v2 and self.api_v2_service:
                # API v2 filter options
                from api_v2.services.browse_service import APIV2BrowseParams

                params = APIV2BrowseParams()
                response = await self.api_v2_service.get_filters(filter_name, params)

                if response.success:
                    return response.data or {}
                else:
                    logger.error(f"API v2 filter options failed: {response.error}")
                    return {}

            elif self.external_api:
                # API v1 filter options through external API service
                response = await self.external_api.get_filter_options()
                if response.success:
                    return response.data or {}
                else:
                    logger.error(f"API v1 filter options failed: {response.error}")
                    return {}

            else:
                logger.warning("No API service available for filter options")
                return {}

        except Exception as e:
            logger.error(f"Error getting filter options: {e}")
            return {}
