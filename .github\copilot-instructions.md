# Copilot Instructions for Demo Wallet Bot v3

## Architecture Overview

This is a **production-ready Telegram bot** using **aiogram 3.x** with a **comprehensive multi-version API integration system** and **complete cryptocurrency payment processing**. The bot manages user wallets, provides catalog browsing functionality, and handles full payment workflows with sophisticated external API interactions.

### Core Components

- **Main Bot**: `main.py` using aiogram with modular handler/middleware architecture via `BotApplication` class with graceful lifecycle management and cross-platform signal handling
- **Multi-API System**: API v1 (legacy), v2 (enhanced), v3 (session-based) with unified abstractions via `ExternalAPIService` (3900+ lines of routing logic)
- **Payment Module**: Complete cryptocurrency payment system (`payment_module/`) with OXAPay integration, multi-currency support, underpayment/overpayment handling, bonus systems, and VIP features
- **Payment Server**: Standalone Flask callback server (`start_payment_server.py`) for webhook processing with automatic database initialization
- **Database**: MongoDB (production) with Motor async driver and comprehensive model system via `BaseDocument` pattern, with SQLite simulation fallback
- **Admin Panel**: Telegram-based interface for real-time API configuration, payment management, and system monitoring without code deployment
- **Services**: Background tasks, retention, health monitoring, checkout queue processing with singleton lifecycle management
- **Shared API Library**: Configuration-driven HTTP client system for unified API management (`shared_api/`)
- **Logging System**: Centralized logging (`utils/central_logger.py`) with advanced multi-file categorization (`utils/multi_file_logger.py`) for specialized monitoring
- **UI Components**: Modular UI system (`utils/ui_components.py`, `utils/enhanced_keyboards.py`) for consistent professional messaging

## Critical Architecture Patterns

### NO FALLBACK DATA GENERATION

**CRITICAL**: This codebase **DOES NOT** generate fallback, mock, or simulated data unless **explicitly requested**. All data must come from real API sources or be explicitly marked as test data in test handlers only.

- **Database**: Uses in-memory simulation for development, NOT fallback data generation
- **API Responses**: Real API calls only - no mock card generation or fallback card data
- **Cards/Products**: Always fetch from external APIs - no synthetic data creation
- **Exception**: Only `handlers/test_handlers.py` may use mock data, clearly marked as "TEST"

### Centralized Card Data Extraction

**CRITICAL**: ALL card data extraction must use `utils/card_data_extractor.py` - the single source of truth (2400+ lines of comprehensive extraction logic).

```python
# ALWAYS use this for ANY API response containing card data
from utils.card_data_extractor import extract_comprehensive_card_data
card_data = extract_comprehensive_card_data(api_response, card_id)

# Or use the singleton instance
from utils.card_data_extractor import get_card_data_extractor
extractor = get_card_data_extractor()
cards = extractor.extract_from_api_response(api_data)
```

**NEVER** create extraction logic elsewhere - it will be considered redundant and deprecated. This includes:

- Parsing card numbers, CVV, expiry dates
- Extracting data from table formats, sections, or text content
- Processing API responses for card information
- All extraction is 100% automatic based on `filter_response.json` with high-speed cached lookups

### API Version Management

The codebase supports **three API versions** through a sophisticated abstraction layer:

```python
# Services automatically route based on EXTERNAL_API_VERSION setting
from services.external_api_service import get_external_api_service
api_service = get_external_api_service()  # Auto-detects version

# API v3 uses form-based authentication with session management
from api_v3.auth import get_authenticated_session
session = await get_authenticated_session(username, password)
```

**Key Pattern**: Each API version has different authentication (v1: bearer tokens, v2: enhanced tokens, v3: form login + sessions). Services use `ExternalAPIService` which automatically handles version routing through a global singleton pattern.

### Application Lifecycle Management

The application uses a sophisticated lifecycle pattern with proper resource management and graceful shutdown:

```python
# BotApplication class manages the entire lifecycle with context manager pattern
class BotApplication:
    async def setup(self) -> None:
        # Initialize logging, database, middleware, handlers, background services

    async def run(self) -> None:
        # Start polling with signal handling (Unix/Windows compatible)

    async def shutdown(self) -> None:
        # Cleanup resources, close sessions, stop background tasks

# Usage with proper lifecycle
async with bot_lifespan() as app:
    await app.run()
```

**Key Features**:

- Cross-platform signal handling (Unix signals + Windows KeyboardInterrupt)
- Automatic service lifecycle management via `services/startup.py`
- Multi-file logging system with categorized log files and proper shutdown
- Background task management with proper cleanup (checkout queue, health monitoring)
- Database connection pooling with Motor async driver for MongoDB
- Menu command configuration with admin-specific commands
- Payment module integration with cryptocurrency processing
- Enhanced metrics and observability

### Configuration Management

API configurations are managed through **shared_api** system with admin panel integration:

```python
# Admin-managed configurations stored in MongoDB
from admin.services.shared_api_admin_service import get_shared_api_admin_service
admin_service = get_shared_api_admin_service()

# Configurations auto-sync with shared API registry
from shared_api.config.registry import api_registry
client = api_registry.get_client("api_name")
```

### Legacy Code Cleanup

The codebase actively removes deprecated and redundant code:

- **Deprecated Files**: `utils/country_flags.py` (use `utils/dynamic_country_flags.py` instead)
- **Deprecated Methods**: `response_processor.py` extraction methods (use `card_data_extractor.py` instead)
- **Clean Architecture**: No fallback data generation, no static card creation, no duplicate extraction logic
- **Unified Logging**: All direct `logging` imports replaced with `utils.central_logger`

### UI Components Pattern

Use modular UI components for consistent user experience:

```python
# Standard message creation with types
from utils.ui_components import create_message, MessageType, data_formatter

message = create_message(
    MessageType.SUCCESS,  # or ERROR, INFO, WARNING
    "Operation completed",
    data={"key": "value"}
)

# Enhanced keyboards with priorities
from utils.enhanced_keyboards import create_enhanced_keyboard, KeyboardStyle, ButtonPriority

keyboard = create_enhanced_keyboard(
    buttons=[
        {"text": "Primary", "callback_data": "action", "priority": ButtonPriority.HIGH},
        {"text": "Secondary", "callback_data": "back", "priority": ButtonPriority.LOW}
    ],
    style=KeyboardStyle.MODERN,
    columns=2
)
```

### Handler Architecture (aiogram 3.x)

Handlers use **Router-based organization** with class-based patterns and common middleware:

```python
# Each handler module follows this pattern with proper middleware attachment
class FeatureHandlers:
    def __init__(self):
        self.settings = get_settings()

    async def handler_method(self, message: Message):
        # Handler implementation
        pass

def get_feature_router() -> Router:
    router = Router()
    attach_common_middlewares(router)  # Rate limiting + user context + error handling
    handlers = FeatureHandlers()

    # Register handlers with F.data filters and Command filters
    router.message.register(handlers.cmd_feature, Command("feature"))
    router.callback_query.register(handlers.cb_feature, F.data == "menu:feature")
    return router

# In handlers/__init__.py - Router inclusion pattern
def setup_handlers(dp: Dispatcher) -> None:
    dp.include_router(get_user_router())
    dp.include_router(get_admin_router())
    # ... other routers in logical order
```

**Critical**: Always use `attach_common_middlewares()` on routers for rate limiting, user context, and consistent error handling.

## Development Workflows

### Environment Setup & Running

**Virtual Environment**: The project supports multiple virtual environment setups (`.venv/`, `venv/`). Choose one and activate it before development:

```bash
# Linux/macOS - Activate virtual environment and set variables
source .venv/bin/activate  # or bot_env/bin/activate or venv/bin/activate
export BOT_TOKEN="your_telegram_bot_token"
export EXTERNAL_API_VERSION="v3"  # or v1, v2
export USE_MONGODB="false"  # Uses SQLite simulation for development

# Install dependencies (if not already installed)
pip install -r requirements.txt

# Run bot with hot reload
python main.py

# Run payment callback server (separate terminal)
python start_payment_server.py
```

```powershell
# Windows PowerShell - Activate virtual environment and set variables
.venv\Scripts\Activate.ps1  # or bot_env\Scripts\Activate.ps1 or venv\Scripts\Activate.ps1
$env:BOT_TOKEN="your_telegram_bot_token"
$env:EXTERNAL_API_VERSION="v3"  # or v1, v2
$env:USE_MONGODB="false"  # Uses SQLite simulation for development

# Install dependencies (if not already installed)
pip install -r requirements.txt

# Run bot with hot reload
python main.py

# Run payment callback server (separate terminal)
python start_payment_server.py
```

### API Configuration Workflow

1. **Admin Panel**: Use `/admin` command in Telegram → API Management
2. **Environment**: Set `EXTERNAL_API_VERSION` to switch API versions
3. **Testing**: Each API version has different test endpoints and authentication

### Database Development

```python
# Always use async context for database operations
from database.connection import get_collection
users = get_collection("users")
user = await users.find_one({"telegram_id": user_id})

# Models inherit from BaseDocument for MongoDB compatibility
from models.user import User
from models.base import now_utc

# Create new documents
user = User(
    telegram_id=*********,
    username="testuser",
    created_at=now_utc()
)

# Convert between formats
user_dict = user.to_mongo()  # For database storage
user_obj = User.from_mongo(user_dict)  # From database
```

**Database Fallback**: Development uses in-memory simulation for MongoDB via `InMemoryCollection` pattern when `USE_MONGODB="false"` - this provides full async API compatibility without requiring MongoDB installation.

## Project-Specific Conventions

### Authentication & Security

- **Admin Access**: Controlled by `ADMIN_USER_IDS` environment variable
- **Rate Limiting**: Configured per-feature (messages, callbacks, purchases)
- **API Auth**: Session cookies persisted in `config/external_auth.json`
- **Encryption**: API credentials encrypted using Fernet (pydantic-settings integration)

### Error Handling Pattern

```python
from utils.decorators import handle_errors, admin_required

@admin_required
@handle_errors()  # Provides consistent error responses
async def handler_function(message: Message):
    # Handler logic here

# Alternative syntax with custom error message
@handle_errors(error_message="❌ Custom error message", show_alert=True)
async def sensitive_handler(callback: CallbackQuery):
    # Handler implementation
```

**Note**: Use `@handle_errors()` (newer pattern) or `@error_handler` (legacy pattern) for consistent error handling.

### Centralized Logging Pattern

```python
# ALWAYS use centralized logger instead of direct logging module
from utils.central_logger import get_logger

logger = get_logger()
logger.info("Use this pattern throughout the codebase")

# DON'T use direct logging imports
# import logging  # ❌ Avoid this
```

### Multi-File Logging System

Advanced logging with categorized log files for better debugging:

```python
from utils.multi_file_logger import (
    get_multi_logger, log_api_request, log_api_response,
    log_performance, PerformanceTimer, LogCategory, LogLevel
)

# Enable multi-file logging in settings
LOG_MULTI_FILE_ENABLED=true
LOG_BASE_PATH="logs"

# Use categorized logging
multi_logger = get_multi_logger()
multi_logger.log(LogCategory.API, LogLevel.INFO, "API call completed")

# Performance tracking with timing
with PerformanceTimer("api_operation") as timer:
    result = await api_call()
    log_performance("external_api", timer.elapsed, {"operation": "list_items"})
```

### Background Services

Services automatically start/stop with application lifecycle:

- **Retention Service**: Cleans old data based on `RETENTION_DAYS`
- **Health Monitoring**: Tracks API health and metrics
- **Checkout Queue**: Processes purchase requests with `CheckoutQueueService`

Services are managed by `services/startup.py` with proper lifecycle hooks in `main.py`.

## Integration Points

### External API Integration

The bot integrates with external APIs that have different authentication patterns:

- **API v1**: Simple bearer token authentication
- **API v2**: Enhanced with session cookies and dynamic tokens
- **API v3**: Form-based login with CSRF and session management

**Critical**: API v3 requires username/password (not tokens) and handles .onion domains with SOCKS proxy support.

### Payment Integration

Complete cryptocurrency payment system:

```python
# Payment module integration
from payment_module.core import PaymentService
from payment_module.handlers.deposit import router as deposit_router

# Multi-currency support (BTC, ETH, USDT, etc.)
# Automatic currency conversion
# Underpayment/overpayment handling
# Bonus system and VIP features
# Webhook callbacks with HMAC verification
```

### Admin Integration

Admin panel provides real-time API configuration and payment management:

```python
# Configurations persist across restarts
# Admin can test connections, update credentials, monitor health
# Payment analytics and manual verification features
# Changes automatically sync with running services
```

### Database Architecture

Primary MongoDB implementation with comprehensive model system:

```python
# MongoDB with Motor async driver
from database.connection import get_collection
from models.base import BaseDocument, now_utc

# All models inherit from BaseDocument
class User(BaseDocument):
    telegram_id: int
    username: Optional[str]
    created_at: datetime = Field(default_factory=now_utc)

# Collection access with indexes
users = get_collection("users")
user = await users.find_one({"telegram_id": user_id})
```

**Key Features**:

- Motor async driver for high performance
- Automatic index creation on startup
- Comprehensive model validation with Pydantic
- ObjectId handling and datetime timezone management
- Connection pooling and error handling
- SQLite simulation fallback for development (`InMemoryCollection` pattern)

## Key Files to Understand

- `main.py`: Application lifecycle with BotApplication class and graceful shutdown using context managers
- `services/external_api_service.py`: Multi-version API abstraction (3800+ lines) with comprehensive filtering and auto-routing
- `handlers/__init__.py`: Handler registration using Router pattern with `dp.include_router()` in logical order
- `middleware/__init__.py`: Common middleware attachment via `attach_common_middlewares()` with rate limiting and user context
- `api_v3/`: Session-based authentication with form login and SOCKS proxy support for .onion domains
- `admin/services/`: Real-time configuration management without code deployment
- `config/settings.py`: Pydantic-based settings with env validation, type safety, and fallback configuration
- `utils/central_logger.py`: Unified logging system replacing all direct logging imports
- `utils/multi_file_logger.py`: Advanced categorized logging with performance tracking and shutdown handling
- `utils/card_data_extractor.py`: Single source of truth for ALL card data extraction across all API versions (2400+ lines of comprehensive extraction logic)
- `database/connection.py`: MongoDB with SQLite simulation fallback via `InMemoryCollection` pattern
- `shared_api/`: Configuration-driven HTTP client system for unified API management
- `start_payment_server.py`: Standalone Flask callback server for webhook processing with automatic database initialization

## Testing Approach

- **Manual Testing**: Use `/admin` panel for API connection testing
- **Payment Testing**: Comprehensive payment module tests in `payment_module/tests/`
- **Integration Tests**: `test_payment_integration.py`, `test_enhanced_payment_integration.py`
- **API Testing**: `test_api_v1_rc_verification.py` for API validation
- **Mock Data**: `test_handlers.py` provides test data endpoints
- **Health Checks**: `/health` command shows system status
- **Environment Switching**: Change `EXTERNAL_API_VERSION` for different API testing

## Common Gotchas

1. **API Version Mismatch**: Ensure `EXTERNAL_API_VERSION` matches your credentials
2. **Database Fallback**: MongoDB connection failures auto-fallback to SQLite simulation via `InMemoryCollection` pattern
3. **Session Persistence**: API v3 sessions persist in `config/external_auth.json` for development
4. **Rate Limiting**: Each handler type has different limits - messages (10/min), callbacks (20/min), purchases (3/min, 50/day)
5. **Windows Compatibility**: Signal handlers disabled on Windows (uses KeyboardInterrupt instead in `main.py`)
6. **uvloop**: Not supported on Windows - automatically skipped in event loop setup
7. **Multi-Logger Initialization**: Multi-file logger must be enabled via `LOG_MULTI_FILE_ENABLED=true`
8. **Service Lifecycle**: Services use singleton pattern with async context managers - always use `async with`
9. **Card Extraction**: Always use `card_data_extractor.py` - never create extraction logic elsewhere
10. **UI Components**: Use `ui_components.py` and `enhanced_keyboards.py` for consistent messaging
11. **Multi-Logger Initialization**: Multi-file logger must be enabled via `LOG_MULTI_FILE_ENABLED=true`
12. **Service Lifecycle**: Services use singleton pattern with async context managers - always use `async with`
13. **Card Extraction**: Always use `card_data_extractor.py` - never create extraction logic elsewhere
14. **UI Components**: Use `ui_components.py` and `enhanced_keyboards.py` for consistent messaging

## Windows Development Notes

- **PowerShell**: Use PowerShell syntax for environment variables (`$env:VARIABLE="value"`)
- **Signal Handling**: Windows uses KeyboardInterrupt instead of Unix signals
- **Path Separators**: Code handles cross-platform paths automatically
- **uvloop**: Excluded on Windows platform - uses standard asyncio event loop

## Environment Configuration

### Required Environment Variables

```bash
# Core Configuration
BOT_TOKEN="*********0:ABC-DEF..."         # Telegram bot token
EXTERNAL_API_VERSION="v3"                 # API version (v1, v2, v3)
USE_MONGODB="false"                       # Database backend selection
ADMIN_USER_IDS="*********,*********"      # Comma-separated admin IDs

# API v3 Configuration (session-based)
EXTERNAL_V3_BASE_URL="https://example.com"
EXTERNAL_V3_USERNAME="username"
EXTERNAL_V3_PASSWORD="password"
EXTERNAL_V3_USE_TOR_PROXY="false"         # For .onion domains

# Payment Module Configuration
OXA_PAY_API_KEY="your_api_key"           # OXAPay API key
OXA_PAY_CALLBACK_URL=""                  # Auto-generated callback URL
PAYMENT_DEVELOPMENT_MODE="true"          # Sandbox mode for testing
PAYMENT_TESTING_MODE="false"            # Skip HMAC verification for testing
PAYMENT_DEBUG_MODE="false"              # Enable payment debug logging
PAYMENT_MIN_AMOUNT="10.0"               # Minimum payment amount
PAYMENT_MAX_AMOUNT="1000.0"             # Maximum payment amount
PAYMENT_CALLBACK_PORT="3000"            # Payment callback server port

# Optional Features
RETENTION_ENABLED="true"                  # Data cleanup
METRICS_ENABLED="true"                    # Prometheus metrics
LOG_LEVEL="INFO"                          # Logging verbosity
LOG_TO_FILE="false"                       # Enable file logging
LOG_STRUCTURED="true"                     # JSON log format
```

### Configuration Priority Order

1. Environment variables
2. `.env` file in project root
3. `config.example.env` fallback
4. Default values in `config/settings.py`

## Service Layer Architecture

### Service Lifecycle Pattern

```python
# Services follow singleton pattern with async context managers
from services.external_api_service import get_external_api_service

async with get_external_api_service() as api_service:
    result = await api_service.list_items()
    # Service automatically closes connections
```

### Background Service Management

Services are automatically managed by the application lifecycle:

```python
# In main.py - services start during bot setup
from services.startup import initialize_services
await initialize_services()  # Starts checkout queue, health monitoring

# Background tasks include:
# - API health monitoring (every 5 minutes)
# - Data retention cleanup (daily at 2 AM)
# - Usage metrics collection (hourly)
# - Checkout queue processing (continuous)
```

## Database Patterns

### Model Usage

```python
# All models inherit from BaseDocument
from models.user import User
from models.base import now_utc

# Creating new documents
user = User(
    telegram_id=*********,
    username="testuser",
    created_at=now_utc()
)

# Converting between formats
user_dict = user.to_mongo()  # For database storage
user_obj = User.from_mongo(user_dict)  # From database
```

### Collection Access

```python
# Direct collection access
from database.connection import get_collection
users = get_collection("users")

# Async operations with proper error handling
try:
    user = await users.find_one({"telegram_id": user_id})
    if user:
        user_obj = User.from_mongo(user)
except Exception as e:
    logger.error(f"Database error: {e}")
```

## Security Considerations

### Rate Limiting Implementation

```python
# Built into middleware - automatically applied via attach_common_middlewares()
# Different limits for different operations:
# - Messages: 10/minute (MESSAGES_PER_MINUTE)
# - Callbacks: 20/minute (CALLBACKS_PER_MINUTE)
# - Purchases: 3/minute, 50/day (PURCHASES_PER_MINUTE, PURCHASES_PER_DAY)
```

### Admin Authentication

```python
# Multi-layer admin protection
@admin_required  # Checks ADMIN_USER_IDS
@handle_errors   # Consistent error responses
async def admin_handler(message: Message):
    pass

# Additional security for sensitive operations
from utils.security import check_rate_limit_security
if not check_rate_limit_security(user_id, "admin_auth", max_attempts=3):
    # Block excessive attempts
```

## Debugging & Monitoring

### Health Check Endpoints

```python
# Available via Telegram commands
/health          # Overall system status
/admin → Health  # Detailed service monitoring via admin panel

# Programmatic health checks
from services.health_service import HealthService
health = HealthService()
status = await health.get_system_status()
```

### Logging Strategy

- **Centralized Logging**: Single `utils.central_logger` replaces all other loggers
- **Structured Logging**: JSON format when `LOG_STRUCTURED=true`
- **API Request Logging**: Separate loggers for API operations
- **Security Events**: Audit trail for admin actions
- **Performance Monitoring**: Request timing and resource usage

**Critical**: Always use `from utils.central_logger import get_logger` instead of direct `logging` module.

### Common Debug Commands

```bash
# View logs in real-time (Linux/macOS)
tail -f logs/bot.log

# Check API routing
python scripts/debug_api_routing.py

# Test API connections
python -c "from admin.services.shared_api_admin_service import *; print('Test API configs')"
```

```powershell
# View logs in real-time (Windows PowerShell)
Get-Content logs\bot.log -Wait -Tail 10

# Check API routing
python scripts\debug_api_routing.py

# Test API connections
python -c "from admin.services.shared_api_admin_service import *; print('Test API configs')"
```

## File Organization Guidelines

### Documentation Files

- **No separate docs folders**: Create `.md` files directly in the relevant directory alongside the code they document
- **Inline documentation**: Prefer keeping README files and documentation in the same folder as the feature/module they describe
- **Avoid nested doc structures**: Don't create separate `docs/` folders unless absolutely necessary for large-scale documentation

### Test File Management

- **Temporary test files**: Any test files created for debugging or experimentation should be **deleted immediately after testing is completed**
- **No persistent test clutter**: Clean up temporary `.py`, `.json`, or other test files to keep the workspace organized
- **Exception**: Only keep test files that are part of the permanent test suite structure
- **Current cleanup needed**: Files like `test_session_manager.py` at project root should be moved to `tests/` or removed after use

### Virtual Environment Management

- **Multiple environments**: The project has `.venv/`, `bot_env/`, and `venv/` directories - choose one and activate before development
- **Preferred environment**: Use `.venv/` for new setups (standard Python convention)
- **Environment isolation**: Always activate virtual environment before running commands or installing packages

## Development Priorities

When adding features:

1. **Use existing patterns**: Follow handler/service/model separation
2. **Admin integration**: Add configuration options to admin panel when possible
3. **Error handling**: Always use `@handle_errors()` decorator
4. **Rate limiting**: Consider if new handlers need specific rate limits
5. **API compatibility**: Test across all three API versions if touching external integration
6. **Database consistency**: Use BaseDocument patterns and async operations
7. **Security first**: Apply rate limiting and admin checks where appropriate
8. **File organization**: Follow the file organization guidelines above - no separate docs folders, clean up test files

## Development Principles

### No Complexity Without Reason

- **Keep It Simple**: Avoid over-engineering solutions unless complexity is justified
- **Incremental Development**: Start with simple implementations and enhance when needed
- **Clear Purpose**: Every abstraction, class, or method should have a clear, single purpose
- **Code Readability**: Prioritize readable code over clever solutions

### Implementation Before Modification

**CRITICAL**: Before making any changes to existing functionality:

1. **Check if implemented**: Always verify if the desired functionality already exists
2. **Read existing code**: Understand current patterns and implementations thoroughly
3. **Update, don't recreate**: If functionality exists but needs improvement, update it
4. **Remove redundancy**: Clean up old, redundant, or deprecated code after updates
5. **Test thoroughly**: Ensure changes don't break existing functionality

### No Fallback Data Policy

**STRICT RULE**: Unless explicitly requested by the user:

- **No mock data generation**: Don't create fake cards, products, or API responses
- **No fallback content**: Don't generate placeholder data when APIs fail
- **Real data only**: All displayed data must come from actual API sources
- **Exception handling**: Show appropriate error messages instead of fake data
- **Test data exception**: Only `handlers/test_handlers.py` may use mock data, clearly labeled as "TEST"

## Development Principles

### No Complexity Without Reason

- **Keep It Simple**: Avoid over-engineering solutions unless complexity is justified
- **Incremental Development**: Start with simple implementations and enhance when needed
- **Clear Purpose**: Every abstraction, class, or method should have a clear, single purpose
- **Code Readability**: Prioritize readable code over clever solutions

### Implementation Before Modification

**CRITICAL**: Before making any changes to existing functionality:

1. **Check if implemented**: Always verify if the desired functionality already exists
2. **Read existing code**: Understand current patterns and implementations thoroughly
3. **Update, don't recreate**: If functionality exists but needs improvement, update it
4. **Remove redundancy**: Clean up old, redundant, or deprecated code after updates
5. **Test thoroughly**: Ensure changes don't break existing functionality

### No Fallback Data Policy

**STRICT RULE**: Unless explicitly requested by the user:

- **No mock data generation**: Don't create fake cards, products, or API responses
- **No fallback content**: Don't generate placeholder data when APIs fail
- **Real data only**: All displayed data must come from actual API sources
- **Exception handling**: Show appropriate error messages instead of fake data
- **Test data exception**: Only `handlers/test_handlers.py` may use mock data, clearly labeled as "TEST"
