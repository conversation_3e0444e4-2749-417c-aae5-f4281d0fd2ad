"""
Enhanced Checkout Notifications

This module provides improved user notifications and checkout processing messages
with better user experience, clearer guidance, and more engaging communication.
"""

from __future__ import annotations

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from utils.central_logger import get_logger
from services.notification_service import NotificationService
from utils.post_checkout_ui import PostCheckoutUI, OrderSummary

logger = get_logger()


class NotificationType(Enum):
    """Enhanced notification types for better categorization"""
    ORDER_QUEUED = "order_queued"
    ORDER_PROCESSING = "order_processing" 
    ORDER_COMPLETED = "order_completed"
    ORDER_FAILED = "order_failed"
    CARD_READY = "card_ready"
    CARD_EXPIRING = "card_expiring"
    PAYMENT_SUCCESS = "payment_success"
    PAYMENT_FAILED = "payment_failed"
    WALLET_LOW = "wallet_low"
    SYSTEM_MAINTENANCE = "system_maintenance"


@dataclass
class CheckoutNotificationData:
    """Data structure for checkout notifications"""
    user_id: int
    order_id: str
    transaction_id: str
    total_amount: float
    item_count: int
    remaining_balance: float
    purchased_cards: List[Dict[str, Any]]
    error_message: Optional[str] = None
    queue_position: Optional[int] = None
    estimated_wait: Optional[int] = None


class EnhancedCheckoutNotifications:
    """Enhanced notification system for checkout processes"""
    
    def __init__(self, notification_service: NotificationService):
        self.notification_service = notification_service
        
    async def send_order_queued_notification(
        self, 
        data: CheckoutNotificationData,
        queue_position: int,
        estimated_wait: int
    ) -> bool:
        """Send enhanced order queued notification"""
        try:
            message = self._format_queued_message(data, queue_position, estimated_wait)
            
            return await self.notification_service.send_notification(
                user_id=data.user_id,
                message=message,
                notification_type="info",
                priority="normal"
            )
        except Exception as e:
            logger.error(f"Error sending order queued notification: {e}")
            return False
    
    async def send_order_processing_notification(
        self, 
        data: CheckoutNotificationData
    ) -> bool:
        """Send order processing notification with progress updates"""
        try:
            message = self._format_processing_message(data)
            
            return await self.notification_service.send_notification(
                user_id=data.user_id,
                message=message,
                notification_type="info",
                priority="normal"
            )
        except Exception as e:
            logger.error(f"Error sending processing notification: {e}")
            return False
    
    async def send_order_completed_notification(
        self, 
        data: CheckoutNotificationData,
        include_card_details: bool = True
    ) -> bool:
        """Send comprehensive order completion notification"""
        try:
            # Send main completion notification
            main_message = self._format_completion_message(data, include_card_details)
            
            success = await self.notification_service.send_notification(
                user_id=data.user_id,
                message=main_message,
                notification_type="success",
                priority="high"
            )
            
            # Send follow-up guidance notification after a short delay
            if success:
                asyncio.create_task(
                    self._send_follow_up_guidance(data.user_id, data.purchased_cards)
                )
            
            return success
        except Exception as e:
            logger.error(f"Error sending completion notification: {e}")
            return False
    
    async def send_order_failed_notification(
        self, 
        data: CheckoutNotificationData,
        error_details: str,
        retry_available: bool = True
    ) -> bool:
        """Send detailed order failure notification with recovery options"""
        try:
            message = self._format_failure_message(data, error_details, retry_available)
            
            return await self.notification_service.send_notification(
                user_id=data.user_id,
                message=message,
                notification_type="error",
                priority="high"
            )
        except Exception as e:
            logger.error(f"Error sending failure notification: {e}")
            return False
    
    async def send_card_ready_notification(
        self, 
        user_id: int, 
        card_data: Dict[str, Any],
        order_id: str
    ) -> bool:
        """Send notification when a specific card is ready for use"""
        try:
            message = self._format_card_ready_message(card_data, order_id)
            
            return await self.notification_service.send_notification(
                user_id=user_id,
                message=message,
                notification_type="success",
                priority="normal"
            )
        except Exception as e:
            logger.error(f"Error sending card ready notification: {e}")
            return False
    
    async def send_card_expiring_notification(
        self, 
        user_id: int, 
        card_data: Dict[str, Any],
        hours_remaining: int
    ) -> bool:
        """Send notification when a card is about to expire"""
        try:
            message = self._format_card_expiring_message(card_data, hours_remaining)
            
            return await self.notification_service.send_notification(
                user_id=user_id,
                message=message,
                notification_type="warning",
                priority="high"
            )
        except Exception as e:
            logger.error(f"Error sending card expiring notification: {e}")
            return False
    
    async def send_wallet_low_notification(
        self, 
        user_id: int, 
        current_balance: float,
        order_total: float
    ) -> bool:
        """Send notification when wallet balance is low"""
        try:
            message = self._format_wallet_low_message(current_balance, order_total)
            
            return await self.notification_service.send_notification(
                user_id=user_id,
                message=message,
                notification_type="warning",
                priority="normal"
            )
        except Exception as e:
            logger.error(f"Error sending wallet low notification: {e}")
            return False
    
    def _format_queued_message(
        self, 
        data: CheckoutNotificationData, 
        queue_position: int, 
        estimated_wait: int
    ) -> str:
        """Format order queued message"""
        minutes = estimated_wait // 60
        seconds = estimated_wait % 60
        
        if minutes > 0:
            wait_time = f"{minutes}m {seconds}s"
        else:
            wait_time = f"{seconds}s"
        
        return f"""
🎯 <b>Order Queued Successfully!</b>

📦 <b>Order ID:</b> #{data.order_id[:8]}...
💰 <b>Total:</b> ${data.total_amount:.2f}
🛒 <b>Items:</b> {data.item_count} card{'s' if data.item_count != 1 else ''}

⏳ <b>Queue Status:</b>
• Position: #{queue_position}
• Estimated wait: {wait_time}

💡 <b>What's happening?</b>
Your order is securely queued for processing. We'll notify you when processing begins and when it's complete.

<i>You can cancel anytime while queued.</i>
"""
    
    def _format_processing_message(self, data: CheckoutNotificationData) -> str:
        """Format order processing message"""
        return f"""
⚡ <b>Order Processing Started!</b>

📦 <b>Order ID:</b> #{data.order_id[:8]}...
💰 <b>Total:</b> ${data.total_amount:.2f}

🔄 <b>Processing Steps:</b>
• ✅ Payment verification
• 🔒 Secure card generation  
• 📋 Order validation
• 🎯 Final preparation

⏱️ <b>Estimated completion:</b> 2-3 minutes

<i>You'll receive a notification when your cards are ready!</i>
"""
    
    def _format_completion_message(
        self, 
        data: CheckoutNotificationData, 
        include_card_details: bool
    ) -> str:
        """Format order completion message"""
        message = f"""
🎉 <b>Order Completed Successfully!</b>

📦 <b>Order ID:</b> #{data.order_id[:8]}...
💳 <b>Transaction:</b> #{data.transaction_id[:8]}...
💰 <b>Total Paid:</b> ${data.total_amount:.2f}
💵 <b>Remaining Balance:</b> ${data.remaining_balance:.2f}

✅ <b>Your {data.item_count} card{'s are' if data.item_count != 1 else ' is'} ready!</b>
"""
        
        if include_card_details and data.purchased_cards:
            message += "\n🃏 <b>Your Cards:</b>\n"
            for i, card in enumerate(data.purchased_cards[:3], 1):
                bank = card.get("bank", "Unknown")
                brand = card.get("brand", "")
                level = card.get("level", "")
                price = card.get("price", 0.0)
                
                card_name = f"{bank} {brand} {level}".strip()
                if not card_name or card_name == "Unknown":
                    card_name = f"Card #{i}"
                
                message += f"• {card_name} - ${price:.2f}\n"
            
            if len(data.purchased_cards) > 3:
                message += f"• ... and {len(data.purchased_cards) - 3} more cards\n"
        
        message += """
🚀 <b>What's Next?</b>
• View your cards to unlock full details
• Check card status within 60 seconds
• Download card data when ready
• Keep your cards secure and private

<i>Thank you for your purchase!</i>
"""
        return message
    
    def _format_failure_message(
        self, 
        data: CheckoutNotificationData, 
        error_details: str,
        retry_available: bool
    ) -> str:
        """Format order failure message"""
        message = f"""
❌ <b>Order Failed</b>

📦 <b>Order ID:</b> #{data.order_id[:8]}...
💰 <b>Amount:</b> ${data.total_amount:.2f}

🔍 <b>Issue:</b> {error_details}

"""
        
        if retry_available:
            message += """
🔄 <b>What you can do:</b>
• Check your wallet balance
• Verify your cart contents
• Try the order again
• Contact support if the issue persists
"""
        else:
            message += """
🚫 <b>This order cannot be retried.</b>
Please contact support for assistance.
"""
        
        message += f"""
💡 <b>Need Help?</b>
• Your funds have been preserved
• No charges were made
• Support is available 24/7

<i>We apologize for the inconvenience.</i>
"""
        return message
    
    def _format_card_ready_message(self, card_data: Dict[str, Any], order_id: str) -> str:
        """Format card ready notification"""
        bank = card_data.get("bank", "Unknown")
        brand = card_data.get("brand", "")
        level = card_data.get("level", "")
        price = card_data.get("price", 0.0)
        
        card_name = f"{bank} {brand} {level}".strip()
        if not card_name or card_name == "Unknown":
            card_name = "Your Card"
        
        return f"""
🃏 <b>{card_name} is Ready!</b>

💰 <b>Value:</b> ${price:.2f}
📦 <b>Order:</b> #{order_id[:8]}...

🔓 <b>Next Steps:</b>
• View card to unlock full details
• Check status within 60 seconds
• Download when ready to use
• Keep information secure

<i>Your card is now available in your orders!</i>
"""
    
    def _format_card_expiring_message(
        self, 
        card_data: Dict[str, Any], 
        hours_remaining: int
    ) -> str:
        """Format card expiring notification"""
        bank = card_data.get("bank", "Unknown")
        brand = card_data.get("brand", "")
        level = card_data.get("level", "")
        
        card_name = f"{bank} {brand} {level}".strip()
        if not card_name or card_name == "Unknown":
            card_name = "Your Card"
        
        if hours_remaining <= 1:
            urgency = "🚨 URGENT"
            time_text = "less than 1 hour"
        elif hours_remaining <= 6:
            urgency = "⚠️ WARNING"
            time_text = f"{hours_remaining} hours"
        else:
            urgency = "⏰ REMINDER"
            time_text = f"{hours_remaining} hours"
        
        return f"""
{urgency} <b>{card_name} Expiring Soon!</b>

⏰ <b>Time Remaining:</b> {time_text}
🃏 <b>Card:</b> {card_name}

🔓 <b>Action Required:</b>
• View and unlock your card NOW
• Check card status immediately
• Download card data if needed
• Use card before it expires

<i>Don't lose your purchase - act quickly!</i>
"""
    
    def _format_wallet_low_message(self, current_balance: float, order_total: float) -> str:
        """Format wallet low balance notification"""
        shortfall = order_total - current_balance
        
        return f"""
💰 <b>Low Wallet Balance</b>

💵 <b>Current Balance:</b> ${current_balance:.2f}
🛒 <b>Order Total:</b> ${order_total:.2f}
⚠️ <b>Shortfall:</b> ${shortfall:.2f}

💡 <b>To complete your order:</b>
• Add ${shortfall:.2f} or more to your wallet
• Use the "Add Funds" option
• Choose your preferred payment method

<i>Your cart is saved and ready when you add funds!</i>
"""
    
    async def _send_follow_up_guidance(
        self, 
        user_id: int, 
        purchased_cards: List[Dict[str, Any]]
    ) -> None:
        """Send follow-up guidance notification after order completion"""
        try:
            # Wait 30 seconds before sending guidance
            await asyncio.sleep(30)
            
            guidance_message = self._format_guidance_message(purchased_cards)
            
            await self.notification_service.send_notification(
                user_id=user_id,
                message=guidance_message,
                notification_type="info",
                priority="low"
            )
        except Exception as e:
            logger.error(f"Error sending follow-up guidance: {e}")
    
    def _format_guidance_message(self, purchased_cards: List[Dict[str, Any]]) -> str:
        """Format follow-up guidance message"""
        return f"""
💡 <b>Card Usage Tips</b>

🃏 <b>You have {len(purchased_cards)} card{'s' if len(purchased_cards) != 1 else ''} ready!</b>

🔍 <b>Important Reminders:</b>
• Check card status within 60 seconds for best results
• Cards may expire after 24-48 hours
• Keep card details secure and private
• Use cards responsibly and legally
• Download data when you're ready to use

⚡ <b>Pro Tips:</b>
• Test with small amounts first
• Check status before major purchases
• Save card details securely
• Monitor for any issues

<i>Need help? Contact support anytime!</i>
"""


class CheckoutProgressTracker:
    """Track and manage checkout progress notifications"""
    
    def __init__(self, notification_service: NotificationService):
        self.notifications = EnhancedCheckoutNotifications(notification_service)
        self.active_tracking = {}  # Track active checkout processes
    
    async def start_tracking(
        self, 
        user_id: int, 
        order_id: str,
        checkout_data: CheckoutNotificationData
    ) -> None:
        """Start tracking a checkout process"""
        self.active_tracking[order_id] = {
            "user_id": user_id,
            "data": checkout_data,
            "started_at": datetime.now(timezone.utc),
            "notifications_sent": []
        }
    
    async def update_progress(
        self, 
        order_id: str, 
        status: str,
        additional_data: Dict[str, Any] = None
    ) -> None:
        """Update checkout progress and send appropriate notifications"""
        if order_id not in self.active_tracking:
            return
        
        tracking = self.active_tracking[order_id]
        user_id = tracking["user_id"]
        data = tracking["data"]
        
        # Update data with additional information
        if additional_data:
            for key, value in additional_data.items():
                setattr(data, key, value)
        
        # Send appropriate notification based on status
        if status == "processing":
            await self.notifications.send_order_processing_notification(data)
            tracking["notifications_sent"].append("processing")
        
        elif status == "completed":
            await self.notifications.send_order_completed_notification(data)
            tracking["notifications_sent"].append("completed")
            # Remove from active tracking
            del self.active_tracking[order_id]
        
        elif status == "failed":
            error_details = additional_data.get("error", "Unknown error") if additional_data else "Unknown error"
            retry_available = additional_data.get("retry_available", True) if additional_data else True
            await self.notifications.send_order_failed_notification(data, error_details, retry_available)
            tracking["notifications_sent"].append("failed")
            # Remove from active tracking
            del self.active_tracking[order_id]
    
    def get_tracking_info(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get tracking information for an order"""
        return self.active_tracking.get(order_id)
    
    def cleanup_expired_tracking(self, max_age_hours: int = 24) -> int:
        """Clean up expired tracking entries"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)
        expired_orders = []
        
        for order_id, tracking in self.active_tracking.items():
            if tracking["started_at"] < cutoff_time:
                expired_orders.append(order_id)
        
        for order_id in expired_orders:
            del self.active_tracking[order_id]
        
        return len(expired_orders)

