"""
Simple runner to test _display_final_response fallback behavior.
"""
import asyncio
import json
import sys
from pathlib import Path

# Ensure repo root is importable when running script directly
repo_root = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(repo_root))

from api_v3.http.client import APIV3HTTPClient

async def main():
    # Create client with minimal dummy args; we only need to call the local method
    client = APIV3HTTPClient(base_url="https://example.local", username="test", password="test")

    # Simulate a response_data containing a single section with a table
    response_data = {
        "sections": [
            {
                "heading": "Shop",
                "tables": [
                    {
                        "header": [{"text": "BIN"}, {"text": "F. Name"}, {"text": "Price"}],
                        "rows": [
                            [
                                {"text": "", "input_value": "card_1"},
                                {"text": "John Doe"},
                                {"text": "10$"}
                            ],
                            [
                                {"text": "", "input_value": "card_2"},
                                {"text": "<PERSON>"},
                                {"text": "12$"}
                            ]
                        ]
                    }
                ]
            }
        ],
        "_token": "abc",
        "payload": {"_token": "abc"},
        "metadata": {"content_type": "application/json"}
    }

    result = client._display_final_response(response_data, endpoint="shop_test")
    print("RESULT:")
    print(json.dumps(result, indent=2))
    print("RESPONSE DATA KEYS:", list(response_data.keys()))

if __name__ == '__main__':
    asyncio.run(main())
