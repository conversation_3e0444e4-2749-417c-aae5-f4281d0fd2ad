{"logging_config": {"description": "Multi-file logging configuration for Demo Wallet Bot v3", "version": "1.0", "base_path": "logs", "max_file_size_mb": 50, "backup_count": 10, "retention_days": 30, "log_categories": {"application": {"enabled": true, "level": "INFO", "max_size_mb": 50, "description": "Main application events and flow"}, "errors": {"enabled": true, "level": "ERROR", "max_size_mb": 25, "description": "Error-level events for quick debugging"}, "debug": {"enabled": true, "level": "DEBUG", "max_size_mb": 100, "description": "Detailed debug information"}, "api-requests": {"enabled": true, "level": "DEBUG", "max_size_mb": 75, "description": "Outgoing API requests with parameters"}, "api-responses": {"enabled": true, "level": "DEBUG", "max_size_mb": 75, "description": "API responses and status codes"}, "http-client": {"enabled": true, "level": "DEBUG", "max_size_mb": 50, "description": "HTTP client operations and connections"}, "telegram-bot": {"enabled": true, "level": "INFO", "max_size_mb": 50, "description": "Telegram bot interactions and commands"}, "transactions": {"enabled": true, "level": "INFO", "max_size_mb": 50, "description": "Financial transactions and wallet operations"}, "user-actions": {"enabled": true, "level": "INFO", "max_size_mb": 50, "description": "User behavior tracking and analytics"}, "database": {"enabled": true, "level": "DEBUG", "max_size_mb": 50, "description": "Database operations and queries"}, "performance": {"enabled": true, "level": "DEBUG", "max_size_mb": 50, "description": "Performance metrics and timing data"}, "security": {"enabled": true, "level": "WARNING", "max_size_mb": 25, "description": "Security events and access control"}, "background-services": {"enabled": true, "level": "INFO", "max_size_mb": 50, "description": "Background tasks and scheduled jobs"}, "health-monitor": {"enabled": true, "level": "INFO", "max_size_mb": 25, "description": "System health checks and monitoring"}, "admin-actions": {"enabled": true, "level": "INFO", "max_size_mb": 25, "description": "Administrative operations and configuration changes"}}, "security_filters": {"enabled": true, "patterns": ["BOT_TOKEN", "PAN_NUMBER", "SSN", "EMAIL", "PASSWORD", "API_KEY", "AUTHORIZATION"]}, "log_formats": {"standard": "{timestamp} [{level}] {module}: {message}", "structured": {"timestamp": "{timestamp}", "level": "{level}", "module": "{module}", "message": "{message}", "extra": "{extra_fields}"}}, "performance_thresholds": {"slow_operation_seconds": 5.0, "very_slow_operation_seconds": 10.0, "api_timeout_seconds": 30.0}, "monitoring": {"error_alerting": {"enabled": false, "threshold_per_hour": 10, "notification_methods": ["log"]}, "performance_alerting": {"enabled": false, "slow_query_threshold_seconds": 5.0, "notification_methods": ["log"]}}}}