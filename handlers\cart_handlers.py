"""
Cart management handlers for Telegram bot
"""

from __future__ import annotations

import asyncio

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from services.cart_service import CartService
from utils.handler_helpers import (
    user_validator,
    keyboard_helper,
    error_handler,
    message_formatter,
    callback_parser
)
from utils.product_display import product_formatter
from utils.ui_components import create_message, MessageType, ErrorHandler as UIErrorHandler
from utils.ui_manager import ui_manager
from utils.enhanced_keyboards import (
    SmartKeyboardLayouts,
    create_enhanced_keyboard,
    KeyboardStyle,
    ButtonPriority,
)
from utils.loading_animations import LoadingStages, PURCHASE_STAGES, CART_STAGES
from utils.ui_components import data_formatter
from utils.multi_file_logger import log_user_action, log_transaction
from utils.texts import DEMO_WATERMARK
from middleware import attach_common_middlewares
from datetime import datetime, timezone
from config.settings import get_settings
from utils.central_logger import get_logger

logger = get_logger()

from utils.central_logger import get_logger

logger = get_logger()


class CartStates(StatesGroup):
    """FSM states for cart operations"""

    waiting_quantity = State()


class LocalCartHandlers:
    """Local cart management handlers - handles internal cart operations"""

    def __init__(self):
        self.cart_service = CartService()
        # Reuse shared user service from validation helper to maintain singleton pattern
        self.user_service = user_validator.user_service
        self._max_display_items = 5

    async def local_cart_view_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart view callback"""
        try:
            user_doc = await user_validator.get_user_from_callback(callback)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return
            
            # Create work coroutine for cart service call
            async def cart_work():
                return await self.cart_service.get_cart_contents(str(user_doc.id))
            
            # Run loading stages concurrently with actual cart loading
            cart_contents = await LoadingStages.run_concurrent_loading(
                callback,
                CART_STAGES,
                cart_work(),
                operation_name="Cart View"
            )

            # Build cart message
            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Shopping Cart", "🛒")

            if cart_contents.get("is_empty", True):
                message_builder.add_content(
                    "Your cart is empty right now. Add cards from the catalog to get started."
                )
                message_builder.add_section(
                    "Next Steps",
                    "Browse the catalog and tap any card to place it in your cart.",
                    "💡",
                )
                keyboard = SmartKeyboardLayouts.create_cart_keyboard()
            else:
                items = cart_contents.get("items", [])
                total_items = cart_contents.get("total_items", 0)
                total_amount = cart_contents.get("total_amount", 0.0)

                # Summary section with cleaner formatting
                summary_parts = []
                summary_parts.append(f"<b>Items:</b> {total_items}")
                summary_parts.append(f"<b>Total:</b> {data_formatter.format_currency(total_amount)}")
                
                # Add cleanup notification if items were cleaned
                cleaned_items = cart_contents.get("cleaned_items", 0)
                if cleaned_items > 0:
                    summary_parts.append(f"<b>Cleaned:</b> {cleaned_items} invalid items")
                
                message_builder.add_content(" • ".join(summary_parts))
                message_builder.add_content("<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>")

                # Use centralized card formatter for consistent UI
                from utils.card_ui_formatter import card_ui_formatter
                
                display_items = items[: self._max_display_items]
                for index, item in enumerate(display_items, 1):
                    try:
                        card_data = dict(item.card_data or {})
                        
                        # Skip items with missing or invalid card data
                        if not card_data or not isinstance(card_data, dict):
                            logger.warning(f"Skipping cart item {index} with missing card data")
                            continue
                        
                        # Check if card data has essential information
                        if not any(card_data.get(field) for field in ['bank', 'bin', 'type', 'brand', 'name']):
                            logger.warning(f"Skipping cart item {index} with no meaningful card data")
                            continue
                        
                        # Use centralized formatter for consistent cart item display
                        line_total = item.get_total_price()
                        cart_item_display = card_ui_formatter.format_cart_item_display(
                            card_data, item.quantity, line_total, index
                        )

                        message_builder.add_content(cart_item_display)
                        
                    except Exception as e:
                        logger.error(f"Error displaying cart item {index}: {e}")
                        # Add a fallback display for problematic items
                        message_builder.add_content(
                            f"<b>{index}.</b> ❌ <i>Item data unavailable - will be refreshed during checkout</i>"
                        )

                # Footer section with separator
                message_builder.add_content("<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>")
                
                footer_parts = []
                
                # Expiry info
                expiry_hint = self._format_cart_expiry(cart_contents.get("cart"))
                if expiry_hint:
                    footer_parts.append(expiry_hint)
                
                # Multiple items notice
                if len(items) > len(display_items):
                    remaining = len(items) - len(display_items)
                    footer_parts.append(
                        f"📋 Showing first {len(display_items)} items • {remaining} more at checkout"
                    )
                
                # Action hint
                footer_parts.append("💡 Tap <b>Checkout</b> when ready to proceed")
                
                # Add all footer parts
                for footer_text in footer_parts:
                    message_builder.add_footer(footer_text, italic=False)

                keyboard = SmartKeyboardLayouts.create_cart_keyboard(
                    has_items=True,
                    item_count=total_items,
                    total_value=total_amount,
                )

            await callback.message.edit_text(
                message_builder.build(add_watermark=False),
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            await error_handler.handle_callback_error(callback, e, "view cart")

    def _format_cart_expiry(self, cart) -> str | None:
        """Human-readable expiry hint for the active cart."""
        try:
            if not cart or not getattr(cart, "expires_at", None):
                return None

            expires_at = cart.expires_at
            if expires_at.tzinfo is None:
                expires_at = expires_at.replace(tzinfo=timezone.utc)

            now = datetime.now(timezone.utc)
            delta = expires_at - now

            total_seconds = int(delta.total_seconds())
            if total_seconds <= 0:
                return "⏰ Cart expires very soon"

            hours, remainder = divmod(total_seconds, 3600)
            minutes = remainder // 60

            if hours > 0:
                return f"⏰ Cart expires in {hours}h {minutes}m"
            if minutes > 0:
                return f"⏰ Cart expires in {minutes} minutes"
            return "⏰ Cart expires in under a minute"
        except Exception:
            return None

    async def local_cart_clear_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart clear callback"""
        try:
            # Get user document using helper
            user_doc = await user_validator.get_user_from_callback(callback)
            if not user_doc:
                return

            # Clear cart
            success, message = await self.cart_service.clear_cart(str(user_doc.id))

            if success:
                await callback.answer(f"✅ {message}")
                # Refresh cart view
                await self.local_cart_view_handler(callback)
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            await error_handler.handle_callback_error(callback, e, "clearing cart")

    async def local_cart_checkout_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart checkout callback"""
        try:
            user_doc = await user_validator.get_user_from_callback(callback)
            if not user_doc:
                return

            user_id = str(user_doc.id)
            telegram_user_id = callback.from_user.id

            # Create work coroutine for checkout process
            async def checkout_work():
                cart_contents = await self.cart_service.get_cart_contents(user_id)
                
                if cart_contents.get("is_empty", True):
                    return "empty", None, None
                
                success, message, job_id = await self.cart_service.queue_checkout(
                    user_id, telegram_user_id
                )
                return success, message, job_id
            
            # Run loading stages concurrently with checkout process
            result = await LoadingStages.run_concurrent_loading(
                callback,
                PURCHASE_STAGES,
                checkout_work(),
                operation_name="Cart Checkout"
            )
            
            success, message, job_id = result

            if success == "empty":
                empty_message = create_message(MessageType.INFO)
                empty_message.set_title("Cart Empty", "🛒")
                empty_message.add_content(
                    "Add cards to your cart before starting checkout."
                )
                empty_message.add_section(
                    "Tip",
                    "Browse the catalog and tap a card to add it instantly.",
                    "💡",
                )

                await ui_manager.edit_message_safely(
                    callback,
                    empty_message.build(add_watermark=False),
                    SmartKeyboardLayouts.create_cart_keyboard(),
                )
                await callback.answer("Cart is empty")
                return

            if success:
                confirmation = create_message(MessageType.SUCCESS)
                confirmation.set_title("Order Queued", "⏳")
                confirmation.add_content(message)
                confirmation.add_list_section(
                    "What happens next",
                    [
                        "Order enters the processing queue",
                        "You'll get progress notifications",
                        "Payment captures when processing begins",
                        "Cancel the order anytime while queued",
                    ],
                    "🔔",
                )

                keyboard_builder = create_enhanced_keyboard().set_style(
                    KeyboardStyle.COMPACT, 2
                )
                if job_id:
                    keyboard_builder.add_button(
                        "❌ Cancel Order",
                        f"purchase:cancel:{job_id}",
                        ButtonPriority.DANGER,
                    )
                    keyboard_builder.add_button(
                        "📊 Queue Status",
                        f"purchase:status:{job_id}",
                        ButtonPriority.SECONDARY,
                    )
                keyboard_builder.add_button(
                    "📜 Order History", "menu:history", ButtonPriority.SECONDARY
                )
                keyboard_builder.add_navigation_row(
                    back_text="🔙 Main Menu", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    confirmation.build(add_watermark=False),
                    keyboard_builder.build(),
                )
                # Start background polling to keep the status current
                if job_id:
                    try:
                        chat_id = callback.message.chat.id
                        message_id = callback.message.message_id
                        asyncio.create_task(self._poll_status_updates(chat_id, message_id, job_id))
                    except Exception:
                        pass

            else:
                error_message = UIErrorHandler.format_error_message(
                    "service",
                    details=message,
                    suggestion="Check your wallet balance and cart contents, then try again.",
                )

                keyboard_builder = create_enhanced_keyboard().set_style(
                    KeyboardStyle.COMPACT, 2
                )
                keyboard_builder.add_button(
                    "💰 View Wallet", "menu:wallet", ButtonPriority.SECONDARY
                )
                keyboard_builder.add_button(
                    "🛒 View Cart", "local:cart:view", ButtonPriority.SECONDARY
                )
                keyboard_builder.add_navigation_row(
                    back_text="🔙 Back", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    error_message,
                    keyboard_builder.build(),
                )

        except Exception as e:
            await error_handler.handle_callback_error(callback, e, "checkout")

    async def _build_status_view(self, queue_service, job, job_id: str):
        """Build a consistent status message and keyboard for a queued/processing job."""
        try:
            emoji_map = {
                "queued": "⏳",
                "processing": "🔄",
                "completed": "✅",
                "failed": "❌",
                "cancelled": "❌",
            }

            state = job.status.value
            emoji = emoji_map.get(state, "❓")
            status_name = state.title()

            # Enhanced details per state with better formatting
            if state == "queued":
                queue_position = await queue_service._get_queue_position(job_id)
                per_job_seconds = 30
                estimated_wait = max(0, queue_position * per_job_seconds)
                details = [
                    f"📍 <b>Queue Position:</b> #{queue_position}",
                    f"⏱️ <b>Estimated Wait:</b> {estimated_wait // 60}m {estimated_wait % 60}s",
                    "💡 You can cancel while the order is queued",
                ]
            elif state == "processing":
                details = [
                    "⚡ <b>Processing your order securely...</b>",
                    "🔒 <b>Payment verification:</b> <code>In Progress</code>",
                    "📦 <b>Digital cards:</b> <code>Being Prepared</code>",
                    "🔔 <b>Notification:</b> <i>You'll receive updates when complete</i>",
                ]
            elif state == "completed":
                details = [
                    "🎉 <b>Order completed successfully!</b>",
                    "✅ <b>Digital cards:</b> <code>Ready for Use</code>",
                    "💳 <b>Access:</b> <i>View and manage your cards anytime</i>",
                    "📋 <b>Receipt:</b> <i>Access order details and history</i>",
                ]
            elif state == "failed":
                details = [
                    f"❌ <b>Order failed:</b> {job.last_error or 'Unknown error'}",
                    "🔄 You can try again from your cart",
                    "💬 Contact support if the issue persists",
                ]
            elif state == "cancelled":
                details = ["🚫 Order was cancelled by user"]
            else:
                details = ["❓ Status unknown - please refresh"]

            msg = create_message(MessageType.INFO)
            msg.set_title(f"Order Status: {status_name}", emoji)
            msg.add_list_section("Status Details", details, "📊")

            kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
            kb.add_button("🔄 Refresh", f"purchase:status:{job_id}", ButtonPriority.SECONDARY)
            if state == "queued":
                kb.add_button("❌ Cancel Order", f"purchase:cancel:{job_id}", ButtonPriority.DANGER)
            kb.add_button("📜 Order History", "menu:history", ButtonPriority.SECONDARY)
            kb.add_navigation_row(back_text="🏠 Main Menu", back_callback="menu:main")

            return msg.build(add_watermark=False), kb.build()
        except Exception:
            return (
                "❌ Error building status view",
                None,
            )

    async def _poll_status_updates(self, chat_id: int, message_id: int, job_id: str):
        """Poll and update the message with current queue status for a limited time."""
        try:
            settings = get_settings()
            from aiogram import Bot
            from services.checkout_queue_service import CheckoutQueueService

            bot = Bot(token=settings.BOT_TOKEN)
            queue_service = CheckoutQueueService()

            for _ in range(9):  # ~90 seconds at 10s interval
                try:
                    job = await queue_service.get_job_status(job_id)
                    if not job:
                        break
                    text, markup = await self._build_status_view(queue_service, job, job_id)
                    if text:
                        await bot.edit_message_text(
                            chat_id=chat_id,
                            message_id=message_id,
                            text=text,
                            reply_markup=markup,
                            parse_mode="HTML",
                        )

                    state = job.status.value
                    if state not in ("queued", "processing"):
                        break
                except Exception:
                    pass

                await asyncio.sleep(10)

            await bot.session.close()
        except Exception:
            pass

    async def local_cart_edit_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart edit callback"""
        try:
            user_doc = await user_validator.get_user_from_callback(callback)
            if not user_doc:
                return

            cart_contents = await self.cart_service.get_cart_contents(str(user_doc.id))

            if cart_contents.get("is_empty", True):
                empty_message = create_message(MessageType.INFO)
                empty_message.set_title("Cart is Empty", "🛒")
                empty_message.add_content("Add items before editing quantities.")
                await ui_manager.edit_message_safely(
                    callback,
                    empty_message.build(add_watermark=False),
                    SmartKeyboardLayouts.create_cart_keyboard(),
                )
                return

            items = cart_contents.get("items", [])
            total_items = cart_contents.get("total_items", len(items))

            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Edit Cart Items", "✏️")
            message_builder.add_content(
                "Select an item below to adjust quantities or remove it from your cart."
            )
            message_builder.add_section(
                "Summary",
                f"• Items in cart: <b>{total_items}</b>",
                "📊",
            )

            keyboard_builder = create_enhanced_keyboard().set_style(
                KeyboardStyle.LIST, 1
            )

            for index, item in enumerate(items[:10], 1):
                card_data = dict(item.card_data or {})
                bank_name = card_data.get("bank") or card_data.get("brand") or "Card"
                bin_number = card_data.get("bin", "N/A")
                quantity = item.quantity
                # Always use the cart item's card_id as the identifier to match DB queries
                identifier = item.card_id

                item_summary = f"BIN {bin_number} • Qty {quantity}" if bin_number else f"Qty {quantity}"
                message_builder.add_section(
                    f"Item {index}",
                    f"<b>{bank_name}</b>\n{item_summary}",
                    "💳",
                )

                keyboard_builder.add_button(
                    f"✏️ Edit {bank_name[:24]}",  # trim long labels
                    f"local:cart:edit_item:{identifier}",
                    ButtonPriority.SECONDARY,
                )

            if len(items) > 10:
                message_builder.add_footer(
                    f"Editing first 10 items. There are {len(items) - 10} more in your cart."
                )

            keyboard_builder.add_navigation_row(
                back_text="🔙 Back to Cart", back_callback="local:cart:view"
            )

            await ui_manager.edit_message_safely(
                callback,
                message_builder.build(add_watermark=False),
                keyboard_builder.build(),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in edit cart: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_edit_item_handler(self, callback: CallbackQuery) -> None:
        """Handle edit specific local cart item callback"""
        try:
            # Extract card ID from callback data (local:cart:edit_item:card_id)
            parts = callback.data.split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[3]  # Card ID is a string hash, not an integer

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get cart item details
            cart_item_doc = await self.cart_service.cart_items_collection.find_one(
                {"user_id": str(user_doc.id), "card_id": card_id}
            )

            if not cart_item_doc:
                await callback.answer("❌ Item not found in cart", show_alert=True)
                return

            from models import CartItem

            cart_item = CartItem.from_mongo(cart_item_doc)
            card_data = cart_item.card_data or {}

            bank_name = card_data.get("bank") or card_data.get("brand") or "Card"
            bin_number = card_data.get("bin", "N/A")
            price = cart_item.price_at_add
            quantity = cart_item.quantity

            details_builder = create_message(MessageType.INFO)
            details_builder.set_title("Edit Cart Item", "✏️")

            card_summary_lines = [
                f"💳 <b>{bank_name}</b>",
                f"🔢 BIN: <code>{bin_number}</code>",
                f"💵 Price: {data_formatter.format_currency(price)}",
                f"📦 Quantity: <b>{quantity}</b>",
            ]
            details_builder.add_section(
                "Current item",
                "\n".join(card_summary_lines),
                "🧾",
            )

            keyboard_builder = create_enhanced_keyboard().set_style(
                KeyboardStyle.COMPACT, 2
            )
            keyboard_builder.add_button(
                "➕ Add 1",
                f"local:cart:qc:{card_id}:+1",  # shortened: qty_change -> qc
                ButtonPriority.SUCCESS,
            )
            keyboard_builder.add_button(
                "➖ Remove 1",
                f"local:cart:qc:{card_id}:-1",  # shortened
                ButtonPriority.SECONDARY,
            )
            keyboard_builder.add_button(
                "🗑️ Remove Item",
                f"local:cart:remove_item:{card_id}",
                ButtonPriority.DANGER,
            )
            keyboard_builder.add_navigation_row(
                back_text="🔙 Back", back_callback="local:cart:edit"
            )

            await ui_manager.edit_message_safely(
                callback,
                details_builder.build(add_watermark=False),
                keyboard_builder.build(),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in edit cart item: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_quantity_change_handler(self, callback: CallbackQuery) -> None:
        """Handle local cart quantity change callback (supports old and new formats)."""
        try:
            # Accept both old and new formats:
            # - Old: local:cart:qty_change:<card_id>:+1
            # - New: local:cart:qc:<card_id>:+1
            parts = callback.data.split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            # Determine indices based on prefix length
            # parts[0] = 'local', parts[1] = 'cart', parts[2] in {'qty_change','qc'}
            if parts[2] not in ("qty_change", "qc"):
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            if len(parts) == 5:
                card_id = parts[3]
                change = int(parts[4])
            else:
                # Some misformatted cases might omit the sign; default to +1
                card_id = parts[3]
                change = 1

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get current quantity
            cart_item_doc = await self.cart_service.cart_items_collection.find_one(
                {"user_id": str(user_doc.id), "card_id": card_id}
            )

            if not cart_item_doc:
                await callback.answer("❌ Item not found", show_alert=True)
                return

            current_qty = cart_item_doc["quantity"]
            new_qty = max(0, current_qty + change)

            # Update quantity
            success, message = await self.cart_service.update_cart_item_quantity(
                str(user_doc.id), card_id, new_qty
            )

            if success:
                await callback.answer(f"✅ {message}")
                # Refresh the edit item view
                new_callback = callback.model_copy(update={"data": f"local:cart:edit_item:{card_id}"})
                await self.local_cart_edit_item_handler(new_callback)
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error in quantity change: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def local_cart_remove_item_handler(self, callback: CallbackQuery) -> None:
        """Handle remove local cart item callback"""
        try:
            # Extract card ID from callback data (local:cart:remove_item:card_id)
            parts = callback.data.split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[3]  # Card ID is a string hash, not an integer

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Log user action
            try:
                log_user_action(
                    user_id=user.id,
                    action="cart_remove_item",
                    details={
                        "card_id": card_id,
                        "user_doc_id": str(user_doc.id)
                    }
                )
            except Exception as e:
                pass  # Don't fail the operation if logging fails

            # Create work coroutine for remove operation
            async def remove_work():
                return await self.cart_service.remove_from_cart(
                    str(user_doc.id), card_id
                )
            
            # Run loading stages concurrently with remove operation
            success, message = await LoadingStages.run_concurrent_loading(
                callback,
                CART_STAGES,
                remove_work(),
                operation_name="Remove Cart Item"
            )

            if success:
                await callback.answer(f"✅ {message}")
                # Go back to cart view
                new_callback = callback.model_copy(update={"data": "local:cart:view"})
                await self.local_cart_view_handler(new_callback)
            else:
                await callback.answer(f"❌ {message}", show_alert=True)

        except Exception as e:
            logger.error(f"Error removing cart item: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)


def get_cart_router() -> Router:
    """Create and return local cart router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = LocalCartHandlers()

    # Local cart callback handlers
    router.callback_query.register(
        handlers.local_cart_view_handler, F.data == "local:cart:view"
    )
    router.callback_query.register(
        handlers.local_cart_clear_handler, F.data == "local:cart:clear"
    )
    router.callback_query.register(
        handlers.local_cart_checkout_handler, F.data == "local:cart:checkout"
    )
    router.callback_query.register(
        handlers.local_cart_edit_handler, F.data == "local:cart:edit"
    )
    router.callback_query.register(
        handlers.local_cart_edit_item_handler,
        F.data.startswith("local:cart:edit_item:"),
    )
    router.callback_query.register(
        handlers.local_cart_quantity_change_handler,
        (F.data.startswith("local:cart:qty_change:") | F.data.startswith("local:cart:qc:")),
    )
    router.callback_query.register(
        handlers.local_cart_remove_item_handler,
        F.data.startswith("local:cart:remove_item:"),
    )

    logger.debug("Local cart handlers registered")
    return router
