"""
Utility functions for payment processing

This module contains all utility functions:
- Payment utilities
- Callback factories
- Template helpers
- VIP payment hooks
- Bonus calculator
- Transaction manager
- Payment amount handler
- HMAC verification
- Auto verification
"""

# Import all utility functions
try:
    from .payment_utils import (
        record_payment_completion,
        log_payment,
        safe_update_data,
        clear_state_data,
        format_crypto_amount,
        get_payment_metrics,
        reset_payment_metrics,
        safe_edit_message,
        sanitize_html,
    )
except ImportError as e:
    # Create placeholder functions
    def record_payment_completion(*args, **kwargs):
        raise NotImplementedError(f"payment_utils not available: {e}")
    log_payment = record_payment_completion
    safe_update_data = record_payment_completion
    clear_state_data = record_payment_completion
    format_crypto_amount = record_payment_completion
    get_payment_metrics = record_payment_completion
    reset_payment_metrics = record_payment_completion
    safe_edit_message = record_payment_completion
    sanitize_html = record_payment_completion

try:
    from .callback_factories import (
        DepositCallback,
        PaymentVerificationCallback,
        PaymentCallback,
    )
except ImportError as e:
    DepositCallback = None
    PaymentVerificationCallback = None
    PaymentCallback = None

try:
    from .template_helpers import (
        format_text,
        update_text,
        create_default_templates,
    )
except ImportError as e:
    def format_text(*args, **kwargs):
        raise NotImplementedError(f"template_helpers not available: {e}")
    update_text = format_text
    create_default_templates = format_text

try:
    from .vip_payment_hooks import (
        trigger_vip_check_on_payment,
        trigger_vip_check_on_verification,
    )
except ImportError as e:
    def trigger_vip_check_on_payment(*args, **kwargs):
        return None  # VIP optional
    trigger_vip_check_on_verification = trigger_vip_check_on_payment

try:
    from .bonus_calculator import (
        calculate_deposit_bonus,
        calculate_deposit_bonus_async,
    )
except ImportError as e:
    def calculate_deposit_bonus(*args, **kwargs):
        return {"success": False, "bonus_amount": 0}  # Bonus optional
    calculate_deposit_bonus_async = calculate_deposit_bonus

try:
    from .transaction_manager import (
        create_payment_transaction,
        TransactionManager,
        transaction_context,
    )
except ImportError as e:
    def create_payment_transaction(*args, **kwargs):
        raise NotImplementedError(f"transaction_manager not available: {e}")
    TransactionManager = None
    transaction_context = None

try:
    from .payment_amount_handler import (
        check_payment_amounts,
        handle_underpayment,
        handle_overpayment,
        handle_payment_completion,
        log_payment_amount_analysis,
        create_underpayment_message,
        create_overpayment_message,
        create_payment_completion_message,
        get_underpayment_threshold,
        get_overpayment_threshold,
        format_amount_difference,
    )
except ImportError as e:
    def check_payment_amounts(*args, **kwargs):
        raise NotImplementedError(f"payment_amount_handler not available: {e}")
    handle_underpayment = check_payment_amounts
    handle_overpayment = check_payment_amounts
    handle_payment_completion = check_payment_amounts
    log_payment_amount_analysis = check_payment_amounts
    create_underpayment_message = check_payment_amounts
    create_overpayment_message = check_payment_amounts
    create_payment_completion_message = check_payment_amounts
    get_underpayment_threshold = check_payment_amounts
    get_overpayment_threshold = check_payment_amounts
    format_amount_difference = check_payment_amounts

try:
    from .hmac_verification import (
        create_hmac_verifier,
        verify_payment_callback,
        generate_payment_signature,
        setup_flask_hmac_verification,
        secure_compare,
        generate_secure_token,
        hash_sensitive_data,
    )
except ImportError as e:
    def create_hmac_verifier(*args, **kwargs):
        raise NotImplementedError(f"hmac_verification not available: {e}")
    verify_payment_callback = create_hmac_verifier
    generate_payment_signature = create_hmac_verifier
    setup_flask_hmac_verification = create_hmac_verifier
    secure_compare = create_hmac_verifier
    generate_secure_token = create_hmac_verifier
    hash_sensitive_data = create_hmac_verifier

try:
    from .auto_verification import (
        get_verification_manager,
        start_auto_verification,
        stop_auto_verification,
        add_payment_for_verification,
        get_payment_verification_status,
        get_verification_statistics,
    )
except ImportError as e:
    def get_verification_manager(*args, **kwargs):
        return None  # Auto-verification optional
    start_auto_verification = get_verification_manager
    stop_auto_verification = get_verification_manager
    add_payment_for_verification = get_verification_manager
    get_payment_verification_status = get_verification_manager
    get_verification_statistics = get_verification_manager

# Export all functions
__all__ = [
    # Payment utilities
    'record_payment_completion',
    'log_payment',
    'safe_update_data',
    'clear_state_data',
    'format_crypto_amount',
    'get_payment_metrics',
    'reset_payment_metrics',
    'safe_edit_message',
    'sanitize_html',
    
    # Callback factories
    'DepositCallback',
    'PaymentVerificationCallback',
    'PaymentCallback',
    
    # Template helpers
    'format_text',
    'update_text',
    'create_default_templates',
    
    # VIP payment hooks
    'trigger_vip_check_on_payment',
    'trigger_vip_check_on_verification',
    
    # Bonus calculator
    'calculate_deposit_bonus',
    'calculate_deposit_bonus_async',
    
    # Transaction manager
    'create_payment_transaction',
    'TransactionManager',
    'transaction_context',
    
    # Payment amount handler
    'check_payment_amounts',
    'handle_underpayment',
    'handle_overpayment',
    'handle_payment_completion',
    'log_payment_amount_analysis',
    'create_underpayment_message',
    'create_overpayment_message',
    'create_payment_completion_message',
    'get_underpayment_threshold',
    'get_overpayment_threshold',
    'format_amount_difference',
    
    # HMAC verification
    'create_hmac_verifier',
    'verify_payment_callback',
    'generate_payment_signature',
    'setup_flask_hmac_verification',
    'secure_compare',
    'generate_secure_token',
    'hash_sensitive_data',
    
    # Auto verification
    'get_verification_manager',
    'start_auto_verification',
    'stop_auto_verification',
    'add_payment_for_verification',
    'get_payment_verification_status',
    'get_verification_statistics',
]
