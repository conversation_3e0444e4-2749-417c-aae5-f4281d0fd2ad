# Payment Module Configuration Template

# OXA Pay Configuration
OXA_PAY_API_KEY="your_oxa_pay_api_key_here"
OXA_PAY_CALLBACK_URL="https://yourdomain.com/callback"

# Environment Configuration
DEVELOPMENT_MODE="false"  # Set to "true" for sandbox mode
TESTING_MODE="false"      # Set to "true" to skip HMAC verification
DEBUG_MODE="false"        # Set to "true" for debug logging

# Server Configuration
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT=3000
CALLBACK_PATH="/callback"

# Payment Limits
MIN_DEPOSIT_AMOUNT=10.0
MAX_DEPOSIT_AMOUNT=1000.0

# Currency Configuration
DEFAULT_CURRENCY="USDT"
SUPPORTED_CURRENCIES=["USDT", "BTC", "ETH", "BNB", "SOL"]

# Cache Configuration
CACHE_EXPIRY_SECONDS=30
CURRENCY_PRICE_CACHE_EXPIRY=300

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FILE="payment_module.log"
CALLBACK_LOG_FILE="callback_debug.log"

