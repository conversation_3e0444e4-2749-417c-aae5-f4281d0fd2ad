"""
Comprehensive External API Integration Service

This service provides a consolidated interface for all external API operations
including list, add_to_cart, view_cart, delete_from_cart, and user authentication.
It integrates with the existing admin panel for configuration management.

Key Features:
- Multi-version API support (v1, v2, v3)
- Automatic data filtering and correction for API v3 responses
- Session management and authentication handling
- Comprehensive error handling and logging

Data Filtering:
API v3 responses are automatically filtered using predefined filter mappings
from data/filters/filter_response.json to correct misspelled or inconsistent
data (countries, card schemes, types, levels, bank names).

Based on external API examples and documentation.
"""

from __future__ import annotations
from utils.central_logger import get_logger
from utils.multi_file_logger import (
    get_multi_logger, log_api_request, log_api_response, 
    log_performance, PerformanceTimer, LogCategory, LogLevel
)

logger = get_logger()

import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta
import json
import time
from dataclasses import dataclass, field
from enum import Enum
import time
from pathlib import Path
import os

import aiohttp
from aiohttp import ClientTimeout, ClientSession
from collections import OrderedDict
from urllib.parse import urlencode

from api_v1.services.api_config import get_api_config_service
from api_v1.utils.authentication import AuthenticationHelper
from api_v1.core.config_names import (
    API_V1_BASE1,
    API_V1_CART,
    API_V2_BASE2,
    API_V1_DUMPS,
)
from api_v3.services.browse_service import (
    APIV3BrowseService,
    APIV3BrowseParams,
    APIV3BrowseResponse,
)
from api_v3.services.cart_service import APIV3CartService
from api_v3.config.api_config import APIV3Config, get_api_v3_config_from_env
from admin.models.api_config_storage import (
    AdminAPIConfiguration,
    get_admin_api_service,
)
from shared_api.config.api_config import APIConfiguration as SharedAPIConfiguration
from config.settings import get_settings
from utils.central_logger import get_logger
from utils.performance import monitor_performance
from utils.api_logging import get_api_logger, LogLevel
from utils.data_filter import get_data_filter

logger = get_logger()
api_logger = get_api_logger("external_api_service", LogLevel.DEBUG)


class APIOperation(str, Enum):
    """Supported API operations"""

    LIST_ITEMS = "list_items"
    ADD_TO_CART = "add_to_cart"
    VIEW_CART = "view_cart"
    DELETE_FROM_CART = "delete_from_cart"
    GET_USER_INFO = "get_user_info"
    CHECKOUT = "checkout"
    LIST_ORDERS = "list_orders"
    VIEW_CARD = "view_card"
    VIEW_ORDER = "view_order"
    CHECK_ORDER = "check_order"
    UNMASK_ORDER = "unmask_order"
    UNMASK_CARD = "unmask_card"
    DOWNLOAD_CARD = "download_card"
    FILTERS = "filters"


# Operation-specific timeout configurations
OPERATION_TIMEOUTS = {
    APIOperation.LIST_ITEMS: 30,
    APIOperation.ADD_TO_CART: 30,
    APIOperation.VIEW_CART: 30,
    APIOperation.DELETE_FROM_CART: 30,
    APIOperation.GET_USER_INFO: 30,
    APIOperation.CHECKOUT: 60,  # Checkout may take longer
    APIOperation.LIST_ORDERS: 45,  # Order listing may take longer
    APIOperation.VIEW_CARD: 30,  # View card details
    APIOperation.CHECK_ORDER: 90,  # Check order can take significantly longer
    APIOperation.UNMASK_ORDER: 60,  # Unmask operations may take longer
    APIOperation.DOWNLOAD_CARD: 30,  # Download card data
    APIOperation.FILTERS: 30,
}


@dataclass
class APIResponse:
    """Standardized API response wrapper"""

    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    raw_response: Optional[str] = None
    operation: Optional[APIOperation] = None
    execution_time: Optional[float] = None


@dataclass
class ListItemsParamsV1V2:
    """Parameters for API v1/v2 list items operation"""

    page: int = 1
    limit: int = 10
    base: str = ""
    bank: str = ""
    bin: str = ""
    country: str = ""
    state: str = ""
    city: str = ""
    brand: str = ""
    type: str = ""
    level: str = ""
    zip: str = ""
    price_from: float = 0
    price_to: float = 500
    zip_check: bool = False
    address: bool = False
    phone: bool = False
    email: bool = False
    without_cvv: bool = False
    refundable: bool = False
    expire_this_month: bool = False
    expiring_soon: bool = False
    expiring_next: bool = False
    dob: bool = False
    ssn: bool = False
    mmn: bool = False
    ip: bool = False
    dl: bool = False
    ua: bool = False
    discount: bool = False


@dataclass
class ListItemsParamsV3:
    """Parameters for API v3 list items operation"""

    page: int = 1
    limit: int = 50
    # Location filters
    continent: str = ""
    country: str = ""
    region: str = ""
    city: str = ""
    postal_code: str = ""
    # Card details
    bins: str = ""
    scheme: str = ""
    type: str = ""
    level: str = ""
    selected_bank: str = ""
    searched_bank: str = ""
    # Contact data filters
    with_billing: bool = False
    with_phone: bool = False
    with_dob: bool = False
    # Quality & status filters
    show_medium_valid: bool = False
    expiring_soon: bool = False
    expiring_next: bool = False
    # Advanced filters
    cc_per_bin: bool = False
    ethnicity: str = ""


# Union type for backward compatibility
ListItemsParams = Union[ListItemsParamsV1V2, ListItemsParamsV3]


@dataclass
class ExternalAPIConfig:
    """Configuration for external API service"""

    base_url: str
    login_token: str
    session_cookies: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


class ExternalAPIService:
    """
    Comprehensive external API service for all operations

    This service handles:
    - List items with filtering
    - Add items to cart
    - View cart contents
    - Delete items from cart
    - User authentication and session management
    - Response caching to prevent duplicate API calls
    """

    def __init__(self, api_version: Optional[str] = None):
        self.settings = get_settings()
        try:
            self.api_config_service = get_api_config_service()
        except Exception:
            # Allow running without DB/admin panel; fall back to defaults
            self.api_config_service = None

        # Response cache to prevent duplicate API calls
        self._response_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl_seconds = 30  # Cache responses for 30 seconds

        # Request deduplication to prevent concurrent identical requests
        self._active_requests: Dict[str, asyncio.Future] = {}
        self._request_lock = asyncio.Lock()

        try:
            self.admin_api_service = get_admin_api_service()
        except Exception:
            self.admin_api_service = None
        # HTTP session not used directly; requests go through unified HTTP client
        self._config_cache: Optional[ExternalAPIConfig] = None
        self._cache_expiry: Optional[datetime] = None
        self._cache_ttl_minutes = 30

        # Dynamic cookie storage for server-provided cookies
        self._dynamic_cookies: Dict[str, str] = {}
        # Persisted auth state (survives restarts) to avoid editing .env constantly
        self._auth_state_path = (
            Path(__file__).resolve().parent.parent / "config" / "external_auth.json"
        )
        self._persisted_login_token: str = ""
        self._load_persisted_auth_state()
        self._session: Optional[ClientSession] = None
        self._auth_helper = AuthenticationHelper()

        # API version routing - use provided version or fall back to global setting
        if api_version is not None:
            self.api_version = api_version.lower()
            logger.debug(
                f"🔵 External API Service initialized with EXPLICIT API version: {self.api_version}"
            )
        else:
            self.api_version = getattr(
                self.settings, "EXTERNAL_API_VERSION", "v2"
            ).lower()
            
            # Auto-detect API v3 if credentials are configured but version not explicitly set
            if self.api_version not in ("v3", "base3"):
                # Check if API v3 credentials are configured
                has_v3_config = (
                    (getattr(self.settings, 'EXTERNAL_V3_BASE_URL', '') or 
                     getattr(self.settings, 'API_V3_BASE_URL', '')) and
                    (getattr(self.settings, 'EXTERNAL_V3_USERNAME', '') or 
                     getattr(self.settings, 'API_V3_USERNAME', '')) and
                    (getattr(self.settings, 'EXTERNAL_V3_PASSWORD', '') or 
                     getattr(self.settings, 'API_V3_PASSWORD', ''))
                )
                
                if has_v3_config:
                    logger.info(f"🔍 Auto-detected API v3 configuration in ExternalAPIService (EXTERNAL_API_VERSION={self.api_version}, but v3 credentials found)")
                    self.api_version = "v3"
            
            logger.debug(
                f"🔵 External API Service initialized with GLOBAL API version: {self.api_version} (from settings.EXTERNAL_API_VERSION)"
            )

        self._api_v3_service: Optional[APIV3BrowseService] = None
        self._api_v3_config: Optional[APIV3Config] = None
        self._api_v3_cart_service: Optional[APIV3CartService] = None
        self._api_v3_order_service: Optional["APIV3OrderService"] = None

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Parameters are required by the async context manager protocol
        await self.close()

    async def close(self):
        """Close HTTP session and all API services with performance logging"""
        try:
            # Log performance stats before closing
            if self._api_v3_service and hasattr(self._api_v3_service, 'get_performance_stats'):
                try:
                    v3_stats = self._api_v3_service.get_performance_stats()
                    logger.info(f"📊 API v3 final performance stats: {v3_stats}")
                except Exception as e:
                    logger.warning(f"Failed to get API v3 final stats: {e}")
        except Exception as e:
            logger.warning(f"Error getting performance stats on close: {e}")
            
        # Close HTTP session
        if self._session and not self._session.closed:
            try:
                await self._session.close()
            except Exception:
                pass

        # Close API v3 service if initialized
        if self._api_v3_service:
            try:
                await self._api_v3_service.close()
            except Exception:
                pass

        if self._api_v3_cart_service:
            try:
                await self._api_v3_cart_service.close()
            except Exception:
                pass

        # Clear cache on close
        self._clear_cache()
        
        logger.info("🏁 ExternalAPIService closed successfully")

    def _create_default_params(self) -> ListItemsParams:
        """Create default parameters based on current API version"""
        if self.api_version == "v3":
            return ListItemsParamsV3()
        else:
            return ListItemsParamsV1V2()
    
    def _convert_to_v3_params(self, params: ListItemsParams) -> ListItemsParamsV3:
        """Convert v1/v2 parameters to v3 parameters"""
        if isinstance(params, ListItemsParamsV3):
            return params
            
        # Convert common fields from v1/v2 to v3
        v3_params = ListItemsParamsV3(
            page=getattr(params, 'page', 1),
            limit=getattr(params, 'limit', 50),
            country=getattr(params, 'country', ''),
            city=getattr(params, 'city', ''),
            type=getattr(params, 'type', ''),
            level=getattr(params, 'level', ''),
            expiring_soon=getattr(params, 'expiring_soon', False),
            expiring_next=getattr(params, 'expiring_next', False),
            # Map v1/v2 fields to v3 equivalents
            bins=getattr(params, 'bin', ''),  # bin -> bins
            selected_bank=getattr(params, 'bank', ''),  # bank -> selected_bank
            scheme=getattr(params, 'brand', ''),  # brand -> scheme
            with_phone=getattr(params, 'phone', False),  # phone -> with_phone
            with_dob=getattr(params, 'dob', False),  # dob -> with_dob
        )
        return v3_params
    
    def _convert_to_v1v2_params(self, params: ListItemsParams) -> ListItemsParamsV1V2:
        """Convert v3 parameters to v1/v2 parameters"""
        if isinstance(params, ListItemsParamsV1V2):
            return params
            
        # Convert v3 fields back to v1/v2
        v1v2_params = ListItemsParamsV1V2(
            page=getattr(params, 'page', 1),
            limit=getattr(params, 'limit', 10),
            country=getattr(params, 'country', ''),
            city=getattr(params, 'city', ''),
            type=getattr(params, 'type', ''),
            level=getattr(params, 'level', ''),
            expiring_soon=getattr(params, 'expiring_soon', False),
            expiring_next=getattr(params, 'expiring_next', False),
            # Map v3 fields to v1/v2 equivalents
            bin=getattr(params, 'bins', ''),  # bins -> bin
            bank=getattr(params, 'selected_bank', ''),  # selected_bank -> bank
            brand=getattr(params, 'scheme', ''),  # scheme -> brand
            phone=getattr(params, 'with_phone', False),  # with_phone -> phone
            dob=getattr(params, 'with_dob', False),  # with_dob -> dob
        )
        return v1v2_params

    def _generate_cache_key(self, operation: APIOperation, **kwargs) -> str:
        """Generate a cache key for the given operation and parameters"""
        import hashlib

        # Create a deterministic key from operation and parameters
        key_data = {
            "operation": operation.value,
            **{k: str(v) for k, v in sorted(kwargs.items()) if v is not None},
        }

        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached response is still valid"""
        if cache_key not in self._response_cache:
            return False

        timestamp = self._cache_timestamps.get(cache_key, 0)
        return (time.time() - timestamp) < self._cache_ttl_seconds

    def _get_cached_response(self, cache_key: str) -> Optional[APIResponse]:
        """Get cached response if valid"""
        if not self._is_cache_valid(cache_key):
            return None

        cached_data = self._response_cache.get(cache_key)
        if cached_data:
            logger.debug(f"🗄️ Returning cached response for key: {cache_key[:8]}...")
            return APIResponse(**cached_data)

        return None

    def _cache_response(self, cache_key: str, response: APIResponse) -> None:
        """Cache a successful response"""
        if response.success:
            self._response_cache[cache_key] = {
                "success": response.success,
                "data": response.data,
                "status_code": response.status_code,
                "operation": response.operation,  # Store operation for invalidation
                "execution_time": response.execution_time,
                "error": response.error,
                "raw_response": None,  # Don't cache raw response to save memory
            }
            self._cache_timestamps[cache_key] = time.time()
            logger.debug(f"💾 Cached response for key: {cache_key[:8]}...")

    def _clear_cache(self) -> None:
        """Clear the response cache"""
        self._response_cache.clear()
        self._cache_timestamps.clear()

    def _cleanup_expired_cache(self) -> None:
        """Remove expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key
            for key, timestamp in self._cache_timestamps.items()
            if (current_time - timestamp) >= self._cache_ttl_seconds
        ]

        for key in expired_keys:
            self._response_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        if expired_keys:
            logger.debug(f"🗑️ Cleaned up {len(expired_keys)} expired cache entries")

    def _invalidate_cart_cache(self) -> None:
        """Invalidate all cart-related cache entries"""
        # Find all cache entries with VIEW_CART operation
        cart_keys = [
            key
            for key, cached_data in self._response_cache.items()
            if isinstance(cached_data, dict) and 
               cached_data.get("operation") == APIOperation.VIEW_CART
        ]

        for key in cart_keys:
            self._response_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        if cart_keys:
            logger.debug(f"🗑️ Invalidated {len(cart_keys)} cart cache entries")
        else:
            logger.debug("🗑️ No cart cache entries found to invalidate")

    async def _deduplicate_request(self, cache_key: str, request_coro) -> APIResponse:
        """
        Deduplicate concurrent identical requests.
        If the same request is already in progress, wait for its result instead of making a new request.
        """
        async with self._request_lock:
            # Check if this request is already in progress
            if cache_key in self._active_requests:
                logger.debug(
                    f"🔄 Request already in progress, waiting: {cache_key[:8]}..."
                )
                # Wait for the existing request to complete
                try:
                    return await self._active_requests[cache_key]
                except Exception as e:
                    # If the existing request failed, remove it and continue with new request
                    self._active_requests.pop(cache_key, None)
                    logger.debug(f"⚠️ Existing request failed, making new request: {e}")

            # Create a new future for this request
            future = asyncio.create_task(request_coro)
            self._active_requests[cache_key] = future

        try:
            # Execute the request
            result = await future
            return result
        finally:
            # Always clean up the active request when done
            async with self._request_lock:
                self._active_requests.pop(cache_key, None)

    # HTTP session is managed by unified HTTP client; no local ensure_session

    async def _ensure_session(self) -> ClientSession:
        """Ensure HTTP session is available"""
        if self._session is None or self._session.closed:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            self._session = ClientSession(
                connector=connector,
                timeout=ClientTimeout(total=60),
            )
        return self._session

    async def _get_api_v3_service(self) -> Optional[APIV3BrowseService]:
        """Get or create API v3 service instance"""
        if self._api_v3_service is not None:
            return self._api_v3_service

        # Try to load configuration
        if self._api_v3_config is None:
            # Try environment variables first
            self._api_v3_config = get_api_v3_config_from_env()

            # If not in env, try to get from admin panel
            if self._api_v3_config is None and self.admin_api_service:
                try:
                    admin_config = await self.admin_api_service.get_api_config("api_v3")
                    if admin_config and getattr(admin_config, "enabled", True):
                        # Extract configuration from admin config
                        base_url = getattr(admin_config, "base_url", "")
                        auth_config = getattr(admin_config, "auth_config", None)
                        if auth_config:
                            username = getattr(auth_config, "username", "")
                            password = getattr(auth_config, "password", "")
                            if base_url and username and password:
                                self._api_v3_config = APIV3Config(
                                    base_url=base_url,
                                    username=username,
                                    password=password,
                                    use_socks_proxy=".onion" in base_url,
                                )
                except Exception as e:
                    logger.warning(
                        f"Failed to load API v3 config from admin panel: {e}"
                    )

        if self._api_v3_config is None:
            logger.error("API v3 configuration not available")
            return None

        # Create service instance
        try:
            # Create optimized API v3 service with enhanced performance settings
            self._api_v3_service = APIV3BrowseService(
                base_url=self._api_v3_config.base_url,
                username=self._api_v3_config.username,
                password=self._api_v3_config.password,
                use_socks_proxy=self._api_v3_config.use_socks_proxy,
                socks_url=self._api_v3_config.socks_url,
                timeout=25,  # Optimized timeout for better performance
            )
            logger.debug("✅ API v3 service initialized with performance optimizations")
            return self._api_v3_service
        except Exception as e:
            logger.error(f"❌ Failed to initialize API v3 service: {e}")
            # Log configuration status for debugging
            if self._api_v3_config:
                logger.error(f"Config available - base_url: {bool(self._api_v3_config.base_url)}, username: {bool(self._api_v3_config.username)}, password: {bool(self._api_v3_config.password)}")
            else:
                logger.error("No API v3 configuration available - check environment variables EXTERNAL_V3_BASE_URL, EXTERNAL_V3_USERNAME, EXTERNAL_V3_PASSWORD")
            return None

    async def _get_api_v3_cart_service(self) -> Optional[APIV3CartService]:
        """Get or create API v3 cart service instance"""
        if self._api_v3_cart_service is not None:
            return self._api_v3_cart_service

        # Ensure configuration is loaded (reuse browse service loader)
        if self._api_v3_config is None:
            await self._get_api_v3_service()

        if self._api_v3_config is None:
            logger.error("API v3 cart configuration not available")
            return None

        try:
            self._api_v3_cart_service = APIV3CartService(
                base_url=self._api_v3_config.base_url,
                username=self._api_v3_config.username,
                password=self._api_v3_config.password,
                use_socks_proxy=self._api_v3_config.use_socks_proxy,
                socks_url=self._api_v3_config.socks_url,
            )
            logger.debug("API v3 cart service initialized successfully")
            return self._api_v3_cart_service
        except Exception as e:
            logger.error(f"Failed to initialize API v3 cart service: {e}")
            return None

    async def _get_api_v3_order_service(self) -> Optional["APIV3OrderService"]:
        """Get or create API v3 order service instance"""
        if (
            hasattr(self, "_api_v3_order_service")
            and self._api_v3_order_service is not None
        ):
            return self._api_v3_order_service

        # Ensure configuration is loaded (reuse browse service loader)
        if self._api_v3_config is None:
            await self._get_api_v3_service()

        if self._api_v3_config is None:
            logger.error("API v3 order configuration not available")
            return None

        try:
            from api_v3.services.order_service import APIV3OrderService

            self._api_v3_order_service = APIV3OrderService(
                base_url=self._api_v3_config.base_url,
                username=self._api_v3_config.username,
                password=self._api_v3_config.password,
                use_socks_proxy=self._api_v3_config.use_socks_proxy,
                socks_url=self._api_v3_config.socks_url,
            )
            logger.debug("API v3 order service initialized successfully")
            return self._api_v3_order_service
        except Exception as e:
            logger.error(f"Failed to initialize API v3 order service: {e}")
            return None

    async def _get_api_config(self) -> Optional[ExternalAPIConfig]:
        """Get API configuration with caching"""
        try:
            # Check cache validity
            if (
                self._config_cache
                and self._cache_expiry
                and datetime.now(timezone.utc) < self._cache_expiry
            ):
                return self._config_cache

            # Prefer admin-managed shared API configuration if available
            admin_config = await self._load_admin_managed_config()
            if admin_config:
                return admin_config

            # Load configuration from API v1 unified config service
            api_config = None
            if self.api_config_service is not None:
                api_config = await self.api_config_service.get_api_config_by_name(
                    API_V1_CART, decrypt_sensitive=True
                )
            # Try alternative name if first lookup fails
            if not api_config and self.api_config_service is not None:
                api_config = await self.api_config_service.get_api_config_by_name(
                    API_V1_BASE1, decrypt_sensitive=True
                )

            if not api_config:
                logger.debug(
                    "No api1_external_cart API configuration found, using default"
                )
                return self._get_default_config()

            # Build configuration details from API v1 config (authentication and headers)
            # Note: API v1 stores auth in api_config.authentication; cookies are expected via env
            token = ""
            try:
                auth = getattr(api_config, "authentication", None)
                if auth and getattr(auth, "bearer_token", None):
                    token = auth.bearer_token or ""
            except Exception:
                token = ""

            # Prefer a persisted login token if none set via admin config
            if not token and self._persisted_login_token:
                token = self._persisted_login_token
            # Fallback to env
            if not token:
                token = getattr(self.settings, "EXTERNAL_LOGIN_TOKEN", "")
                if token:
                    logger.debug(f"🔐 Using EXTERNAL_LOGIN_TOKEN from settings (len: {len(token)})")
                else:
                    logger.warning("❌ No login token found in config or environment")

            # Compose headers using defaults plus any config defaults
            headers = self._get_default_headers()
            try:
                if getattr(api_config, "default_headers", None):
                    headers.update(api_config.default_headers)
            except Exception:
                pass

            # Build cookies merged from persisted auth state and environment variables
            cookies = self._get_env_cookies()

            config = ExternalAPIConfig(
                base_url=api_config.base_url,
                login_token=token,
                session_cookies=cookies,
                headers=headers,
                timeout=30,
                max_retries=3,
                retry_delay=1.0,
            )

            # Update cache
            self._config_cache = config
            self._cache_expiry = datetime.now(timezone.utc) + timedelta(
                minutes=self._cache_ttl_minutes
            )

            return config

        except Exception as e:
            logger.error(f"Error loading API configuration: {e}")
            return self._get_default_config()

    async def _load_admin_managed_config(self) -> Optional[ExternalAPIConfig]:
        """Load configuration managed through the admin shared API system."""
        if not getattr(self, "admin_api_service", None):
            return None

        try:
            candidate_names = [
                API_V1_CART,
                API_V1_BASE1,
                "api_v1",
            ]

            for name in candidate_names:
                admin_config: Optional[AdminAPIConfiguration] = (
                    await self.admin_api_service.get_api_config(name)
                )
                if not admin_config:
                    continue

                if not getattr(admin_config, "enabled", True):
                    logger.debug(
                        "Admin API configuration '%s' is disabled; skipping",
                        name,
                    )
                    continue

                shared_config = admin_config.to_shared_config()
                external_config = self._build_external_config_from_shared(shared_config)

                if external_config:
                    self._config_cache = external_config
                    self._cache_expiry = datetime.now(timezone.utc) + timedelta(
                        minutes=self._cache_ttl_minutes
                    )
                    logger.debug(
                        "Loaded API configuration '%s' from admin management system",
                        name,
                    )
                    return external_config

        except Exception as admin_error:
            logger.warning(
                "Failed to load admin-managed API configuration: %s",
                admin_error,
            )

        return None

    def _build_external_config_from_shared(
        self, shared_config: SharedAPIConfiguration
    ) -> ExternalAPIConfig:
        """Convert shared API configuration into ExternalAPIService config."""

        # Start with baseline headers (lower-case for internal consistency)
        headers = {k.lower(): v for k, v in self._get_default_headers().items() if v}

        default_headers = getattr(shared_config, "default_headers", {}) or {}
        for key, value in default_headers.items():
            if value:
                headers[key.lower()] = value

        # Authentication headers and login token extraction
        login_token = ""
        auth_config = getattr(shared_config, "auth_config", None)
        if auth_config:
            login_token = getattr(auth_config, "bearer_token", "") or ""
            auth_headers = auth_config.get_headers()
            for key, value in (auth_headers or {}).items():
                if value:
                    headers[key.lower()] = value

            custom_headers = getattr(auth_config, "custom_headers", {}) or {}
            for key, value in custom_headers.items():
                if value:
                    headers[key.lower()] = value
        else:
            authentication = getattr(shared_config, "authentication", None)
            if isinstance(authentication, dict):
                login_token = authentication.get(
                    "bearer_token", ""
                ) or authentication.get("token", "")
                for key, value in authentication.items():
                    if isinstance(value, str) and value:
                        headers[key.lower()] = value
            elif authentication is not None:
                login_token = getattr(authentication, "bearer_token", "") or ""
                custom_headers = getattr(authentication, "custom_headers", {}) or {}
                for key, value in custom_headers.items():
                    if value:
                        headers[key.lower()] = value

        # Extract cookies from headers if present
        cookie_header = None
        if "cookie" in headers:
            cookie_header = headers.pop("cookie")

        cookies = self._get_env_cookies()
        if cookie_header:
            cookies.update(self._parse_cookie_header(cookie_header))

        # CRITICAL: Environment token takes priority over admin config demo tokens
        # This allows .env to override the default "demo-token" from admin configs
        env_token = getattr(self.settings, "EXTERNAL_LOGIN_TOKEN", "")
        if env_token and env_token != "demo-token":
            # Real token from .env - use it
            login_token = env_token
            logger.debug(f"🔐 Using real token from EXTERNAL_LOGIN_TOKEN (overriding admin config)")
        elif not login_token or login_token == "demo-token":
            # No valid token yet, try persisted or env
            login_token = self._persisted_login_token or env_token
            if login_token == "demo-token":
                logger.warning("⚠️  Using demo-token - this will cause 401 errors!")

        # CRITICAL: Ensure loginToken cookie matches the actual token being used
        # The cookies dict may have a stale or demo loginToken from parsing
        if login_token and login_token != "demo-token":
            cookies["loginToken"] = login_token
            logger.debug(f"✅ Updated loginToken cookie to match real token (len={len(login_token)})")

        timeout_config = getattr(shared_config, "timeout_config", None)
        timeout = getattr(timeout_config, "total", 60)

        retry_config = getattr(shared_config, "retry_config", None)
        max_retries = getattr(retry_config, "max_attempts", 3)
        retry_delay = getattr(retry_config, "delay", 1.0)

        return ExternalAPIConfig(
            base_url=shared_config.base_url,
            login_token=login_token,
            session_cookies=cookies,
            headers=headers,
            timeout=timeout,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

    @staticmethod
    def _parse_cookie_header(cookie_header: str) -> Dict[str, str]:
        """Parse Cookie header string into dictionary."""
        cookies: Dict[str, str] = {}
        for part in cookie_header.split(";"):
            if "=" not in part:
                continue
            name, value = part.split("=", 1)
            name = name.strip()
            value = value.strip()
            if name and value:
                cookies[name] = value
        return cookies

    def _get_default_config(self) -> ExternalAPIConfig:
        """Get default configuration using shared authentication"""
        from services.shared_auth import get_shared_auth_config

        # Use shared authentication configuration
        shared_auth = get_shared_auth_config()

        # Always prioritize fresh environment variables over shared auth
        # This ensures that fresh cookies from .env always take precedence
        login_token = self._persisted_login_token or getattr(
            self.settings, "EXTERNAL_LOGIN_TOKEN", ""
        )
        session_cookies = self._get_env_cookies()
        
        # If no env cookies, then try shared auth as fallback
        if not session_cookies or not any(session_cookies.values()):
            if shared_auth:
                login_token = shared_auth.login_token
                session_cookies = shared_auth.session_cookies

        # Use environment variable for base URL, fallback to ronaldo-club.to for API v1
        base_url = getattr(self.settings, "EXTERNAL_API_V1_BASE_URL", None)
        if not base_url:
            # Default to ronaldo-club.to for API v1 (production API)
            base_url = "https://ronaldo-club.to/api"

        return ExternalAPIConfig(
            base_url=base_url,
            login_token=login_token,
            session_cookies=session_cookies,
            headers=self._get_default_headers(),
            timeout=30,
            max_retries=3,
            retry_delay=1.0,
        )

    def _get_env_cookies(self) -> Dict[str, str]:
        """
        Build cookie dictionary from environment variables.
        
        IMPORTANT: Environment variables are the SINGLE SOURCE OF TRUTH.
        Persisted cookies are NOT merged to prevent stale cookie issues.
        """
        # First check if EXTERNAL_COOKIES is configured (preferred method)
        external_cookies = getattr(self.settings, "EXTERNAL_COOKIES", "")
        cookies = {}
        
        if external_cookies:
            # Parse the EXTERNAL_COOKIES string (format: key1=value1;key2=value2)
            logger.debug("Loading cookies from EXTERNAL_COOKIES setting")
            for cookie_pair in external_cookies.split(';'):
                if '=' in cookie_pair:
                    key, value = cookie_pair.strip().split('=', 1)
                    if value:  # Only add non-empty values
                        cookies[key] = value
        else:
            # Fallback to individual environment variables (legacy support)
            logger.debug("Loading cookies from individual EXTERNAL_DDG* settings")
            cookies = {
                "__ddg1_": getattr(self.settings, "EXTERNAL_DDG1", ""),
                "__ddg8_": getattr(self.settings, "EXTERNAL_DDG8", ""),
                "__ddg9_": getattr(self.settings, "EXTERNAL_DDG9", ""),
                "__ddg10_": getattr(self.settings, "EXTERNAL_DDG10", ""),
                "_ga": getattr(self.settings, "EXTERNAL_GA", ""),
                "_ga_KZWCRF57VT": getattr(self.settings, "EXTERNAL_GA_KZWCRF57VT", ""),
                "testcookie": "1",
            }
            # Filter out empty values
            cookies = {k: v for k, v in cookies.items() if v}
        
        # DO NOT merge persisted cookies - environment is the single source of truth
        # This prevents stale cached cookies from overriding fresh environment values
        logger.debug(f"Loaded {len(cookies)} cookies from environment (no cache merge)")
        return cookies

    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers matching RC demo patterns exactly

        CRITICAL: Based on RC demo files, API v1 uses session cookies only (no Authorization header).
        Content-Length handled by aiohttp automatically.
        """
        return {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "origin": "https://ronaldo-club.to",
            "priority": "u=1, i",
            "referer": "https://ronaldo-club.to/store/cards/hq",
            "sec-ch-ua": '"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
    
    def _filter_download_fields(self, pipe_delimited_text: str) -> str:
        """
        Filter out unwanted fields from pipe-delimited download response.
        
        Removes: _id, base, paid_price
        
        Args:
            pipe_delimited_text: Raw pipe-delimited text from API
            
        Returns:
            Filtered pipe-delimited text without unwanted fields
        """
        try:
            lines = pipe_delimited_text.strip().split('\n')
            if len(lines) < 2:
                return pipe_delimited_text
            
            # First line is headers
            headers = lines[0].split('|')
            
            # Fields to remove
            fields_to_remove = {'_id', 'base', 'paid_price'}
            
            # Find indices of fields to keep
            indices_to_keep = [
                i for i, header in enumerate(headers) 
                if header.strip() not in fields_to_remove
            ]
            
            # Filter headers
            filtered_headers = [headers[i] for i in indices_to_keep]
            
            # Filter data lines
            # Start with headers, then add blank line
            filtered_lines = [('|'.join(filtered_headers)), '']
            
            for line in lines[1:]:
                if line.strip():  # Skip empty lines (unless it's our intentional blank line)
                    values = line.split('|')
                    if len(values) == len(headers):  # Ensure correct number of values
                        filtered_values = [values[i] for i in indices_to_keep]
                        filtered_lines.append('|'.join(filtered_values))
                    else:
                        # If line has wrong number of values, keep as-is
                        filtered_lines.append(line)
            
            filtered_text = '\n'.join(filtered_lines)
            logger.debug(f"🔧 Filtered out fields: {fields_to_remove}")
            logger.debug(f"📊 Kept {len(indices_to_keep)}/{len(headers)} fields")
            
            return filtered_text
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to filter download fields: {e}, returning original")
            return pipe_delimited_text

    def _build_headers(
        self, config: ExternalAPIConfig, operation: APIOperation
    ) -> Dict[str, str]:
        """Build headers for specific operation based on RC demo patterns"""
        # Base headers from configuration
        headers = config.headers.copy()

        # RC analysis: API v1 authenticates with cookies ONLY, NO Authorization header
        # The loginToken is sent in the Cookie header by aiohttp automatically
        # DO NOT add Authorization header - it will cause 401 errors
        logger.debug(f"🔐 Using cookie-only authentication (no Authorization header needed)")

        # Operation-specific header adjustments matching RC demo exactly
        if operation == APIOperation.LIST_ITEMS:
            # RC demo: POST with no content-type (no body)
            headers.update({
                "referer": "https://ronaldo-club.to/store/cards/hq",
            })
            headers.pop("content-type", None)
            
        elif operation == APIOperation.ADD_TO_CART:
            # RC demo: POST with JSON body
            headers.update({
                "content-type": "application/json",
                "referer": "https://ronaldo-club.to/store/cards/hq",
            })
            
        elif operation == APIOperation.VIEW_CART:
            # RC demo: GET request
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            headers.pop("content-type", None)
            
        elif operation == APIOperation.DELETE_FROM_CART:
            # RC demo: DELETE request
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            headers.pop("content-type", None)
            
        elif operation == APIOperation.GET_USER_INFO:
            # RC demo: GET request with content-type application/json
            headers.update({
                "content-type": "application/json",
                "referer": "https://ronaldo-club.to/",
            })
            
        elif operation == APIOperation.CHECKOUT:
            # RC demo: GET request (no body)
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            headers.pop("content-type", None)
            
        elif operation == APIOperation.LIST_ORDERS:
            headers.update({
                "referer": "https://ronaldo-club.to/orders/cards/hq",
            })
            headers.pop("content-type", None)
            
        elif operation == APIOperation.VIEW_CARD:
            headers.update({
                "content-type": "application/json",
                "referer": "https://ronaldo-club.to/orders/cards/hq",
            })
            
        elif operation == APIOperation.CHECK_ORDER:
            headers.update({
                "content-type": "application/json",
                "referer": "https://ronaldo-club.to/orders/cards/hq",
            })
            
        elif operation == APIOperation.FILTERS:
            headers.update({
                "content-type": "application/json",
                "referer": "https://ronaldo-club.to/store/cards/hq",
            })

        # Always remove content-length - let aiohttp handle it
        headers.pop("content-length", None)
        
        return headers

    def _build_cookies(self, config: ExternalAPIConfig) -> Dict[str, str]:
        """Build cookies with login token and dynamic server-provided cookies"""
        cookies = config.session_cookies.copy()

        # Update with any dynamic cookies provided by the server
        cookies.update(self._dynamic_cookies)

        # Ensure login token is included
        if config.login_token:
            cookies["loginToken"] = config.login_token
            # Track latest login token in memory
            self._persisted_login_token = config.login_token
            # Ensure it's persisted so next run doesn't require .env edits
            self._persist_auth_state()

        # Ensure testcookie is set
        cookies["testcookie"] = "1"
        # Drop empty cookie values to avoid sending blank cookies
        cookies = {k: v for k, v in cookies.items() if v}
        
        logger.debug(f"Built cookies: {', '.join(cookies.keys())} (total: {len(cookies)})")

        return cookies

    def _update_dynamic_cookies(self, response_headers: Dict[str, str]) -> None:
        """Update dynamic cookies from server response and persist auth state"""
        try:
            set_cookie_header = response_headers.get("Set-Cookie", "")
            if not set_cookie_header:
                return

            # Parse Set-Cookie header for __ddg* cookies
            import re

            # Look for __ddg1_, __ddg8_, __ddg9_, __ddg10_ cookies
            ddg_patterns = [
                r"__ddg1_=([^;]+)",
                r"__ddg8_=([^;]+)",
                r"__ddg9_=([^;]+)",
                r"__ddg10_=([^;]+)",
                r"_ga=([^;]+)",
                r"_ga_KZWCRF57VT=([^;]+)",
                r"loginToken=([^;]+)",
            ]

            for pattern in ddg_patterns:
                match = re.search(pattern, set_cookie_header)
                if match:
                    # Extract cookie name from pattern
                    if "__ddg1_" in pattern:
                        cookie_name = "__ddg1_"
                    elif "__ddg8_" in pattern:
                        cookie_name = "__ddg8_"
                    elif "__ddg9_" in pattern:
                        cookie_name = "__ddg9_"
                    elif "__ddg10_" in pattern:
                        cookie_name = "__ddg10_"
                    elif pattern.startswith("_ga="):
                        cookie_name = "_ga"
                    elif "_ga_KZWCRF57VT" in pattern:
                        cookie_name = "_ga_KZWCRF57VT"
                    elif "loginToken" in pattern:
                        cookie_name = "loginToken"
                    else:
                        continue

                    cookie_value = match.group(1)
                    if cookie_name == "loginToken":
                        self._persisted_login_token = cookie_value
                    else:
                        self._dynamic_cookies[cookie_name] = cookie_value
                    # Avoid logging sensitive cookie values
                    logger.debug(f"Updated dynamic cookie: {cookie_name}")

            # Persist any updated auth state
            self._persist_auth_state()

        except Exception as e:
            logger.debug(f"Error updating dynamic cookies: {e}")

    # --- Persistence helpers ---
    def _load_persisted_auth_state(self) -> Optional[Dict[str, Any]]:
        """
        DEPRECATED: Persistent auth state is disabled to prevent stale cookie issues.
        
        Environment variables (EXTERNAL_COOKIES) are the single source of truth.
        This method always returns None to prevent loading stale cached cookies.
        """
        # Persistent auth disabled - always return None to use fresh environment values
        logger.debug("Persistent auth state loading is disabled (environment is source of truth)")
        return None

    def _persist_auth_state(self) -> None:
        """
        DEPRECATED: Persistent auth state is disabled to prevent stale cookie issues.
        
        Environment variables (EXTERNAL_COOKIES) are the single source of truth.
        This method is kept for backward compatibility but does nothing.
        """
        # Persistent auth disabled - environment variables are the single source of truth
        logger.debug("Persistent auth state saving is disabled (environment is source of truth)")
        return

    @staticmethod
    def _format_numeric(value: Any) -> str:
        """Format numeric values to align with the demo request patterns."""
        if value is None:
            return ""
        if isinstance(value, (int, float)):
            as_float = float(value)
            if as_float.is_integer():
                return str(int(as_float))
            return f"{as_float:.2f}".rstrip("0").rstrip(".")
        try:
            as_float = float(str(value))
            if as_float.is_integer():
                return str(int(as_float))
            return f"{as_float:.2f}".rstrip("0").rstrip(".")
        except (ValueError, TypeError):
            return str(value)

    def _build_filter_query_pairs(
        self, params: ListItemsParams
    ) -> list[tuple[str, str]]:
        """Build ordered key/value pairs shared by list and filter endpoints."""

        def add(key: str, value: Any) -> None:
            if value is None:
                text = ""
            else:
                text = str(value)
            pairs.append((key, text))

        pairs: list[tuple[str, str]] = []
        add("base", params.base or "")
        add("bank", params.bank or "")
        add("bin", params.bin or "")
        add("country", params.country or "")
        add("state", params.state or "")
        add("city", params.city or "")
        add("brand", params.brand or "")
        add("type", params.type or "")
        add("level", params.level or "")
        add("zip", params.zip or "")
        add("priceFrom", self._format_numeric(params.price_from))
        add("priceTo", self._format_numeric(params.price_to))
        add("zipCheck", "true" if params.zip_check else "false")
        add("address", "true" if params.address else "false")
        add("phone", "true" if params.phone else "false")
        add("email", "true" if params.email else "false")
        add("withoutcvv", "true" if params.without_cvv else "false")
        add("refundable", "true" if params.refundable else "false")
        add("expirethismonth", "true" if params.expire_this_month else "false")
        add("dob", "true" if params.dob else "false")
        add("ssn", "true" if params.ssn else "false")
        add("mmn", "true" if params.mmn else "false")
        add("ip", "true" if params.ip else "false")
        add("dl", "true" if params.dl else "false")
        add("ua", "true" if params.ua else "false")
        add("discount", "true" if params.discount else "false")
        return pairs

    def _build_filter_query_string(
        self, params: ListItemsParams, *, for_body: bool = False
    ) -> str:
        """Build query string for request URL or request body."""
        pairs = self._build_filter_query_pairs(params)
        if for_body:
            return "&".join(f"{key}={value}" for key, value in pairs)
        return urlencode(pairs, doseq=True, safe="")

    def _build_filters_url(
        self, config: ExternalAPIConfig, params: ListItemsParams
    ) -> str:
        base_url = f"{config.base_url}/cards/hq/filters"
        query_string = self._build_filter_query_string(params)
        return f"{base_url}?{query_string}"

    def _build_list_url(
        self, config: ExternalAPIConfig, params: ListItemsParams
    ) -> str:
        """Build URL for list items operation"""
        base_url = f"{config.base_url}/cards/hq/list"
        pairs = [("page", str(int(params.page))), ("limit", str(int(params.limit)))]
        pairs.extend(self._build_filter_query_pairs(params))
        return f"{base_url}?{urlencode(pairs, doseq=True, safe='')}"

    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        cookies: Dict[str, str],
        operation: APIOperation,
        json_data: Optional[Dict[str, Any]] = None,
        max_retries: int = 3,
        user_id: Optional[str] = None,
        timeout: Optional[int] = None,
    ) -> APIResponse:
        """Make HTTP request with comprehensive logging and retry logic"""
        start_time = time.time()

        # Determine timeout for this operation
        if timeout is None:
            timeout = OPERATION_TIMEOUTS.get(operation, 30)

        # Log API request to multi-file logger
        try:
            log_api_request(
                method=method,
                url=url,
                headers={k: v for k, v in headers.items() if k.lower() not in ['authorization']},
                data=json_data,
                duration=None  # Will be updated after response
            )
        except Exception as e:
            logger.warning(f"Failed to log API request: {e}")

        # Create logging context
        context = api_logger.create_context(user_id=user_id, operation=operation.value)

        for attempt in range(max_retries + 1):
            try:
                # Log authentication context
                auth_method = (
                    "bearer_token"
                    if headers.get("Authorization")
                    else "session_cookies" if cookies else "none"
                )
                token_valid = bool(
                    headers.get("Authorization") or cookies.get("loginToken")
                )

                api_logger.log_authentication_context(
                    context=context,
                    auth_method=auth_method,
                    token_valid=token_valid,
                    user_permissions=["api_access"] if token_valid else [],
                    rate_limit_info={},
                )

                # Log the API request
                api_logger.log_request(
                    context=context,
                    method=method,
                    url=url,
                    headers=headers,
                    query_params=None,  # URL already contains query params
                    body=json_data,
                    timeout=float(timeout),
                    retry_count=attempt,
                )

                # Also log to console for immediate debugging
                logger.info(f"🚀 API REQUEST: {method} {url}")
                if json_data:
                    logger.info(f"📦 REQUEST BODY: {json.dumps(json_data, indent=2)[:500]}")
                if cookies:
                    logger.debug(f"🍪 COOKIES: {', '.join(cookies.keys())}")

                # Suppress verbose request debug logs; response logging handled centrally

                # Use aiohttp session directly
                session = await self._ensure_session()
                
                # Build Cookie header explicitly instead of using cookies param
                # This ensures cookies are properly sent with every request
                request_headers = headers.copy()
                if cookies:
                    cookie_header = "; ".join(f"{k}={v}" for k, v in cookies.items())
                    request_headers["Cookie"] = cookie_header
                    logger.debug(f"📤 COOKIE HEADER: {cookie_header[:100]}..." if len(cookie_header) > 100 else f"📤 COOKIE HEADER: {cookie_header}")

                async with session.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    json=json_data,
                    timeout=ClientTimeout(total=timeout),
                ) as http_resp:
                    execution_time = time.time() - start_time
                    raw_response = await http_resp.text()
                    response_headers = dict(http_resp.headers)

                    class _RespLike:
                        status = http_resp.status
                        reason = http_resp.reason

                    response = _RespLike()

                # Response details are logged by API logger; avoid duplicate noisy logs

                # Update dynamic cookies from server response
                self._update_dynamic_cookies(response_headers)

                # Log the API response
                api_logger.log_response(
                    context=context,
                    status_code=response.status,
                    status_message=response.reason or "Unknown",
                    headers=response_headers,
                    body=raw_response,
                    error_type="http_error" if response.status >= 400 else None,
                    error_message=(
                        f"HTTP {response.status}" if response.status >= 400 else None
                    ),
                )

                # Log API response to multi-file logger
                try:
                    log_api_response(
                        status_code=response.status,
                        url=url,
                        response_size=len(raw_response) if raw_response else 0,
                        duration=execution_time,
                        success=response.status < 400
                    )
                except Exception as e:
                    logger.warning(f"Failed to log API response: {e}")

                # Also log to console for immediate debugging
                logger.info(
                    f"📥 API RESPONSE: {response.status} {response.reason or 'OK'} ({execution_time*1000:.1f}ms)"
                )
                if raw_response:
                    # Truncate large responses to prevent terminal flooding
                    if len(raw_response) > 500:
                        truncated = raw_response[:500] + f"... (truncated, {len(raw_response)} chars total)"
                        logger.debug(f"📋 RESPONSE BODY: {truncated}")
                    else:
                        logger.debug(f"📋 RESPONSE BODY: {raw_response}")

                # Parse JSON response
                try:
                    data = json.loads(raw_response) if raw_response else {}
                except json.JSONDecodeError:
                    logger.warning(
                        f"Failed to parse JSON response: {raw_response[:200]}"
                    )
                    data = {}

                    # Log JSON parsing error
                    api_logger.log_response(
                        context=context,
                        status_code=response.status,
                        status_message="JSON Parse Error",
                        headers=response_headers,
                        body=raw_response,
                        error_type="json_parse_error",
                        error_message="Failed to parse JSON response",
                    )

                    data = {"raw_response": raw_response}

                # Special handling for 403 Forbidden errors
                if response.status == 403:
                    api_logger.log_403_error_context(
                        context=context,
                        endpoint=url,
                        auth_method=auth_method,
                        token_status="valid" if token_valid else "invalid",
                        user_role="user" if token_valid else "anonymous",
                        required_permissions=["api_access", operation.value],
                        rate_limit_headers={
                            k: v
                            for k, v in response_headers.items()
                            if k.lower().startswith(
                                ("x-rate", "x-ratelimit", "retry-after")
                            )
                        },
                    )

                # Determine success based on status code and response content
                # CRITICAL FIX: Don't default to True if "success" field is missing
                if response.status == 200 and isinstance(data, dict):
                    api_success = data.get("success")
                    if api_success is True:
                        success = True
                    elif api_success is False:
                        success = False
                    else:
                        success = False
                        logger.warning(
                            f"API response missing or invalid 'success' field: {data}"
                        )
                else:
                    success = False

                return APIResponse(
                    success=success,
                    data=data,
                    status_code=response.status,
                    raw_response=raw_response,
                    operation=operation,
                    execution_time=execution_time,
                    error=(
                        None
                        if success
                        else f"HTTP {response.status}: {raw_response[:200]}"
                    ),
                )

            except asyncio.TimeoutError:
                error_msg = f"Request timeout on attempt {attempt + 1}"
                logger.warning(error_msg)
                if attempt == max_retries:
                    return APIResponse(
                        success=False,
                        error=f"Request timeout after {max_retries + 1} attempts",
                        operation=operation,
                        execution_time=time.time() - start_time,
                    )
                await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

            except Exception as e:
                error_msg = f"Request error on attempt {attempt + 1}: {str(e)}"
                logger.error(error_msg)
                if attempt == max_retries:
                    return APIResponse(
                        success=False,
                        error=f"Request failed after {max_retries + 1} attempts: {str(e)}",
                        operation=operation,
                        execution_time=time.time() - start_time,
                    )
                await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

    @monitor_performance("list_items")
    async def list_items(
        self, params: Optional[ListItemsParams] = None, user_id: Optional[str] = None
    ) -> APIResponse:
        """
        List items with filtering parameters

        Routes to appropriate API version based on configuration.
        Uses response caching to prevent duplicate API calls.
        """
        try:
            # Use default parameters based on API version if none provided
            if params is None:
                params = self._create_default_params()
            
            # Ensure we have the correct parameter type for the API version
            if self.api_version == "v3" and not isinstance(params, ListItemsParamsV3):
                params = self._convert_to_v3_params(params)
            elif self.api_version != "v3" and not isinstance(params, ListItemsParamsV1V2):
                params = self._convert_to_v1v2_params(params)

            # Generate cache key for this request - handle different parameter types
            cache_key = self._generate_cache_key(
                APIOperation.LIST_ITEMS,
                page=params.page,
                limit=params.limit,
                # Use getattr for fields that may not exist in all parameter types
                base=getattr(params, 'base', ''),
                bank=getattr(params, 'bank', getattr(params, 'selected_bank', '')),
                bin=getattr(params, 'bin', getattr(params, 'bins', '')),
                country=getattr(params, 'country', ''),
                state=getattr(params, 'state', getattr(params, 'region', '')),
                city=getattr(params, 'city', ''),
                brand=getattr(params, 'brand', getattr(params, 'scheme', '')),
                type=getattr(params, 'type', ''),
                level=getattr(params, 'level', ''),
                zip=getattr(params, 'zip', getattr(params, 'postal_code', '')),
                price_from=getattr(params, 'price_from', 0),
                price_to=getattr(params, 'price_to', 500),
                # Add API v3 specific parameters to cache key
                continent=getattr(params, 'continent', ''),
                with_billing=getattr(params, 'with_billing', False),
                with_phone=getattr(params, 'with_phone', False),
                with_dob=getattr(params, 'with_dob', False),
                api_version=self.api_version,
                user_id=user_id,
            )

            # Check cache first
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response

            # Clean up expired cache entries periodically
            self._cleanup_expired_cache()

            # Use request deduplication for identical concurrent requests
            async def make_api_request():
                # Route to API v3 if configured
                if self.api_version == "v3":
                    logger.debug("🔵 ROUTING: list_items → API v3 (_list_items_v3)")
                    response = await self._list_items_v3(params, user_id)
                else:
                    # Default to API v2 (or v1)
                    logger.debug(
                        f"🔴 ROUTING: list_items → API {self.api_version} (NOT v3)"
                    )

                    config = await self._get_api_config()
                    if not config:
                        return APIResponse(
                            success=False,
                            error="API configuration not available",
                            operation=APIOperation.LIST_ITEMS,
                        )

                    # Build request components
                    url = self._build_list_url(config, params)
                    headers = self._build_headers(config, APIOperation.LIST_ITEMS)
                    cookies = self._build_cookies(config)

                    # Keep logs concise; response logger prints results
                    logger.debug(
                        f"Listing items with params: page={params.page}, limit={params.limit}"
                    )

                    # Make POST request (as per demo example and real API server expectation)
                    # Important: send no body (Content-Length: 0) to mirror site behavior
                    response = await self._make_request(
                        method="POST",
                        url=url,
                        headers=headers,
                        cookies=cookies,
                        operation=APIOperation.LIST_ITEMS,
                        user_id=user_id,
                        json_data=None,
                    )

                # Cache successful responses
                if response and response.success:
                    self._cache_response(cache_key, response)

                return response

            # Deduplicate concurrent identical requests
            return await self._deduplicate_request(cache_key, make_api_request())

        except Exception as e:
            logger.error(f"Error in list_items: {e}")
            return APIResponse(
                success=False,
                error=f"List items operation failed: {str(e)}",
                operation=APIOperation.LIST_ITEMS,
            )

    async def _list_items_v3(
        self, params: Optional[ListItemsParams] = None, user_id: Optional[str] = None
    ) -> APIResponse:
        """List items using API v3"""
        try:
            logger.debug("🔵 _list_items_v3: Initializing API v3 service...")

            # Get API v3 service
            service = await self._get_api_v3_service()
            if not service:
                logger.error("❌ _list_items_v3: API v3 service not available")
                # Check if configuration exists
                config_status = "Configuration not checked"
                try:
                    config = await self._get_api_config() or get_api_v3_config_from_env()
                    if config:
                        config_status = f"Config exists: base_url={bool(getattr(config, 'base_url', ''))}, username={bool(getattr(config, 'username', ''))}"
                    else:
                        config_status = "No configuration found - check EXTERNAL_V3_BASE_URL, EXTERNAL_V3_USERNAME, EXTERNAL_V3_PASSWORD"
                except Exception as config_error:
                    config_status = f"Config check failed: {config_error}"
                
                return APIResponse(
                    success=False,
                    error=f"API v3 service not available. {config_status}",
                    operation=APIOperation.LIST_ITEMS,
                )

            logger.debug(
                f"✅ _list_items_v3: API v3 service ready (Service ID: {id(service)})"
            )

            # Params should already be converted to ListItemsParamsV3 by the main list_items method
            assert isinstance(params, ListItemsParamsV3), f"Expected ListItemsParamsV3, got {type(params).__name__}"
                
            # Convert API v3 params to APIV3BrowseParams - direct field mapping
            filter_dict = {
                "page": params.page,
                "limit": params.limit,
                "continent": params.continent,
                "country": params.country,
                "region": params.region,
                "city": params.city,
                "postal_code": params.postal_code,
                "bins": params.bins,
                "scheme": params.scheme,
                "type": params.type,
                "level": params.level,
                "selected_bank": params.selected_bank,
                "searched_bank": params.searched_bank,
                "ethnicity": params.ethnicity,
                # Convert boolean fields to appropriate values
                "with_billing": params.with_billing,
                "with_phone": params.with_phone,
                "with_dob": params.with_dob,
                "show_medium_valid": params.show_medium_valid,
                "expiring_soon": params.expiring_soon,
                "expiring_next": params.expiring_next,
                "cc_per_bin": params.cc_per_bin,
            }
            
            api_v3_params = APIV3BrowseParams.from_standard_filters(filter_dict)

            # Make request
            start_time = time.time()
            response: APIV3BrowseResponse = await service.list_items(
                params=api_v3_params,
                user_id=user_id,
            )
            execution_time = time.time() - start_time

            # Convert response to standard format
            if response.success:
                # Apply data filtering to correct misspelled/incorrect data
                filtered_data = get_data_filter().filter_response_data(response.data)
                logger.debug(f"✅ Applied data filtering to API v3 list_items response")

                return APIResponse(
                    success=True,
                    data=filtered_data,
                    status_code=response.status_code,
                    operation=APIOperation.LIST_ITEMS,
                    execution_time=execution_time,
                )
            else:
                return APIResponse(
                    success=False,
                    error=response.error,
                    operation=APIOperation.LIST_ITEMS,
                    execution_time=execution_time,
                )

        except Exception as e:
            logger.error(f"Error in API v3 list_items: {e}", exc_info=True)
            
            # Provide specific error context for common issues
            error_context = ""
            if "configuration" in str(e).lower():
                error_context = " Please check EXTERNAL_V3_BASE_URL, EXTERNAL_V3_USERNAME, and EXTERNAL_V3_PASSWORD environment variables."
            elif "authentication" in str(e).lower() or "login" in str(e).lower():
                error_context = " Authentication failed - check your API v3 credentials."
            elif "connection" in str(e).lower() or "timeout" in str(e).lower():
                error_context = " Network connection issue - check your internet connection and proxy settings."
            
            return APIResponse(
                success=False,
                error=f"API v3 list items failed: {str(e)}{error_context}",
                operation=APIOperation.LIST_ITEMS,
            )

    @monitor_performance("get_filters")
    async def get_filters(
        self,
        filter_name: str,
        params: Optional[ListItemsParams] = None,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        """Fetch filter options using the working demo request structure."""

        try:
            if not filter_name:
                return APIResponse(
                    success=False,
                    error="Filter name is required",
                    operation=APIOperation.FILTERS,
                )

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.FILTERS,
                )

            if params is None:
                params = self._create_default_params()

            url = self._build_filters_url(config, params)
            headers = self._build_headers(config, APIOperation.FILTERS)
            cookies = self._build_cookies(config)
            payload = {
                "name": filter_name,
                "stringfilterData": self._build_filter_query_string(
                    params, for_body=True
                ),
            }

            return await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.FILTERS,
                json_data=payload,
                user_id=user_id,
            )

        except Exception as e:
            logger.error(f"Error in get_filters: {e}")
            return APIResponse(
                success=False,
                error=f"Filter operation failed: {str(e)}",
                operation=APIOperation.FILTERS,
            )

    @monitor_performance("list_orders")
    async def list_orders(
        self, page: int = 1, limit: int = 10, force_api_v3: bool = False
    ) -> APIResponse:
        """
        List recent orders from the external API.

        Routes to appropriate API version:
        - API v3: Uses GET /orders endpoint
        - API v1/v2: Uses GET /cards/hq/orders endpoint

        Args:
            page: Page number (1-based)
            limit: Number of orders per page
            force_api_v3: If True, forces API v3 usage regardless of configured version
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 list_orders despite API version {self.api_version}"
                    )
                logger.debug(
                    f"🔵 ROUTING: list_orders → API v3 (page: {page}, limit: {limit})"
                )
                return await self._list_orders_v3(page, limit)

            logger.debug(
                f"🔴 ROUTING: list_orders → API {self.api_version} (page: {page}, limit: {limit})"
            )

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.LIST_ORDERS,
                )

            # Build URL with query parameters
            url = f"{config.base_url}/cards/hq/orders?page={int(page)}&&limit={int(limit)}"
            headers = self._build_headers(config, APIOperation.LIST_ORDERS)
            cookies = self._build_cookies(config)

            # Perform GET request
            response = await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.LIST_ORDERS,
                json_data=None,
            )
            return response
        except Exception as e:
            logger.error(f"Error in list_orders: {e}")
            return APIResponse(
                success=False,
                error=f"List orders operation failed: {str(e)}",
                operation=APIOperation.LIST_ORDERS,
            )

    async def _list_orders_v3(self, page: int = 1, limit: int = 10) -> APIResponse:
        """List orders using API v3"""
        start_time = time.time()
        context = api_logger.create_context(user_id=None, operation="list_orders")

        try:
            # For API v3, we can use the session to get orders from /orders endpoint
            # This is a simple implementation - in practice, API v3 might not have
            # a separate list orders endpoint, but we'll provide a mock response
            # based on the user's purchase history

            logger.debug(
                "🔵 API v3 list_orders: Using mock response (API v3 orders are viewed individually)"
            )

            api_logger.log_request(
                context=context,
                method="GET",
                url=f"api_v3/orders?page={page}&limit={limit}",
                headers={"X-API-Version": "v3"},
            )

            execution_time = time.time() - start_time

            # Return a mock successful response for API v3
            # In practice, API v3 orders are accessed individually by order ID
            api_logger.log_response(
                context=context,
                status_code=200,
                status_message="OK",
                headers={},
                body={"message": "API v3 orders accessed individually by order ID"},
            )

            return APIResponse(
                success=True,
                data={
                    "success": True,
                    "message": "API v3 orders are accessed individually by order ID",
                    "orders": [],
                    "page": page,
                    "limit": limit,
                    "total": 0,
                },
                operation=APIOperation.LIST_ORDERS,
                execution_time=execution_time,
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Exception in API v3 list_orders: {e}")

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={},
                body={"error": str(e)},
            )

            return APIResponse(
                success=False,
                error=f"API v3 list_orders failed: {str(e)}",
                operation=APIOperation.LIST_ORDERS,
                execution_time=execution_time,
            )

    @monitor_performance("view_card")
    async def view_card(self, order_id: int, force_api_v3: bool = False) -> APIResponse:
        """
        View card details for a specific order.

        Routes to appropriate API version:
        - API v3: Uses view_order + unmask_card methods
        - API v1/v2: Uses POST /cards/hq/order/view endpoint

        Args:
            order_id: The order ID to view card details for
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Returns:
            APIResponse with card details data
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 view_card despite API version {self.api_version}"
                    )
                logger.debug(f"🔵 ROUTING: view_card → API v3 (order ID: {order_id})")

                # For API v3, we need to use view_order method instead
                return await self.view_order(str(order_id), force_api_v3=True)

            logger.debug(
                f"🔴 ROUTING: view_card → API {self.api_version} (order ID: {order_id})"
            )

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.VIEW_CARD,
                )

            url = f"{config.base_url}/cards/hq/order/view"
            headers = self._build_headers(config, APIOperation.VIEW_CARD)
            cookies = self._build_cookies(config)

            payload = {"id": int(order_id)}

            # Log the view card operation start
            logger.info(f"Starting view_card for order_id={order_id}")

            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.VIEW_CARD,
            )

            # Log the result
            if response.success:
                logger.debug(f"View card completed successfully for order_id={order_id}")
            else:
                logger.warning(
                    f"View card failed for order_id={order_id}: {response.error}"
                )

            return response
        except Exception as e:
            logger.error(f"Error in view_card for order_id={order_id}: {e}")
            return APIResponse(
                success=False,
                error=f"View card operation failed: {str(e)}",
                operation=APIOperation.VIEW_CARD,
            )

    @monitor_performance("add_to_cart")
    async def add_to_cart(
        self,
        item_id: Union[int, str],
        product_table_name: str = "Cards",
        force_api_v3: bool = False,
    ) -> APIResponse:
        """
        Add item to cart

        Args:
            item_id: Item ID to add to cart
            product_table_name: Product table name (default: "Cards")
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Based on demo/add_to_cart.py - Uses POST request with JSON payload
        """
        try:
            item_id_str = str(item_id)

            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if not item_id_str.isdigit() and not use_v3:
                logger.info(
                    "Detected non-numeric card ID; routing add_to_cart to API v3"
                )
                return await self._add_to_cart_v3(item_id_str)

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔀 ROUTING: add_to_cart → API v3 (FORCED despite version {self.api_version})"
                    )
                else:
                    logger.debug(
                        f"🔵 ROUTING: add_to_cart → API v3 (version: {self.api_version})"
                    )
                return await self._add_to_cart_v3(item_id_str)

            logger.debug(f"🔴 ROUTING: add_to_cart → API {self.api_version} (NOT v3)")

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.ADD_TO_CART,
                )

            # Build request components
            url = f"{config.base_url}/cart/"
            headers = self._build_headers(config, APIOperation.ADD_TO_CART)
            cookies = self._build_cookies(config)

            # Payload as per demo example
            payload = {"id": item_id, "product_table_name": product_table_name}

            logger.debug(f"Adding item {item_id} to cart")

            # Make POST request with JSON payload
            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.ADD_TO_CART,
            )

            # Response content is logged centrally; keep a concise summary
            logger.debug(
                f"add_to_cart {response.status_code} -> success={response.success}"
            )

            if not response.success:
                logger.error(f"Add to cart failed for item {item_id}: {response.error}")
                if response.data and isinstance(response.data, dict):
                    # Log any additional error details from the API response
                    error_details = (
                        response.data.get("error")
                        or response.data.get("message")
                        or response.data.get("details")
                    )
                    if error_details:
                        logger.error(f"  API Error Details: {error_details}")

            # Invalidate cart cache if item was successfully added
            if response.success:
                self._invalidate_cart_cache()

            return response

        except Exception as e:
            logger.error(f"Error in add_to_cart: {e}")
            return APIResponse(
                success=False,
                error=f"Add to cart operation failed: {str(e)}",
                operation=APIOperation.ADD_TO_CART,
            )

    async def _add_to_cart_v3(self, item_id: str) -> APIResponse:
        """Add item to cart using API v3 cart service"""
        start_time = time.time()
        context = api_logger.create_context(
            user_id=None, operation=APIOperation.ADD_TO_CART.value
        )

        try:
            service = await self._get_api_v3_cart_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 cart service not available",
                    operation=APIOperation.ADD_TO_CART,
                )

            api_logger.log_request(
                context=context,
                method="POST",
                url=f"{service.base_url}/cart",
                headers={"X-API-Version": "v3"},
                query_params=None,
                body={"checked[]": [item_id]},
                timeout=60.0,
                retry_count=0,
            )

            response = await service.add_to_cart([item_id])

            execution_time = time.time() - start_time

            success = bool(response.get("success"))
            status_code = 200 if success else 500

            # Apply data filtering to correct misspelled/incorrect data if response contains item data
            filtered_response = (
                get_data_filter().filter_response_data(response)
                if success
                else response
            )
            if success:
                logger.debug(
                    f"✅ Applied data filtering to API v3 add_to_cart response"
                )

            api_logger.log_response(
                context=context,
                status_code=status_code,
                status_message="OK" if success else "Error",
                headers={"X-API-Version": "v3"},
                body=(
                    json.dumps(filtered_response, default=str)
                    if filtered_response
                    else "{}"
                ),
                error_type=None if success else "api_error",
                error_message=None if success else response.get("error", "Unknown"),
            )

            return APIResponse(
                success=success,
                data=filtered_response,
                status_code=status_code,
                operation=APIOperation.ADD_TO_CART,
                execution_time=execution_time,
                error=(
                    None
                    if success
                    else response.get("error") or response.get("message")
                ),
            )

        except Exception as e:
            logger.error(f"API v3 add_to_cart failed: {e}", exc_info=True)
            execution_time = time.time() - start_time
            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Exception",
                headers={"X-API-Version": "v3"},
                body=str(e),
                error_type="exception",
                error_message=str(e),
            )
            return APIResponse(
                success=False,
                error=f"API v3 add_to_cart failed: {str(e)}",
                operation=APIOperation.ADD_TO_CART,
                execution_time=execution_time,
            )

    @monitor_performance("view_cart")
    async def view_cart(self, force_api_v3: bool = False) -> APIResponse:
        """
        View cart contents

        Args:
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Uses response caching with short TTL since cart contents can change frequently.
        """
        try:
            # Generate cache key for this request
            cache_key = self._generate_cache_key(
                APIOperation.VIEW_CART,
                api_version=self.api_version,
                force_api_v3=force_api_v3,
            )

            # Use shorter TTL for cart since it changes frequently
            original_ttl = self._cache_ttl_seconds
            self._cache_ttl_seconds = 10  # 10 seconds for cart

            # Check cache first
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                self._cache_ttl_seconds = original_ttl  # Restore original TTL
                return cached_response

            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 ROUTING: view_cart → API v3 (FORCED despite version {self.api_version})"
                    )
                else:
                    logger.debug(
                        f"🔵 ROUTING: view_cart → API v3 (version: {self.api_version})"
                    )
                response = await self._view_cart_v3()
            else:
                logger.debug(f"🔴 ROUTING: view_cart → API {self.api_version} (NOT v3)")

                config = await self._get_api_config()
                if not config:
                    return APIResponse(
                        success=False,
                        error="API configuration not available",
                        operation=APIOperation.VIEW_CART,
                    )

                # Build request components
                url = f"{config.base_url}/cart/"
                headers = self._build_headers(config, APIOperation.VIEW_CART)
                cookies = self._build_cookies(config)

                logger.debug("Viewing cart contents")

                # Make GET request
                response = await self._make_request(
                    method="GET",
                    url=url,
                    headers=headers,
                    cookies=cookies,
                    operation=APIOperation.VIEW_CART,
                )

            # Cache successful responses with shorter TTL
            if response and response.success:
                self._cache_response(cache_key, response)

            self._cache_ttl_seconds = original_ttl  # Restore original TTL
            return response

        except Exception as e:
            logger.error(f"Error in view_cart: {e}")
            return APIResponse(
                success=False,
                error=f"View cart operation failed: {str(e)}",
                operation=APIOperation.VIEW_CART,
            )

    async def _view_cart_v3(self) -> APIResponse:
        """View cart using API v3 cart service"""
        start_time = time.time()
        context = api_logger.create_context(
            user_id=None, operation=APIOperation.VIEW_CART.value
        )

        try:
            service = await self._get_api_v3_cart_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 cart service not available",
                    operation=APIOperation.VIEW_CART,
                )

            api_logger.log_request(
                context=context,
                method="GET",
                url=f"{service.base_url}/cart",
                headers={"X-API-Version": "v3"},
                query_params=None,
                body=None,
                timeout=60.0,
                retry_count=0,
            )

            response = await service.view_cart()

            execution_time = time.time() - start_time

            if response.get("success"):
                # Convert API v3 response format to match expected format
                cart_items = response.get("items", [])

                # Apply data filtering to correct misspelled/incorrect data
                filtered_items = get_data_filter().filter_response_data(cart_items)
                logger.debug(f"✅ Applied data filtering to API v3 view_cart response")

                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={},
                    body={"success": True, "data": filtered_items},
                )

                return APIResponse(
                    success=True,
                    data={
                        "data": filtered_items
                    },  # Wrap in data field for compatibility
                    operation=APIOperation.VIEW_CART,
                    execution_time=execution_time,
                )
            else:
                error_msg = response.get("error", "Unknown error")

                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={},
                    body={"success": False, "error": error_msg},
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.VIEW_CART,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={},
                body={"success": False, "error": str(e)},
            )

            return APIResponse(
                success=False,
                error=f"View cart v3 failed: {str(e)}",
                operation=APIOperation.VIEW_CART,
                execution_time=execution_time,
            )

    @monitor_performance("delete_from_cart")
    async def delete_from_cart(self, cart_item_id: Union[int, str]) -> APIResponse:
        """
        Delete item from cart

        Routes to API v3 when configured, otherwise uses legacy API.
        """
        try:
            # Check if we should use API v3
            use_v3 = self.api_version == "v3" or self.api_version == "base3"

            if use_v3:
                # For API v3, use remove_from_cart with single item
                return await self._remove_from_cart_v3([str(cart_item_id)])

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            # Build request components - item ID is part of URL
            url = f"{config.base_url}/cart/{cart_item_id}"
            headers = self._build_headers(config, APIOperation.DELETE_FROM_CART)
            cookies = self._build_cookies(config)

            logger.debug(f"Deleting cart item {cart_item_id}")

            # Make DELETE request
            response = await self._make_request(
                method="DELETE",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.DELETE_FROM_CART,
            )

            # Invalidate cart cache if item was successfully deleted
            if response.success:
                self._invalidate_cart_cache()

            return response

        except Exception as e:
            logger.error(f"Error in delete_from_cart: {e}")
            return APIResponse(
                success=False,
                error=f"Delete from cart operation failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
            )

    async def _remove_from_cart_legacy(self, item_ids: List[str]) -> APIResponse:
        """Remove multiple items from cart using legacy API (individual DELETE requests)"""
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            successful_removals = []
            failed_removals = []

            # Remove each item individually using DELETE requests
            for item_id in item_ids:
                try:
                    url = f"{config.base_url}/cart/{item_id}"
                    headers = self._build_headers(config, APIOperation.DELETE_FROM_CART)
                    cookies = self._build_cookies(config)

                    response = await self._make_request(
                        method="DELETE",
                        url=url,
                        headers=headers,
                        cookies=cookies,
                        operation=APIOperation.DELETE_FROM_CART,
                    )

                    if response.success:
                        successful_removals.append(item_id)
                    else:
                        failed_removals.append({"id": item_id, "error": response.error})

                except Exception as e:
                    failed_removals.append({"id": item_id, "error": str(e)})

            # Return success if at least some items were removed
            if successful_removals:
                result_data = {
                    "removed_items": successful_removals,
                    "removed_count": len(successful_removals),
                }

                if failed_removals:
                    result_data["failed_items"] = failed_removals
                    result_data["failed_count"] = len(failed_removals)

                return APIResponse(
                    success=True,
                    data=result_data,
                    operation=APIOperation.DELETE_FROM_CART,
                )
            else:
                return APIResponse(
                    success=False,
                    error=f"Failed to remove any items. Errors: {failed_removals}",
                    operation=APIOperation.DELETE_FROM_CART,
                )

        except Exception as e:
            return APIResponse(
                success=False,
                error=f"Legacy remove from cart failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
            )

    async def _remove_from_cart_v3(self, card_ids: List[str]) -> APIResponse:
        """Remove specific items from cart using API v3 cart service"""
        start_time = time.time()
        context = api_logger.create_context(
            user_id=None, operation=APIOperation.DELETE_FROM_CART.value
        )

        try:
            service = await self._get_api_v3_cart_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 cart service not available",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            api_logger.log_request(
                context=context,
                method="POST",
                url=f"{service.base_url}/cart",
                headers={"X-API-Version": "v3"},
                query_params=None,
                body={
                    "_method": "PUT",
                    "target": "Remove selected",
                    "checked[]": card_ids,
                },
                timeout=60.0,
                retry_count=0,
            )

            response = await service.remove_from_cart(card_ids)

            execution_time = time.time() - start_time

            if response.get("success"):
                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={},
                    body={"success": True, "removed_ids": card_ids},
                )

                return APIResponse(
                    success=True,
                    data={"removed_ids": card_ids},
                    operation=APIOperation.DELETE_FROM_CART,
                    execution_time=execution_time,
                )
            else:
                error_msg = response.get("error", "Unknown error")

                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={},
                    body={"success": False, "error": error_msg},
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.DELETE_FROM_CART,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={},
                body={"success": False, "error": str(e)},
            )

            return APIResponse(
                success=False,
                error=f"Remove from cart v3 failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
                execution_time=execution_time,
            )

    async def clear_cart(self, force_api_v3: bool = False) -> APIResponse:
        """
        Clear all items from cart

        Args:
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Routes to API v3 when configured or forced, otherwise uses legacy API.
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 clear_cart despite API version {self.api_version}"
                    )
                return await self._clear_cart_v3()

            # Legacy API implementation: First get cart items, then remove them all
            logger.info(f"Clearing cart using API {self.api_version}")

            # Get current cart contents
            view_response = await self.view_cart()
            if not view_response.success:
                return APIResponse(
                    success=False,
                    error=f"Failed to get cart contents for clearing: {view_response.error}",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            cart_data = view_response.data or {}
            items = cart_data.get("items", [])

            if not items:
                # Cart is already empty
                return APIResponse(
                    success=True,
                    data={"message": "Cart was already empty"},
                    operation=APIOperation.DELETE_FROM_CART,
                )

            # Extract item IDs for removal
            item_ids = []
            for item in items:
                if isinstance(item, dict):
                    item_id = item.get("id") or item.get("_id") or item.get("card_id")
                    if item_id:
                        item_ids.append(str(item_id))

            if not item_ids:
                return APIResponse(
                    success=False,
                    error="No valid item IDs found in cart",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            # Remove all items using the existing remove_from_cart method
            remove_response = await self._remove_from_cart_legacy(item_ids)

            if remove_response.success:
                return APIResponse(
                    success=True,
                    data={"message": f"Cleared {len(item_ids)} items from cart"},
                    operation=APIOperation.DELETE_FROM_CART,
                )
            else:
                return APIResponse(
                    success=False,
                    error=f"Failed to clear cart: {remove_response.error}",
                    operation=APIOperation.DELETE_FROM_CART,
                )

        except Exception as e:
            return APIResponse(
                success=False,
                error=f"Clear cart operation failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
            )

    async def _clear_cart_v3(self) -> APIResponse:
        """Clear all items from cart using API v3 cart service"""
        start_time = time.time()
        context = api_logger.create_context(
            user_id=None, operation=APIOperation.DELETE_FROM_CART.value
        )

        try:
            service = await self._get_api_v3_cart_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 cart service not available",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            api_logger.log_request(
                context=context,
                method="POST",
                url=f"{service.base_url}/cart",
                headers={"X-API-Version": "v3"},
                query_params=None,
                body={"_method": "PUT", "target": "Empty cart"},
                timeout=60.0,
                retry_count=0,
            )

            response = await service.clear_cart()

            execution_time = time.time() - start_time

            if response.get("success"):
                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={},
                    body={"success": True, "message": "Cart cleared"},
                )

                return APIResponse(
                    success=True,
                    data={"message": "Cart cleared successfully"},
                    operation=APIOperation.DELETE_FROM_CART,
                    execution_time=execution_time,
                )
            else:
                error_msg = response.get("error", "Unknown error")

                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={},
                    body={"success": False, "error": error_msg},
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.DELETE_FROM_CART,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={},
                body={"success": False, "error": str(e)},
            )

            return APIResponse(
                success=False,
                error=f"Clear cart v3 failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
                execution_time=execution_time,
            )

    @monitor_performance("get_user_info")
    async def get_user_info(self) -> APIResponse:
        """
        Get user information

        Routes to appropriate API version:
        - API v3: Returns mock response (no user/getme endpoint in API v3)
        - API v1/v2: Uses GET request to user/getme endpoint
        """
        try:
            # Check if we should use API v3
            use_v3 = self.api_version == "v3" or self.api_version == "base3"

            if use_v3:
                logger.debug(
                    "🔵 ROUTING: get_user_info → API v3 (mock response - no user endpoint)"
                )
                # API v3 doesn't have a user/getme endpoint
                # Return a mock successful response since balance verification
                # will be handled during the actual checkout process
                return APIResponse(
                    success=True,
                    data={
                        "success": True,
                        "user": {
                            "_id": "api_v3_user",
                            "username": "api_v3_user",
                            "balance": "999999.99",  # High balance to pass validation
                            "status": "active",
                        },
                    },
                    operation=APIOperation.GET_USER_INFO,
                    status_code=200,
                )

            logger.debug(
                f"🔴 ROUTING: get_user_info → API {self.api_version} (user/getme endpoint)"
            )

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.GET_USER_INFO,
                )

            # Build request components
            url = f"{config.base_url}/user/getme"
            headers = self._build_headers(config, APIOperation.GET_USER_INFO)
            cookies = self._build_cookies(config)

            logger.info("Getting user information")

            # Make GET request
            return await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.GET_USER_INFO,
            )
        except Exception as e:
            logger.error(f"Error in get_user_info: {e}")
            return APIResponse(
                success=False,
                error=f"Get user info operation failed: {str(e)}",
                operation=APIOperation.GET_USER_INFO,
            )

    @monitor_performance("checkout")
    async def checkout(self, force_api_v3: bool = False) -> APIResponse:
        """
        Perform checkout operation

        Args:
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Routes to API v3 when configured or forced, otherwise uses legacy API.
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 checkout despite API version {self.api_version}"
                    )
                else:
                    logger.debug(
                        f"🔵 ROUTING: checkout → API v3 (version: {self.api_version})"
                    )
                return await self._checkout_v3()

            logger.debug(f"🔴 ROUTING: checkout → API {self.api_version} (legacy)")
            # Legacy API implementation
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.CHECKOUT,
                )

            url = f"{config.base_url}/cart/checkout"
            headers = self._build_headers(config, APIOperation.CHECKOUT)
            cookies = self._build_cookies(config)

            # Validate critical cookie presence
            if not cookies.get("loginToken"):
                logger.error("Checkout aborted: missing loginToken cookie")
                return APIResponse(
                    success=False,
                    error="Missing login token for checkout",
                    operation=APIOperation.CHECKOUT,
                )

            # Log cookie keys (not values) for traceability
            try:
                logger.debug(
                    "Checkout using cookies: " + ", ".join(sorted(cookies.keys()))
                )
            except Exception:
                pass

            logger.info("Executing external checkout")

            response = await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.CHECKOUT,
                json_data=None,
                max_retries=0,  # Do not retry checkout; run once and report
            )
            if not response.success:
                logger.error(
                    f"Checkout failed: status={response.status_code} "
                    f"body={(response.raw_response or '')[:500]}"
                )
            else:
                # Invalidate cart cache after successful checkout (cart is cleared)
                self._invalidate_cart_cache()

            return response
        except Exception as e:
            logger.error(f"Error in checkout: {e}")
            return APIResponse(
                success=False,
                error=f"Checkout operation failed: {str(e)}",
                operation=APIOperation.CHECKOUT,
            )

    @monitor_performance("view_order")
    async def view_order(
        self, order_id: str, force_api_v3: bool = False
    ) -> APIResponse:
        """
        View order details by order ID

        Routes to appropriate API version:
        - API v3: Uses GET /orders/{order_id} endpoint
        - API v1/v2: Uses legacy order viewing method
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 view_order despite API version {self.api_version}"
                    )
                logger.debug(f"🔵 ROUTING: view_order → API v3 (order ID: {order_id})")
                return await self._view_order_v3(order_id)

            logger.debug(
                f"🔴 ROUTING: view_order → API {self.api_version} (order ID: {order_id})"
            )

            # API v1/v2 implementation
            return await self._view_order_v1(order_id)

        except Exception as e:
            logger.error(f"Error in view_order: {e}")
            return APIResponse(
                success=False,
                error=f"View order operation failed: {str(e)}",
                operation=APIOperation.VIEW_ORDER,
            )

    async def _view_order_v3(self, order_id: str) -> APIResponse:
        """View order using API v3 order service"""
        start_time = time.time()
        context = api_logger.create_context(user_id=None, operation="view_order")

        try:
            logger.debug(f"🔵 Starting API v3 view_order for order_id: {order_id}")

            service = await self._get_api_v3_order_service()
            if not service:
                logger.error(
                    f"🔵 API v3 order service not available for order {order_id}"
                )
                return APIResponse(
                    success=False,
                    error="API v3 order service not available",
                    operation=APIOperation.VIEW_ORDER,
                )

            api_logger.log_request(
                context=context,
                method="GET",
                url=f"{service.base_url}/orders/{order_id}",
                headers={"X-API-Version": "v3"},
            )

            # View order using API v3 order service
            logger.debug(f"🔵 Calling service.view_order for {order_id}")
            result = await service.view_order(order_id)

            execution_time = time.time() - start_time
            logger.debug(f"🔵 API v3 view_order completed in {execution_time:.2f}s")

            if result.get("success"):
                items = result.get("items", [])
                raw_data = result.get("raw_data", {})

                # Apply data filtering to correct misspelled/incorrect data
                filtered_result = get_data_filter().filter_response_data(result)
                logger.debug(f"✅ Applied data filtering to API v3 view_order response")

                logger.info(
                    f"✅ API v3 view_order succeeded - Order ID: {order_id}, Items: {len(items)}"
                )
                logger.info(f"🔵 Raw data keys: {list(raw_data.keys())}")

                # Log item details for debugging
                for i, item in enumerate(items[:3]):  # Log first 3 items
                    logger.debug(f"🔵 Item {i+1}: {list(item.keys())}")

                # Log the COMPLETE API response data to terminal
                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={"Content-Type": "application/json"},
                    body=filtered_result,  # Log the FILTERED result
                )

                return APIResponse(
                    success=True,
                    data=filtered_result,
                    operation=APIOperation.VIEW_ORDER,
                    execution_time=execution_time,
                )
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ API v3 view_order failed for {order_id}: {error_msg}")

                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={},
                    body={"error": error_msg},
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.VIEW_ORDER,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Exception in API v3 view_order: {e}")

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={},
                body={"error": str(e)},
            )

            return APIResponse(
                success=False,
                error=f"API v3 view_order failed: {str(e)}",
                operation=APIOperation.VIEW_ORDER,
                execution_time=execution_time,
            )

    async def _view_order_v1(self, order_id: str | int) -> APIResponse:
        """
        View order using API v1 - POST /api/cards/hq/order/view
        
        This endpoint returns the full card data for a specific order.
        Similar to viewing the card details on the website.
        
        Args:
            order_id: The order/product ID (_id from orders list)
            
        Returns:
            APIResponse with order data
        """
        start_time = time.time()
        
        try:
            # Get API config
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.VIEW_ORDER,
                )
            
            # Convert order_id to int if it's a string
            if isinstance(order_id, str) and order_id.isdigit():
                order_id = int(order_id)
            elif isinstance(order_id, str):
                # Non-numeric order ID not supported for v1
                return APIResponse(
                    success=False,
                    error=f"API v1 view_order requires numeric order ID, got: {order_id}",
                    operation=APIOperation.VIEW_ORDER,
                )
            
            url = f"{config.base_url}/cards/hq/order/view"
            headers = self._build_headers(config, APIOperation.VIEW_ORDER)
            cookies = self._build_cookies(config)
            payload = {"id": order_id}
            
            logger.info(f"📞 [API v1] Calling view_order endpoint for order {order_id}")
            logger.debug(f"📞 [API v1] URL: {url}")
            logger.debug(f"📞 [API v1] Payload: {payload}")
            
            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.VIEW_ORDER,
                max_retries=2,
            )
            
            execution_time = time.time() - start_time
            
            if response.success:
                logger.info(f"✅ [API v1] view_order successful for order {order_id}")
                
                # Extract the card data from response
                data = response.data
                if isinstance(data, dict) and "data" in data:
                    card_data = data["data"]
                    logger.info(f"📦 [API v1] Order data fields: {list(card_data.keys())}")
                    
                    return APIResponse(
                        success=True,
                        data=data,
                        operation=APIOperation.VIEW_ORDER,
                        execution_time=execution_time,
                    )
                else:
                    logger.warning(f"⚠️ [API v1] Unexpected response format: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    return response
            else:
                logger.error(f"❌ [API v1] view_order failed for order {order_id}: {response.error}")
                return response
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"💥 [API v1] Exception in view_order: {e}")
            return APIResponse(
                success=False,
                error=f"API v1 view_order failed: {str(e)}",
                operation=APIOperation.VIEW_ORDER,
                execution_time=execution_time,
            )

    @monitor_performance("unmask_card")
    async def unmask_card(
        self, order_id: str, card_id: str, force_api_v3: bool = False
    ) -> APIResponse:
        """
        Unmask/view card details within an order

        Routes to appropriate API version:
        - API v3: Uses POST /orders/{order_id} with unmask form data
        - API v1/v2: Uses legacy card viewing method
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 unmask_card despite API version {self.api_version}"
                    )
                logger.debug(
                    f"🔵 ROUTING: unmask_card → API v3 (order: {order_id}, card: {card_id})"
                )
                return await self._unmask_card_v3(order_id, card_id)

            logger.debug(
                f"🔴 ROUTING: unmask_card → API {self.api_version} (order: {order_id}, card: {card_id})"
            )

            # Legacy API implementation (if needed)
            return APIResponse(
                success=False,
                error=f"Card unmasking not implemented for API {self.api_version}",
                operation=APIOperation.UNMASK_CARD,
            )

        except Exception as e:
            logger.error(f"Error in unmask_card: {e}")
            return APIResponse(
                success=False,
                error=f"Unmask card operation failed: {str(e)}",
                operation=APIOperation.UNMASK_CARD,
            )

    async def _unmask_card_v3(self, order_id: str, card_id: str) -> APIResponse:
        """Unmask card using API v3 order service"""
        start_time = time.time()
        context = api_logger.create_context(user_id=None, operation="unmask_card")

        try:
            service = await self._get_api_v3_order_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 order service not available",
                    operation=APIOperation.UNMASK_CARD,
                )

            api_logger.log_request(
                context=context,
                method="POST",
                url=f"{service.base_url}/orders/{order_id}",
                headers={"X-API-Version": "v3"},
            )

            # Unmask card using API v3 order service
            result = await service.unmask_card(order_id, card_id)

            # Log the complete unmask response
            logger.debug(f"🔵 API v3 unmask_card response type: {type(result)}")
            if hasattr(result, "__dict__"):
                logger.info(
                    f"🔵 API v3 unmask_card response attributes: {list(result.__dict__.keys())}"
                )

            execution_time = time.time() - start_time

            if result.get("success"):
                # Extract and process unmasked data
                unmask_data = result.get("unmask_data", {})
                
                # Extract cards from the unmasked data using card data extractor
                from utils.card_data_extractor import get_card_data_extractor
                extractor = get_card_data_extractor()
                
                # Extract cards from the unmasked HTML response
                extracted_cards = extractor.extract_from_api_response(unmask_data)
                logger.info(f"[CARD-EXTRACTION] ✅ Extracted {len(extracted_cards)} cards from API response")
                
                # Add extracted cards to the result
                enhanced_result = {
                    **result,
                    "extracted_cards": extracted_cards,
                    "unmask_data": unmask_data,
                }
                
                # Apply data filtering to correct misspelled/incorrect data
                filtered_result = get_data_filter().filter_response_data(enhanced_result)
                logger.debug(
                    f"✅ Applied data filtering to API v3 unmask_card response"
                )
                
                # Add extracted cards to final response data
                if extracted_cards:
                    filtered_result["extracted_cards"] = extracted_cards
                    logger.info(f"✅ Added {len(extracted_cards)} extracted cards to response data")

                logger.info(
                    f"✅ API v3 unmask_card succeeded - Order: {order_id}, Card: {card_id}"
                )

                # Log the COMPLETE unmask response data to terminal
                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={"Content-Type": "application/json"},
                    body=filtered_result,  # Log the FILTERED unmask result
                )

                return APIResponse(
                    success=True,
                    data=filtered_result,
                    operation=APIOperation.UNMASK_CARD,
                    execution_time=execution_time,
                )
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ API v3 unmask_card failed: {error_msg}")

                # Log the COMPLETE error response data
                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={"Content-Type": "application/json"},
                    body=result,  # Log the FULL error result
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.UNMASK_CARD,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Exception in API v3 unmask_card: {e}")

            # Log complete exception details
            import traceback

            exception_details = {
                "error": str(e),
                "exception_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "order_id": order_id,
                "card_id": card_id,
            }

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={"Content-Type": "application/json"},
                body=exception_details,
            )

            return APIResponse(
                success=False,
                error=f"API v3 unmask_card failed: {str(e)}",
                operation=APIOperation.UNMASK_CARD,
                execution_time=execution_time,
            )

    @monitor_performance("check_card_status")
    async def check_card_status(
        self, order_id: str, card_id: str, force_api_v3: bool = False
    ) -> APIResponse:
        """
        Check card status within an order

        Routes to appropriate API version:
        - API v3: Uses POST /orders/{order_id} with check form data
        - API v1/v2: Uses legacy card checking method
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 check_card_status despite API version {self.api_version}"
                    )
                logger.debug(
                    f"🔵 ROUTING: check_card_status → API v3 (order: {order_id}, card: {card_id})"
                )
                return await self._check_card_status_v3(order_id, card_id)

            logger.debug(
                f"🔴 ROUTING: check_card_status → API {self.api_version} (order: {order_id}, card: {card_id})"
            )

            # Legacy API implementation (if needed)
            return APIResponse(
                success=False,
                error=f"Card status checking not implemented for API {self.api_version}",
                operation=APIOperation.CHECK_ORDER,
            )

        except Exception as e:
            logger.error(f"Error in check_card_status: {e}")
            return APIResponse(
                success=False,
                error=f"Check card status operation failed: {str(e)}",
                operation=APIOperation.CHECK_ORDER,
            )

    async def _check_card_status_v3(self, order_id: str, card_id: str) -> APIResponse:
        """Check card status using API v3 order service"""
        start_time = time.time()
        context = api_logger.create_context(user_id=None, operation="check_card_status")

        try:
            service = await self._get_api_v3_order_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 order service not available",
                    operation=APIOperation.CHECK_ORDER,
                )

            api_logger.log_request(
                context=context,
                method="POST",
                url=f"{service.base_url}/orders/{order_id}",
                headers={"X-API-Version": "v3"},
            )

            # Check card status using API v3 order service
            result = await service.check_card(order_id, card_id, user_id="")

            # Log the complete check response
            logger.debug(f"🔵 API v3 check_card_status response type: {type(result)}")
            if hasattr(result, "__dict__"):
                logger.info(
                    f"🔵 API v3 check_card_status response attributes: {list(result.__dict__.keys())}"
                )

            execution_time = time.time() - start_time

            if result.get("success"):
                # Apply data filtering to correct misspelled/incorrect data
                filtered_result = get_data_filter().filter_response_data(result)
                logger.debug(
                    f"✅ Applied data filtering to API v3 check_card_status response"
                )

                logger.info(
                    f"✅ API v3 check_card_status succeeded - Order: {order_id}, Card: {card_id}"
                )

                # Log the COMPLETE check response data to terminal
                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={"Content-Type": "application/json"},
                    body=filtered_result,  # Log the FILTERED check result
                )

                return APIResponse(
                    success=True,
                    data=filtered_result,
                    operation=APIOperation.CHECK_ORDER,
                    execution_time=execution_time,
                )
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ API v3 check_card_status failed: {error_msg}")

                # Log the COMPLETE error response data
                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={"Content-Type": "application/json"},
                    body=result,  # Log the FULL error result
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.CHECK_ORDER,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Exception in API v3 check_card_status: {e}")

            # Log complete exception details
            import traceback

            exception_details = {
                "error": str(e),
                "exception_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "order_id": order_id,
                "card_id": card_id,
            }

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={"Content-Type": "application/json"},
                body=exception_details,
            )

            return APIResponse(
                success=False,
                error=f"API v3 check_card_status failed: {str(e)}",
                operation=APIOperation.CHECK_ORDER,
                execution_time=execution_time,
            )

    async def _checkout_v3(self) -> APIResponse:
        """Checkout using API v3 order service"""
        start_time = time.time()
        context = api_logger.create_context(
            user_id=None, operation=APIOperation.CHECKOUT.value
        )

        try:
            service = await self._get_api_v3_order_service()
            if not service:
                return APIResponse(
                    success=False,
                    error="API v3 order service not available",
                    operation=APIOperation.CHECKOUT,
                )

            api_logger.log_request(
                context=context,
                method="POST",
                url=f"{service.base_url}/order",
                headers={"X-API-Version": "v3"},
            )

            # Create order using API v3 order service
            # Default to no_refund=False (allow refunds) for standard checkout
            result = await service.create_order(no_refund=False)

            execution_time = time.time() - start_time

            if result.get("success"):
                order_id = result.get("order_id")
                raw_data = result.get("raw_data", {})
                extracted_cards = result.get("extracted_cards", [])
                
                logger.info(f"✅ API v3 checkout succeeded - Order ID: {order_id}")
                if extracted_cards:
                    logger.info(f"✅ Checkout includes {len(extracted_cards)} extracted cards")

                api_logger.log_response(
                    context=context,
                    status_code=200,
                    status_message="OK",
                    headers={},
                    body={"order_id": order_id, "cards_count": len(extracted_cards)},
                )

                # Include order_id in response data for checkout queue service compatibility
                response_data = result.get("order_data", {})
                if order_id:
                    response_data["order_id"] = order_id
                
                # Include raw_data and extracted_cards for database storage
                response_data["raw_data"] = raw_data
                response_data["extracted_cards"] = extracted_cards

                return APIResponse(
                    success=True,
                    data=response_data,
                    operation=APIOperation.CHECKOUT,
                    execution_time=execution_time,
                )
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ API v3 checkout failed: {error_msg}")

                api_logger.log_response(
                    context=context,
                    status_code=400,
                    status_message="Bad Request",
                    headers={},
                    body={"error": error_msg},
                )

                return APIResponse(
                    success=False,
                    error=error_msg,
                    operation=APIOperation.CHECKOUT,
                    execution_time=execution_time,
                )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ API v3 checkout exception: {e}", exc_info=True)

            api_logger.log_response(
                context=context,
                status_code=500,
                status_message="Internal Server Error",
                headers={},
                body={"error": str(e)},
            )

            return APIResponse(
                success=False,
                error=f"API v3 checkout failed: {str(e)}",
                operation=APIOperation.CHECKOUT,
                execution_time=execution_time,
            )

    @monitor_performance("check_order")
    async def check_order(
        self, order_id: int | str, card_id: Optional[str] = None
    ) -> APIResponse:
        """
        Run the external check API for a specific order id.

        Routes to appropriate API version based on configuration.
        - API v3: Uses GET /orders/{order_id}/check?cc_id={card_id}
        - API v1/v2: Uses POST /cards/hq/check with {"id": order_id}

        Args:
            order_id: Order ID (int for API v1/v2, str for API v3)
            card_id: Optional card ID for API v3
        """
        try:
            # Route to API v3 if configured
            use_v3 = self.api_version == "v3" or self.api_version == "base3"

            if use_v3:
                logger.error(
                    "API v3 check_order not implemented in this service - no fallback available"
                )
                return APIResponse(
                    success=False,
                    error="API v3 check_order not supported in this service",
                    operation=APIOperation.CHECK_ORDER,
                )

            # Default to API v1/v2 implementation
            logger.info(f"Routing check_order to API {self.api_version}")

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.CHECK_ORDER,
                )

            url = f"{config.base_url}/cards/hq/check"
            headers = self._build_headers(config, APIOperation.CHECK_ORDER)
            cookies = self._build_cookies(config)

            # Handle both int and string order IDs (API v1/v2 expects int)
            if isinstance(order_id, str) and order_id.isdigit():
                payload = {"id": int(order_id)}
            elif isinstance(order_id, int):
                payload = {"id": order_id}
            else:
                # For API v1/v2, non-numeric order IDs are not supported
                return APIResponse(
                    success=False,
                    error=f"API v1/v2 check_order requires numeric order ID, got: {order_id}",
                    operation=APIOperation.CHECK_ORDER,
                )

            # Log the check operation start with detailed info
            logger.info(
                f"Starting check_order for order_id={order_id}, card_id={card_id}"
            )
            logger.info(f"API v1 check_order URL: {url}")
            logger.info(f"API v1 check_order payload: {payload}")
            logger.info(
                f"API v1 check_order timeout: {OPERATION_TIMEOUTS.get(APIOperation.CHECK_ORDER, 30)}s"
            )

            # Log headers and cookies for debugging (without sensitive data)
            debug_headers = {
                k: v
                for k, v in headers.items()
                if "token" not in k.lower() and "auth" not in k.lower()
            }
            logger.debug(f"API v1 check_order headers: {debug_headers}")
            debug_cookies = {
                k: v
                for k, v in cookies.items()
                if "token" not in k.lower() and "auth" not in k.lower()
            }
            logger.debug(f"API v1 check_order cookies: {debug_cookies}")

            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.CHECK_ORDER,
                max_retries=2,  # Reduce retries for check operations
            )

            # Enhanced error logging
            logger.info(f"📥 Check API Response: success={response.success}")
            if response.success:
                logger.info(f"📦 Response data type: {type(response.data)}")
                if isinstance(response.data, dict):
                    logger.info(f"📋 Response keys: {list(response.data.keys())}")
                    if 'data' in response.data:
                        logger.info(f"✅ Found 'data' in response")
                        logger.info(f"📊 Card status in response: {response.data.get('data', {}).get('status')}")
                        logger.info(f"🔑 Card _id in response: {response.data.get('data', {}).get('_id')}")
                    else:
                        logger.warning(f"⚠️ No 'data' key in response!")
                
                logger.info(
                    f"Check order completed successfully for order_id={order_id}"
                )
            else:
                error_msg = response.error or "Unknown error"
                logger.warning(
                    f"Check order failed for order_id={order_id}: {error_msg}"
                )

                # Special handling for 404 with timeout message
                if "404" in error_msg and "Timed Out" in error_msg:
                    logger.error(
                        f"API v1 returned 404 with timeout message - this may indicate:"
                    )
                    logger.error(
                        f"  1. Order ID {order_id} does not exist or is not accessible"
                    )
                    logger.error(f"  2. Authentication issue with loginToken")
                    logger.error(
                        f"  3. API endpoint has changed or is temporarily unavailable"
                    )
                    logger.error(
                        f"  4. Server-side timeout before processing the request"
                    )

                    # Return error without fallback - keep API versions separate
                    return APIResponse(
                        success=False,
                        error="Card check failed: Order not found or temporarily unavailable. Please try again later or contact support if the issue persists.",
                        operation=APIOperation.CHECK_ORDER,
                    )

            return response
        except Exception as e:
            logger.error(f"Error in check_order for order_id={order_id}: {e}")
            return APIResponse(
                success=False,
                error=f"Check order failed: {str(e)}",
                operation=APIOperation.CHECK_ORDER,
            )

    @monitor_performance("download_card")
    async def download_card(
        self, order_id: int | str, force_api_v3: bool = False
    ) -> APIResponse:
        """
        Download card data for a specific order in CSV/text format.

        Routes to appropriate API version:
        - API v3: Uses view_order to get card data (no separate download endpoint)
        - API v1/v2: Uses POST /cards/hq/download/single endpoint

        Args:
            order_id: The order ID (_id field) to download card data for (int for API v1/v2, str for API v3)
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Returns:
            APIResponse with card data in text/CSV format
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 download_card despite API version {self.api_version}"
                    )
                logger.info(
                    f"🔵 ROUTING: download_card → API v3 (order ID: {order_id})"
                )

                # Use the existing API v3 order service
                order_service = await self._get_api_v3_order_service()
                if not order_service:
                    return APIResponse(
                        success=False,
                        error="Failed to initialize API v3 order service",
                        operation=APIOperation.DOWNLOAD_CARD,
                    )
                
                try:
                    # Download all items in the order
                    result = await order_service.download_items(str(order_id))
                    
                    if result.get("success"):
                        return APIResponse(
                            success=True,
                            data=result.get("raw_data", ""),
                            raw_response=result.get("raw_data", ""),
                            operation=APIOperation.DOWNLOAD_CARD
                        )
                    else:
                        return APIResponse(
                            success=False,
                            error=result.get("error", "Download failed"),
                            operation=APIOperation.DOWNLOAD_CARD,
                        )
                except Exception as e:
                    logger.error(f"Error during API v3 download: {e}")
                    return APIResponse(
                        success=False,
                        error=f"Download operation failed: {str(e)}",
                        operation=APIOperation.DOWNLOAD_CARD,
                    )

            logger.info(
                f"🔴 ROUTING: download_card → API {self.api_version} (order ID: {order_id})"
            )

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.DOWNLOAD_CARD,
                )

            url = f"{config.base_url}/cards/hq/download/single"
            headers = self._build_headers(config, APIOperation.DOWNLOAD_CARD)
            cookies = self._build_cookies(config)

            # Payload uses _id field as per documentation (API v1/v2 expects int)
            if isinstance(order_id, str) and order_id.isdigit():
                payload = {"_id": int(order_id)}
            elif isinstance(order_id, int):
                payload = {"_id": order_id}
            else:
                # For API v1/v2, non-numeric order IDs are not supported
                return APIResponse(
                    success=False,
                    error=f"API v1/v2 download_card requires numeric order ID, got: {order_id}",
                    operation=APIOperation.DOWNLOAD_CARD,
                )

            logger.info(f"Starting download_card for order_id={order_id}")

            # API v1 download returns text/plain (pipe-delimited), not JSON
            # So we need to handle it differently from other endpoints
            try:
                session = await self._ensure_session()
                
                async with session.request(
                    "POST",
                    url,
                    headers=headers,
                    json=payload,
                    cookies=cookies,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as resp:
                    response_text = await resp.text()
                    
                    if resp.status == 200:
                        # Success - text response with card data
                        logger.info(
                            f"✅ Download card completed successfully for order_id={order_id}"
                        )
                        logger.debug(f"📄 Downloaded data length: {len(response_text)} chars")
                        
                        # Filter out unwanted fields (_id, base, paid_price)
                        filtered_text = self._filter_download_fields(response_text)
                        logger.debug(f"📄 Filtered data length: {len(filtered_text)} chars")
                        
                        return APIResponse(
                            success=True,
                            data=filtered_text,
                            raw_response=response_text,  # Keep original for debugging
                            status_code=resp.status,
                            operation=APIOperation.DOWNLOAD_CARD,
                        )
                    else:
                        # Error
                        logger.warning(
                            f"❌ Download card failed for order_id={order_id}: HTTP {resp.status}"
                        )
                        return APIResponse(
                            success=False,
                            error=f"HTTP {resp.status}: {response_text[:200]}",
                            status_code=resp.status,
                            operation=APIOperation.DOWNLOAD_CARD,
                        )
            except Exception as e:
                logger.error(f"❌ Download card request failed: {e}")
                return APIResponse(
                    success=False,
                    error=f"Request failed: {str(e)}",
                    operation=APIOperation.DOWNLOAD_CARD,
                )
        except Exception as e:
            logger.error(f"Error in download_card for order_id={order_id}: {e}")
            return APIResponse(
                success=False,
                error=f"Download card operation failed: {str(e)}",
                operation=APIOperation.DOWNLOAD_CARD,
            )

    async def unmask_order(
        self,
        order_id: int | str,
        card_ids: Optional[List[int | str]] = None,
        force_api_v3: bool = False,
    ) -> APIResponse:
        """Unmask selected cards in an order.

        Routes to appropriate API version:
        - API v3: Uses unmask_card method for each card
        - API v1/v2: Legacy unmask functionality (deprecated)

        Args:
            order_id: The order ID to unmask cards for (int for API v1/v2, str for API v3)
            card_ids: List of card IDs to unmask. If None, will unmask all available cards.
            force_api_v3: If True, forces API v3 usage regardless of configured version

        Returns:
            APIResponse with unmask operation results
        """
        try:
            # Check if we should use API v3
            use_v3 = (
                self.api_version == "v3" or self.api_version == "base3" or force_api_v3
            )

            if use_v3:
                if force_api_v3 and self.api_version not in ["v3", "base3"]:
                    logger.info(
                        f"🔄 Forcing API v3 unmask_order despite API version {self.api_version}"
                    )
                logger.info(f"🔵 ROUTING: unmask_order → API v3 (order ID: {order_id})")

                # For API v3, use the new unmask_card method
                if card_ids:
                    # Unmask specific cards
                    results = []
                    for card_id in card_ids:
                        result = await self.unmask_card(
                            str(order_id), str(card_id), force_api_v3=True
                        )
                        results.append(result)

                    # Return combined result
                    all_success = all(r.success for r in results)
                    return APIResponse(
                        success=all_success,
                        data={"results": [r.data for r in results]},
                        error=None if all_success else "Some cards failed to unmask",
                        operation=APIOperation.UNMASK_ORDER,
                    )
                else:
                    # Need to view order first to get all card IDs
                    view_resp = await self.view_order(str(order_id), force_api_v3=True)
                    if not view_resp.success:
                        return APIResponse(
                            success=False,
                            error=f"Failed to view order to get card IDs: {view_resp.error}",
                            operation=APIOperation.UNMASK_ORDER,
                        )

                    # Extract card IDs and unmask all
                    items = view_resp.data.get("items", [])
                    all_card_ids = [
                        str(item.get("id")) for item in items if item.get("id")
                    ]

                    if not all_card_ids:
                        return APIResponse(
                            success=False,
                            error="No cards found in order",
                            operation=APIOperation.UNMASK_ORDER,
                        )

                    # Unmask all cards
                    results = []
                    for card_id in all_card_ids:
                        result = await self.unmask_card(
                            str(order_id), card_id, force_api_v3=True
                        )
                        results.append(result)

                    all_success = all(r.success for r in results)
                    return APIResponse(
                        success=all_success,
                        data={"results": [r.data for r in results]},
                        error=None if all_success else "Some cards failed to unmask",
                        operation=APIOperation.UNMASK_ORDER,
                    )

            logger.info(
                f"🔴 ROUTING: unmask_order → API {self.api_version} (not supported)"
            )

            # API v1/v2 do not support bulk unmask operations
            return APIResponse(
                success=False,
                error="Unmask order functionality is only available in API v3.",
                operation=APIOperation.UNMASK_ORDER,
            )

        except Exception as exc:
            logger.error("Unmask order exception: %s", exc)
            return APIResponse(
                success=False,
                error=f"Unmask order failed: {exc}",
                operation=APIOperation.UNMASK_ORDER,
            )

    async def health_check(self) -> bool:
        """
        Perform a health check on the external API.

        Returns:
            True if API is healthy, False otherwise
        """
        try:
            # Try a simple list_items request with minimal parameters
            # Use the correct parameter class based on API version
            if self.api_version == "v3" or self.api_version == "base3":
                params = ListItemsParamsV3(page=1, limit=1)
            else:
                params = ListItemsParamsV1V2(page=1, limit=1)
            response = await self.list_items(params)

            # Consider the API healthy if we get any response (even errors can indicate the API is responding)
            if response.success:
                logger.debug("API health check passed - successful response")
                return True
            elif response.status_code and response.status_code < 500:
                # 4xx errors indicate the API is responding, just authentication/request issues
                logger.debug(
                    f"API health check passed - API responding with status {response.status_code}"
                )
                return True
            else:
                # 5xx errors or no response indicate API problems
                logger.warning(
                    f"API health check failed - status {response.status_code}: {response.error}"
                )
                return False

        except Exception as e:
            logger.error(f"API health check exception: {e}")
            return False


# Global service instance
_external_api_service: Optional[ExternalAPIService] = None


def get_external_api_service() -> ExternalAPIService:
    """Get the global external API service instance"""
    global _external_api_service
    if _external_api_service is None:
        _external_api_service = ExternalAPIService()
    return _external_api_service


async def close_external_api_service():
    """Close the global external API service instance"""
    global _external_api_service
    if _external_api_service is not None:
        await _external_api_service.close()
        _external_api_service = None
