# API v1 Cart Service Integration Guide

## Quick Start

This guide shows how to integrate the new cart service endpoints into your application.

## Installation

The cart service is already integrated into the API v1 module. No additional installation is required.

## Configuration

The cart service uses the unified API configuration system. Make sure you have a valid API configuration set up:

```python
from api_v1.services.api_config import get_unified_api_config_service

# Ensure your API configuration is set up
config_service = get_unified_api_config_service()
config = await config_service.get_api_config("api_v1_base", decrypt_sensitive=True)
```

## Basic Integration

### 1. Import the Service

```python
from api_v1.services.cart_service import get_cart_service, CartServiceError
```

### 2. Initialize the Service

```python
# Use default config
cart_service = get_cart_service()

# Or specify custom config
cart_service = get_cart_service(config_name="my_custom_config")
```

### 3. Use the Endpoints

```python
# View cart
cart_result = await cart_service.view_cart(user_id="user123")

# List orders
orders_result = await cart_service.list_orders(
    category="hq",
    page=1,
    limit=20,
    user_id="user123"
)

# View specific order
order_result = await cart_service.view_order(
    order_id=347387,
    category="hq",
    user_id="user123"
)

# Check order (mark as non-refundable)
check_result = await cart_service.check_order(
    order_id=347387,
    category="hq",
    user_id="user123"
)

# Download order data
download_result = await cart_service.download_order(
    order_id=347387,
    category="hq",
    user_id="user123"
)
```

## Telegram Bot Integration

### Handler Example

Here's how to integrate the cart service into a Telegram bot handler:

```python
from aiogram import Router, types
from aiogram.filters import Command
from api_v1.services.cart_service import get_cart_service

router = Router()
cart_service = get_cart_service()

@router.message(Command("cart"))
async def view_cart_handler(message: types.Message):
    """Handler to view user's cart"""
    user_id = str(message.from_user.id)
    
    try:
        result = await cart_service.view_cart(user_id=user_id)
        
        if result.success:
            items = result.data['items']
            total_price = result.data['total_price']
            
            if not items:
                await message.answer("🛒 Your cart is empty")
                return
            
            cart_text = f"🛒 Your Cart ({len(items)} items)\n\n"
            
            for item in items:
                cart_text += (
                    f"• {item['brand']} {item['bin']}\n"
                    f"  {item['country']} - {item['city']}, {item['state']}\n"
                    f"  💰 ${item['price']}\n\n"
                )
            
            cart_text += f"Total: ${total_price:.2f}"
            await message.answer(cart_text)
        else:
            await message.answer(f"❌ Failed to load cart: {result.message}")
            
    except Exception as e:
        await message.answer(f"❌ Error: {str(e)}")


@router.message(Command("orders"))
async def view_orders_handler(message: types.Message):
    """Handler to view user's orders"""
    user_id = str(message.from_user.id)
    
    try:
        result = await cart_service.list_orders(
            category="hq",
            page=1,
            limit=10,
            user_id=user_id
        )
        
        if result.success:
            orders = result.data['orders']
            total_count = result.data['total_count']
            
            if not orders:
                await message.answer("📦 You have no orders yet")
                return
            
            orders_text = f"📦 Your Orders (showing {len(orders)} of {total_count})\n\n"
            
            for order in orders:
                status_emoji = {
                    "Started": "🕐",
                    "NonRefundable": "✅",
                    "Refunded": "↩️"
                }.get(order['status'], "📋")
                
                orders_text += (
                    f"{status_emoji} Order #{order['_id']}\n"
                    f"  {order['brand']} {order['bin']}\n"
                    f"  Status: {order['status']}\n"
                    f"  💰 ${order['price']}\n\n"
                )
            
            await message.answer(orders_text)
        else:
            await message.answer(f"❌ Failed to load orders: {result.message}")
            
    except Exception as e:
        await message.answer(f"❌ Error: {str(e)}")
```

### Callback Query Handler

For viewing and checking specific orders:

```python
from aiogram.filters import StateFilter
from aiogram.fsm.context import FSMContext

@router.callback_query(lambda c: c.data.startswith("view_order:"))
async def view_order_callback(callback: types.CallbackQuery):
    """View order details"""
    order_id = int(callback.data.split(":")[1])
    user_id = str(callback.from_user.id)
    
    try:
        result = await cart_service.view_order(
            order_id=order_id,
            category="hq",
            user_id=user_id
        )
        
        if result.success:
            order = result.data['order']
            
            order_text = (
                f"📋 Order #{order['_id']}\n\n"
                f"💳 {order['brand']} {order['bin']}\n"
                f"📅 Exp: {order['exp']}\n"
                f"🔐 CVV: {order['cvv']}\n"
                f"👤 {order['name']}\n"
                f"📍 {order['address']}\n"
                f"   {order['city']}, {order['state']} {order['zip']}\n"
                f"🌍 {order['country']}\n"
                f"🏦 {order['bank']}\n"
                f"💰 ${order['price']}\n"
                f"📊 Status: {order['status']}\n"
            )
            
            # Add check button if order can be checked
            keyboard = None
            if order.get('canCheck', 0) == 1:
                keyboard = types.InlineKeyboardMarkup(inline_keyboard=[
                    [types.InlineKeyboardButton(
                        text="✅ Mark as Checked",
                        callback_data=f"check_order:{order_id}"
                    )],
                    [types.InlineKeyboardButton(
                        text="📥 Download",
                        callback_data=f"download_order:{order_id}"
                    )]
                ])
            
            await callback.message.edit_text(order_text, reply_markup=keyboard)
        else:
            await callback.answer(f"Failed: {result.message}", show_alert=True)
            
    except Exception as e:
        await callback.answer(f"Error: {str(e)}", show_alert=True)


@router.callback_query(lambda c: c.data.startswith("check_order:"))
async def check_order_callback(callback: types.CallbackQuery):
    """Check order (mark as non-refundable)"""
    order_id = int(callback.data.split(":")[1])
    user_id = str(callback.from_user.id)
    
    try:
        result = await cart_service.check_order(
            order_id=order_id,
            category="hq",
            user_id=user_id
        )
        
        if result.success:
            await callback.answer("✅ Order marked as checked!", show_alert=True)
            # Refresh order view
            await view_order_callback(callback)
        else:
            await callback.answer(f"Failed: {result.message}", show_alert=True)
            
    except Exception as e:
        await callback.answer(f"Error: {str(e)}", show_alert=True)
```

## Error Handling Best Practices

### 1. Always Check Success Status

```python
result = await cart_service.view_cart()
if not result.success:
    # Handle error
    logger.error(f"Cart fetch failed: {result.message} (code: {result.error_code})")
    return
```

### 2. Use Try-Except Blocks

```python
try:
    result = await cart_service.view_order(order_id=123)
except CartServiceError as e:
    # Handle cart-specific errors
    logger.error(f"Cart error: {e.message}")
except Exception as e:
    # Handle unexpected errors
    logger.error(f"Unexpected error: {e}")
```

### 3. Provide User Feedback

```python
result = await cart_service.check_order(order_id=123)
if result.success:
    await message.answer("✅ Order checked successfully!")
else:
    error_messages = {
        "ORDER_CHECK_ERROR": "Could not check order. Please try again.",
        "CONFIG_NOT_FOUND": "Service configuration error. Contact support.",
        "INVALID_RESPONSE": "Service returned invalid data. Try again later."
    }
    user_message = error_messages.get(
        result.error_code,
        f"An error occurred: {result.message}"
    )
    await message.answer(f"❌ {user_message}")
```

## Performance Optimization

### 1. Caching

The service includes built-in caching for configuration. For additional caching:

```python
from functools import lru_cache
from datetime import datetime, timedelta

# Cache cart data for 5 minutes
cart_cache = {}

async def get_cached_cart(user_id: str):
    cache_key = f"cart_{user_id}"
    
    if cache_key in cart_cache:
        data, timestamp = cart_cache[cache_key]
        if datetime.now() - timestamp < timedelta(minutes=5):
            return data
    
    result = await cart_service.view_cart(user_id=user_id)
    if result.success:
        cart_cache[cache_key] = (result.data, datetime.now())
    
    return result.data if result.success else None
```

### 2. Batch Processing

For processing multiple orders:

```python
import asyncio

async def process_multiple_orders(order_ids: list):
    """Process multiple orders concurrently"""
    tasks = [
        cart_service.view_order(order_id=order_id)
        for order_id in order_ids
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful = [r.data['order'] for r in results if isinstance(r, BaseResponse) and r.success]
    failed = [r for r in results if isinstance(r, Exception) or not r.success]
    
    return successful, failed
```

## Testing

### Unit Tests

```python
import pytest
from api_v1.services.cart_service import get_cart_service

@pytest.mark.asyncio
async def test_view_cart():
    cart_service = get_cart_service()
    result = await cart_service.view_cart(user_id="test_user")
    
    assert result.success
    assert 'items' in result.data
    assert 'total_price' in result.data

@pytest.mark.asyncio
async def test_list_orders():
    cart_service = get_cart_service()
    result = await cart_service.list_orders(page=1, limit=10)
    
    assert result.success
    assert 'orders' in result.data
    assert result.data['page'] == 1
```

### Integration Tests

See [api_v1/tests/test_cart_service.py](./tests/test_cart_service.py) for complete test suite.

## Monitoring and Logging

The service uses the centralized logger. Monitor these log levels:

- **INFO**: Successful operations
- **WARNING**: Retries and recoverable errors
- **ERROR**: Failed operations

```python
# Example log output
2025-10-23 20:15:03 INFO: Fetching cart for user: 12345
2025-10-23 20:15:04 INFO: Cart retrieved successfully
```

## API Rate Limiting

The service includes automatic retry logic, but be aware of API rate limits:

- Maximum 3 retries per request
- Exponential backoff (1s, 2s, 4s)
- Consider implementing request throttling for high-volume operations

```python
import asyncio
from asyncio import Semaphore

# Limit concurrent requests
semaphore = Semaphore(5)

async def rate_limited_view_order(order_id: int):
    async with semaphore:
        return await cart_service.view_order(order_id=order_id)
```

## Troubleshooting

### Common Issues

1. **"API configuration not found"**
   - Ensure your API configuration exists in the database
   - Check the config_name parameter

2. **"Authentication failed"**
   - Verify your login token is valid
   - Check session cookies in the configuration

3. **"Invalid response format"**
   - API may have changed
   - Check the base URL in configuration

### Debug Mode

Enable debug logging:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Support

For issues or questions:
- Check the [CART_ENDPOINTS.md](./CART_ENDPOINTS.md) documentation
- Review [cart_service_example.py](./examples/cart_service_example.py)
- Contact the development team

