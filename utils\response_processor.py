"""
API v3 Response Processor

This module provides automatic JSON response display and card extraction
for API v3 endpoints. It integrates with the existing card data extractor
a        try:
            logger.info(f"🌐 API CALL: {endpoint.upper()}")
            
            # Log first 3 items from response data
            logger.info("📊 API RESPONSE (First 3):")
            if isinstance(response_data, dict):
                keys = list(response_data.keys())
                if "headers" in response_data and "rows" in response_data:
                    headers = response_data.get("headers", [])
                    rows = response_data.get("rows", [])
                    response_summary = {
                        "format": "headers/rows",
                        "headers": headers[:3],
                        "rows": rows[:3],
                        "total_headers": len(headers),
                        "total_rows": len(rows)
                    }
                else:
                    response_summary = {
                        "format": "other_dict",
                        "first_3_keys": dict(list(response_data.items())[:3]),
                        "total_keys": len(keys)
                    }
                logger.info(json.dumps(response_summary, indent=2, ensure_ascii=False))
            
            # Log first 3 extraction results
            logger.info("💳 CARD EXTRACTION (First 3):")
            if extracted_cards:
                extraction_summary = {
                    "total_cards": len(extracted_cards),
                    "first_3_cards": extracted_cards[:3]
                }
                logger.info(json.dumps(extraction_summary, indent=2, ensure_ascii=False))
            else:
                logger.info(json.dumps({"total_cards": 0, "message": "No cards extracted"}, indent=2))
            
            logger.info("─" * 50)raw JSON responses and extracted card data in the terminal.

Features:
- Automatic JSON response display for all API v3 calls
- Card data extraction using the centralized CardDataExtractor
- Terminal output formatting for debugging and monitoring
- Integration with existing API v3 HTTP client
"""

from __future__ import annotations

import json
import time
from typing import Any, Dict, List, Optional, Union

from utils.card_data_extractor import get_card_data_extractor
from utils.central_logger import get_logger
from utils.response_display_ui import get_display_formatter

logger = get_logger()


class AutomaticAPIV3ResponseProcessor:
    """
    Fully automatic API v3 response processor with intelligent detection
    and optimized performance. Zero manual configuration needed.
    """
    
    def __init__(self):
        """Initialize automatic processor with optimized defaults."""
        self.card_extractor = get_card_data_extractor()
        self.display_formatter = get_display_formatter()
        
        # Performance tracking
        self.response_count = 0
        self.processing_time_total = 0.0
        self.cache_hits = 0
        self._start_time = time.time()
        
        # Automatic configuration (adapts based on usage)
        self._auto_extract = True
        self._auto_display = False  # Only in debug mode
        self._response_cache = {}  # LRU cache for repeated requests
        
        logger.debug("AutomaticAPIV3ResponseProcessor initialized with optimized defaults")
        
    def process_api_v3_response(
        self,
        endpoint: str,
        response_data: Dict[str, Any],
        card_id: Optional[str] = None,
        show_json: bool = None,  # Auto-determined
        extract_cards: bool = None,  # Auto-determined
        json_output: bool = False,
    ) -> Dict[str, Any]:
        """
        Automatic API v3 response processing with intelligent defaults.
        
        Args:
            endpoint: API endpoint called
            response_data: Raw response data
            card_id: Optional specific card ID
            show_json: Auto-determined if None
            extract_cards: Auto-determined if None
            json_output: JSON format output flag
            
        Returns:
            Processed response with extracted cards
        """
        start_time = time.time()
        self.response_count += 1
        
        try:
            # Auto-determine processing options
            if show_json is None:
                show_json = self._auto_display and logger.isEnabledFor(10)
            if extract_cards is None:
                extract_cards = self._auto_extract
            
            # Fast cache check for repeated requests
            cache_key = f"{endpoint}_{hash(str(response_data))}"
            if cache_key in self._response_cache:
                self.cache_hits += 1
                cached_result = self._response_cache[cache_key].copy()
                cached_result["cache_hit"] = True
                return cached_result
            
            # Automatic card extraction (optimized)
            extracted_cards = []
            if extract_cards:
                extracted_cards = self._auto_extract_cards(response_data, card_id)
            
            # Performance tracking
            processing_time = time.time() - start_time
            self.processing_time_total += processing_time
            
            # Build result
            result = {
                "endpoint": endpoint,
                "extracted_cards": extracted_cards,
                "processing_time_ms": round(processing_time * 1000, 2),
                "response_number": self.response_count,
                "cache_hit": False,
                "timestamp": time.time(),
            }
            
            # Note: Logging is handled by card_data_extractor to avoid duplication
            # Card extractor already logs API response and extraction results
            
            # Cache result for future use (keep last 100 responses)
            if len(self._response_cache) > 100:
                # Remove oldest entry
                oldest_key = next(iter(self._response_cache))
                del self._response_cache[oldest_key]
            self._response_cache[cache_key] = result.copy()
            
            # Automatic JSON display if enabled
            if show_json:
                self._auto_display_results(endpoint, extracted_cards, processing_time)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Automatic processing error for {endpoint}: {e}")
            return {
                "endpoint": endpoint,
                "extracted_cards": [],
                "error": str(e),
                "processing_time_ms": round((time.time() - start_time) * 1000, 2),
                "response_number": self.response_count,
                "timestamp": time.time(),
            }
    
    def _auto_extract_cards(self, response_data: Dict[str, Any], card_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """DEPRECATED: Use card_extractor.py directly. This delegates to centralized extractor."""
        logger.warning("⚠️ DEPRECATED: _auto_extract_cards in response_processor.py - use card_extractor.py directly")
        
        try:
            # Check for pre-extracted cards first (avoid redundant extraction)
            if "extracted_cards" in response_data and response_data["extracted_cards"]:
                logger.debug("♻️ Reusing pre-extracted cards from response_data")
                return response_data["extracted_cards"]
            if "cards" in response_data and response_data["cards"]:
                logger.debug("♻️ Reusing pre-extracted cards from response_data['cards']")
                return response_data["cards"]
            
            # Delegate to centralized extractor
            if card_id:
                card = self.card_extractor.extract_single_card(response_data, card_id)
                return [card] if card else []
            else:
                return self.card_extractor.extract_from_api_response(response_data)
                
        except Exception as e:
            logger.error(f"Auto extraction delegation error: {e}")
            return []
    
    def _auto_display_results(self, endpoint: str, cards: List[Dict[str, Any]], processing_time: float) -> None:
        """Automatic display of results when in debug mode."""
        try:
            if cards:
                logger.debug(f"🔍 [{endpoint}] Extracted {len(cards)} cards in {processing_time*1000:.1f}ms")
                for i, card in enumerate(cards[:3], 1):  # Show first 3 only
                    logger.debug(f"   Card {i}: {card.get('bin', 'N/A')} | {card.get('country', 'N/A')} | {card.get('brand', 'N/A')}")
            else:
                logger.debug(f"🔍 [{endpoint}] No cards extracted")
        except Exception as e:
            logger.debug(f"Display error: {e}")
    
    def _log_first_three_comparison(self, response_data: Dict[str, Any], extracted_cards: List[Dict[str, Any]], 
                                   endpoint: str = "unknown", enable_debug: bool = True) -> None:
        """
        Automatically log API call and extraction results with truncation support.
        
        Args:
            response_data: Original API response
            extracted_cards: Processed card data
            endpoint: API endpoint name for context
            enable_debug: Whether to enable logging (default: True for automatic logging)
        """
        if not enable_debug:
            return
            
        try:
            from config.settings import get_settings
            settings = get_settings()
            
            logger.info(f"🌐 API CALL: {endpoint.upper()}")
            
            # Check if full logging is enabled
            if settings.LOG_FULL_API_RESPONSES:
                # Log complete response data (only if enabled)
                logger.info("📊 FULL API RESPONSE:")
                logger.info(json.dumps(response_data, indent=2, ensure_ascii=False))
                
                # Log complete extraction results
                logger.info("📋 FULL CARD EXTRACTION RESULTS:")
                if extracted_cards:
                    logger.info(f"Total Cards Extracted: {len(extracted_cards)}")
                    logger.info("Complete Card Data:")
                    logger.info(json.dumps(extracted_cards, indent=2, ensure_ascii=False))
                else:
                    logger.info("No cards extracted")
            else:
                # Truncated logging (default behavior)
                max_items = settings.LOG_API_RESPONSES_MAX_ITEMS
                
                # Truncate response data
                logger.info(f"📊 API RESPONSE (First {max_items} items):")
                if isinstance(response_data, dict):
                    truncated_response = self._create_truncated_response(response_data, max_items)
                    response_str = json.dumps(truncated_response, indent=2, ensure_ascii=False)
                    if len(response_str) > 500:  # Truncate very long strings
                        response_str = response_str[:500] + f"... (truncated, {len(response_str)} chars total)"
                    logger.info(response_str)
                elif isinstance(response_data, list):
                    logger.info(json.dumps(response_data[:max_items], indent=2, ensure_ascii=False))
                    if len(response_data) > max_items:
                        logger.info(f"... and {len(response_data) - max_items} more items (total: {len(response_data)})")
                else:
                    logger.info(str(response_data)[:500])
                
                # Truncate extraction results
                logger.info(f"💳 CARD EXTRACTION (First {max_items} cards):")
                if extracted_cards:
                    logger.info(f"Total Cards Extracted: {len(extracted_cards)}")
                    cards_str = json.dumps(extracted_cards[:max_items], indent=2, ensure_ascii=False)
                    if len(cards_str) > 500:
                        cards_str = cards_str[:500] + f"... (truncated, {len(cards_str)} chars total)"
                    logger.info(cards_str)
                    if len(extracted_cards) > max_items:
                        logger.info(f"... and {len(extracted_cards) - max_items} more cards (total: {len(extracted_cards)})")
                else:
                    logger.info("No cards extracted")
            
            logger.info("─" * 80)
            
        except Exception as e:
            logger.error(f"Error in response processor debug logging: {e}")

    def _create_truncated_response(self, response_data: Dict[str, Any], max_items: int = 3) -> Dict[str, Any]:
        """
        Create a truncated version of the response showing only the first N items.
        
        Args:
            response_data: Original API response data
            max_items: Maximum number of items to show (default 3)
            
        Returns:
            Truncated response data
        """
        try:
            if not isinstance(response_data, dict):
                return response_data
            
            import copy
            truncated = copy.deepcopy(response_data)  # Use deepcopy to avoid modifying original data
            original_counts = {}
            
            # Handle sections with tables structure
            if "sections" in truncated and isinstance(truncated["sections"], list):
                for section_idx, section in enumerate(truncated["sections"]):
                    if isinstance(section, dict) and "tables" in section:
                        for table_idx, table in enumerate(section["tables"]):
                            if isinstance(table, dict) and "rows" in table and isinstance(table["rows"], list):
                                original_count = len(table["rows"])
                                if original_count > max_items:
                                    # Keep only first 3 items
                                    truncated["sections"][section_idx]["tables"][table_idx]["rows"] = table["rows"][:max_items]
                                    original_counts[f"section_{section_idx}_table_{table_idx}"] = original_count
            
            # Handle direct rows structure
            elif "rows" in truncated and isinstance(truncated["rows"], list):
                original_count = len(truncated["rows"])
                if original_count > max_items:
                    truncated["rows"] = truncated["rows"][:max_items]
                    original_counts["direct_rows"] = original_count
            
            # Handle direct list of items
            elif "items" in truncated and isinstance(truncated["items"], list):
                original_count = len(truncated["items"])
                if original_count > max_items:
                    truncated["items"] = truncated["items"][:max_items]
                    original_counts["items"] = original_count
            
            # Add metadata about truncation (don't include in response)
            if original_counts:
                # Store truncation info for logging but don't add to response
                self._last_truncation_info = {
                    "truncated": True,
                    "showing_first_items": max_items,
                    "original_counts": original_counts,
                    "note": f"Showing first {max_items} items from each collection"
                }
            else:
                self._last_truncation_info = None
            
            return truncated
            
        except Exception as e:
            logger.error(f"Error creating truncated response: {e}")
            return response_data
    
    def _display_json_response(
        self, 
        endpoint: str, 
        response_data: Dict[str, Any]
    ) -> None:
        """
        Display the JSON response in terminal with smart truncation.
        
        Args:
            endpoint: The API endpoint
            response_data: Response data to display
        """
        try:
            # Use info level for essential information only
            logger.info(f"[API-RESPONSE] API v3 Response #{self.response_count} - {endpoint.upper()}")
            
            # Show only essential metadata at info level
            response_format = self._detect_response_format(response_data)
            logger.info(f"[FORMAT] {response_format}")
            
            # Log response size for visibility
            response_size = len(json.dumps(response_data)) if response_data else 0
            logger.info(f"[SIZE] Response size: {response_size:,} characters")
            
            # Show response structure summary at info level
            if isinstance(response_data, dict):
                key_count = len(response_data)
                if key_count <= 5:
                    logger.info(f"[KEYS] {list(response_data.keys())}")
                else:
                    sample_keys = list(response_data.keys())[:3]
                    logger.info(f"[KEYS] ({key_count} total): {sample_keys}... (+{key_count-3} more)")
                
                # Show data counts for common structures
                if "sections" in response_data:
                    sections = response_data["sections"]
                    if isinstance(sections, list):
                        logger.info(f"[SECTIONS] {len(sections)}")
                        
                if "rows" in response_data:
                    rows = response_data["rows"]
                    if isinstance(rows, list):
                        logger.info(f"[ROWS] {len(rows)}")
            
            # Show truncated JSON response at INFO level when LOG_FULL_API_RESPONSES is enabled
            from config.settings import get_settings
            settings = get_settings()
            
            if settings.LOG_FULL_API_RESPONSES or logger.isEnabledFor(10):  # INFO level when enabled, or DEBUG level
                # Create truncated version showing only first N items (default 3)
                max_items = getattr(settings, 'LOG_API_RESPONSES_MAX_ITEMS', 3)
                truncated_data = self._create_truncated_response(response_data, max_items=max_items)
                json_str = json.dumps(truncated_data, indent=2, ensure_ascii=False)
                
                # Additional size-based truncation to prevent massive logs
                MAX_LOG_SIZE = 5000  # Maximum 5000 characters
                if len(json_str) > MAX_LOG_SIZE:
                    json_str = json_str[:MAX_LOG_SIZE] + f"\n... (truncated {len(json_str) - MAX_LOG_SIZE:,} characters)"
                
                if settings.LOG_FULL_API_RESPONSES:
                    logger.info(f"[JSON-RESPONSE] API Response (First {max_items} Items Only):\n{json_str}")
                    logger.info(f"[JSON-SIZE] Displayed size: {min(len(json_str), MAX_LOG_SIZE):,} characters")
                    
                    # Always show truncation info to make it clear we're only showing limited items
                    if hasattr(self, '_last_truncation_info') and self._last_truncation_info:
                        info = self._last_truncation_info
                        logger.info(f"[TRUNCATION] ⚠️ {info['note']} (Full responses not shown)")
                        for key, count in info["original_counts"].items():
                            logger.info(f"[ORIGINAL-COUNT] {key}: {count} total items (showing {max_items})")
                    else:
                        logger.info(f"[TRUNCATION] ⚠️ Response limited to first {max_items} items per collection")
                else:
                    logger.debug(f"📄 Complete JSON Response:\n{json_str}")
                    logger.debug(f"📏 Response size: {min(len(json_str), MAX_LOG_SIZE):,} characters")
            
        except Exception as e:
            logger.error(f"❌ Error displaying JSON response: {e}")
    
    def _extract_card_data(
        self,
        response_data: Dict[str, Any],
        card_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        DEPRECATED: Use card_extractor.py directly instead.
        This method now delegates to the centralized card data extractor.
        """
        logger.warning("⚠️ DEPRECATED: _extract_card_data in response_processor.py - use card_extractor.py directly")
        
        try:
            if card_id:
                card_data = self.card_extractor.extract_single_card(response_data, card_id)
                return [card_data] if card_data else []
            else:
                return self.card_extractor.extract_from_api_response(response_data)
        except Exception as e:
            logger.error(f"❌ Card extraction delegation error: {e}")
            return []
    
    def _extract_all_cards(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        DEPRECATED: Use card_extractor.py directly instead.
        This method now delegates to the centralized card data extractor.
        """
        logger.warning("⚠️ DEPRECATED: _extract_all_cards in response_processor.py - use card_extractor.py directly")
        
        try:
            return self.card_extractor.extract_from_api_response(response_data)
        except Exception as e:
            logger.error(f"❌ Card extraction delegation error: {e}")
            return []
    
    def _get_response_structure(self, data: Any, max_depth: int = 3, current_depth: int = 0) -> Any:
        """
        Get a structural summary of response data for large responses.
        
        Args:
            data: Data to analyze
            max_depth: Maximum depth to traverse
            current_depth: Current traversal depth
            
        Returns:
            Structural summary of the data
        """
        if current_depth >= max_depth:
            return f"<{type(data).__name__}>"
        
        if isinstance(data, dict):
            if len(data) > 10:  # Large dict
                sample_keys = list(data.keys())[:5]
                return {
                    "type": "dict",
                    "total_keys": len(data),
                    "sample_keys": sample_keys,
                    "sample_values": {k: self._get_response_structure(data[k], max_depth, current_depth + 1) for k in sample_keys}
                }
            else:
                return {k: self._get_response_structure(v, max_depth, current_depth + 1) for k, v in data.items()}
        elif isinstance(data, list):
            if len(data) > 5:  # Large list
                return {
                    "type": "list",
                    "length": len(data),
                    "sample_items": [self._get_response_structure(data[i], max_depth, current_depth + 1) for i in range(min(3, len(data)))]
                }
            else:
                return [self._get_response_structure(item, max_depth, current_depth + 1) for item in data]
        else:
            return f"{type(data).__name__}: {str(data)[:50]}{'...' if len(str(data)) > 50 else ''}"
    
    def _detect_response_format(self, response_data: Dict[str, Any]) -> str:
        """
        Detect the format of the API response.
        
        Args:
            response_data: API response data
            
        Returns:
            String describing the response format
        """
        try:
            if "sections" in response_data:
                sections = response_data["sections"]
                if isinstance(sections, list) and len(sections) > 0:
                    section = sections[0]
                    if "tables" in section:
                        return "Sections with Tables"
                    elif "paragraphs" in section:
                        return "Sections with Paragraphs"
                    else:
                        return "Sections (Unknown Content)"
                return "Sections (Empty)"
            
            elif "headers" in response_data and "rows" in response_data:
                headers_count = len(response_data.get("headers", []))
                rows_count = len(response_data.get("rows", []))
                return f"Legacy Format ({headers_count} headers, {rows_count} rows)"
            
            elif "user" in response_data or "user_info" in response_data:
                return "User Information"
            
            elif "balance" in response_data or "wallet" in response_data:
                return "Wallet/Balance Data"
            
            elif "orders" in response_data:
                return "Orders List"
            
            elif "cart" in response_data:
                return "Cart Data"
            
            elif "filters" in response_data:
                return "Filter Options"
            
            else:
                keys = list(response_data.keys())[:5]  # First 5 keys
                return f"Custom Format (keys: {', '.join(keys)})"
                
        except Exception as e:
            logger.debug(f"Error detecting response format: {e}")
            return "Unknown Format"
    
    def _extract_all_cards_clean(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        DEPRECATED: Use card_extractor.py directly instead.
        Cards from card_extractor are already cleaned and validated.
        """
        logger.warning("⚠️ DEPRECATED: _extract_all_cards_clean in response_processor.py - use card_extractor.py directly")
        
        try:
            # card_extractor already returns clean, validated cards
            return self.card_extractor.extract_from_api_response(response_data)
        except Exception as e:
            logger.error(f"❌ Card extraction delegation error: {e}")
            return []
    
    def _clean_empty_fields(self, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Remove empty, None, or meaningless fields from card data.
        
        Args:
            card_data: Raw card data
            
        Returns:
            Cleaned card data with only meaningful fields
        """
        if not card_data:
            return {}
        
        cleaned = {}
        
        # List of fields that should be excluded if empty or meaningless
        meaningless_values = [
            "", None, "Unknown", "Unknown Bank", "N/A", "n/a", 
            "No address", "no address", "NO ADDRESS", 
            "Unknown Bank", "UNKNOWN", "unknown",
            "0", 0, "0.0", 0.0, "false", False,
            "None", "null", "NULL", "-", "--", "---"
        ]
        
        for key, value in card_data.items():
            # Skip internal/processing fields
            if key.startswith("_") or key in ["extraction_complete", "processing_notes"]:
                continue
            
            # Convert value to string for checking
            value_str = str(value).strip() if value is not None else ""
            
            # Skip if value is meaningless
            if value in meaningless_values or value_str in meaningless_values:
                continue
            
            # Skip if value is just whitespace
            if isinstance(value, str) and not value.strip():
                continue
            
            # Skip if value is just repeated characters (like ******)
            if isinstance(value, str) and len(set(value.replace(" ", ""))) <= 1 and len(value) > 3:
                continue
            
            # Include the field if it has meaningful content
            cleaned[key] = value
        
        return cleaned
    
    # REMOVED: Detailed card display methods - now only showing JSON results
    # These methods were removed to eliminate formatted card display output
    # Only _display_json_extraction_results is used now
    
    def _display_json_extraction_results(
        self,
        extracted_cards: List[Dict[str, Any]],
        processing_time: float
    ) -> None:
        """
        Display extraction results in optimized JSON format.
        
        Args:
            extracted_cards: List of extracted cards
            processing_time: Time taken for processing
        """
        try:
            # Summary at info level
            card_count = len(extracted_cards)
            logger.info(f"[CARD-COUNT] Extracted {card_count} cards in {processing_time*1000:.1f}ms")
            
            if card_count == 0:
                logger.info("No cards found in response")
                return
            
            # Show sample card info at info level
            if card_count > 0:
                sample_card = extracted_cards[0]
                field_count = len([k for k, v in sample_card.items() if v not in ["", None, "Unknown"]])
                logger.info(f"[SAMPLE-CARD] Sample card fields: {field_count} meaningful fields")
            
            # Detailed JSON at INFO level when LOG_FULL_API_RESPONSES is enabled
            from config.settings import get_settings
            settings = get_settings()
            
            if settings.LOG_FULL_API_RESPONSES or logger.isEnabledFor(10):  # INFO level when enabled, or DEBUG level
                # Build result for output - show first 3 cards for better focus (no metadata)
                display_cards = extracted_cards[:3] if len(extracted_cards) > 3 else extracted_cards
                
                json_result = {
                    "cards": display_cards
                }
                
                json_output = json.dumps(json_result, indent=2, ensure_ascii=False)
                
                if settings.LOG_FULL_API_RESPONSES:
                    if card_count > 3:
                        logger.info(f"[EXTRACTION-DETAILS] Card extraction results (First 3 of {card_count}):\n{json_output}")
                    else:
                        logger.info(f"[EXTRACTION-DETAILS] Card extraction results:\n{json_output}")
                else:
                    logger.debug(f"Card extraction results:\n{json_output}")
            
            # Update last activity for admin tracking
            self._last_activity = time.strftime('%Y-%m-%d %H:%M:%S')
            
        except Exception as e:
            logger.error(f"❌ Error displaying extraction results: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive automatic processing statistics."""
        uptime = time.time() - self._start_time
        avg_processing_time = (self.processing_time_total / self.response_count) if self.response_count > 0 else 0
        
        return {
            "total_responses_processed": self.response_count,
            "cache_hits": self.cache_hits,
            "cache_hit_rate_percent": round((self.cache_hits / max(self.response_count, 1)) * 100, 1),
            "average_processing_time_ms": round(avg_processing_time * 1000, 2),
            "total_processing_time_seconds": round(self.processing_time_total, 2),
            "processor_uptime_seconds": uptime,
            "processor_uptime_human": self._format_uptime(uptime),
            "responses_per_second": round(self.response_count / max(uptime, 1), 2),
            "cached_responses_count": len(self._response_cache),
            "automatic_settings": {
                "auto_extract_enabled": self._auto_extract,
                "auto_display_enabled": self._auto_display
            },
            "extractor_performance": {
                "extraction_count": getattr(self.card_extractor, 'extraction_count', 0),
                "extractor_cache_hits": getattr(self.card_extractor, 'cache_hits', 0)
            },
            "status": "optimal" if self.cache_hits > self.response_count * 0.1 else "active"
        }
    
    def _format_uptime(self, seconds: float) -> str:
        """Format uptime in human-readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"
    
    def get_admin_summary(self) -> str:
        """
        Get formatted summary for admin panel display.
        
        Returns:
            Formatted string for admin UI
        """
        stats = self.get_stats()
        
        summary = f"🔄 <b>Response Processor Status</b>\n\n"
        summary += f"📊 <b>Statistics:</b>\n"
        summary += f"• Responses Processed: {stats['total_responses_processed']}\n"
        summary += f"• Uptime: {stats['processor_uptime_human']}\n"
        summary += f"• Status: {stats['status'].title()}\n"
        summary += f"• Last Activity: {stats['last_activity']}\n\n"
        
        summary += f"🎯 <b>Performance:</b>\n"
        summary += f"• Auto-processing: Enabled\n"
        summary += f"• JSON Display: Optimized\n"
        summary += f"• Card Extraction: Active\n"
        
        return summary
    
    def configure_display_options(self, 
                                show_json: bool = True, 
                                extract_cards: bool = True,
                                debug_mode: bool = False) -> None:
        """
        Configure display options for admin control.
        
        Args:
            show_json: Whether to display JSON responses
            extract_cards: Whether to extract card data
            debug_mode: Whether to enable debug logging
        """
        self._show_json_default = show_json
        self._extract_cards_default = extract_cards
        self._debug_mode = debug_mode
        
        logger.info(f"[CONFIG] Response processor configured: JSON={show_json}, Cards={extract_cards}, Debug={debug_mode}")
    
    def get_ui_summary(self, 
                      endpoint: str, 
                      response_data: Dict[str, Any],
                      extracted_cards: Optional[List[Dict[str, Any]]] = None,
                      processing_time: Optional[float] = None) -> str:
        """
        Get formatted UI summary for Telegram display.
        
        Args:
            endpoint: API endpoint name
            response_data: Raw response data
            extracted_cards: Optional extracted cards
            processing_time: Optional processing time
            
        Returns:
            Formatted summary for Telegram UI
        """
        try:
            # Response summary
            response_summary = self.display_formatter.format_api_response_summary(
                endpoint, response_data, processing_time
            )
            
            # Card extraction summary if available
            if extracted_cards is not None and len(extracted_cards) > 0:
                card_summary = self.display_formatter.format_card_extraction_summary(
                    extracted_cards, processing_time or 0.0
                )
                return f"{response_summary}\n\n{card_summary}"
            
            return response_summary
            
        except Exception as e:
            logger.error(f"Error generating UI summary: {e}")
            return f"❌ Error generating response summary: {e}"
    
    def get_json_for_display(self, data: Any, compact: bool = False) -> str:
        """
        Get JSON formatted for Telegram display.
        
        Args:
            data: Data to format
            compact: Whether to use compact formatting
            
        Returns:
            Formatted JSON string
        """
        return self.display_formatter.format_json_for_telegram(data, compact)


# Global instance
_response_processor: Optional[AutomaticAPIV3ResponseProcessor] = None


def get_response_processor() -> AutomaticAPIV3ResponseProcessor:
    """Get the global automatic response processor instance."""
    global _response_processor
    if _response_processor is None:
        _response_processor = AutomaticAPIV3ResponseProcessor()
    return _response_processor


def process_api_v3_response(
    endpoint: str,
    response_data: Dict[str, Any],
    card_id: Optional[str] = None,
    show_json: bool = True,
    extract_cards: bool = True,
    json_output: bool = True,  # Default to JSON output only
) -> Dict[str, Any]:
    """
    Convenience function to process API v3 response.
    
    Args:
        endpoint: API endpoint name
        response_data: Response data from API
        card_id: Optional specific card ID
        show_json: Whether to show JSON in terminal
        extract_cards: Whether to extract card data
        json_output: Whether to return extraction results in JSON format
        
    Returns:
        Processed response with extracted data
    """
    processor = get_response_processor()
    return processor.process_api_v3_response(
        endpoint=endpoint,
        response_data=response_data,
        card_id=card_id,
        show_json=show_json,
        extract_cards=extract_cards,
        json_output=json_output,
    )


def enable_auto_processing() -> None:
    """Enable automatic response processing for all API v3 calls."""
    logger.info("🔄 Auto-processing enabled for API v3 responses")


def disable_auto_processing() -> None:
    """Disable automatic response processing."""
    logger.info("⏸️  Auto-processing disabled for API v3 responses")