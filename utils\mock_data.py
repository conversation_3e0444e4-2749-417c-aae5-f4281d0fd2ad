"""
Mock data for testing and demonstrations.
Data originally extracted from demo/RC folder responses (now integrated).
"""

from typing import Dict, List, Any
from datetime import datetime, timezone, timedelta


class MockOrderData:
    """Mock order data for testing"""
    
    @staticmethod
    def get_orders_list() -> Dict[str, Any]:
        """Get mock orders list response (from RC/1_orders.py)"""
        return {
            "success": True,
            "totalCount": 11,
            "data": [
                {
                    "_id": 299187,
                    "user_id": 197870,
                    "api_user_id": None,
                    "seller_id": "Not Allowed",
                    "product_id": 1738739,
                    "status": "Pending",
                    "createdAt": "2025-09-12T14:30:47.000Z",
                    "refundAt": None,
                    "start_Date": "2025-09-13T00:30:47.000Z",
                    "viewedAt": None,
                    "check_Date": None,
                    "isviewed": None,
                    "price": "8.9900",
                    "canCheck": 0,
                    "checkedAt": None,
                    "base": "31AUG_82VR_PHONE_EMAIL_IP",
                    "cc": None,
                    "exp": None,
                    "cvv": None,
                    "expmonth": None,
                    "expyear": None,
                    "name": "<PERSON><PERSON><PERSON> patel",
                    "firstname": None,
                    "lastname": None,
                    "email": "<EMAIL>",
                    "phone": "**********",
                    "country": "US",
                    "state": "IN",
                    "city": "Kokomo",
                    "zip": "46902",
                    "address": "4021 S Lafountain St",
                    "level": "WORLD ELITE",
                    "bank": "CHASE BANK USA, N.A.",
                    "brand": "MASTERCARD",
                    "type": "CREDIT",
                    "ip": "2603:300f:c00:9600:a426:a54d:6960:896f",
                    "dob": None,
                    "dl": None,
                    "ssn": None,
                    "mmn": None,
                    "ua": None,
                    "refundable": 1,
                    "other": None
                },
                {
                    "_id": 285607,
                    "user_id": 197870,
                    "api_user_id": None,
                    "seller_id": "Not Allowed",
                    "product_id": 1682470,
                    "status": "NonRefundable",
                    "createdAt": "2025-09-01T02:54:09.000Z",
                    "refundAt": None,
                    "start_Date": "2025-09-01T12:54:09.000Z",
                    "viewedAt": "2025-09-01T02:54:15.000Z",
                    "check_Date": None,
                    "isviewed": 1,
                    "price": "3.9900",
                    "canCheck": 0,
                    "checkedAt": "2025-09-01T02:54:29.000Z",
                    "base": "12AUG_83VR_NO_AVS",
                    "cc": "5143773266588663",
                    "exp": "09/27",
                    "cvv": "224",
                    "expmonth": None,
                    "expyear": None,
                    "name": "Sarah Edwards",
                    "firstname": None,
                    "lastname": None,
                    "email": "<EMAIL>",
                    "phone": "**********",
                    "country": "US",
                    "state": None,
                    "city": None,
                    "zip": None,
                    "address": None,
                    "level": "ENHANCED",
                    "bank": "BANCORP BANK, THE",
                    "brand": "MASTERCARD",
                    "type": "DEBIT",
                    "ip": "**************",
                    "dob": None,
                    "dl": None,
                    "ssn": None,
                    "mmn": None,
                    "ua": None,
                    "refundable": 1,
                    "other": None
                },
                {
                    "_id": 282695,
                    "user_id": 197870,
                    "api_user_id": None,
                    "seller_id": "Not Allowed",
                    "product_id": 1682637,
                    "status": "Started",
                    "createdAt": "2025-08-29T04:52:51.000Z",
                    "refundAt": None,
                    "start_Date": "2025-08-29T14:52:51.000Z",
                    "viewedAt": "2025-08-29T20:52:57.000Z",
                    "check_Date": "2025-08-29T14:54:51.000Z",
                    "isviewed": 1,
                    "price": "3.9900",
                    "canCheck": 0,
                    "checkedAt": None,
                    "base": "12AUG_83VR_NO_AVS",
                    "cc": "4427560341921344",
                    "exp": "09/29",
                    "cvv": "830",
                    "expmonth": None,
                    "expyear": None,
                    "name": "Oscar M Vazquez",
                    "firstname": None,
                    "lastname": None,
                    "email": "<EMAIL>",
                    "phone": "**********",
                    "country": "US",
                    "state": None,
                    "city": None,
                    "zip": None,
                    "address": None,
                    "level": "CLASSIC",
                    "bank": "JPMORGAN CHASE BANK, N.A.",
                    "brand": "VISA",
                    "type": "DEBIT",
                    "ip": "**************",
                    "dob": None,
                    "dl": None,
                    "ssn": None,
                    "mmn": None,
                    "ua": None,
                    "refundable": 1,
                    "other": None
                }
            ],
            "limit": 10
        }
    
    @staticmethod
    def get_order_view(order_id: int = 299187) -> Dict[str, Any]:
        """Get mock order view response (from RC/2_view.py)"""
        return {
            "success": True,
            "data": {
                "_id": order_id,
                "user_id": 197870,
                "api_user_id": None,
                "seller_id": "Not Allowed",
                "product_id": 1738739,
                "status": "Started",
                "createdAt": "2025-09-12T14:30:47.000Z",
                "refundAt": None,
                "start_Date": "2025-09-13T00:30:47.000Z",
                "viewedAt": "2025-09-12T14:31:15.000Z",
                "check_Date": "2025-09-12T14:32:15.000Z",
                "isviewed": 1,
                "price": "8.9900",
                "canCheck": 0,
                "checkedAt": None,
                "base": "31AUG_82VR_PHONE_EMAIL_IP",
                "cc": "****************",
                "exp": "12/27",
                "cvv": "499",
                "expmonth": None,
                "expyear": None,
                "name": "Kimeeben patel",
                "firstname": None,
                "lastname": None,
                "email": "<EMAIL>",
                "phone": "**********",
                "country": "US",
                "state": "IN",
                "city": "Kokomo",
                "zip": "46902",
                "address": "4021 S Lafountain St",
                "level": "WORLD ELITE",
                "bank": "CHASE BANK USA, N.A.",
                "brand": "MASTERCARD",
                "type": "CREDIT",
                "ip": "2603:300f:c00:9600:a426:a54d:6960:896f",
                "dob": None,
                "dl": None,
                "ssn": None,
                "mmn": None,
                "ua": None,
                "refundable": 1,
                "other": None
            }
        }
    
    @staticmethod
    def get_check_response(order_id: int = 299187) -> Dict[str, Any]:
        """Get mock check response (from RC/3_check.py)"""
        return {
            "success": True,
            "data": {
                "_id": order_id,
                "user_id": 197870,
                "api_user_id": None,
                "seller_id": "Not Allowed",
                "product_id": 1738739,
                "status": "Refunded",
                "createdAt": "2025-09-12T14:30:47.000Z",
                "refundAt": "2025-09-12T14:31:27.000Z",
                "start_Date": "2025-09-13T00:30:47.000Z",
                "viewedAt": "2025-09-12T14:31:15.000Z",
                "check_Date": None,
                "isviewed": 1,
                "price": "8.9900",
                "canCheck": 0,
                "checkedAt": "2025-09-12T14:31:27.000Z",
                "base": "31AUG_82VR_PHONE_EMAIL_IP",
                "cc": "****************",
                "exp": "12/27",
                "cvv": "499",
                "expmonth": None,
                "expyear": None,
                "name": "Kimeeben patel",
                "firstname": None,
                "lastname": None,
                "email": "<EMAIL>",
                "phone": "**********",
                "country": "US",
                "state": "IN",
                "city": "Kokomo",
                "zip": "46902",
                "address": "4021 S Lafountain St",
                "level": "WORLD ELITE",
                "bank": "CHASE BANK USA, N.A.",
                "brand": "MASTERCARD",
                "type": "CREDIT",
                "ip": "2603:300f:c00:9600:a426:a54d:6960:896f",
                "dob": None,
                "dl": None,
                "ssn": None,
                "mmn": None,
                "refundable": 1,
                "ua": None,
                "other": None
            }
        }
    
    @staticmethod
    def get_mock_order_for_post_checkout() -> Dict[str, Any]:
        """Get a mock completed order for post-checkout testing"""
        now = datetime.now(timezone.utc)
        return {
            "order_id": "TEST_ORDER_001",
            "status": "completed",
            "created_at": now.isoformat(),
            "total_price": 8.99,
            "item_count": 1,
            "cards": [
                {
                    "card_id": "TEST_CARD_001",
                    "_id": "TEST_CARD_001",
                    "bank": "CHASE BANK USA, N.A.",
                    "brand": "MASTERCARD",
                    "type": "CREDIT",
                    "level": "WORLD ELITE",
                    "country": "US",
                    "state": "IN",
                    "city": "Kokomo",
                    "zip": "46902",
                    "price": 8.99,
                    "status": "Started",
                    "base": "31AUG_82VR_PHONE_EMAIL_IP",
                    "canCheck": 1,
                    "check_Date": (now + timedelta(seconds=60)).isoformat(),
                    "name": "Kimeeben patel",
                    "email": "<EMAIL>",
                    "phone": "**********",
                    "address": "4021 S Lafountain St",
                    "cc": "****************",
                    "exp": "12/27",
                    "cvv": "499",
                    "ip": "2603:300f:c00:9600:a426:a54d:6960:896f"
                }
            ]
        }

