"""
API v1 Migration Guide

This script provides guidance and utilities for migrating from the old
scattered API structure to the new organized API v1 structure.
"""

import os
import re
from typing import Dict, List, Tuple

from utils.central_logger import get_logger

logger = get_logger()

# Mapping of old imports to new API v1 imports
IMPORT_MAPPINGS = {
    # Configuration services
    "from services.api_config_service import": "from api_v1.services.api_config import",
    "from services.api_service import": "from api_v1.services.api_config import",
    
    # HTTP client
    "from services.external_api_service import": "from api_v1.services.http_client import",
    
    # Utilities
    "from utils.api_logging import": "from api_v1.utils.logging import",
    "from utils.encryption import": "from api_v1.utils.encryption import",
    
    # Error handling
    "from services.api_config_errors import": "from api_v1.utils.error_handling import",
    
    # Authentication
    "from services.auth_profile_service import": "from api_v1.utils.authentication import",
}

# Class/function name mappings
CLASS_MAPPINGS = {
    "APIConfigurationService": "UnifiedAPIConfigurationService",
    "ExternalAPIService": "UnifiedHTTPClient",
    "EncryptionService": "EncryptionService",  # Same name
    "get_api_config_service": "get_unified_api_config_service",
    "get_encryption_service": "get_encryption_service",  # Same name
}

def analyze_file_dependencies(file_path: str) -> List[str]:
    """Analyze a file to find API-related imports that need updating"""
    dependencies = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for old_import, new_import in IMPORT_MAPPINGS.items():
            if old_import in content:
                dependencies.append(f"Update: {old_import} -> {new_import}")
                
        for old_class, new_class in CLASS_MAPPINGS.items():
            if old_class in content and old_class != new_class:
                dependencies.append(f"Rename: {old_class} -> {new_class}")
                
    except Exception as e:
        dependencies.append(f"Error analyzing {file_path}: {e}")
        
    return dependencies

def generate_migration_report() -> Dict[str, List[str]]:
    """Generate a migration report for all Python files in the project"""
    report = {}
    
    # Directories to scan
    scan_dirs = ['services', 'handlers', 'utils', 'middleware', 'models']
    
    for dir_name in scan_dirs:
        if os.path.exists(dir_name):
            for root, dirs, files in os.walk(dir_name):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        dependencies = analyze_file_dependencies(file_path)
                        if dependencies:
                            report[file_path] = dependencies
                            
    return report

def print_migration_report():
    """Print a detailed migration report"""
    logger.info("=" * 80)
    logger.info("API v1 MIGRATION REPORT")
    logger.info("=" * 80)
    logger.info()
    
    report = generate_migration_report()
    
    if not report:
        logger.info("✅ No files require migration updates!")
        return
        
    logger.info(f"📋 Found {len(report)} files that need updates:")
    logger.info()
    
    for file_path, dependencies in report.items():
        logger.info(f"📄 {file_path}")
        for dep in dependencies:
            logger.info(f"   • {dep}")
        logger.info()
        
    logger.info("=" * 80)
    logger.info("MIGRATION STEPS:")
    logger.info("=" * 80)
    logger.info()
    logger.info("1. Update import statements using the mappings above")
    logger.info("2. Update class/function names where they have changed")
    logger.info("3. Test functionality to ensure everything works")
    logger.info("4. Remove old service files once migration is complete")
    logger.info()
    logger.info("For detailed migration instructions, see the API v1 documentation.")

if __name__ == "__main__":
    print_migration_report()
