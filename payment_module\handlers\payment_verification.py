from aiogram import Router, F, Dispatcher
from aiogram.fsm.context import FSMContext
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.filters.callback_data import CallbackData
from datetime import datetime
import logging
from typing import Optional, Dict, Any

from ..core.oxa_verify import check_oxapay_payment
from ..core.payment_config import OXA_PAY_API_KEY
from ..keyboards.deposit_kb import (
    payment_verification_keyboard,
    payment_success_keyboard,
    payment_processing_keyboard,
)
from ..utils.payment_utils import safe_edit_message, clear_state_data
from ..utils.template_helpers import format_text
from ..database.payment_operations import get_payment_by_track_id, update_payment_status
from ..database.user_operations import get_user_balance, update_user_balance, add_transaction


class PaymentVerificationStates:
    waiting_for_track_id = "waiting_for_track_id"


router = Router()
router.name = "payment_verification_router"


def is_track_id_already_verified(track_id: str) -> tuple[bool, dict | None]:
    """
    Check if a track_id has already been verified and processed.
    
    Args:
        track_id: The payment tracking ID
        
    Returns:
        tuple: (is_verified, payment_data)
    """
    # In a real implementation, you would check your database
    # For now, we'll return False to allow verification
    return False, None


@router.callback_query(F.data == "verify_latest_payment")
async def verify_latest_payment(callback_query: CallbackQuery, state: FSMContext):
    """Handle manual payment verification."""
    await callback_query.answer("Checking payment status...")
    
    # Get payment data from state
    state_data = await state.get_data()
    track_id = state_data.get("track_id")
    
    if not track_id:
        await callback_query.message.edit_text(
            "❌ <b>No payment found to verify</b>\n\n"
            "Please start a new deposit process.",
            reply_markup=payment_verification_keyboard(),
            parse_mode="HTML"
        )
        return
    
    # Check if already verified
    is_verified, payment_data = is_track_id_already_verified(track_id)
    
    if is_verified:
        await callback_query.message.edit_text(
            "✅ <b>Payment already verified</b>\n\n"
            f"Transaction ID: <code>{track_id}</code>\n"
            f"Status: Completed",
            reply_markup=payment_success_keyboard(),
            parse_mode="HTML"
        )
        return
    
    # Show processing message
    await callback_query.message.edit_text(
        "⏳ <b>Verifying payment...</b>\n\n"
        f"Transaction ID: <code>{track_id}</code>\n"
        "Please wait while we check the payment status.",
        parse_mode="HTML"
    )
    
    try:
        # Verify payment with OXA Pay
        verification_result = await check_oxapay_payment(track_id, OXA_PAY_API_KEY)
        
        if verification_result.get("status") == "success":
            payment_data = verification_result.get("data", {})
            payment_status = payment_data.get("status", "").lower()
            
            if payment_status in ["completed", "confirmed", "success", "paid"]:
                # Payment completed successfully
                amount = payment_data.get("amount", 0)
                currency = payment_data.get("currency", "USDT")
                
                success_message = (
                    "🎉 <b>• PAYMENT SUCCESSFUL •</b> 🎉\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>TRANSACTION COMPLETED</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"✅ <b>Status:</b> Completed\n"
                    f"💰 <b>Amount:</b> <code>{amount} {currency}</code>\n"
                    f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n"
                    f"⏰ <b>Completed:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"<i>Your balance has been updated successfully!</i>"
                )
                
                await callback_query.message.edit_text(
                    success_message,
                    reply_markup=payment_success_keyboard(),
                    parse_mode="HTML"
                )
                
                # Clear state
                await state.clear()
                
            elif payment_status in ["pending", "confirming", "waiting"]:
                # Payment still processing
                processing_message = (
                    "⏳ <b>• PAYMENT PROCESSING •</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>STATUS UPDATE</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"🔄 <b>Status:</b> {payment_status.title()}\n"
                    f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n\n"
                    f"<i>Your payment is being processed. This may take a few minutes.</i>\n"
                    f"<i>You can check again in a moment.</i>"
                )
                
                await callback_query.message.edit_text(
                    processing_message,
                    reply_markup=payment_processing_keyboard(track_id),
                    parse_mode="HTML"
                )
                
            else:
                # Payment failed or other status
                failed_message = (
                    "❌ <b>• PAYMENT ISSUE •</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>STATUS UPDATE</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"⚠️ <b>Status:</b> {payment_status.title()}\n"
                    f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n\n"
                    f"<i>There was an issue with your payment. Please try again or contact support.</i>"
                )
                
                await callback_query.message.edit_text(
                    failed_message,
                    reply_markup=payment_verification_keyboard(),
                    parse_mode="HTML"
                )
        
        else:
            # Verification failed
            error_message = verification_result.get("message", "Unknown error")
            
            error_text = (
                "❌ <b>• VERIFICATION FAILED •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>ERROR DETAILS</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n"
                f"⚠️ <b>Error:</b> {error_message}\n\n"
                f"<i>Unable to verify payment status. Please try again later.</i>"
            )
            
            await callback_query.message.edit_text(
                error_text,
                reply_markup=payment_verification_keyboard(),
                parse_mode="HTML"
            )
    
    except Exception as e:
        logging.error(f"Error verifying payment {track_id}: {e}")
        
        error_text = (
            "❌ <b>• VERIFICATION ERROR •</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>SYSTEM ERROR</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n"
            f"⚠️ <b>Error:</b> {str(e)}\n\n"
            f"<i>An error occurred while verifying your payment. Please try again later.</i>"
        )
        
        await callback_query.message.edit_text(
            error_text,
            reply_markup=payment_verification_keyboard(),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("check_payment:"))
async def check_payment_status(callback_query: CallbackQuery, state: FSMContext):
    """Check payment status by track_id."""
    await callback_query.answer("Checking payment status...")
    
    # Extract track_id from callback data
    track_id = callback_query.data.split(":", 1)[1]
    
    try:
        # Verify payment with OXA Pay
        verification_result = await check_oxapay_payment(track_id, OXA_PAY_API_KEY)
        
        if verification_result.get("status") == "success":
            payment_data = verification_result.get("data", {})
            payment_status = payment_data.get("status", "").lower()
            
            if payment_status in ["completed", "confirmed", "success", "paid"]:
                # Payment completed successfully
                amount = payment_data.get("amount", 0)
                currency = payment_data.get("currency", "USDT")
                
                success_message = (
                    "🎉 <b>• PAYMENT SUCCESSFUL •</b> 🎉\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>TRANSACTION COMPLETED</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"✅ <b>Status:</b> Completed\n"
                    f"💰 <b>Amount:</b> <code>{amount} {currency}</code>\n"
                    f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n"
                    f"⏰ <b>Completed:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"<i>Your balance has been updated successfully!</i>"
                )
                
                await callback_query.message.edit_text(
                    success_message,
                    reply_markup=payment_success_keyboard(),
                    parse_mode="HTML"
                )
                
                # Clear state
                await state.clear()
                
            else:
                # Payment still processing or failed
                status_message = (
                    f"⏳ <b>• PAYMENT STATUS •</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>CURRENT STATUS</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"🔄 <b>Status:</b> {payment_status.title()}\n"
                    f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n\n"
                    f"<i>Payment is still being processed. You can check again in a moment.</i>"
                )
                
                await callback_query.message.edit_text(
                    status_message,
                    reply_markup=payment_processing_keyboard(track_id),
                    parse_mode="HTML"
                )
        
        else:
            # Verification failed
            error_message = verification_result.get("message", "Unknown error")
            
            error_text = (
                "❌ <b>• VERIFICATION FAILED •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>ERROR DETAILS</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n"
                f"⚠️ <b>Error:</b> {error_message}\n\n"
                f"<i>Unable to verify payment status. Please try again later.</i>"
            )
            
            await callback_query.message.edit_text(
                error_text,
                reply_markup=payment_verification_keyboard(),
                parse_mode="HTML"
            )
    
    except Exception as e:
        logging.error(f"Error checking payment {track_id}: {e}")
        
        error_text = (
            "❌ <b>• CHECK ERROR •</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>SYSTEM ERROR</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>\n"
            f"⚠️ <b>Error:</b> {str(e)}\n\n"
            f"<i>An error occurred while checking your payment. Please try again later.</i>"
        )
        
        await callback_query.message.edit_text(
            error_text,
            reply_markup=payment_verification_keyboard(),
            parse_mode="HTML"
        )


def register_payment_verification_handlers(dp: Dispatcher):
    """Register all payment verification handlers."""
    try:
        dp.include_router(router)
        logging.info(f"Registered payment verification handlers")
    except RuntimeError as e:
        logging.warning(f"Payment verification router already attached, skipping registration: {e}")
    except Exception as e:
        logging.error(f"Failed to register payment verification handlers: {e}")