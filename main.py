"""
Main application entry point for Demo Wallet Bot v3
"""

from __future__ import annotations

import asyncio
import signal
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from aiogram import Bo<PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode

from config.settings import get_settings
from database.connection import init_database, close_database
from handlers import setup_handlers
from middleware import setup_middleware
from utils.central_logger import setup_logging, get_logger
from utils.multi_file_logger import setup_multi_file_logging, get_multi_logger, LogCategory, LogLevel

logger = get_logger()

# Test logging immediately  
logger.info("=== MAIN.PY STARTUP ===")
logger.info("Central logger initialized successfully")


class BotApplication:
    """Main bot application class"""

    def __init__(self):
        self.settings = get_settings()
        self.bot: Bot | None = None
        self.dp: Dispatcher | None = None
        self._shutdown_event = asyncio.Event()
        self.multi_logger = None

    async def setup(self) -> None:
        """Initialize bot application"""
        try:
            # Setup centralized logging
            consolidated_path = (self.settings.LOG_CONSOLIDATED_FILE or "").strip()
            setup_logging(
                level=self.settings.LOG_LEVEL,
                log_file=(
                    self.settings.LOG_FILE_PATH if self.settings.LOG_TO_FILE else None
                ),
                max_file_size=self.settings.LOG_MAX_SIZE,
                backup_count=self.settings.LOG_BACKUP_COUNT,
                consolidated_log_file=consolidated_path or None,
            )
            
            # Setup multi-file logging system
            if self.settings.LOG_MULTI_FILE_ENABLED:
                self.multi_logger = setup_multi_file_logging(self.settings.LOG_BASE_PATH)
                logger.info("Multi-file logging system enabled")
                
                # Log application startup event
                self.multi_logger.log(
                    LogCategory.APPLICATION, 
                    LogLevel.INFO, 
                    "Bot application starting up",
                    extra={
                        "version": "3.0",
                        "environment": self.settings.ENVIRONMENT,
                        "log_level": self.settings.LOG_LEVEL
                    }
                )
            logger.info("Starting Demo Wallet Bot v3...")

            # Suppress HTTP request logging noise
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # Validate bot token
            await self._validate_bot_token()

            # Initialize database
            await init_database()
            logger.debug("Database connection established")

            # Sync shared API registry with admin-managed configurations
            try:
                from admin.services.shared_api_integration import (
                    get_shared_api_integration_service,
                )

                integration_service = get_shared_api_integration_service()
                initialized = await integration_service.initialize_integration()
                if initialized:
                    logger.debug("API integration ready")
                else:
                    logger.warning("API integration failed")
            except Exception as integration_error:
                logger.warning(
                    "Unable to initialize shared API integration: %s",
                    integration_error,
                )

            # Create bot and dispatcher
            self.bot = Bot(
                token=self.settings.BOT_TOKEN,
                default=DefaultBotProperties(parse_mode=ParseMode.HTML),
            )
            self.dp = Dispatcher()

            # Setup middleware and handlers
            setup_middleware(self.dp)
            setup_handlers(self.dp)

            # Setup menu button commands
            await self._setup_menu_commands()

            # Setup metrics if enabled
            if self.settings.METRICS_ENABLED:
                await self._setup_metrics()

            logger.info("Bot application setup completed")

        except Exception as e:
            logger.error(f"Failed to setup bot application: {e}")
            raise

    async def _validate_bot_token(self) -> None:
        """Validate Telegram bot token format"""
        token = self.settings.BOT_TOKEN.strip()
        if not token or ":" not in token:
            raise ValueError(
                "BOT_TOKEN is missing or invalid. "
                "Set it in .env (format: <digits>:<secret>)."
            )

        prefix = token.split(":", 1)[0]
        if not prefix.isdigit():
            raise ValueError("BOT_TOKEN format invalid (should start with digits:...).")

        logger.debug("Bot token validation passed")

    async def _setup_metrics(self) -> None:
        """Setup Prometheus metrics server"""
        try:
            from prometheus_client import start_http_server

            start_http_server(self.settings.METRICS_PORT)
            logger.debug(f"Metrics server started on port {self.settings.METRICS_PORT}")
        except OSError as e:
            if e.errno == 98:  # EADDRINUSE
                logger.debug(
                    f"Metrics port {self.settings.METRICS_PORT} in use, metrics disabled"
                )
            else:
                logger.debug(f"Metrics server unavailable: {e}")
        except Exception as e:
            logger.warning(f"Failed to start metrics server: {e}")

    async def _setup_menu_commands(self) -> None:
        """Setup Telegram menu button commands"""
        try:
            from utils.menu_commands import setup_menu_commands, setup_admin_commands
            
            if not self.bot:
                logger.error("Bot instance not available for menu command setup")
                return
            
            # Setup general user commands
            await setup_menu_commands(self.bot)
            
            # Setup admin commands if admin users are configured
            admin_user_ids = []
            try:
                admin_ids_str = self.settings.ADMIN_USER_IDS.strip()
                if admin_ids_str:
                    admin_user_ids = [int(uid.strip()) for uid in admin_ids_str.split(",") if uid.strip()]
                    if admin_user_ids:
                        await setup_admin_commands(self.bot, admin_user_ids)
                        logger.debug(f"Admin menu commands configured for {len(admin_user_ids)} admins")
                    else:
                        logger.debug("No admin user IDs configured")
                else:
                    logger.debug("ADMIN_USER_IDS not set, skipping admin commands")
            except (ValueError, AttributeError) as e:
                logger.warning(f"Invalid ADMIN_USER_IDS format: {e}")
                
            logger.debug("Menu button commands setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup menu commands: {e}")
            # Don't raise - menu commands are not critical for bot functionality

    async def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        # Note: loop.add_signal_handler() is not supported on Windows
        # Signal handling is done via KeyboardInterrupt exception instead
        if sys.platform != "win32":
            loop = asyncio.get_running_loop()
            for sig in (signal.SIGINT, signal.SIGTERM):
                loop.add_signal_handler(sig, self._shutdown_event.set)
        else:
            # On Windows, we rely on KeyboardInterrupt being raised
            logger.debug(
                "Signal handlers not available on Windows, using KeyboardInterrupt"
            )

    async def run(self) -> None:
        """Run the bot application"""
        if not self.bot or not self.dp:
            raise RuntimeError("Bot not setup. Call setup() first.")

        try:
            # Setup signal handlers
            await self._setup_signal_handlers()

            # Start retention worker if enabled
            if self.settings.RETENTION_ENABLED:
                logger.debug("Starting retention worker...")
                asyncio.create_task(self._retention_worker())
            else:
                logger.debug("Retention worker disabled")

            # Start background services
            try:
                from services.background_tasks import start_background_tasks
                from services.startup import initialize_services

                await start_background_tasks()
                await initialize_services()
                logger.debug("Background services ready")
            except Exception as e:
                logger.error(f"Failed to start background services: {e}")

            # Initialize payment service (callback server handles all verification)
            try:
                from services.payment_service import get_payment_service
                
                payment_service = get_payment_service()
                if payment_service.is_available():
                    logger.info("✅ Payment service initialized - callback-based verification enabled")
                    logger.info("📡 Payments will be processed automatically via webhooks")
                    logger.info("🔗 Callback URL configured - no manual verification needed")
                    logger.info("ℹ️  Note: Start callback server separately with: python start_payment_server.py")
                else:
                    logger.warning("Payment service not available - check OXA_PAY_API_KEY configuration")
            except Exception as e:
                logger.error(f"Failed to initialize payment service: {e}")

            logger.info("Bot polling...")

            # Start payment notification processor
            async def process_payment_notifications():
                """Background task to process payment notifications from callback queue"""
                try:
                    from payment_module.utils.notification_queue import process_notifications
                    while True:
                        try:
                            processed = await process_notifications(self.bot)
                            if processed > 0:
                                logger.debug(f"Processed {processed} payment notifications")
                        except Exception as e:
                            logger.error(f"Error processing payment notifications: {e}")
                        await asyncio.sleep(2)  # Check every 2 seconds
                except Exception as e:
                    logger.error(f"Payment notification processor crashed: {e}")

            # Start notification processor in background
            try:
                notification_task = asyncio.create_task(process_payment_notifications())
                logger.info("✅ Payment notification processor started")
            except Exception as e:
                logger.error(f"Failed to start notification processor: {e}")

            # Start polling with graceful shutdown
            # Use resolved update types and ensure callback queries are included
            resolved = set(self.dp.resolve_used_update_types() or [])
            # Always allow messages and callback queries
            resolved.update({"message", "callback_query"})
            polling_task = asyncio.create_task(
                self.dp.start_polling(self.bot, allowed_updates=list(resolved))
            )

            # Wait for shutdown signal or polling to complete
            # On Windows, KeyboardInterrupt will cancel everything
            # On Unix, signal handlers will set the shutdown event
            try:
                if sys.platform == "win32":
                    # On Windows, just wait for the polling task
                    # KeyboardInterrupt will cancel it
                    await polling_task
                else:
                    # On Unix, wait for either polling or shutdown signal
                    shutdown_task = asyncio.create_task(self._shutdown_event.wait())
                    done, pending = await asyncio.wait(
                        {polling_task, shutdown_task},
                        return_when=asyncio.FIRST_COMPLETED,
                    )
                    # Cancel pending tasks
                    for task in pending:
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
            except asyncio.CancelledError:
                # Graceful cancellation (e.g., Ctrl+C)
                self._shutdown_event.set()
                polling_task.cancel()
                try:
                    await polling_task
                except asyncio.CancelledError:
                    pass
                raise

            logger.info("Bot polling stopped")

        except asyncio.CancelledError:
            # Normal shutdown path due to cancellation
            pass
        except Exception as e:
            logger.error(f"Error during bot execution: {e}", exc_info=True)
            raise

    async def _retention_worker(self) -> None:
        """Background worker for data retention cleanup"""
        try:
            from services.retention_service import RetentionService

            retention_service = RetentionService()
            logger.debug("Retention worker started")
        except ImportError as e:
            logger.warning(f"Retention service not available: {e}")
            return
        except Exception as e:
            logger.error(f"Failed to initialize retention service: {e}")
            return

        while not self._shutdown_event.is_set():
            try:
                removed = await retention_service.purge_old_data(
                    self.settings.RETENTION_DAYS
                )
                if removed > 0:
                    logger.info(f"Retention cleanup removed {removed} records")
                else:
                    logger.debug("No old data found for retention cleanup")
            except Exception as e:
                logger.error(f"Retention cleanup failed: {e}")

            # Wait 24 hours or until shutdown
            try:
                await asyncio.wait_for(self._shutdown_event.wait(), timeout=24 * 3600)
                break  # Shutdown requested
            except asyncio.TimeoutError:
                continue  # Continue with next cleanup cycle

        logger.debug("Retention worker stopped")

    async def shutdown(self) -> None:
        """Shutdown bot application"""
        try:
            logger.info("Shutting down bot application...")
            
            # Log shutdown event to multi-file logger
            if self.multi_logger:
                self.multi_logger.log(
                    LogCategory.APPLICATION,
                    LogLevel.INFO,
                    "Bot application shutting down",
                    extra={"reason": "graceful_shutdown"}
                )

            # Stop background tasks
            try:
                from services.background_tasks import stop_background_tasks

                await stop_background_tasks()
                logger.debug("Background tasks stopped")
            except Exception as e:
                logger.error(f"Error stopping background tasks: {e}")

            # Stop checkout queue and other background services
            try:
                from services.startup import shutdown_services

                await shutdown_services()
                logger.debug("Background services shutdown")
            except Exception as e:
                logger.error(f"Error stopping background services: {e}")

            # Close service sessions
            try:
                from services.card_service import CardService
                from services.cart_service import CartService
                from services.dump_service import close_dump_service

                card_service = CardService()
                await card_service.close()
                logger.debug("Card service session closed")

                cart_service = CartService()
                await cart_service.close()
                logger.debug("Cart service session closed")

                await close_dump_service()
                logger.debug("Dump service session closed")
            except Exception as e:
                logger.warning(f"Error closing services: {e}")

            if self.bot:
                await self.bot.session.close()

            await close_database()
            
            # Close multi-file logger last
            if self.multi_logger:
                try:
                    self.multi_logger.close()
                    logger.debug("Multi-file logger closed")
                except Exception as e:
                    logger.warning(f"Error closing multi-file logger: {e}")

            logger.info("Bot application shutdown completed")

            # Flush and close all logging handlers to avoid atexit errors
            # Note: Using centralized logger shutdown method
            import logging
            logging.shutdown()

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


@asynccontextmanager
async def bot_lifespan() -> AsyncGenerator[BotApplication, None]:
    """Context manager for bot application lifecycle"""
    app = BotApplication()
    try:
        await app.setup()
        yield app
    finally:
        await app.shutdown()


async def main() -> None:
    """Main application entry point"""
    try:
        async with bot_lifespan() as app:
            await app.run()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except asyncio.CancelledError:
        logger.info("Shutdown requested (cancelled)")
    except Exception as e:
        logger.error(f"Application failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
