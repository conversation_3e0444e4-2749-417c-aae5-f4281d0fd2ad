"""
API v3 Virtual Cart to Main Cart Workflow Service

This service implements the complete workflow for transferring items from a virtual cart
to the main API cart, including cart state checking, cleanup, and verification.

Based on the requirements:
1. Check if main cart has existing items
2. Clear main cart if needed (using empty_cart and remove_selected as fallback)
3. Transfer virtual cart items to main cart
4. Verify the transfer was successful
5. Maintain session reuse throughout

Does NOT implement checkout or order placement endpoints for safety.
"""

from __future__ import annotations

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from .virtual_cart import VirtualCart, VirtualCartItem, get_virtual_cart
from .cart_service import APIV3CartService
from .browse_service import APIV3BrowseService
from models.catalog import CartItem, CatalogItem

from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class WorkflowResult:
    """Result of the virtual cart to main cart workflow."""

    success: bool
    virtual_cart_summary: Dict[str, Any]
    main_cart_initial_state: Dict[str, Any]
    cart_cleanup_result: Optional[Dict[str, Any]]
    transfer_result: Dict[str, Any]
    verification_result: Dict[str, Any]
    error: Optional[str] = None
    execution_time: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)


@dataclass
class VerificationResult:
    """Result of cart transfer verification."""

    success: bool
    virtual_items_count: int
    main_cart_items_count: int
    virtual_item_ids: List[str]
    main_cart_item_ids: List[str]
    items_match: bool
    discrepancies: List[str]
    virtual_total_price: float = 0.0
    main_cart_total_price: float = 0.0
    price_match: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)


class APIV3VirtualCartWorkflow:
    """
    Orchestrates the complete workflow for transferring virtual cart items to main cart.

    This service handles:
    - Cart state checking
    - Cart cleanup (empty_cart with remove_selected fallback)
    - Virtual cart item transfer
    - Transfer verification
    - Session management throughout
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        """
        Initialize the workflow service.

        Args:
            base_url: API base URL
            username: Authentication username
            password: Authentication password
            use_socks_proxy: Whether to use SOCKS proxy
            socks_url: SOCKS proxy URL
        """
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy
        self.socks_url = socks_url

        # Create cart service (reuses session)
        self.cart_service = APIV3CartService(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )

        self.logger = get_logger()

    async def transfer_virtual_cart_to_main(
        self,
        user_id: str,
        force_cleanup: bool = True,
        verify_transfer: bool = True,
    ) -> WorkflowResult:
        """
        Execute the complete workflow to transfer virtual cart items to main cart.

        Args:
            user_id: User identifier
            force_cleanup: Whether to force cleanup of main cart before transfer
            verify_transfer: Whether to verify the transfer was successful

        Returns:
            WorkflowResult with detailed information about the process
        """
        start_time = time.time()

        try:
            self.logger.info(
                f"🚀 Starting virtual cart to main cart workflow for user {user_id}"
            )

            # Step 1: Get virtual cart and check if it has items
            virtual_cart = get_virtual_cart(user_id)
            virtual_summary = virtual_cart.get_summary()

            if virtual_summary.total_items == 0:
                self.logger.warning(f"⚠️  Virtual cart is empty for user {user_id}")
                return WorkflowResult(
                    success=False,
                    virtual_cart_summary=virtual_summary.to_dict(),
                    main_cart_initial_state={},
                    cart_cleanup_result=None,
                    transfer_result={},
                    verification_result={},
                    error="Virtual cart is empty",
                    execution_time=time.time() - start_time,
                )

            self.logger.info(
                f"📋 Virtual cart contains {virtual_summary.total_items} items (${virtual_summary.total_price:.2f})"
            )

            # Step 2: Check main cart initial state
            main_cart_initial = await self._check_main_cart_state(user_id)

            # Step 3: Clean up main cart if needed
            cleanup_result = None
            if force_cleanup or main_cart_initial.get("item_count", 0) > 0:
                cleanup_result = await self._cleanup_main_cart(user_id)
                if not cleanup_result.get("success"):
                    self.logger.error(
                        f"❌ Failed to cleanup main cart: {cleanup_result.get('error')}"
                    )
                    return WorkflowResult(
                        success=False,
                        virtual_cart_summary=virtual_summary.to_dict(),
                        main_cart_initial_state=main_cart_initial,
                        cart_cleanup_result=cleanup_result,
                        transfer_result={},
                        verification_result={},
                        error=f"Cart cleanup failed: {cleanup_result.get('error')}",
                        execution_time=time.time() - start_time,
                    )

            # Step 4: Transfer virtual cart items to main cart
            transfer_result = await self._transfer_items(virtual_cart, user_id)

            if not transfer_result.get("success"):
                self.logger.error(
                    f"❌ Failed to transfer items: {transfer_result.get('error')}"
                )
                return WorkflowResult(
                    success=False,
                    virtual_cart_summary=virtual_summary.to_dict(),
                    main_cart_initial_state=main_cart_initial,
                    cart_cleanup_result=cleanup_result,
                    transfer_result=transfer_result,
                    verification_result={},
                    error=f"Item transfer failed: {transfer_result.get('error')}",
                    execution_time=time.time() - start_time,
                )

            # Step 5: Verify transfer if requested
            verification_result = {}
            if verify_transfer:
                verification_result = await self._verify_transfer(virtual_cart, user_id)

                if not verification_result.get("success"):
                    self.logger.warning(
                        f"⚠️  Transfer verification failed: {verification_result}"
                    )

            execution_time = time.time() - start_time
            success = transfer_result.get("success", False) and (
                not verify_transfer or verification_result.get("success", False)
            )

            if success:
                self.logger.info(
                    f"✅ Workflow completed successfully in {execution_time:.2f}s"
                )
            else:
                self.logger.warning(
                    f"⚠️  Workflow completed with issues in {execution_time:.2f}s"
                )

            return WorkflowResult(
                success=success,
                virtual_cart_summary=virtual_summary.to_dict(),
                main_cart_initial_state=main_cart_initial,
                cart_cleanup_result=cleanup_result,
                transfer_result=transfer_result,
                verification_result=verification_result,
                execution_time=execution_time,
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ Workflow failed with exception: {e}", exc_info=True)
            return WorkflowResult(
                success=False,
                virtual_cart_summary={},
                main_cart_initial_state={},
                cart_cleanup_result=None,
                transfer_result={},
                verification_result={},
                error=str(e),
                execution_time=execution_time,
            )

    async def _check_main_cart_state(self, user_id: str) -> Dict[str, Any]:
        """
        Check the current state of the main cart.

        Args:
            user_id: User identifier

        Returns:
            Dictionary with cart state information
        """
        try:
            self.logger.info(f"🔍 Checking main cart state for user {user_id}")

            cart_response = await self.cart_service.view_cart(user_id)

            if cart_response.get("success"):
                item_count = cart_response.get("item_count", 0)
                items = cart_response.get("items", [])

                self.logger.info(f"📊 Main cart contains {item_count} items")

                return {
                    "success": True,
                    "item_count": item_count,
                    "items": items,
                    "has_items": item_count > 0,
                    "raw_data": cart_response.get("raw_data", {}),
                }
            else:
                self.logger.error(
                    f"Failed to check cart state: {cart_response.get('error')}"
                )
                return {
                    "success": False,
                    "error": cart_response.get("error"),
                    "item_count": 0,
                    "items": [],
                    "has_items": False,
                }

        except Exception as e:
            self.logger.error(f"Error checking main cart state: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "item_count": 0,
                "items": [],
                "has_items": False,
            }

    async def _cleanup_main_cart(self, user_id: str) -> Dict[str, Any]:
        """
        Clean up the main cart by removing all items.

        Uses empty_cart as primary method with remove_selected as fallback.

        Args:
            user_id: User identifier

        Returns:
            Dictionary with cleanup result information
        """
        try:
            self.logger.info(f"🗑️  Cleaning up main cart for user {user_id}")

            # First attempt: Use empty_cart endpoint
            empty_result = await self.cart_service.clear_cart(user_id)

            if empty_result.get("success"):
                self.logger.info("✅ Successfully emptied cart using clear_cart")
                return {
                    "success": True,
                    "method": "empty_cart",
                    "message": "Cart cleared successfully",
                    "empty_cart_result": empty_result,
                }

            # Fallback: Get current items and remove them individually
            self.logger.warning("⚠️  empty_cart failed, trying remove_selected fallback")

            cart_state = await self._check_main_cart_state(user_id)
            if not cart_state.get("success") or cart_state.get("item_count", 0) == 0:
                # Cart is already empty or we can't check it
                return {
                    "success": True,
                    "method": "no_action_needed",
                    "message": "Cart appears to be empty or inaccessible",
                    "empty_cart_result": empty_result,
                    "cart_state": cart_state,
                }

            # Extract item IDs for removal
            items = cart_state.get("items", [])
            item_ids = [item.get("id") for item in items if item.get("id")]

            if not item_ids:
                self.logger.warning("⚠️  No item IDs found for removal")
                return {
                    "success": False,
                    "method": "remove_selected_failed",
                    "error": "No item IDs found for removal",
                    "empty_cart_result": empty_result,
                    "cart_state": cart_state,
                }

            # Remove items using remove_selected
            remove_result = await self.cart_service.remove_from_cart(item_ids, user_id)

            if remove_result.get("success"):
                self.logger.info(
                    f"✅ Successfully removed {len(item_ids)} items using remove_selected"
                )
                return {
                    "success": True,
                    "method": "remove_selected",
                    "message": f"Removed {len(item_ids)} items from cart",
                    "empty_cart_result": empty_result,
                    "remove_selected_result": remove_result,
                    "removed_item_ids": item_ids,
                }
            else:
                self.logger.error(f"❌ Both empty_cart and remove_selected failed")
                return {
                    "success": False,
                    "method": "both_failed",
                    "error": f"empty_cart failed: {empty_result.get('error')}, remove_selected failed: {remove_result.get('error')}",
                    "empty_cart_result": empty_result,
                    "remove_selected_result": remove_result,
                }

        except Exception as e:
            self.logger.error(f"Error cleaning up main cart: {e}", exc_info=True)
            return {
                "success": False,
                "method": "exception",
                "error": str(e),
            }

    async def _transfer_items(
        self, virtual_cart: VirtualCart, user_id: str
    ) -> Dict[str, Any]:
        """
        Transfer items from virtual cart to main cart.

        Args:
            virtual_cart: Virtual cart instance
            user_id: User identifier

        Returns:
            Dictionary with transfer result information
        """
        try:
            self.logger.info(
                f"📤 Transferring virtual cart items to main cart for user {user_id}"
            )

            # Get item IDs from virtual cart
            item_ids = virtual_cart.get_cart_data_for_api()

            if not item_ids:
                return {
                    "success": False,
                    "error": "No items to transfer from virtual cart",
                    "item_ids": [],
                }

            self.logger.info(
                f"📋 Transferring {len(item_ids)} item instances to main cart"
            )
            self.logger.debug(f"Item IDs: {item_ids}")

            # Add items to main cart
            add_result = await self.cart_service.add_to_cart(item_ids, user_id)

            if add_result.get("success"):
                self.logger.info(
                    f"✅ Successfully transferred {len(item_ids)} items to main cart"
                )
                return {
                    "success": True,
                    "message": f"Transferred {len(item_ids)} items to main cart",
                    "transferred_item_ids": item_ids,
                    "add_to_cart_result": add_result,
                }
            else:
                self.logger.error(
                    f"❌ Failed to transfer items: {add_result.get('error')}"
                )
                return {
                    "success": False,
                    "error": add_result.get("error"),
                    "item_ids": item_ids,
                    "add_to_cart_result": add_result,
                }

        except Exception as e:
            self.logger.error(f"Error transferring items: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def _verify_transfer(
        self, virtual_cart: VirtualCart, user_id: str
    ) -> Dict[str, Any]:
        """
        Verify that virtual cart items were successfully transferred to main cart.

        Args:
            virtual_cart: Virtual cart instance
            user_id: User identifier

        Returns:
            Dictionary with verification result information
        """
        try:
            self.logger.info(f"🔍 Verifying cart transfer for user {user_id}")

            # Get virtual cart data
            virtual_items = virtual_cart.get_items()
            virtual_item_ids = virtual_cart.get_cart_data_for_api()
            virtual_total_price = virtual_cart.get_total_price()

            # Get main cart data
            main_cart_response = await self.cart_service.view_cart(user_id)

            if not main_cart_response.get("success"):
                return {
                    "success": False,
                    "error": f"Failed to retrieve main cart for verification: {main_cart_response.get('error')}",
                    "verification_result": VerificationResult(
                        success=False,
                        virtual_items_count=len(virtual_item_ids),
                        main_cart_items_count=0,
                        virtual_item_ids=virtual_item_ids,
                        main_cart_item_ids=[],
                        items_match=False,
                        discrepancies=["Failed to retrieve main cart"],
                        virtual_total_price=virtual_total_price,
                    ).to_dict(),
                }

            # Extract main cart item IDs
            main_cart_items = main_cart_response.get("items", [])
            main_cart_item_ids = []
            main_cart_total_price = 0.0

            for item in main_cart_items:
                item_id = item.get("id")
                if item_id:
                    main_cart_item_ids.append(item_id)

                # Try to parse price
                price_str = item.get("price", "0")
                try:
                    # Remove currency symbols and parse
                    price_clean = price_str.replace("$", "").replace(",", "").strip()
                    if price_clean:
                        main_cart_total_price += float(price_clean)
                except (ValueError, TypeError):
                    pass

            # Compare counts
            virtual_count = len(virtual_item_ids)
            main_cart_count = len(main_cart_item_ids)

            # Compare item IDs (accounting for quantities)
            virtual_id_set = set(virtual_item_ids)
            main_cart_id_set = set(main_cart_item_ids)

            items_match = (
                virtual_id_set == main_cart_id_set and virtual_count == main_cart_count
            )

            # Check for discrepancies
            discrepancies = []
            if virtual_count != main_cart_count:
                discrepancies.append(
                    f"Item count mismatch: virtual={virtual_count}, main={main_cart_count}"
                )

            missing_in_main = virtual_id_set - main_cart_id_set
            if missing_in_main:
                discrepancies.append(
                    f"Items missing in main cart: {list(missing_in_main)}"
                )

            extra_in_main = main_cart_id_set - virtual_id_set
            if extra_in_main:
                discrepancies.append(f"Extra items in main cart: {list(extra_in_main)}")

            # Price comparison (with tolerance for floating point differences)
            price_match = abs(virtual_total_price - main_cart_total_price) < 0.01
            if not price_match:
                discrepancies.append(
                    f"Price mismatch: virtual=${virtual_total_price:.2f}, main=${main_cart_total_price:.2f}"
                )

            verification_success = items_match and len(discrepancies) == 0

            verification_result = VerificationResult(
                success=verification_success,
                virtual_items_count=virtual_count,
                main_cart_items_count=main_cart_count,
                virtual_item_ids=virtual_item_ids,
                main_cart_item_ids=main_cart_item_ids,
                items_match=items_match,
                discrepancies=discrepancies,
                virtual_total_price=virtual_total_price,
                main_cart_total_price=main_cart_total_price,
                price_match=price_match,
            )

            if verification_success:
                self.logger.info(
                    f"✅ Transfer verification successful: {virtual_count} items, ${virtual_total_price:.2f}"
                )
            else:
                self.logger.warning(f"⚠️  Transfer verification failed: {discrepancies}")

            return {
                "success": verification_success,
                "verification_result": verification_result.to_dict(),
                "main_cart_response": main_cart_response,
            }

        except Exception as e:
            self.logger.error(f"Error verifying transfer: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "verification_result": VerificationResult(
                    success=False,
                    virtual_items_count=0,
                    main_cart_items_count=0,
                    virtual_item_ids=[],
                    main_cart_item_ids=[],
                    items_match=False,
                    discrepancies=[f"Verification exception: {str(e)}"],
                ).to_dict(),
            }

    async def get_cart_state_summary(self, user_id: str) -> Dict[str, Any]:
        """
        Get a comprehensive summary of both virtual and main cart states.

        Args:
            user_id: User identifier

        Returns:
            Dictionary with both cart states for comparison
        """
        try:
            self.logger.info(f"📊 Getting cart state summary for user {user_id}")

            # Get virtual cart state
            virtual_cart = get_virtual_cart(user_id)
            virtual_summary = virtual_cart.get_summary()

            # Get main cart state
            main_cart_state = await self._check_main_cart_state(user_id)

            return {
                "success": True,
                "virtual_cart": {
                    "total_items": virtual_summary.total_items,
                    "unique_items": virtual_summary.unique_items,
                    "total_price": virtual_summary.total_price,
                    "items_by_brand": virtual_summary.items_by_brand,
                    "items_by_country": virtual_summary.items_by_country,
                    "has_items": virtual_summary.total_items > 0,
                },
                "main_cart": {
                    "item_count": main_cart_state.get("item_count", 0),
                    "has_items": main_cart_state.get("has_items", False),
                    "success": main_cart_state.get("success", False),
                    "items": main_cart_state.get("items", []),
                },
                "needs_cleanup": main_cart_state.get("has_items", False),
                "ready_for_transfer": virtual_summary.total_items > 0,
            }

        except Exception as e:
            self.logger.error(f"Error getting cart state summary: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "virtual_cart": {},
                "main_cart": {},
                "needs_cleanup": False,
                "ready_for_transfer": False,
            }

    async def check_transfer_readiness(self, user_id: str) -> Dict[str, Any]:
        """
        Check if the system is ready for a virtual cart transfer.

        Args:
            user_id: User identifier

        Returns:
            Dictionary with readiness status and recommendations
        """
        try:
            self.logger.info(f"🔍 Checking transfer readiness for user {user_id}")

            cart_summary = await self.get_cart_state_summary(user_id)

            if not cart_summary.get("success"):
                return {
                    "ready": False,
                    "error": cart_summary.get("error"),
                    "recommendations": ["Fix cart state checking issues"],
                }

            virtual_cart = cart_summary["virtual_cart"]
            main_cart = cart_summary["main_cart"]

            ready = True
            recommendations = []
            warnings = []

            # Check virtual cart
            if not virtual_cart.get("has_items"):
                ready = False
                recommendations.append("Add items to virtual cart before transfer")

            # Check main cart
            if main_cart.get("has_items"):
                warnings.append(
                    f"Main cart contains {main_cart.get('item_count', 0)} items that will be cleared"
                )
                recommendations.append(
                    "Consider reviewing main cart contents before transfer"
                )

            # Check main cart accessibility
            if not main_cart.get("success"):
                ready = False
                recommendations.append("Fix main cart access issues")

            return {
                "ready": ready,
                "virtual_cart_items": virtual_cart.get("total_items", 0),
                "main_cart_items": main_cart.get("item_count", 0),
                "needs_cleanup": cart_summary.get("needs_cleanup", False),
                "warnings": warnings,
                "recommendations": recommendations,
                "cart_summary": cart_summary,
            }

        except Exception as e:
            self.logger.error(f"Error checking transfer readiness: {e}", exc_info=True)
            return {
                "ready": False,
                "error": str(e),
                "recommendations": ["Fix system errors before attempting transfer"],
            }

    async def close(self):
        """Close the workflow service and underlying services."""
        await self.cart_service.close()
