"""
Enhanced Payment Handlers with New Payment Module Features
Handles advanced payment features like underpayment/overpayment detection,
manual verification, analytics, and VIP integration
"""

from __future__ import annotations

from aiogram import Router, F, Bot
from aiogram.filters import Command
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from services.payment_service import get_payment_service
from services.user_service import UserService
from utils.keyboards import wallet_menu_keyboard
from utils.texts import DEMO_WATERMARK

from utils.central_logger import get_logger

logger = get_logger()


class ManualVerificationStates(StatesGroup):
    """States for manual verification flow"""
    waiting_for_track_id = State()
    verifying_payment = State()


class EnhancedPaymentHandlers:
    """Enhanced payment handlers with new payment module features"""

    def __init__(self):
        self.payment_service = get_payment_service()
        self.user_service = UserService()

    async def cmd_verify(self, message: Message, state: FSMContext) -> None:
        """Handle /verify command - manual payment verification"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found" + DEMO_WATERMARK)
                return

            if not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK
                )
                return

            verify_text = (
                "🔍 <b>Manual Payment Verification</b>\n\n"
                "Enter the Track ID of the payment you want to verify:\n\n"
                "📝 <b>Format:</b> Enter the Track ID (e.g., ABC123XYZ)\n"
                "💡 <b>Tip:</b> You can find this in your payment confirmation\n\n"
                "⚠️ <b>Note:</b> This will check the payment status and handle any issues"
            )

            # Create cancel keyboard
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel",
                            callback_data="cancel_manual_verification"
                        )
                    ]
                ]
            )

            await message.answer(
                verify_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )

            await state.set_state(ManualVerificationStates.waiting_for_track_id)

        except Exception as e:
            logger.error(f"Error in verify command: {e}")
            await message.answer("❌ Error starting verification" + DEMO_WATERMARK)

    async def process_track_id(self, message: Message, state: FSMContext) -> None:
        """Process track ID input for manual verification"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found" + DEMO_WATERMARK)
                return

            track_id = message.text.strip()
            if not track_id:
                await message.answer("❌ Please enter a valid Track ID")
                return

            # Update state
            await state.update_data(track_id=track_id)
            await state.set_state(ManualVerificationStates.verifying_payment)

            # Show verification in progress
            verifying_text = (
                f"⏳ <b>Verifying Payment</b>\n\n"
                f"🆔 Track ID: <code>{track_id}</code>\n"
                f"🔄 Status: <i>Checking payment status...</i>\n\n"
                f"<i>Please wait while we verify your payment.</i>"
            )

            await message.answer(verifying_text, parse_mode="HTML")

            # Verify payment
            success, verification_data, error_msg = await self.payment_service.verify_payment(track_id)

            if not success:
                await message.answer(
                    f"❌ <b>Verification Failed</b>\n\n"
                    f"🆔 Track ID: <code>{track_id}</code>\n"
                    f"❌ Error: {error_msg}\n\n"
                    f"<i>Please check the Track ID and try again.</i>",
                    parse_mode="HTML"
                )
                await state.clear()
                return

            # Check payment status
            status = verification_data.get('status', 'unknown')
            amount = verification_data.get('amount', 0)

            if status == 'completed':
                # Check for underpayment/overpayment
                status_result, details = await self.payment_service.check_payment_amounts(
                    amount, amount, track_id
                )

                if status_result == "underpayment":
                    # Handle underpayment
                    await self.payment_service.handle_underpayment(
                        message.bot, user.id, track_id, amount, amount
                    )
                elif status_result == "overpayment":
                    # Handle overpayment
                    await self.payment_service.handle_overpayment(
                        message.bot, user.id, track_id, amount, amount
                    )
                else:
                    # Normal payment completion
                    # Update user balance (amount only, no bonus)
                    balance_success, balance_error = await self.payment_service.update_user_balance(
                        user_id=user.id,
                        amount=amount,
                        transaction_type="deposit"
                    )

                    if balance_success:
                        # Handle payment completion
                        await self.payment_service.handle_payment_completion(
                            message.bot, user.id, track_id, amount, amount
                        )

                        # Trigger VIP check
                        vip_result = self.payment_service.trigger_vip_check(
                            user.id, amount, user.username
                        )

                        if vip_result.get('success'):
                            await message.answer(
                                f"👑 <b>VIP Status Update</b>\n\n"
                                f"{vip_result.get('message', 'VIP tier updated!')}",
                                parse_mode="HTML"
                            )
                    else:
                        await message.answer(
                            f"⚠️ <b>Payment Verified but Balance Update Failed</b>\n\n"
                            f"🆔 Track ID: <code>{track_id}</code>\n"
                            f"💰 Amount: ${amount:.2f}\n"
                            f"❌ Error: {balance_error}\n\n"
                            f"<i>Please contact support for assistance.</i>",
                            parse_mode="HTML"
                        )
            else:
                # Payment not completed
                await message.answer(
                    f"⏳ <b>Payment Status</b>\n\n"
                    f"🆔 Track ID: <code>{track_id}</code>\n"
                    f"💰 Amount: ${amount:.2f}\n"
                    f"📊 Status: <b>{status.upper()}</b>\n\n"
                    f"<i>Payment is still processing. Please wait for completion.</i>",
                    parse_mode="HTML"
                )

            await state.clear()

        except Exception as e:
            logger.error(f"Error processing track ID: {e}")
            await message.answer("❌ Error verifying payment" + DEMO_WATERMARK)
            await state.clear()

    async def cb_cancel_manual_verification(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Cancel manual verification"""
        try:
            await state.clear()
            
            await callback.message.edit_text(
                "❌ Manual verification cancelled.\n\n"
                "You can start verification anytime using /verify" + DEMO_WATERMARK,
                reply_markup=wallet_menu_keyboard()
            )
            await callback.answer("Verification cancelled")

        except Exception as e:
            logger.error(f"Error cancelling manual verification: {e}")
            await callback.answer("❌ Error cancelling verification", show_alert=True)

    async def cmd_payment_stats(self, message: Message) -> None:
        """Handle /payment_stats command - show payment statistics"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found" + DEMO_WATERMARK)
                return

            if not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK
                )
                return

            # Get payment statistics
            stats = self.payment_service.get_payment_statistics(user.id, days=30)

            if "error" in stats:
                await message.answer(
                    f"❌ <b>Error retrieving statistics</b>\n\n"
                    f"Error: {stats['error']}" + DEMO_WATERMARK,
                    parse_mode="HTML"
                )
                return

            # Format statistics
            stats_text = (
                f"📊 <b>Payment Statistics (Last 30 Days)</b>\n\n"
                f"💰 <b>Total Payments:</b> {stats.get('total_payments', 0)}\n"
                f"💵 <b>Total Amount:</b> ${stats.get('total_amount', 0):.2f}\n"
                f"✅ <b>Success Rate:</b> {stats.get('success_rate', 0):.1f}%\n"
                f"⚠️ <b>Underpayment Rate:</b> {stats.get('underpayment_rate', 0):.1f}%\n"
                f"💰 <b>Overpayment Rate:</b> {stats.get('overpayment_rate', 0):.1f}%\n\n"
            )

            # Add status breakdown
            by_status = stats.get('by_status', {})
            if by_status:
                stats_text += f"📈 <b>Status Breakdown:</b>\n"
                for status, data in by_status.items():
                    stats_text += f"• {status.title()}: {data.get('count', 0)} payments (${data.get('total_amount', 0):.2f})\n"

            stats_text += f"\n<i>Statistics updated in real-time</i>" + DEMO_WATERMARK

            await message.answer(stats_text, parse_mode="HTML")

        except Exception as e:
            logger.error(f"Error in payment stats command: {e}")
            await message.answer("❌ Error retrieving payment statistics" + DEMO_WATERMARK)

    async def cmd_payment_analytics(self, message: Message) -> None:
        """Handle /payment_analytics command - show detailed payment analytics"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found" + DEMO_WATERMARK)
                return

            if not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK
                )
                return

            # Get payment analytics
            analytics = self.payment_service.get_payment_analytics(user.id, days=30)

            if "error" in analytics:
                await message.answer(
                    f"❌ <b>Error retrieving analytics</b>\n\n"
                    f"Error: {analytics['error']}" + DEMO_WATERMARK,
                    parse_mode="HTML"
                )
                return

            # Format analytics
            summary = analytics.get('summary', {})
            analytics_text = (
                f"📈 <b>Payment Analytics (Last 30 Days)</b>\n\n"
                f"📊 <b>Summary:</b>\n"
                f"• Total Payments: {summary.get('total_payments', 0)}\n"
                f"• Total Amount: ${summary.get('total_amount', 0):.2f}\n"
                f"• Success Rate: {summary.get('success_rate', 0):.1f}%\n"
                f"• Avg Daily Payments: {summary.get('avg_daily_payments', 0):.1f}\n"
                f"• Avg Daily Amount: ${summary.get('avg_daily_amount', 0):.2f}\n\n"
            )

            # Add trends if available
            daily_trends = analytics.get('daily_trends', [])
            if daily_trends:
                analytics_text += f"📅 <b>Recent Trends:</b>\n"
                for trend in daily_trends[-5:]:  # Show last 5 days
                    analytics_text += f"• {trend.get('date', 'N/A')}: {trend.get('count', 0)} payments (${trend.get('amount', 0):.2f})\n"

            analytics_text += f"\n<i>Analytics updated in real-time</i>" + DEMO_WATERMARK

            await message.answer(analytics_text, parse_mode="HTML")

        except Exception as e:
            logger.error(f"Error in payment analytics command: {e}")
            await message.answer("❌ Error retrieving payment analytics" + DEMO_WATERMARK)

    async def cmd_search_payments(self, message: Message) -> None:
        """Handle /search_payments command - search payment history"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found" + DEMO_WATERMARK)
                return

            if not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK
                )
                return

            # Extract search query from message
            search_query = message.text.replace('/search_payments', '').strip()
            if not search_query:
                await message.answer(
                    "🔍 <b>Search Payments</b>\n\n"
                    "Usage: <code>/search_payments [query]</code>\n\n"
                    "You can search by:\n"
                    "• Track ID (e.g., ABC123)\n"
                    "• Order ID (e.g., ORD-123)\n"
                    "• Description keywords\n\n"
                    "Example: <code>/search_payments ABC123</code>",
                    parse_mode="HTML"
                )
                return

            # Search payments
            payments = self.payment_service.search_payments(search_query, user.id, limit=10)

            if not payments:
                await message.answer(
                    f"🔍 <b>No payments found</b>\n\n"
                    f"Query: <code>{search_query}</code>\n\n"
                    f"<i>Try a different search term or check your payment history.</i>",
                    parse_mode="HTML"
                )
                return

            # Format search results
            search_text = f"🔍 <b>Search Results for: {search_query}</b>\n\n"
            
            for i, payment in enumerate(payments[:5], 1):  # Show max 5 results
                track_id = payment.get('track_id', 'N/A')
                amount = payment.get('amount', 0)
                status = payment.get('status', 'unknown')
                created_at = payment.get('created_at', 'N/A')
                
                search_text += (
                    f"{i}. <b>Track ID:</b> <code>{track_id}</code>\n"
                    f"   💰 Amount: ${amount:.2f}\n"
                    f"   📊 Status: {status.upper()}\n"
                    f"   📅 Date: {created_at}\n\n"
                )

            if len(payments) > 5:
                search_text += f"<i>... and {len(payments) - 5} more results</i>\n"

            search_text += f"<i>Found {len(payments)} payment(s)</i>" + DEMO_WATERMARK

            await message.answer(search_text, parse_mode="HTML")

        except Exception as e:
            logger.error(f"Error in search payments command: {e}")
            await message.answer("❌ Error searching payments" + DEMO_WATERMARK)

    async def cb_view_payment_details(self, callback: CallbackQuery) -> None:
        """Handle payment details view"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract track_id from callback data
            track_id = callback.data.split(":")[-1]

            # Get payment details
            success, payments, error_msg = await self.payment_service.get_user_payment_history(user.id)
            
            if not success:
                await callback.answer(f"❌ {error_msg}", show_alert=True)
                return

            # Find the specific payment
            payment = None
            for p in payments:
                if p.get('track_id') == track_id:
                    payment = p
                    break

            if not payment:
                await callback.answer("❌ Payment not found", show_alert=True)
                return

            # Format payment details
            details_text = (
                f"💳 <b>Payment Details</b>\n\n"
                f"🆔 <b>Track ID:</b> <code>{payment.get('track_id', 'N/A')}</code>\n"
                f"📋 <b>Order ID:</b> <code>{payment.get('order_id', 'N/A')}</code>\n"
                f"💰 <b>Amount:</b> ${payment.get('amount', 0):.2f}\n"
                f"📊 <b>Status:</b> {payment.get('status', 'unknown').upper()}\n"
                f"📅 <b>Created:</b> {payment.get('created_at', 'N/A')}\n"
                f"🔄 <b>Updated:</b> {payment.get('updated_at', 'N/A')}\n\n"
            )

            # Add description if available
            description = payment.get('description', '')
            if description:
                details_text += f"📝 <b>Description:</b> {description}\n\n"

            # Add user info
            details_text += f"👤 <b>User ID:</b> {user.id}\n"
            details_text += f"📱 <b>Username:</b> @{user.username or 'N/A'}\n"

            await callback.message.edit_text(
                details_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error viewing payment details: {e}")
            await callback.answer("❌ Error viewing payment details", show_alert=True)


def get_enhanced_payment_router() -> Router:
    """Get enhanced payment router with new features"""
    router = Router()
    handlers = EnhancedPaymentHandlers()
    
    # Commands
    router.message.register(handlers.cmd_verify, Command("verify"))
    router.message.register(handlers.cmd_payment_stats, Command("payment_stats"))
    router.message.register(handlers.cmd_payment_analytics, Command("payment_analytics"))
    router.message.register(handlers.cmd_search_payments, Command("search_payments"))
    
    # State handlers
    router.message.register(
        handlers.process_track_id,
        ManualVerificationStates.waiting_for_track_id
    )
    
    # Callbacks
    router.callback_query.register(
        handlers.cb_cancel_manual_verification,
        F.data == "cancel_manual_verification"
    )
    router.callback_query.register(
        handlers.cb_view_payment_details,
        F.data.startswith("view_payment_details:")
    )
    
    return router
