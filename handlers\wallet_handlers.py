"""
Wallet-related Telegram bot handlers
"""

from __future__ import annotations

import re
from typing import Optional

from aiogram import Router, F
from aiogram.filters import Command
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from services.user_service import UserService
from services.export_service import ExportService
from utils.keyboards import wallet_menu_keyboard, back_keyboard
from utils.texts import (
    SUCCESS_FUNDS_ADDED,
    ERROR_INVALID_AMOUNT,
    ERROR_USER_NOT_FOUND,
    ERROR_INSUFFICIENT_FUNDS,
    ERROR_WALLET_LOCKED,
    ERROR_VALIDATION_FAILED,
    INFO_WALLET_BALANCE,
    VALIDATION_AMOUNT_FORMAT,
    VALIDATION_AMOUNT_RANGE,
    DEMO_WATERMARK,
)
from utils.validation import validate_amount, ValidationError
from utils.loading_animations import LoadingStages, WALLET_STAGES
from middleware import attach_common_middlewares

from utils.central_logger import get_logger

logger = get_logger()


class AddFundsStates(StatesGroup):
    """States for add funds flow"""

    waiting_for_amount = State()


class WalletHandlers:
    """Wallet-related message and callback handlers"""

    def __init__(self):
        self.user_service = UserService()
        self.export_service = ExportService()

    async def cmd_balance(self, message: Message) -> None:
        """Handle /balance command - shows external API balance"""
        try:
            user = message.from_user
            if not user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            # Get user and wallet for currency info
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            wallet = await self.user_service.get_wallet_by_user_id(str(db_user.id))
            if not wallet:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                return

            # Show wallet balance (single source of truth)
            balance_emoji = "💎" if wallet.balance >= 100 else "💰" if wallet.balance >= 10 else "💸"
            balance_text = (
                f"{balance_emoji} <b>Your Balance: ${wallet.balance:.2f} {wallet.currency}</b>\n"
                f"📊 <i>Wallet Balance</i>"
                + DEMO_WATERMARK
            )

            await message.answer(balance_text, reply_markup=wallet_menu_keyboard())

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await message.answer("❌ Error retrieving balance" + DEMO_WATERMARK)

    async def _get_external_balance(self) -> Optional[float]:
        """Get balance from external API"""
        try:
            from services.external_api_service import ExternalAPIService

            api_service = ExternalAPIService()
            response = await api_service.get_user_info()

            if response.success and response.data and isinstance(response.data, dict):
                # Extract balance from user object
                user_data = response.data.get('user', {})
                if isinstance(user_data, dict) and 'balance' in user_data:
                    try:
                        return float(user_data['balance'])
                    except (ValueError, TypeError):
                        pass

            return None

        except Exception as e:
            logger.error(f"Error getting external balance: {e}")
            return None

    async def cb_wallet_menu(self, callback: CallbackQuery) -> None:
        """Handle wallet menu callback"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Create work coroutine for wallet operations
            async def wallet_work():
                # Get wallet balance
                db_user = await self.user_service.get_user_by_telegram_id(user.id)
                if not db_user:
                    return None, None, None

                wallet = await self.user_service.get_wallet_by_user_id(str(db_user.id))
                if not wallet:
                    return None, None, None

                # Return user and wallet data (no external balance needed)
                return db_user, wallet, None
            
            # Run loading stages concurrently with wallet operations
            result = await LoadingStages.run_concurrent_loading(
                callback,
                WALLET_STAGES,
                wallet_work(),
                operation_name="Wallet Balance Check"
            )
            
            db_user, wallet, external_balance = result
            
            if not db_user or not wallet:
                await callback.answer("❌ Wallet not found", show_alert=True)
                return

            # Show wallet balance (single source of truth)
            balance_emoji = "💎" if wallet.balance >= 100 else "💰" if wallet.balance >= 10 else "💸"
            balance_text = (
                f"<b>💼 WALLET DASHBOARD</b>\n\n"
                f"{balance_emoji} <b>Current Balance: ${wallet.balance:.2f}</b>\n"
                f"📊 <i>Wallet Balance</i>\n"
                f"🔄 <i>Last Updated: Just now</i>\n\n"
                f"<b>📋 Available Actions:</b>\n"
                f"• Add funds to your wallet\n"
                f"• Verify payments manually\n"
                f"• View wallet statistics\n\n"
                f"<i>Choose an option below:</i>" + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                balance_text, reply_markup=wallet_menu_keyboard(), parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in wallet menu: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_add_funds(self, callback: CallbackQuery) -> None:
        """Handle add funds callbacks (10, 25, 50)"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract amount from callback data
            amount_map = {
                "wallet:add10": 10.0,
                "wallet:add25": 25.0,
                "wallet:add50": 50.0,
            }

            amount = amount_map.get(callback.data)
            if not amount:
                await callback.answer("❌ Invalid amount", show_alert=True)
                return

            # Get user
            db_user = await self.user_service.ensure_user_and_wallet(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                language_code=user.language_code,
            )

            # Add funds
            wallet = await self.user_service.add_funds(str(db_user.id), amount)

            success_text = (
                SUCCESS_FUNDS_ADDED.format(amount=amount, balance=wallet.balance)
                + DEMO_WATERMARK
            )

            await callback.message.edit_text(
                success_text, reply_markup=wallet_menu_keyboard()
            )
            await callback.answer(f"✅ Added ${amount:.2f}")

            logger.debug(f"Added ${amount} to wallet for user {user.id}")

        except ValueError as e:
            logger.warning(f"Validation error adding funds: {e}")
            error_msg = str(e)
            if "locked" in error_msg.lower():
                await callback.answer(ERROR_WALLET_LOCKED, show_alert=True)
            elif "amount" in error_msg.lower():
                await callback.answer(ERROR_INVALID_AMOUNT, show_alert=True)
            else:
                await callback.answer(ERROR_VALIDATION_FAILED, show_alert=True)
        except Exception as e:
            logger.error(f"Error adding funds: {e}")
            await callback.answer("❌ Error adding funds", show_alert=True)

    async def cb_add_custom(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle custom add funds callback"""
        try:
            await callback.message.edit_text(
                "💰 <b>Add Custom Amount</b>\n\n"
                "Enter the amount you want to add (e.g., 75.50):\n\n"
                "<i>Minimum: $1.00, Maximum: $1000.00</i>" + DEMO_WATERMARK,
                reply_markup=back_keyboard("menu:wallet"),
            )
            await state.set_state(AddFundsStates.waiting_for_amount)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in custom add funds: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def process_custom_amount(self, message: Message, state: FSMContext) -> None:
        """Process custom amount input with enhanced security validation"""
        try:
            user = message.from_user
            if not user:
                await message.answer(ERROR_USER_NOT_FOUND + DEMO_WATERMARK)
                await state.clear()
                return

            # Enhanced input validation and sanitization
            user_input = message.text
            if not user_input or not user_input.strip():
                await message.answer("❌ Please enter a valid amount" + DEMO_WATERMARK)
                await state.clear()
                return

            # Rate limiting for wallet operations
            from utils.security import check_rate_limit_security

            if not check_rate_limit_security(
                user.id, "wallet_operation", max_attempts=5, window_seconds=60
            ):
                await message.answer(
                    "⏳ Too many wallet operations. Please wait a moment before trying again."
                    + DEMO_WATERMARK
                )
                await state.clear()
                return

            # Parse and validate amount using utility with enhanced bounds
            try:
                amount = validate_amount(
                    user_input.strip(), min_amount=0.01, max_amount=1000.0
                )

                # Additional business logic validation
                if amount > 500.0:
                    await message.answer(
                        "⚠️ Large amount detected. For demo purposes, amounts over $500 require additional verification."
                        + DEMO_WATERMARK
                    )
                    # In production, this could trigger additional verification steps

            except ValidationError as e:
                logger.warning(
                    f"Invalid amount input from user {user.id}: {user_input}"
                )
                await message.answer(f"❌ {str(e)}" + DEMO_WATERMARK)
                await state.clear()
                return

            # Get user with additional security checks
            try:
                db_user = await self.user_service.ensure_user_and_wallet(
                    telegram_id=user.id,
                    username=user.username,
                    first_name=user.first_name,
                    language_code=user.language_code,
                )

                # Verify user account is active and not locked
                if not db_user or not db_user.active:
                    await message.answer("❌ Account is not active" + DEMO_WATERMARK)
                    await state.clear()
                    return

            except Exception as e:
                logger.error(f"Error retrieving user {user.id}: {e}")
                await message.answer(
                    "❌ Unable to process request. Please try again." + DEMO_WATERMARK
                )
                await state.clear()
                return

            # Add funds
            wallet = await self.user_service.add_funds(str(db_user.id), amount)

            success_text = (
                SUCCESS_FUNDS_ADDED.format(amount=amount, balance=wallet.balance)
                + DEMO_WATERMARK
            )

            await message.answer(success_text, reply_markup=wallet_menu_keyboard())
            await state.clear()

            logger.debug(f"Added custom amount ${amount} to wallet for user {user.id}")

        except ValueError as e:
            logger.warning(f"Validation error in custom amount: {e}")
            error_msg = str(e)
            if "locked" in error_msg.lower():
                await message.answer(ERROR_WALLET_LOCKED + DEMO_WATERMARK)
            elif "insufficient" in error_msg.lower():
                await message.answer(ERROR_INSUFFICIENT_FUNDS + DEMO_WATERMARK)
            else:
                await message.answer(ERROR_VALIDATION_FAILED + DEMO_WATERMARK)
            await state.clear()
        except Exception as e:
            logger.error(f"Error processing custom amount: {e}")
            await message.answer("❌ Error adding funds" + DEMO_WATERMARK)
            await state.clear()

    async def cb_wallet_stats(self, callback: CallbackQuery) -> None:
        """Show detailed wallet statistics"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Create work coroutine for wallet stats generation
            async def stats_work():
                # Get user data
                user_obj = await self.user_service.get_user_by_telegram_id(user.id)
                if not user_obj:
                    return None

                # Generate wallet report
                report = await self.export_service.generate_wallet_report(str(user_obj.id))
                return report
            
            # Run loading stages concurrently with stats generation
            report = await LoadingStages.run_concurrent_loading(
                callback,
                WALLET_STAGES,
                stats_work(),
                operation_name="Wallet Statistics"
            )
            
            if not report:
                await callback.answer(ERROR_USER_NOT_FOUND, show_alert=True)
                return

            # Format statistics message
            wallet_info = report["wallet_info"]
            tx_summary = report["transaction_summary"]["last_30_days"]

            stats_text = f"""
📊 <b>Wallet Statistics</b>

💰 <b>Current Balance:</b> ${wallet_info['balance']:.2f} {wallet_info['currency']}
🏦 <b>Daily Limit:</b> ${wallet_info['daily_cap']:.2f}
📅 <b>Monthly Limit:</b> ${wallet_info['monthly_cap']:.2f}
🔒 <b>Status:</b> {'🔴 Locked' if wallet_info['locked'] else '🟢 Active'}

📈 <b>Last 30 Days Activity:</b>
"""

            # Add transaction type summaries
            for tx_type, stats in tx_summary.items():
                if stats["count"] > 0:
                    stats_text += f"• {tx_type}: {stats['count']} transactions, ${stats['total']:.2f}\n"

            if not tx_summary:
                stats_text += "• No transactions in the last 30 days\n"

            stats_text += DEMO_WATERMARK

            # Create enhanced keyboard with export option
            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📄 Export CSV", callback_data="wallet:export_csv"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh Stats", callback_data="wallet:stats"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Wallet", callback_data="menu:wallet"
                        )
                    ],
                ]
            )

            await callback.message.edit_text(stats_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error showing wallet stats: {e}")
            await callback.answer("❌ Error loading statistics", show_alert=True)

    async def cb_export_csv(self, callback: CallbackQuery) -> None:
        """Export transaction history as CSV"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Create work coroutine for CSV export generation
            async def export_work():
                # Get user data
                user_obj = await self.user_service.get_user_by_telegram_id(user.id)
                if not user_obj:
                    return None

                # Generate CSV export
                csv_content = await self.export_service.export_transactions_csv(
                    str(user_obj.id)
                )
                return csv_content
            
            # Run loading stages concurrently with CSV generation
            csv_content = await LoadingStages.run_concurrent_loading(
                callback,
                WALLET_STAGES,
                export_work(),
                operation_name="CSV Export Generation"
            )
            
            if not csv_content:
                await callback.answer(ERROR_USER_NOT_FOUND, show_alert=True)
                return

            # For demo purposes, show a preview instead of sending file
            lines = csv_content.split("\n")
            preview = "\n".join(lines[:6])  # Show first 5 rows + header

            preview_text = f"""
📄 <b>Transaction Export Preview</b>

<pre>{preview}</pre>

<i>In a production environment, this would be sent as a downloadable CSV file.</i>

{DEMO_WATERMARK}
"""

            await callback.message.edit_text(
                preview_text, reply_markup=back_keyboard("wallet:stats")
            )
            await callback.answer("📄 Export generated!")

        except Exception as e:
            logger.error(f"Error exporting CSV: {e}")
            await callback.answer("❌ Error generating export", show_alert=True)




def get_wallet_router() -> Router:
    """Create and return wallet router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = WalletHandlers()

    # Command handlers
    router.message.register(handlers.cmd_balance, Command("balance"))

    # Callback handlers
    router.callback_query.register(handlers.cb_wallet_menu, F.data == "menu:wallet")
    router.callback_query.register(
        handlers.cb_add_funds,
        F.data.in_(["wallet:add10", "wallet:add25", "wallet:add50"]),
    )
    router.callback_query.register(
        handlers.cb_add_custom, F.data == "wallet:add_custom"
    )
    router.callback_query.register(handlers.cb_wallet_stats, F.data == "wallet:stats")
    router.callback_query.register(
        handlers.cb_export_csv, F.data == "wallet:export_csv"
    )
    

    # State handlers
    router.message.register(
        handlers.process_custom_amount, AddFundsStates.waiting_for_amount
    )

    logger.debug("Wallet handlers registered")
    return router
