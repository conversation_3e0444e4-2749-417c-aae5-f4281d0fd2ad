"""
Compatibility Layer for API v1

Provides backward compatibility for existing API v1 code while using
the new shared API system under the hood.
"""

import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from .config.client_factory import api_client_factory
from .config.api_config import APIConfiguration
from .examples.api_v1_config import create_api_v1_configuration
from .core.exceptions import SharedAPIException, HTTPClientError
from .core.interfaces import APIClientProtocol

from utils.central_logger import get_logger

logger = get_logger()


class APIv1CompatibilityClient:
    """
    Compatibility wrapper that provides the same interface as the original
    API v1 implementation while using the shared API system internally.
    
    This allows existing code to work without changes while benefiting
    from the improved shared API infrastructure.
    """
    
    def __init__(
        self,
        base_url: str = "https://ronaldo-club.to/api",
        login_token: Optional[str] = None,
        session_cookies: Optional[Dict[str, str]] = None,
        api_config_service=None,
        settings=None
    ):
        """
        Initialize compatibility client with same parameters as original
        
        Args:
            base_url: Base URL for the API
            login_token: Bearer token for authentication
            session_cookies: Session cookies for authentication
            api_config_service: Legacy parameter for compatibility
            settings: Legacy parameter for compatibility
        """
        self.base_url = base_url
        self.login_token = login_token
        self.session_cookies = session_cookies or {}
        self.api_config_service = api_config_service
        self.settings = settings
        
        # Create shared API configuration
        self._config = create_api_v1_configuration(
            base_url=base_url,
            login_token=login_token,
            session_cookies=session_cookies
        )
        
        # Create shared API client
        self._client: Optional[APIClientProtocol] = None
        self._closed = False
    
    async def _ensure_client(self) -> APIClientProtocol:
        """Ensure the shared API client is available"""
        if self._client is None or self._closed:
            self._client = api_client_factory.create_client(self._config)
            self._closed = False
        return self._client
    
    async def close(self) -> None:
        """Close the client and clean up resources"""
        if self._client and not self._closed:
            await self._client.close()
            self._closed = True
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    # Original API v1 methods - maintaining exact same interface
    
    async def list_items(
        self,
        params: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        List items with filtering (compatible with original implementation)
        
        Args:
            params: Query parameters for filtering
            user_id: User ID for logging context
            
        Returns:
            API response with items data
        """
        try:
            client = await self._ensure_client()
            
            # Convert params to the format expected by the API
            request_data = params or {}
            
            # Set defaults that match original implementation
            defaults = {
                "page": 1,
                "limit": 10,
                "base": "",
                "bank": "",
                "bin": "",
                "country": "",
                "state": "",
                "city": "",
                "brand": "",
                "type": "",
                "zip": "",
                "priceFrom": 0,
                "priceTo": 500,
                "zipCheck": False,
                "address": False,
                "phone": False,
                "email": False,
                "withoutcvv": False,
                "refundable": False,
                "expirethismonth": False,
                "dob": False,
                "ssn": False,
                "mmn": False,
                "ip": False,
                "dl": False,
                "ua": False,
                "discount": False,
            }
            
            # Merge defaults with provided params
            for key, default_value in defaults.items():
                if key not in request_data:
                    request_data[key] = default_value
            
            response = await client.post("list_items", data=request_data)
            
            # Wrap response in the format expected by original code
            return {
                "success": True,
                "data": response.get("data", []),
                "totalCount": response.get("totalCount", 0),
                "limit": response.get("limit", request_data["limit"]),
                "page": request_data["page"],
            }
            
        except Exception as e:
            logger.error(f"Error in list_items: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": [],
                "totalCount": 0,
            }
    
    async def add_to_cart(
        self,
        item_id: Union[str, int],
        quantity: int = 1,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Add item to cart (compatible with original implementation)
        
        Args:
            item_id: ID of item to add
            quantity: Quantity to add
            user_id: User ID for logging context
            
        Returns:
            API response
        """
        try:
            client = await self._ensure_client()
            
            response = await client.post("cart_add", data={
                "item_id": str(item_id),
                "quantity": quantity
            })
            
            return {
                "success": True,
                "data": response,
                "message": "Item added to cart successfully"
            }
            
        except Exception as e:
            logger.error(f"Error in add_to_cart: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to add item to cart"
            }
    
    async def view_cart(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        View cart contents (compatible with original implementation)
        
        Args:
            user_id: User ID for logging context
            
        Returns:
            API response with cart data
        """
        try:
            client = await self._ensure_client()
            response = await client.get("cart_view")
            
            return {
                "success": True,
                "data": response,
            }
            
        except Exception as e:
            logger.error(f"Error in view_cart: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {}
            }
    
    async def delete_from_cart(
        self,
        item_id: Union[str, int],
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Remove item from cart (compatible with original implementation)
        
        Args:
            item_id: ID of item to remove
            user_id: User ID for logging context
            
        Returns:
            API response
        """
        try:
            client = await self._ensure_client()
            
            response = await client.delete("cart_remove", data={
                "item_id": str(item_id)
            })
            
            return {
                "success": True,
                "data": response,
                "message": "Item removed from cart successfully"
            }
            
        except Exception as e:
            logger.error(f"Error in delete_from_cart: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to remove item from cart"
            }
    
    async def get_user_info(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get user information (compatible with original implementation)
        
        Args:
            user_id: User ID for logging context
            
        Returns:
            API response with user data
        """
        try:
            client = await self._ensure_client()
            response = await client.get("user_info")
            
            return {
                "success": True,
                "data": response,
            }
            
        except Exception as e:
            logger.error(f"Error in get_user_info: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {}
            }
    
    async def checkout(
        self,
        checkout_data: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process checkout (compatible with original implementation)
        
        Args:
            checkout_data: Checkout information
            user_id: User ID for logging context
            
        Returns:
            API response
        """
        try:
            client = await self._ensure_client()
            response = await client.post("checkout", data=checkout_data)
            
            return {
                "success": True,
                "data": response,
                "message": "Checkout completed successfully"
            }
            
        except Exception as e:
            logger.error(f"Error in checkout: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Checkout failed"
            }
    
    async def check_order(
        self,
        order_id: Union[str, int],
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Check order status (compatible with original implementation)
        
        Args:
            order_id: ID of order to check
            user_id: User ID for logging context
            
        Returns:
            API response with order data
        """
        try:
            client = await self._ensure_client()
            response = await client.post("check_order", data={
                "id": int(order_id)
            })
            
            return {
                "success": True,
                "data": response,
            }
            
        except Exception as e:
            logger.error(f"Error in check_order: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {}
            }


# Factory function to create compatibility client (matches original pattern)
def get_external_api_service(
    base_url: str = "https://ronaldo-club.to/api",
    login_token: Optional[str] = None,
    session_cookies: Optional[Dict[str, str]] = None,
    api_config_service=None,
    settings=None
) -> APIv1CompatibilityClient:
    """
    Factory function to create API v1 compatibility client
    
    This function provides the same interface as the original
    get_external_api_service function for backward compatibility.
    """
    return APIv1CompatibilityClient(
        base_url=base_url,
        login_token=login_token,
        session_cookies=session_cookies,
        api_config_service=api_config_service,
        settings=settings
    )
