"""
Order Dump: Fetch and save order information from authenticated session.

Usage:
    python order_dump.py                    # Fetch orders list + ALL individual orders (saved in orders_data folder)
    python order_dump.py --order-id ABC123  # Fetch specific order only (saved in orders_data folder)
"""

import json
import os
from urllib.parse import urljoin

import requests

from login import logger, REFERER, log_request, log_response
from session_manager import (
    get_authenticated_session,
    save_session_cookies,
    is_unauthenticated,
)

# Import HTML->JSON converter
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _orders_url() -> str:
    """Build the absolute URL for the orders page."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "orders")


def _is_valid_order_id(order_id: str) -> bool:
    """Check if string looks like a legitimate order ID."""
    if not order_id or not order_id.isalnum():
        return False

    # Length and content checks
    if not (6 <= len(order_id) <= 12):
        return False

    # Must have both letters and numbers
    has_letter = any(c.isalpha() for c in order_id)
    has_digit = any(c.isdigit() for c in order_id)
    if not (has_letter and has_digit):
        return False

    # Exclude common UI elements
    excluded = {"unmask-all", "view-all", "show-all", "load-more", "export"}
    if order_id.lower() in excluded:
        return False

    return True


def get_orders(session: requests.Session) -> requests.Response:
    """Fetch the orders page using the authenticated session."""
    url = _orders_url()
    logger.info("Fetching orders page: %s", url)
    r = session.get(url, allow_redirects=True, timeout=60)
    log_request(r)
    log_response(r)
    return r


def get_specific_order(session: requests.Session, order_id: str) -> requests.Response:
    """Fetch a specific order by ID using the authenticated session."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    url = urljoin(base, f"orders/{order_id}")
    logger.info("Fetching specific order: %s", url)
    r = session.get(url, allow_redirects=True, timeout=60)
    log_request(r)
    log_response(r)
    return r


def save_orders_result(
    resp: requests.Response, out_path: str = "orders_response.json"
) -> str:
    """Save orders list response to JSON file."""
    data = {"orders": _response_to_jsonable(resp)}

    # Print JSON response to console
    print("\n" + "=" * 60)
    print("ORDERS LIST RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")

    # Ensure directory exists
    os.makedirs(os.path.dirname(out_path), exist_ok=True)

    # Save to file
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return out_path


def save_specific_order_result(
    resp: requests.Response, order_id: str, out_path: str | None = None
) -> str:
    """Save specific order response to JSON file."""
    if out_path is None:
        out_path = f"orders_data/order_{order_id}_response.json"

    data = {"order": _response_to_jsonable(resp), "order_id": order_id}

    # Print JSON response to console
    print("\n" + "=" * 60)
    print(f"ORDER {order_id} RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")

    # Ensure directory exists
    os.makedirs(os.path.dirname(out_path), exist_ok=True)

    # Save to file
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return out_path


def extract_order_ids_from_response(resp: requests.Response) -> list[str]:
    """Extract order IDs from the orders list page."""
    order_ids = []

    try:
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(resp.text or "", "html.parser")

        # Find links with /orders/{id} pattern
        for link in soup.find_all("a", href=True):
            href = link["href"]
            if "/orders/" in href:
                parts = href.split("/")
                if "orders" in parts:
                    idx = parts.index("orders")
                    if idx + 1 < len(parts):
                        order_id = (
                            parts[idx + 1].split("?")[0].split("#")[0]
                        )  # Clean ID
                        if _is_valid_order_id(order_id) and order_id not in order_ids:
                            order_ids.append(order_id)

    except Exception as e:
        logger.warning("Failed to extract order IDs: %s", e)

    return order_ids


def main() -> None:
    """Main function to view orders."""
    logger.info("=== Order View Start ===")

    session = get_authenticated_session(logger)

    # Fetch orders list
    resp = get_orders(session)
    if is_unauthenticated(resp):
        logger.error("Authentication failed")
        return

    # Create orders data folder
    orders_folder = "orders_data"
    os.makedirs(orders_folder, exist_ok=True)

    # Save orders list
    orders_list_path = os.path.join(orders_folder, "orders_response.json")
    save_orders_result(resp, orders_list_path)
    logger.info("Saved orders list to %s", orders_list_path)

    # Extract and fetch individual orders
    order_ids = extract_order_ids_from_response(resp)
    if order_ids:
        logger.info("Found %d order IDs: %s", len(order_ids), order_ids[:5])

        # Fetch ALL orders (removed the limit of 3)
        for i, order_id in enumerate(order_ids):
            logger.info("Fetching order %d/%d: %s", i + 1, len(order_ids), order_id)
            try:
                order_resp = get_specific_order(session, order_id)
                if order_resp.status_code == 200:
                    save_specific_order_result(order_resp, order_id)
                    logger.info("Saved order %s", order_id)
            except Exception as e:
                logger.warning("Failed to fetch order %s: %s", order_id, e)
    else:
        logger.info("No order IDs found")

    save_session_cookies(session, logger=logger)
    logger.info("=== Order View Complete ===")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="View and fetch order details")
    parser.add_argument("--order-id", type=str, help="Fetch specific order ID only")

    args = parser.parse_args()

    if args.order_id:
        # Fetch specific order
        logger.info("=== Fetching Order: %s ===", args.order_id)
        session = get_authenticated_session(logger)
        order_resp = get_specific_order(session, args.order_id)

        if order_resp.status_code == 200:
            # Create orders data folder for single order fetch too
            os.makedirs("orders_data", exist_ok=True)
            save_specific_order_result(order_resp, args.order_id)
            logger.info("Saved order %s", args.order_id)
        else:
            logger.error(
                "Failed to fetch order %s - HTTP %d",
                args.order_id,
                order_resp.status_code,
            )

        save_session_cookies(session, logger=logger)
    else:
        # Run main function (always fetches 3 orders)
        main()
