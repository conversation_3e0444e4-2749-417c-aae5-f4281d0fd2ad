#!/usr/bin/env python3
"""
Payment Module Setup Script

This script helps set up the payment module for your project.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_env_file():
    """Create .env file from template."""
    env_file = Path(".env")
    template_file = Path("config_template.env")
    
    if env_file.exists():
        print("⚠️  .env file already exists, skipping creation")
        return True
    
    if template_file.exists():
        shutil.copy(template_file, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your configuration")
        return True
    else:
        print("⚠️  config_template.env not found, creating basic .env file")
        
        env_content = """# Payment Module Configuration
OXA_PAY_API_KEY=your_api_key_here
OXA_PAY_CALLBACK_URL=https://yourdomain.com/callback
DEVELOPMENT_MODE=true
TESTING_MODE=false
DEBUG_MODE=false
"""
        
        with open(env_file, "w") as f:
            f.write(env_content)
        
        print("✅ Created basic .env file")
        print("📝 Please edit .env file with your configuration")
        return True


def create_directories():
    """Create necessary directories."""
    directories = ["logs", "temp", "backups"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def run_tests():
    """Run the test suite."""
    print("\n🧪 Running tests...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ])
        print("✅ All tests passed!")
        return True
    except subprocess.CalledProcessError:
        print("⚠️  Some tests failed, but this is normal for initial setup")
        return True
    except FileNotFoundError:
        print("⚠️  pytest not found, skipping tests")
        return True


def show_next_steps():
    """Show next steps for the user."""
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your OXA Pay API key")
    print("2. Set up your callback URL (if using webhooks)")
    print("3. Review the examples in examples/ directory")
    print("4. Check the documentation in docs/ directory")
    print("5. Test the integration with your bot")
    
    print("\n🔧 Configuration:")
    print("- Set OXA_PAY_API_KEY in .env file")
    print("- Set OXA_PAY_CALLBACK_URL for webhook callbacks")
    print("- Set DEVELOPMENT_MODE=true for testing")
    print("- Set DEVELOPMENT_MODE=false for production")
    
    print("\n📚 Examples:")
    print("- examples/basic_bot.py - Basic bot integration")
    print("- examples/callback_server.py - Callback server setup")
    print("- examples/custom_payment.py - Custom payment processing")
    
    print("\n🆘 Support:")
    print("- Check docs/README.md for detailed documentation")
    print("- Review examples for integration patterns")
    print("- Test in sandbox mode first")


def main():
    """Main setup function."""
    print("🚀 Payment Module Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create .env file
    create_env_file()
    
    # Create directories
    create_directories()
    
    # Run tests
    run_tests()
    
    # Show next steps
    show_next_steps()


if __name__ == "__main__":
    main()

