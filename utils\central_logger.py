"""
Centralized logging system for Demo Wallet Bot v3

This module provides a single, unified logger that replaces all other logging 
configurations throughout the codebase. All logging will go through this 
centralized system.

Features:
- Single logger instance used throughout the application
- Configurable console and file output
- Structured formatting with timestamps
- Security filtering for sensitive data
- Color support for console output
- Rotating file handler for log management
"""

from __future__ import annotations

import logging
import logging.handlers
import re
import sys
import inspect
from pathlib import Path
from typing import Optional

try:
    from colorama import init as colorama_init, Fore, Style
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False
    Fore = Style = type('MockColorama', (), {
        'RED': '', 'GREEN': '', 'YELLOW': '', 'BLUE': '', 'MAGENTA': '', 'CYAN': '', 'WHITE': '',
        'BRIGHT': '', 'DIM': '', 'RESET_ALL': ''
    })()


class SecurityFilter(logging.Filter):
    """Filter to remove sensitive information from logs"""

    SENSITIVE_PATTERNS = [
        (re.compile(r"\b\d{10}:\w{35}\b"), "[BOT_TOKEN_REDACTED]"),
        (re.compile(r"\b\d{13,19}\b"), "[PAN_REDACTED]"),
        (re.compile(r"\b\d{3}-\d{2}-\d{4}\b"), "[SSN_REDACTED]"),
        (re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"), "[EMAIL_REDACTED]"),
        (re.compile(r'\bpassword["\']?\s*[:=]\s*["\']?[^\s"\']+', re.IGNORECASE), "password=[REDACTED]"),
        (re.compile(r'\btoken["\']?\s*[:=]\s*["\']?[^\s"\']+', re.IGNORECASE), "token=[REDACTED]"),
        (re.compile(r'\bapi_key["\']?\s*[:=]\s*["\']?[^\s"\']+', re.IGNORECASE), "api_key=[REDACTED]"),
    ]

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter sensitive information from log records"""
        if hasattr(record, "msg") and record.msg:
            message = str(record.msg)
            for pattern, replacement in self.SENSITIVE_PATTERNS:
                message = pattern.sub(replacement, message)
            record.msg = message

        if hasattr(record, "args") and record.args:
            filtered_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    for pattern, replacement in self.SENSITIVE_PATTERNS:
                        arg = pattern.sub(replacement, arg)
                filtered_args.append(arg)
            record.args = tuple(filtered_args)

        return True


class CentralFormatter(logging.Formatter):
    """Centralized formatter with color support and consistent structure"""

    # Color mapping for different log levels
    LEVEL_COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.MAGENTA,
    }

    def __init__(self, colored: bool = True, include_module: bool = True):
        super().__init__()
        self.colored = colored and COLORAMA_AVAILABLE
        self.include_module = include_module
        
        if self.colored and COLORAMA_AVAILABLE:
            try:
                colorama_init(autoreset=True)
            except:
                self.colored = False

    def _supports_color(self) -> bool:
        """Check if terminal supports color output"""
        if not self.colored:
            return False
        try:
            return hasattr(sys.stdout, 'isatty') and sys.stdout.isatty()
        except:
            return False

    def format(self, record: logging.LogRecord) -> str:
        """Format log record with consistent structure"""
        # Create timestamp
        timestamp = self.formatTime(record, datefmt='%Y-%m-%d %H:%M:%S')
        
        # Get level name and apply color if supported
        level = record.levelname
        if self._supports_color():
            color = self.LEVEL_COLORS.get(level, '')
            level = f"{color}{level}{Style.RESET_ALL}"
        
        # Get module name for context - enhanced to show actual caller
        module_name = record.name
        if module_name == 'bot_central':
            # Try to get actual caller from stack
            import inspect
            try:
                frame = inspect.currentframe()
                # Go up the stack to find the actual caller (skip logging internals)
                for i in range(10):  # Limit search depth
                    frame = frame.f_back
                    if frame is None:
                        break
                    filename = frame.f_globals.get('__name__', '')
                    if filename and filename not in ['logging', 'utils.central_logger', '__main__']:
                        # Convert module path to shorter name
                        if '.' in filename:
                            module_name = filename.split('.')[-1]
                        else:
                            module_name = filename
                        break
                    elif filename == '__main__':
                        module_name = 'main'
                        break
            except:
                pass  # Fallback to original name
        
        if self.include_module:
            # Simplify long module names
            if len(module_name) > 30:
                parts = module_name.split('.')
                if len(parts) > 2:
                    module_name = f"{parts[0]}...{parts[-1]}"
        
        # Get the actual message
        message = record.getMessage()
        
        # Build the log line
        if self.include_module:
            log_line = f"{timestamp} [{level}] {module_name}: {message}"
        else:
            log_line = f"{timestamp} [{level}]: {message}"
        
        # Add exception info if present
        if record.exc_info:
            log_line += f"\n{self.formatException(record.exc_info)}"
        
        return log_line


class CentralLogger:
    """Centralized logger instance for the entire application"""
    
    _instance: Optional['CentralLogger'] = None
    _logger: Optional[logging.Logger] = None
    
    def __new__(cls) -> 'CentralLogger':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._logger is None:
            self._consolidated_handler: Optional[logging.Handler] = None
            self._setup_logger()
    
    def _setup_logger(self):
        """Setup the central logger with default configuration"""
        self._logger = logging.getLogger("bot_central")
        self._logger.setLevel(logging.DEBUG)
        
        # Clear any existing handlers
        self._logger.handlers.clear()
        
        # Prevent propagation to avoid duplicate logs
        self._logger.propagate = False
        
        # Setup default console handler
        self._setup_console_handler()
        
        # Setup security filter
        security_filter = SecurityFilter()
        for handler in self._logger.handlers:
            handler.addFilter(security_filter)
    
    def _setup_console_handler(self):
        """Setup console handler with colored output"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        formatter = CentralFormatter(colored=True, include_module=True)
        console_handler.setFormatter(formatter)
        
        self._logger.addHandler(console_handler)
    
    def setup_file_logging(
        self, 
        log_file: str,
        level: str = "DEBUG",
        max_file_size: int = 50 * 1024 * 1024,  # 50MB
        backup_count: int = 10
    ):
        """Setup file logging with rotation"""
        # Create log directory if it doesn't exist
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Remove any existing file handlers targeting the same file to prevent duplicates
        target_path = log_path.resolve()
        for handler in list(self._logger.handlers):
            if isinstance(handler, logging.handlers.RotatingFileHandler):
                try:
                    existing_path = Path(getattr(handler, "baseFilename", "")).resolve()
                except Exception:
                    existing_path = Path(getattr(handler, "baseFilename", ""))
                if existing_path == target_path:
                    self._logger.removeHandler(handler)
                    try:
                        handler.close()
                    except Exception:
                        pass

        # Create rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # Set level
        numeric_level = getattr(logging, level.upper(), logging.DEBUG)
        file_handler.setLevel(numeric_level)
        
        # Use non-colored formatter for files
        formatter = CentralFormatter(colored=False, include_module=True)
        file_handler.setFormatter(formatter)
        
        # Add security filter
        file_handler.addFilter(SecurityFilter())
        
        # Add to logger
        self._logger.addHandler(file_handler)
    
    def setup_consolidated_logging(
        self,
        log_file: str,
        level: str = "DEBUG",
        max_file_size: int = 50 * 1024 * 1024,
        backup_count: int = 10
    ) -> None:
        """Setup or refresh consolidated log handler capturing all log streams"""
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        target_path = log_path.resolve()

        numeric_level = getattr(logging, level.upper(), logging.DEBUG)

        existing_path = None
        if self._consolidated_handler:
            try:
                existing_path = Path(
                    getattr(self._consolidated_handler, "baseFilename", "")
                ).resolve()
            except Exception:
                existing_path = Path(
                    getattr(self._consolidated_handler, "baseFilename", "")
                )

        if self._consolidated_handler and existing_path == target_path:
            self._consolidated_handler.setLevel(numeric_level)
            return

        if self._consolidated_handler:
            self._logger.removeHandler(self._consolidated_handler)
            try:
                self._consolidated_handler.close()
            except Exception:
                pass
            self._consolidated_handler = None

        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding="utf-8"
        )
        handler.setLevel(numeric_level)
        handler.setFormatter(CentralFormatter(colored=False, include_module=True))
        handler.addFilter(SecurityFilter())
        handler.set_name("consolidated-log")

        self._logger.addHandler(handler)
        self._consolidated_handler = handler
    
    def get_consolidated_handler(self) -> Optional[logging.Handler]:
        """Return the consolidated log handler if configured"""
        return self._consolidated_handler
    
    def set_level(self, level: str):
        """Set the logging level"""
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        self._logger.setLevel(numeric_level)
        
        # Also update console handler level
        for handler in self._logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                handler.setLevel(numeric_level)
    
    def debug(self, msg, *args, **kwargs):
        """Log debug message"""
        self._logger.debug(msg, *args, **kwargs)
    
    def info(self, msg, *args, **kwargs):
        """Log info message"""
        self._logger.info(msg, *args, **kwargs)
    
    def warning(self, msg, *args, **kwargs):
        """Log warning message"""
        self._logger.warning(msg, *args, **kwargs)
    
    def error(self, msg, *args, **kwargs):
        """Log error message"""
        self._logger.error(msg, *args, **kwargs)
    
    def critical(self, msg, *args, **kwargs):
        """Log critical message"""
        self._logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg, *args, **kwargs):
        """Log exception with traceback"""
        self._logger.exception(msg, *args, **kwargs)
    
    def isEnabledFor(self, level):
        """Check if this logger will process a logging event at level 'level'"""
        return self._logger.isEnabledFor(level)
    
    def getEffectiveLevel(self):
        """Get the effective minimum level for this logger"""
        return self._logger.getEffectiveLevel()
    
    def setLevel(self, level):
        """Set the effective level for this logger"""
        self._logger.setLevel(level)
    
    def hasHandlers(self):
        """Check if this logger has any handlers configured"""
        return self._logger.hasHandlers()
    
    def addHandler(self, handler):
        """Add the specified handler to this logger"""
        self._logger.addHandler(handler)
    
    def removeHandler(self, handler):
        """Remove the specified handler from this logger"""
        self._logger.removeHandler(handler)
    
    @property
    def level(self):
        """Get the current logging level"""
        return self._logger.level
    
    @property
    def handlers(self):
        """Get the list of handlers for this logger"""
        return self._logger.handlers


# Global logger instance
_central_logger = CentralLogger()


def get_logger() -> CentralLogger:
    """Get the central logger instance"""
    return _central_logger


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: int = 50 * 1024 * 1024,
    backup_count: int = 10,
    consolidated_log_file: Optional[str] = None,
    consolidated_max_file_size: Optional[int] = None,
    consolidated_backup_count: Optional[int] = None,
    **kwargs  # Accept any other kwargs for compatibility
) -> None:
    """
    Setup centralized logging configuration
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path for file output
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
    """
    logger = get_logger()
    
    # Set logging level
    logger.set_level(level)
    
    # Setup file logging if specified
    if log_file:
        logger.setup_file_logging(
            log_file=log_file,
            level=level,
            max_file_size=max_file_size,
            backup_count=backup_count
        )
    
    if consolidated_log_file:
        logger.setup_consolidated_logging(
            log_file=consolidated_log_file,
            level=level,
            max_file_size=consolidated_max_file_size or max_file_size,
            backup_count=consolidated_backup_count or backup_count,
        )
    
    # Suppress noisy third-party loggers
    logging.getLogger("aiogram").setLevel(logging.WARNING)
    logging.getLogger("motor").setLevel(logging.WARNING)
    logging.getLogger("pymongo").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    
    logger.info(f"Central logging initialized - Level: {level}, File: {log_file or 'Console only'}")

# Convenience functions for backward compatibility
def debug(msg, *args, **kwargs):
    """Log debug message using central logger"""
    get_logger().debug(msg, *args, **kwargs)


def info(msg, *args, **kwargs):
    """Log info message using central logger"""
    get_logger().info(msg, *args, **kwargs)


def warning(msg, *args, **kwargs):
    """Log warning message using central logger"""
    get_logger().warning(msg, *args, **kwargs)


def error(msg, *args, **kwargs):
    """Log error message using central logger"""
    get_logger().error(msg, *args, **kwargs)


def critical(msg, *args, **kwargs):
    """Log critical message using central logger"""
    get_logger().critical(msg, *args, **kwargs)


def exception(msg, *args, **kwargs):
    """Log exception with traceback using central logger"""
    get_logger().exception(msg, *args, **kwargs)
