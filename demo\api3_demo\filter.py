"""
Ultra-fast filter: extracts ONLY filters data - nothing else.
Excludes forms, metadata, timestamps, and all other data.
Only returns the filters array with select options.
"""

import json
import os
from urllib.parse import urljoin

import requests

from login import logger, REFERER
from session_manager import get_authenticated_session, save_session_cookies

# Import standard response converter for consistency
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _filter_url():
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "shop")


def _filter_params():
    return {
        "base_id[]": "",
        "continent[]": "",
        "country[]": "",
        "scheme[]": "",
        "type[]": "",
        "level[]": "",
        "ethnicity": "",
        "postal_code": "",
        "searched_bank": "",
        "selected_bank": "",
        "region": "",
        "city": "",
        "bins": "",
        "with_billing": "",
        "with_phone": "",
        "with_dob": "",
    }


def extract_filter_options(form_data):
    """Convert form select options to API-style filter format. Excludes base_id field."""
    filter_options = []

    for form in form_data.get("GET", []):
        for select in form.get("selects", []):
            select_name = select.get("name", "")

            # Skip base_id field (first array index)
            if select_name == "base_id[]":
                continue

            options = select.get("options", [])

            # Convert options to API format
            api_options = []
            for opt in options:
                if opt.get("value") and opt.get("label"):
                    # Count format like "US (194472)" - extract count if present
                    label = opt.get("label", "")
                    value = opt.get("value", "")

                    api_options.append(
                        {
                            "label": label,
                            "value": value,
                            "selected": bool(opt.get("selected", False)),
                        }
                    )

            if api_options:
                filter_options.append({"name": select_name, "options": api_options})

    return filter_options


def create_filters_only_response(filter_options):
    """Create response with only filters data - nothing else."""
    return filter_options


def extract_forms_fast(html):
    """Ultra-fast form extraction - GET method forms only."""
    try:
        # Import BeautifulSoup for form parsing
        from bs4 import BeautifulSoup
        from urllib.parse import urljoin

        # Parse HTML and extract forms manually since we removed _extract_forms
        soup = BeautifulSoup(html, "html.parser")
        forms = []

        for form in soup.find_all("form"):
            form_data = {
                "method": form.get("method", "GET").upper(),
                "action": urljoin(REFERER, form.get("action", "")),
                "inputs": [],
                "selects": [],
            }

            # Extract input fields
            for inp in form.find_all("input"):
                input_data = {
                    "name": inp.get("name", ""),
                    "type": inp.get("type", "text"),
                    "value": inp.get("value", ""),
                    "checked": inp.has_attr("checked"),
                }
                form_data["inputs"].append(input_data)

            # Extract select fields
            for select in form.find_all("select"):
                select_data = {
                    "name": select.get("name", ""),
                    "options": [],
                }

                for option in select.find_all("option"):
                    option_data = {
                        "value": option.get("value", ""),
                        "label": option.get_text(strip=True),
                        "selected": option.has_attr("selected"),
                    }
                    select_data["options"].append(option_data)

                form_data["selects"].append(select_data)

            forms.append(form_data)

        # Quick filter: exclude logout, only GET method forms
        get_forms = []

        for f in forms:
            action = f.get("action", "").lower()
            if "/logout" in action:
                continue

            method = (f.get("method") or "GET").upper()

            # Only process GET method forms
            if method == "GET":
                # Build minimal form data
                form_data = {
                    "action": f.get("action", ""),
                    "method": method,
                    "inputs": [
                        {
                            "name": i.get("name", ""),
                            "type": i.get("type", "text"),
                            "value": i.get("value", ""),
                            "checked": bool(i.get("checked", False)),
                        }
                        for i in f.get("inputs", [])
                    ],
                    "selects": [
                        {
                            "name": s.get("name", ""),
                            "options": [
                                {
                                    "value": o.get("value"),
                                    "label": o.get("label"),
                                    "selected": bool(o.get("selected", False)),
                                }
                                for o in s.get("options", [])
                            ],
                        }
                        for s in f.get("selects", [])
                    ],
                }
                get_forms.append(form_data)

        return {"GET": get_forms}

    except Exception as e:
        logger.error(f"Form extraction error: {e}")
        return {"GET": []}


def fetch_shop_fast(session):
    """Fast shop page fetch with minimal logging."""
    url = _filter_url()
    params = _filter_params()
    logger.info(f"Fetching shop: {url}")

    try:
        r = session.get(url, params=params, allow_redirects=True, timeout=60)
        logger.info(f"Response: {r.status_code} ({len(r.text)} chars)")
        return r
    except Exception as e:
        logger.error(f"Fetch error: {e}")
        raise


def save_filters_only(filter_options, out_path="filter_response.json"):
    """Save only filters data - nothing else."""
    try:
        # Print JSON response to console
        print("\n" + "=" * 60)
        print("FILTER RESPONSE (JSON)")
        print("=" * 60)
        print(json.dumps(filter_options, ensure_ascii=False, indent=2))
        print("=" * 60 + "\n")
        
        # Save to file
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(filter_options, f, ensure_ascii=False, indent=2)

        filters_count = len(filter_options)
        logger.info(f"Saved: {filters_count} filters to {out_path}")
        return out_path

    except Exception as e:
        logger.error(f"Save error: {e}")
        return None


def main():
    """Extract and save only filters data - nothing else."""
    logger.info("=== Filters Only Start ===")

    # Get session (reuse existing)
    session = get_authenticated_session(logger)

    # Fetch shop page
    resp = fetch_shop_fast(session)

    # Extract forms (minimal processing)
    forms_data = extract_forms_fast(resp.text or "")

    # Extract filter options from forms
    filter_options = extract_filter_options(forms_data)

    # Save only filters data
    output_file = save_filters_only(filter_options)

    # Save session
    save_session_cookies(session, logger=logger)

    logger.info("=== Filters Only Complete ===")
    return filter_options


if __name__ == "__main__":
    main()
