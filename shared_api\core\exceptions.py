"""
Shared API Exceptions

Defines the exception hierarchy for the shared API system,
providing consistent error handling across all API clients.
"""

from typing import Optional, Dict, Any


class SharedAPIException(Exception):
    """Base exception for all shared API errors"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "SHARED_API_ERROR"
        self.details = details or {}


class ConfigurationError(SharedAPIException):
    """Raised when there are issues with API configuration"""
    
    def __init__(
        self, 
        message: str, 
        config_field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "CONFIGURATION_ERROR", details)
        self.config_field = config_field


class AuthenticationError(SharedAPIException):
    """Raised when authentication fails"""
    
    def __init__(
        self, 
        message: str, 
        auth_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "AUTHENTICATION_ERROR", details)
        self.auth_type = auth_type


class HTTPClientError(SharedAPIException):
    """Raised when HTTP requests fail"""
    
    def __init__(
        self, 
        message: str, 
        status_code: Optional[int] = None,
        response_text: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "HTTP_CLIENT_ERROR", details)
        self.status_code = status_code
        self.response_text = response_text


class ValidationError(SharedAPIException):
    """Raised when data validation fails"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field_name = field_name
        self.field_value = field_value


class RateLimitError(SharedAPIException):
    """Raised when rate limits are exceeded"""
    
    def __init__(
        self, 
        message: str, 
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "RATE_LIMIT_ERROR", details)
        self.retry_after = retry_after


class TimeoutError(SharedAPIException):
    """Raised when requests timeout"""
    
    def __init__(
        self, 
        message: str, 
        timeout_duration: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "TIMEOUT_ERROR", details)
        self.timeout_duration = timeout_duration


class NetworkError(SharedAPIException):
    """Raised when network connectivity issues occur"""
    
    def __init__(
        self, 
        message: str, 
        original_error: Optional[Exception] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "NETWORK_ERROR", details)
        self.original_error = original_error
