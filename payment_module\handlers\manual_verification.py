"""
Manual Payment Verification Handlers

This module provides comprehensive manual payment verification handlers
with detailed status checking, user notifications, and admin controls.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Union
from aiogram import Bot, Router, F
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from ..core.oxa_verify import check_oxapay_payment
from ..database.payment_operations import get_payment_by_track_id, update_payment_status
from ..database.user_operations import get_user_balance, update_user_balance, add_transaction
from ..utils.payment_amount_handler import (
    check_payment_amounts, handle_underpayment, handle_overpayment,
    handle_payment_completion, create_underpayment_message, create_overpayment_message
)
from ..utils.bonus_calculator import calculate_deposit_bonus
from ..utils.vip_payment_hooks import trigger_vip_check_on_verification
from ..utils.auto_verification import get_verification_manager, VerificationStatus
from ..utils.template_helpers import format_text
from ..keyboards.deposit_kb import (
    payment_verification_keyboard, payment_success_keyboard, 
    payment_processing_keyboard, underpayment_keyboard, overpayment_keyboard
)

logger = logging.getLogger(__name__)

# Create router for manual verification handlers
router = Router()


class ManualVerificationStates(StatesGroup):
    """States for manual payment verification."""
    waiting_for_track_id = State()
    verifying_payment = State()
    handling_underpayment = State()
    handling_overpayment = State()


async def cmd_verify_payment(message: Message, state: FSMContext):
    """
    Handle /verify command for manual payment verification.
    
    Args:
        message: Telegram message
        state: FSM context
    """
    user_id = message.from_user.id
    
    # Check if user has admin privileges (optional)
    # admin_users = [123456789, 987654321]  # Add your admin user IDs
    # if user_id not in admin_users:
    #     await message.answer("❌ You don't have permission to verify payments.")
    #     return
    
    await state.set_state(ManualVerificationStates.waiting_for_track_id)
    
    text = (
        "🔍 <b>Manual Payment Verification</b>\n\n"
        "Please enter the payment track ID you want to verify:\n\n"
        "<i>Track ID format: Usually starts with letters/numbers</i>\n"
        "<i>Example: ABC123DEF456</i>"
    )
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data="cancel_verification")]
    ])
    
    await message.answer(text, reply_markup=keyboard, parse_mode="HTML")


async def process_track_id_input(message: Message, state: FSMContext):
    """
    Process track ID input for verification.
    
    Args:
        message: Telegram message
        state: FSM context
    """
    track_id = message.text.strip()
    
    if not track_id or len(track_id) < 3:
        await message.answer(
            "❌ Invalid track ID. Please enter a valid track ID.",
            parse_mode="HTML"
        )
        return
    
    await state.update_data(track_id=track_id)
    await state.set_state(ManualVerificationStates.verifying_payment)
    
    # Start verification process
    await verify_payment_manually(message, track_id, state)


async def verify_payment_manually(
    target: Union[Message, CallbackQuery], 
    track_id: str, 
    state: FSMContext
):
    """
    Manually verify payment by track ID.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        state: FSM context
    """
    user_id = target.from_user.id if hasattr(target, 'from_user') else target.message.from_user.id
    
    try:
        # Send processing message
        processing_text = (
            "⏳ <b>Verifying Payment...</b>\n\n"
            f"Track ID: <code>{track_id}</code>\n"
            "Please wait while we check the payment status."
        )
        
        if isinstance(target, CallbackQuery):
            await target.message.edit_text(
                processing_text, 
                reply_markup=payment_processing_keyboard(),
                parse_mode="HTML"
            )
        else:
            await target.answer(
                processing_text,
                reply_markup=payment_processing_keyboard(),
                parse_mode="HTML"
            )
        
        # Get payment record from database
        payment_record = get_payment_by_track_id(track_id)
        
        if not payment_record:
            await send_verification_error(
                target, track_id, "Payment record not found in database"
            )
            return
        
        # Check if payment is already verified
        if payment_record.get("status") == "completed":
            await send_already_verified_message(target, track_id, payment_record)
            return
        
        # Perform OXA Pay verification
        verification_result = await check_oxapay_payment(track_id)
        
        if verification_result.get("status") == "error":
            await send_verification_error(
                target, track_id, verification_result.get("message", "Verification failed")
            )
            return
        
        # Process verification result
        await process_verification_result(target, track_id, verification_result, payment_record, state)
        
    except Exception as e:
        logger.error(f"Error in manual verification for {track_id}: {e}")
        await send_verification_error(target, track_id, f"Verification error: {str(e)}")


async def process_verification_result(
    target: Union[Message, CallbackQuery],
    track_id: str,
    verification_result: Dict[str, Any],
    payment_record: Dict[str, Any],
    state: FSMContext
):
    """
    Process verification result and handle different scenarios.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        verification_result: Verification result from OXA Pay
        payment_record: Payment record from database
        state: FSM context
    """
    user_id = target.from_user.id if hasattr(target, 'from_user') else target.message.from_user.id
    status = verification_result.get("status", "").lower()
    
    if status == "completed":
        # Payment is completed - check for underpayment/overpayment
        await handle_completed_payment(target, track_id, verification_result, payment_record, state)
        
    elif status == "pending":
        # Payment is still pending
        await send_pending_message(target, track_id, verification_result)
        
    elif status == "failed":
        # Payment failed
        await send_failed_message(target, track_id, verification_result)
        
    elif status == "expired":
        # Payment expired
        await send_expired_message(target, track_id, verification_result)
        
    else:
        # Unknown status
        await send_unknown_status_message(target, track_id, verification_result)


async def handle_completed_payment(
    target: Union[Message, CallbackQuery],
    track_id: str,
    verification_result: Dict[str, Any],
    payment_record: Dict[str, Any],
    state: FSMContext
):
    """
    Handle completed payment with underpayment/overpayment detection.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        verification_result: Verification result
        payment_record: Payment record
        state: FSM context
    """
    user_id = target.from_user.id if hasattr(target, 'from_user') else target.message.from_user.id
    
    # Get payment amounts
    received_amount = float(verification_result.get("amount", 0))
    required_amount = float(payment_record.get("amount", 0))
    
    # Check for underpayment/overpayment
    payment_status, payment_details = check_payment_amounts(
        received_amount, required_amount, track_id
    )
    
    if payment_status == "underpayment":
        # Handle underpayment
        await state.set_state(ManualVerificationStates.handling_underpayment)
        await handle_underpayment_scenario(target, track_id, received_amount, required_amount, payment_record)
        
    elif payment_status in ["overpayment", "significant_overpayment"]:
        # Handle overpayment
        await state.set_state(ManualVerificationStates.handling_overpayment)
        await handle_overpayment_scenario(target, track_id, received_amount, required_amount, payment_record)
        
    else:
        # Normal payment - process completion
        await process_normal_payment_completion(target, track_id, received_amount, payment_record, state)


async def handle_underpayment_scenario(
    target: Union[Message, CallbackQuery],
    track_id: str,
    received_amount: float,
    required_amount: float,
    payment_record: Dict[str, Any]
):
    """
    Handle underpayment scenario.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        payment_record: Payment record
    """
    remaining_amount = required_amount - received_amount
    payment_url = payment_record.get("payment_url")
    
    # Create underpayment message
    message_text, keyboard = create_underpayment_message(
        track_id, received_amount, required_amount, remaining_amount, payment_url
    )
    
    # Update payment status
    update_payment_status(
        track_id,
        "underpaid",
        actual_paid_amount=received_amount,
        required_amount=required_amount,
        remaining_amount=remaining_amount,
        underpayment_percent=((required_amount - received_amount) / required_amount) * 100
    )
    
    # Send message
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")
    
    logger.info(f"Underpayment detected for {track_id}: ${received_amount:.2f} < ${required_amount:.2f}")


async def handle_overpayment_scenario(
    target: Union[Message, CallbackQuery],
    track_id: str,
    received_amount: float,
    required_amount: float,
    payment_record: Dict[str, Any]
):
    """
    Handle overpayment scenario.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        payment_record: Payment record
    """
    overpayment_amount = received_amount - required_amount
    overpayment_percent = (overpayment_amount / required_amount) * 100
    
    # Create overpayment message
    message_text, keyboard = create_overpayment_message(
        track_id, received_amount, required_amount, overpayment_amount, overpayment_percent
    )
    
    # Update payment status
    update_payment_status(
        track_id,
        "completed",
        actual_paid_amount=received_amount,
        required_amount=required_amount,
        overpayment_amount=overpayment_amount,
        overpayment_percent=overpayment_percent,
        payment_verified=True
    )
    
    # Process payment completion with overpayment
    await process_payment_completion_with_overpayment(
        target, track_id, received_amount, required_amount, overpayment_amount
    )
    
    # Send message
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")
    
    logger.info(f"Overpayment detected for {track_id}: ${received_amount:.2f} > ${required_amount:.2f}")


async def process_normal_payment_completion(
    target: Union[Message, CallbackQuery],
    track_id: str,
    received_amount: float,
    payment_record: Dict[str, Any],
    state: FSMContext
):
    """
    Process normal payment completion.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        received_amount: Amount received
        payment_record: Payment record
        state: FSM context
    """
    user_id = target.from_user.id if hasattr(target, 'from_user') else target.message.from_user.id
    
    # Update payment status
    update_payment_status(
        track_id,
        "completed",
        actual_paid_amount=received_amount,
        payment_verified=True,
        completed_at=datetime.now()
    )
    
    # Update user balance
    current_balance = get_user_balance(user_id)
    new_balance = current_balance + received_amount
    
    balance_updated = update_user_balance(user_id, new_balance)
    
    if balance_updated:
        # Record transaction
        add_transaction(
            user_id=user_id,
            transaction_type="deposit",
            amount=total_amount,
            track_id=track_id,
            payment_amount=received_amount,
            bonus_amount=bonus_result.get("bonus_amount", 0),
            status="completed"
        )
        
        # Trigger VIP check
        vip_result = trigger_vip_check_on_verification(user_id, {
            "amount": received_amount,
            "track_id": track_id,
            "status": "completed"
        })
        
        # Send success message
        await send_payment_completion_message(
            target, track_id, received_amount, bonus_result.get("bonus_amount", 0)
        )
        
        logger.info(f"Payment {track_id} completed successfully - Balance updated: {current_balance:.2f} -> {new_balance:.2f}")
    else:
        await send_verification_error(target, track_id, "Failed to update user balance")


async def process_payment_completion_with_overpayment(
    target: Union[Message, CallbackQuery],
    track_id: str,
    received_amount: float,
    required_amount: float,
    overpayment_amount: float
):
    """
    Process payment completion with overpayment.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        overpayment_amount: Overpayment amount
    """
    user_id = target.from_user.id if hasattr(target, 'from_user') else target.message.from_user.id
    
    # Calculate bonus based on required amount, not received amount
    bonus_result = calculate_deposit_bonus(required_amount, user_id)
    
    # Update user balance with full amount (including overpayment)
    current_balance = get_user_balance(user_id)
    total_amount = received_amount + bonus_result.get("bonus_amount", 0)
    new_balance = current_balance + total_amount
    
    balance_updated = update_user_balance(user_id, new_balance)
    
    if balance_updated:
        # Record transaction with overpayment details
        add_transaction(
            user_id=user_id,
            transaction_type="deposit",
            amount=total_amount,
            track_id=track_id,
            payment_amount=received_amount,
            required_amount=required_amount,
            overpayment_amount=overpayment_amount,
            bonus_amount=bonus_result.get("bonus_amount", 0),
            status="completed"
        )
        
        # Trigger VIP check
        vip_result = trigger_vip_check_on_verification(user_id, {
            "amount": received_amount,
            "track_id": track_id,
            "status": "completed"
        })
        
        logger.info(f"Overpayment processed for {track_id} - Balance updated: {current_balance:.2f} -> {new_balance:.2f}")


async def send_payment_completion_message(
    target: Union[Message, CallbackQuery],
    track_id: str,
    received_amount: float,
    bonus_amount: float
):
    """
    Send payment completion message.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        received_amount: Amount received
        bonus_amount: Bonus amount
    """
    total_credited = received_amount + bonus_amount
    
    message_text = (
        "🎉 <b>Payment Verification Successful!</b>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>TRANSACTION SUMMARY</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"• <b>Amount Paid:</b> <code>${received_amount:.2f}</code>\n"
        f"• <b>Bonus Awarded:</b> <code>${bonus_amount:.2f}</code>\n"
        f"• <b>Total Credited:</b> <code>${total_credited:.2f}</code>\n"
        f"• <b>Transaction ID:</b> <code>{track_id}</code>\n\n"
        "✅ <b>SUCCESS</b>\n"
        "Your payment has been verified and processed!\n"
        "Your balance has been updated.\n\n"
        "<i>Thank you for your payment!</i>"
    )
    
    keyboard = payment_success_keyboard()
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


async def send_verification_error(
    target: Union[Message, CallbackQuery],
    track_id: str,
    error_message: str
):
    """
    Send verification error message.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        error_message: Error message
    """
    message_text = (
        "❌ <b>Verification Failed</b>\n\n"
        f"<b>Track ID:</b> <code>{track_id}</code>\n"
        f"<b>Error:</b> {error_message}\n\n"
        "Please check the track ID and try again, or contact support if the issue persists."
    )
    
    keyboard = payment_verification_keyboard()
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


async def send_already_verified_message(
    target: Union[Message, CallbackQuery],
    track_id: str,
    payment_record: Dict[str, Any]
):
    """
    Send message for already verified payment.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        payment_record: Payment record
    """
    amount = payment_record.get("amount", 0)
    completed_at = payment_record.get("completed_at", "Unknown")
    
    message_text = (
        "✅ <b>Payment Already Verified</b>\n\n"
        f"<b>Track ID:</b> <code>{track_id}</code>\n"
        f"<b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"<b>Status:</b> Completed\n"
        f"<b>Completed At:</b> {completed_at}\n\n"
        "This payment has already been verified and processed."
    )
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🔄 Verify Another", callback_data="verify_another_payment")],
        [InlineKeyboardButton(text="📊 View Balance", callback_data="view_balance")]
    ])
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


async def send_pending_message(
    target: Union[Message, CallbackQuery],
    track_id: str,
    verification_result: Dict[str, Any]
):
    """
    Send pending payment message.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        verification_result: Verification result
    """
    message_text = (
        "⏳ <b>Payment Still Pending</b>\n\n"
        f"<b>Track ID:</b> <code>{track_id}</code>\n"
        f"<b>Status:</b> Pending\n\n"
        "The payment is still being processed. Please wait for confirmation or try again later."
    )
    
    keyboard = payment_verification_keyboard()
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


async def send_failed_message(
    target: Union[Message, CallbackQuery],
    track_id: str,
    verification_result: Dict[str, Any]
):
    """
    Send failed payment message.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        verification_result: Verification result
    """
    message_text = (
        "❌ <b>Payment Failed</b>\n\n"
        f"<b>Track ID:</b> <code>{track_id}</code>\n"
        f"<b>Status:</b> Failed\n\n"
        "The payment could not be completed. Please try again or contact support."
    )
    
    keyboard = payment_verification_keyboard()
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


async def send_expired_message(
    target: Union[Message, CallbackQuery],
    track_id: str,
    verification_result: Dict[str, Any]
):
    """
    Send expired payment message.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        verification_result: Verification result
    """
    message_text = (
        "⏰ <b>Payment Expired</b>\n\n"
        f"<b>Track ID:</b> <code>{track_id}</code>\n"
        f"<b>Status:</b> Expired\n\n"
        "The payment link has expired. Please create a new payment."
    )
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="💰 Create New Payment", callback_data="deposit_funds")],
        [InlineKeyboardButton(text="🔄 Try Another", callback_data="verify_another_payment")]
    ])
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


async def send_unknown_status_message(
    target: Union[Message, CallbackQuery],
    track_id: str,
    verification_result: Dict[str, Any]
):
    """
    Send unknown status message.
    
    Args:
        target: Message or CallbackQuery
        track_id: Payment tracking ID
        verification_result: Verification result
    """
    status = verification_result.get("status", "unknown")
    
    message_text = (
        "❓ <b>Unknown Payment Status</b>\n\n"
        f"<b>Track ID:</b> <code>{track_id}</code>\n"
        f"<b>Status:</b> {status}\n\n"
        "The payment status could not be determined. Please contact support."
    )
    
    keyboard = payment_verification_keyboard()
    
    if isinstance(target, CallbackQuery):
        await target.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
    else:
        await target.answer(message_text, reply_markup=keyboard, parse_mode="HTML")


# Callback handlers
async def handle_verify_another_payment(callback_query: CallbackQuery, state: FSMContext):
    """Handle verify another payment callback."""
    await callback_query.answer()
    await cmd_verify_payment(callback_query.message, state)


async def handle_cancel_verification(callback_query: CallbackQuery, state: FSMContext):
    """Handle cancel verification callback."""
    await callback_query.answer("Verification cancelled")
    await state.clear()
    
    await callback_query.message.edit_text(
        "❌ Payment verification cancelled.",
        parse_mode="HTML"
    )


async def handle_retry_verification(callback_query: CallbackQuery, state: FSMContext):
    """Handle retry verification callback."""
    await callback_query.answer()
    
    data = await state.get_data()
    track_id = data.get("track_id")
    
    if track_id:
        await verify_payment_manually(callback_query, track_id, state)
    else:
        await callback_query.message.edit_text(
            "❌ No track ID found. Please start verification again.",
            parse_mode="HTML"
        )


# Register handlers
router.message.register(cmd_verify_payment, F.text == "/verify")
router.message.register(process_track_id_input, ManualVerificationStates.waiting_for_track_id)
router.callback_query.register(handle_verify_another_payment, F.data == "verify_another_payment")
router.callback_query.register(handle_cancel_verification, F.data == "cancel_verification")
router.callback_query.register(handle_retry_verification, F.data == "retry_verification")
