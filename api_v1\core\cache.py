"""
API v1 Configuration Cache

Provides caching layer for API configurations to reduce database queries.
Implements TTL-based caching with automatic invalidation.
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field

from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class CacheEntry:
    """Single cache entry with TTL"""

    value: Any
    created_at: datetime
    ttl_seconds: int

    def is_expired(self) -> bool:
        """Check if cache entry has expired"""
        age = (datetime.utcnow() - self.created_at).total_seconds()
        return age >= self.ttl_seconds


class ConfigCache:
    """
    Configuration cache with TTL and automatic cleanup

    Features:
    - TTL-based expiration (default 30 minutes)
    - Automatic cleanup of expired entries
    - Thread-safe operations with asyncio locks
    - Cache statistics tracking
    """

    def __init__(self, default_ttl: int = 1800):  # 30 minutes default
        """
        Initialize configuration cache

        Args:
            default_ttl: Default TTL in seconds (default: 30 minutes)
        """
        self._cache: Dict[str, CacheEntry] = {}
        self._default_ttl = default_ttl
        self._lock = asyncio.Lock()
        self._hits = 0
        self._misses = 0
        self._evictions = 0

        logger.info(f"ConfigCache initialized with default TTL: {default_ttl}s")

    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found/expired
        """
        async with self._lock:
            entry = self._cache.get(key)

            if entry is None:
                self._misses += 1
                return None

            if entry.is_expired():
                # Remove expired entry
                del self._cache[key]
                self._evictions += 1
                self._misses += 1
                logger.debug(f"Cache expired: {key}")
                return None

            self._hits += 1
            logger.debug(f"Cache hit: {key}")
            return entry.value

    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """
        Set value in cache

        Args:
            key: Cache key
            value: Value to cache
            ttl: TTL in seconds (uses default if not specified)
        """
        async with self._lock:
            ttl_seconds = ttl if ttl is not None else self._default_ttl
            self._cache[key] = CacheEntry(
                value=value, created_at=datetime.utcnow(), ttl_seconds=ttl_seconds
            )
            logger.debug(f"Cache set: {key} (TTL: {ttl_seconds}s)")

    async def delete(self, key: str) -> bool:
        """
        Delete value from cache

        Args:
            key: Cache key

        Returns:
            True if key was found and deleted
        """
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                logger.debug(f"Cache deleted: {key}")
                return True
            return False

    async def clear(self):
        """Clear all cache entries"""
        async with self._lock:
            count = len(self._cache)
            self._cache.clear()
            logger.info(f"Cache cleared: {count} entries removed")

    async def invalidate_pattern(self, pattern: str):
        """
        Invalidate all keys matching a pattern

        Args:
            pattern: Pattern to match (simple prefix matching)
        """
        async with self._lock:
            keys_to_delete = [k for k in self._cache.keys() if k.startswith(pattern)]
            for key in keys_to_delete:
                del self._cache[key]
                self._evictions += 1

            if keys_to_delete:
                logger.info(
                    f"Invalidated {len(keys_to_delete)} cache entries matching: {pattern}"
                )

    async def cleanup_expired(self):
        """Remove all expired entries"""
        async with self._lock:
            keys_to_delete = [
                key for key, entry in self._cache.items() if entry.is_expired()
            ]

            for key in keys_to_delete:
                del self._cache[key]
                self._evictions += 1

            if keys_to_delete:
                logger.debug(f"Cleaned up {len(keys_to_delete)} expired cache entries")

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics

        Returns:
            Dictionary with cache statistics
        """
        total_requests = self._hits + self._misses
        hit_rate = (self._hits / total_requests * 100) if total_requests > 0 else 0

        return {
            "entries": len(self._cache),
            "hits": self._hits,
            "misses": self._misses,
            "evictions": self._evictions,
            "total_requests": total_requests,
            "hit_rate_percent": round(hit_rate, 2),
            "default_ttl": self._default_ttl,
        }

    def reset_stats(self):
        """Reset cache statistics"""
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        logger.debug("Cache statistics reset")


# Singleton instance
_cache_instance: Optional[ConfigCache] = None


def get_config_cache(default_ttl: int = 1800) -> ConfigCache:
    """
    Get singleton configuration cache instance

    Args:
        default_ttl: Default TTL in seconds (only used on first call)

    Returns:
        ConfigCache instance
    """
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = ConfigCache(default_ttl=default_ttl)
    return _cache_instance


async def warm_cache(cache: ConfigCache, configs: Dict[str, Any]):
    """
    Warm up cache with frequently accessed configurations

    Args:
        cache: ConfigCache instance
        configs: Dictionary of config_name -> config_data
    """
    logger.info(f"Warming cache with {len(configs)} configurations")
    for name, config in configs.items():
        await cache.set(f"config:{name}", config)
    logger.info("Cache warming complete")
