"""
Data filtering utility for API v3 responses.

This module provides functionality to filter and correct API response data
using predefined filter mappings from the data/filters directory.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from utils.central_logger import get_logger
from difflib import get_close_matches

logger = get_logger()


class DataFilter:
    """Data filtering utility for correcting API response data"""

    def __init__(self):
        self.filters: Dict[str, Dict[str, Any]] = {}
        self.loaded = False
        self._load_filters()

    def _load_filters(self):
        """Load filter data from JSON file"""
        try:
            filters_path = (
                Path(__file__).parent.parent
                / "data"
                / "filters"
                / "filter_response.json"
            )

            if not filters_path.exists():
                logger.warning(f"Filter file not found: {filters_path}")
                return

            with open(filters_path, "r", encoding="utf-8") as f:
                filter_data = json.load(f)

            # Convert filter array to dictionary for easier lookup
            for filter_group in filter_data:
                filter_name = filter_group.get("name", "")
                options = filter_group.get("options", [])

                # Create mapping of values to labels and a set of valid values
                value_map = {}
                valid_values = set()

                for option in options:
                    label = option.get("label", "")
                    value = option.get("value", "")

                    if label and value:
                        value_map[value.upper()] = {"label": label, "value": value}
                        valid_values.add(value.upper())

                self.filters[filter_name] = {
                    "value_map": value_map,
                    "valid_values": valid_values,
                }

            self.loaded = True
            logger.debug(f"Loaded {len(self.filters)} filter groups from {filters_path}")

        except Exception as e:
            logger.error(f"Failed to load filters: {e}")
            self.loaded = False

    def correct_country(self, country: str) -> str:
        """Correct country name using filter data"""
        if not self.loaded or not country:
            return country

        country_filter = self.filters.get("country[]", {})
        if not country_filter:
            return country

        # Direct match
        country_upper = country.upper().strip()
        if country_upper in country_filter["valid_values"]:
            return country_filter["value_map"][country_upper]["value"]

        # Fuzzy match
        valid_values = list(country_filter["valid_values"])
        matches = get_close_matches(country_upper, valid_values, n=1, cutoff=0.8)

        if matches:
            matched_value = country_filter["value_map"][matches[0]]["value"]
            logger.debug(f"Corrected country '{country}' -> '{matched_value}'")
            return matched_value

        logger.debug(f"No correction found for country: {country}")
        return country

    def correct_scheme(self, scheme: str) -> str:
        """Correct card scheme/brand name using filter data"""
        if not self.loaded or not scheme:
            return scheme

        scheme_filter = self.filters.get("scheme[]", {})
        if not scheme_filter:
            return scheme

        # Direct match
        scheme_upper = scheme.upper().strip()
        if scheme_upper in scheme_filter["valid_values"]:
            return scheme_filter["value_map"][scheme_upper]["value"]

        # Fuzzy match with common variations
        # Handle common variations like "MC" -> "MASTERCARD", "VISA" variations
        variations = {
            "MC": "MASTERCARD",
            "VISA ELECTRON": "VISA",
            "VISA DEBIT": "VISA",
            "MASTERCARD DEBIT": "MASTERCARD",
            "AMEX": "AMERICAN EXPRESS",
            "DINERS": "DINERS CLUB INTERNATIONAL",
        }

        if scheme_upper in variations:
            corrected = variations[scheme_upper]
            if corrected in scheme_filter["valid_values"]:
                logger.debug(
                    f"Applied variation correction '{scheme}' -> '{corrected}'"
                )
                return corrected

        # Fuzzy match
        valid_values = list(scheme_filter["valid_values"])
        matches = get_close_matches(scheme_upper, valid_values, n=1, cutoff=0.7)

        if matches:
            matched_value = scheme_filter["value_map"][matches[0]]["value"]
            logger.debug(f"Corrected scheme '{scheme}' -> '{matched_value}'")
            return matched_value

        logger.debug(f"No correction found for scheme: {scheme}")
        return scheme

    def correct_card_type(self, card_type: str) -> str:
        """Correct card type using filter data"""
        if not self.loaded or not card_type:
            return card_type

        type_filter = self.filters.get("type[]", {})
        if not type_filter:
            return card_type

        # Direct match
        type_upper = card_type.upper().strip()
        if type_upper in type_filter["valid_values"]:
            return type_filter["value_map"][type_upper]["value"]

        # Common variations
        variations = {
            "DEBIT CARD": "DEBIT",
            "CREDIT CARD": "CREDIT",
            "CHARGE": "CHARGE CARD",
        }

        if type_upper in variations:
            corrected = variations[type_upper]
            if corrected in type_filter["valid_values"]:
                logger.debug(f"Applied type variation '{card_type}' -> '{corrected}'")
                return corrected

        # Fuzzy match
        valid_values = list(type_filter["valid_values"])
        matches = get_close_matches(type_upper, valid_values, n=1, cutoff=0.8)

        if matches:
            matched_value = type_filter["value_map"][matches[0]]["value"]
            logger.debug(f"Corrected card type '{card_type}' -> '{matched_value}'")
            return matched_value

        return card_type

    def correct_level(self, level: str) -> str:
        """Correct card level using filter data"""
        if not self.loaded or not level:
            return level

        level_filter = self.filters.get("level[]", {})
        if not level_filter:
            return level

        # Direct match
        level_upper = level.upper().strip()
        if level_upper in level_filter["valid_values"]:
            return level_filter["value_map"][level_upper]["value"]

        # Fuzzy match
        valid_values = list(level_filter["valid_values"])
        matches = get_close_matches(level_upper, valid_values, n=1, cutoff=0.8)

        if matches:
            matched_value = level_filter["value_map"][matches[0]]["value"]
            logger.debug(f"Corrected level '{level}' -> '{matched_value}'")
            return matched_value

        return level

    def correct_bank(self, bank: str) -> str:
        """Correct bank name using filter data"""
        if not self.loaded or not bank:
            return bank

        bank_filter = self.filters.get("selected_bank", {})
        if not bank_filter:
            return bank

        # Direct match
        bank_upper = bank.upper().strip()
        if bank_upper in bank_filter["valid_values"]:
            return bank_filter["value_map"][bank_upper]["value"]

        # Fuzzy match with higher threshold for bank names
        valid_values = list(bank_filter["valid_values"])
        matches = get_close_matches(bank_upper, valid_values, n=1, cutoff=0.9)

        if matches:
            matched_value = bank_filter["value_map"][matches[0]]["value"]
            logger.debug(f"Corrected bank '{bank}' -> '{matched_value}'")
            return matched_value

        return bank

    def filter_card_data(self, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """Filter and correct a single card's data"""
        if not isinstance(card_data, dict):
            return card_data

        filtered_data = card_data.copy()

        # Apply corrections to common fields
        corrections = [
            ("country", self.correct_country),
            ("scheme", self.correct_scheme),
            ("brand", self.correct_scheme),  # Sometimes called brand instead of scheme
            ("type", self.correct_card_type),
            ("level", self.correct_level),
            ("bank", self.correct_bank),
            ("bank_name", self.correct_bank),
        ]

        for field_name, correction_func in corrections:
            if field_name in filtered_data:
                original_value = filtered_data[field_name]
                if isinstance(original_value, str):
                    corrected_value = correction_func(original_value)
                    if corrected_value != original_value:
                        filtered_data[field_name] = corrected_value

        return filtered_data

    def filter_response_data(self, response_data: Any) -> Any:
        """Filter and correct API response data"""
        if not self.loaded:
            logger.warning("Data filters not loaded, returning original data")
            return response_data

        try:
            if isinstance(response_data, dict):
                # Handle different response structures
                filtered_data = response_data.copy()

                # Filter items array if present
                if "items" in filtered_data and isinstance(
                    filtered_data["items"], list
                ):
                    filtered_data["items"] = [
                        self.filter_card_data(item) for item in filtered_data["items"]
                    ]

                # Filter data array if present
                if "data" in filtered_data and isinstance(filtered_data["data"], list):
                    filtered_data["data"] = [
                        self.filter_card_data(item) for item in filtered_data["data"]
                    ]

                # Filter cards array if present
                if "cards" in filtered_data and isinstance(
                    filtered_data["cards"], list
                ):
                    filtered_data["cards"] = [
                        self.filter_card_data(item) for item in filtered_data["cards"]
                    ]

                # Filter top-level card data
                if any(
                    field in filtered_data
                    for field in ["country", "scheme", "type", "level", "bank"]
                ):
                    filtered_data = self.filter_card_data(filtered_data)

                return filtered_data

            elif isinstance(response_data, list):
                # Filter array of card data
                return [self.filter_card_data(item) for item in response_data]

            else:
                # Return as-is for non-dict/list data
                return response_data

        except Exception as e:
            logger.error(f"Error filtering response data: {e}")
            return response_data

    def get_valid_values(self, filter_name: str) -> Set[str]:
        """Get valid values for a specific filter"""
        if not self.loaded:
            return set()

        filter_data = self.filters.get(filter_name, {})
        return filter_data.get("valid_values", set())

    def reload_filters(self):
        """Reload filter data from file"""
        self.filters.clear()
        self.loaded = False
        self._load_filters()


# Global instance
_data_filter: Optional[DataFilter] = None


def get_data_filter() -> DataFilter:
    """Get the global data filter instance"""
    global _data_filter
    if _data_filter is None:
        _data_filter = DataFilter()
    return _data_filter


def filter_api_response(response_data: Any) -> Any:
    """Convenience function to filter API response data"""
    return get_data_filter().filter_response_data(response_data)
