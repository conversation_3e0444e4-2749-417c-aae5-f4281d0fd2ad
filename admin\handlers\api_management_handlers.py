"""
Telegram Bot Admin Handlers for Shared API Management

This module provides simplified, intuitive Telegram bot handlers for managing
API configurations using the shared API system.
"""

from typing import Dict, Any, List, Optional
import json

from aiogram import Router, F
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from admin.services.shared_api_admin_service import get_shared_api_admin_service
from admin.services.auth_sync_service import get_auth_sync_service
from admin.ui.api_management_ui import APIManagementUI
from utils.decorators import admin_required, error_handler
from utils.loading_animations import LoadingStages, ADMIN_STAGES
from middleware import attach_common_middlewares

from utils.central_logger import get_logger

logger = get_logger()

router = Router()
attach_common_middlewares(router)


class APIManagementStates(StatesGroup):
    """States for API management workflows"""

    # Creation workflow
    waiting_for_name = State()
    waiting_for_base_url = State()
    waiting_for_auth_type = State()
    waiting_for_auth_data = State()
    waiting_for_endpoints = State()

    # Editing workflow
    editing_base_url = State()
    editing_auth = State()
    editing_endpoints = State()
    editing_auth_credentials = State()
    editing_headers = State()
    editing_base_url_value = State()
    editing_timeout_values = State()
    editing_retry_settings = State()
    editing_category_name = State()
    editing_environment_type = State()

    # Testing workflow
    selecting_test_endpoint = State()

    # Search and filtering workflow
    searching_apis = State()


class APIManagementHandlers:
    """Handlers for API management through Telegram bot interface"""
    
    def __init__(self):
        self.admin_service = get_shared_api_admin_service()
        self.auth_sync_service = get_auth_sync_service()
        self.ui = APIManagementUI()
    
    @admin_required
    @error_handler
    async def cmd_api_management(self, message: Message, state: FSMContext):
        """Main API management command"""
        await state.clear()
        
        # Get API configurations summary
        api_configs = await self.admin_service.list_api_configurations()
        
        text = self.ui.format_main_menu(api_configs)
        keyboard = self.ui.create_main_menu_keyboard()
        
        await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_api_main(self, callback: CallbackQuery, state: FSMContext):
        """Main API management menu"""
        await state.clear()
        
        api_configs = await self.admin_service.list_api_configurations()
        
        text = self.ui.format_main_menu(api_configs)
        keyboard = self.ui.create_main_menu_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()



    @admin_required
    @error_handler
    async def callback_api_list(self, callback: CallbackQuery, state: FSMContext):
        """List all API configurations"""
        environment = None
        if ":" in callback.data:
            environment = callback.data.split(":", 1)[1]
        
        # Get all configurations (the service method doesn't accept parameters)
        try:
            api_configs = await self.admin_service.list_api_configurations()

            # Filter by environment if specified
            if environment and environment != "all":
                api_configs = [config for config in api_configs if config.get("environment") == environment]

        except Exception as e:
            # Fallback to empty list if database is not available
            logger.error(f"Error listing API configurations: {e}")
            api_configs = []

            # Show error message to user
            await callback.answer("⚠️ Database connection issue. Please check system status.", show_alert=True)
        
        text = self.ui.format_api_list(api_configs, environment)
        keyboard = self.ui.create_api_list_keyboard(api_configs, environment)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    @admin_required
    @error_handler
    async def callback_api_create(self, callback: CallbackQuery, state: FSMContext):
        """Start API creation workflow"""
        await state.set_state(APIManagementStates.waiting_for_name)
        
        text = self.ui.format_create_api_step1()
        keyboard = self.ui.create_cancel_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer("🆕 Starting API creation...")
    
    @admin_required
    @error_handler
    async def handle_api_name_input(self, message: Message, state: FSMContext):
        """Handle API name input"""
        api_name = message.text.strip()
        
        # Validate name
        if not api_name or len(api_name) < 2:
            await message.answer("❌ API name must be at least 2 characters long. Please try again:")
            return
        
        if not api_name.replace("_", "").replace("-", "").isalnum():
            await message.answer("❌ API name can only contain letters, numbers, hyphens, and underscores. Please try again:")
            return
        
        # Check if name already exists
        existing = await self.admin_service.admin_storage.get_api_config(api_name)
        if existing:
            await message.answer(f"❌ API configuration '{api_name}' already exists. Please choose a different name:")
            return
        
        # Store name and move to next step
        await state.update_data(api_name=api_name)
        await state.set_state(APIManagementStates.waiting_for_base_url)
        
        text = self.ui.format_create_api_step2(api_name)
        keyboard = self.ui.create_cancel_keyboard()
        
        await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def handle_base_url_input(self, message: Message, state: FSMContext):
        """Handle base URL input"""
        base_url = message.text.strip()
        
        # Validate URL
        if not base_url.startswith(("http://", "https://")):
            await message.answer("❌ Base URL must start with http:// or https://. Please try again:")
            return
        
        # Store URL and move to next step
        data = await state.get_data()
        await state.update_data(base_url=base_url)
        
        text = self.ui.format_create_api_step3(data["api_name"], base_url)
        keyboard = self.ui.create_auth_type_keyboard()
        
        await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_auth_type_select(self, callback: CallbackQuery, state: FSMContext):
        """Handle authentication type selection"""
        auth_type = callback.data.split(":", 1)[1]
        
        data = await state.get_data()
        await state.update_data(auth_type=auth_type)
        
        if auth_type == "none":
            # No authentication needed, proceed to endpoints
            await self._proceed_to_endpoints(callback, state)
        else:
            # Need authentication data
            await state.set_state(APIManagementStates.waiting_for_auth_data)
            
            text = self.ui.format_auth_data_input(auth_type)
            keyboard = self.ui.create_cancel_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        
        await callback.answer()
    
    async def _proceed_to_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Proceed to endpoints configuration"""
        data = await state.get_data()
        
        text = self.ui.format_create_api_step4(data["api_name"])
        keyboard = self.ui.create_endpoints_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_skip_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Skip endpoints configuration and create API"""
        await self._create_api_configuration(callback, state)
    
    @admin_required
    @error_handler
    async def callback_add_basic_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Add basic endpoints and create API"""
        # Add basic CRUD endpoints
        basic_endpoints = {
            "list": {"path": "/list", "method": "GET", "description": "List items"},
            "get": {"path": "/get", "method": "GET", "description": "Get item"},
            "create": {"path": "/create", "method": "POST", "description": "Create item"},
            "update": {"path": "/update", "method": "PUT", "description": "Update item"},
            "delete": {"path": "/delete", "method": "DELETE", "description": "Delete item"}
        }
        
        await state.update_data(endpoints=basic_endpoints)
        await self._create_api_configuration(callback, state)
    
    async def _create_api_configuration(self, callback: CallbackQuery, state: FSMContext):
        """Create the API configuration"""
        try:
            data = await state.get_data()
            user_id = str(callback.from_user.id)
            
            # Prepare authentication data
            auth_data = {}
            auth_type = data.get("auth_type", "none")
            
            if auth_type != "none" and "auth_data" in data:
                auth_data = data["auth_data"]
            
            # Create the configuration
            success, message, admin_config = await self.admin_service.create_api_configuration(
                name=data["api_name"],
                base_url=data["base_url"],
                created_by=user_id,
                display_name=data.get("display_name", data["api_name"]),
                description=data.get("description", ""),
                auth_type=auth_type,
                auth_data=auth_data,
                endpoints=data.get("endpoints", {}),
                environment="development"
            )
            
            if success:
                # Show success message with options
                text = self.ui.format_creation_success(data["api_name"], message)
                keyboard = self.ui.create_post_creation_keyboard(data["api_name"])
                
                await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
                await callback.answer("✅ API created successfully!")
            else:
                # Show error message
                text = self.ui.format_creation_error(message)
                keyboard = self.ui.create_retry_keyboard()
                
                await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
                await callback.answer("❌ Creation failed", show_alert=True)
            
            await state.clear()
            
        except Exception as e:
            logger.error(f"Failed to create API configuration: {e}")
            await callback.answer("❌ An error occurred", show_alert=True)
            await state.clear()
    
    @admin_required
    @error_handler
    async def callback_api_view(self, callback: CallbackQuery, state: FSMContext):
        """View API configuration details"""
        api_name = callback.data.split(":", 1)[1]
        
        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return
        
        text = self.ui.format_api_details(api_details)
        keyboard = self.ui.create_api_details_keyboard(api_name)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    @admin_required
    @error_handler
    async def callback_api_test(self, callback: CallbackQuery, state: FSMContext):
        """Test API configuration"""
        api_name = callback.data.split(":", 1)[1]
        
        # Show loading stages concurrently with API testing
        success, message, test_results = await LoadingStages.run_concurrent_loading(
            callback,
            ADMIN_STAGES,
            self.admin_service.test_api_connection(api_name),
            operation_name="API Connection Test"
        )
        
        # Format and display results
        text = self.ui.format_test_results(api_name, success, message, test_results)
        keyboard = self.ui.create_test_results_keyboard(api_name, success)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_api_test_endpoint(self, callback: CallbackQuery, state: FSMContext):
        """Test specific API endpoint"""
        parts = callback.data.split(":", 2)
        api_name = parts[1]
        endpoint_name = parts[2]
        
        await callback.answer(f"🧪 Testing endpoint '{endpoint_name}'...")
        
        # Perform the test
        success, message, test_results = await self.admin_service.test_api_connection(
            api_name, endpoint_name
        )
        
        # Format and display results
        text = self.ui.format_endpoint_test_results(api_name, endpoint_name, success, message, test_results)
        keyboard = self.ui.create_test_results_keyboard(api_name, success)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_api_health_check_all(self, callback: CallbackQuery, state: FSMContext):
        """Check health of all APIs"""
        await callback.answer("🔍 Checking health of all APIs...")
        
        api_configs = await self.admin_service.list_api_configurations(enabled_only=True)
        
        health_results = []
        for config in api_configs:
            success, message, test_results = await self.admin_service.test_api_connection(config["name"])
            health_results.append({
                "name": config["name"],
                "success": success,
                "message": message,
                "test_results": test_results
            })
        
        text = self.ui.format_health_check_results(health_results)
        keyboard = self.ui.create_health_check_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")





    @admin_required
    @error_handler
    async def callback_api_edit(self, callback: CallbackQuery, state: FSMContext):
        """Edit API configuration"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        text = self.ui.format_api_edit_menu(api_details)
        keyboard = self.ui.create_api_edit_keyboard(api_name)

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()







    @admin_required
    @error_handler
    async def handle_auth_credentials_input(self, message: Message, state: FSMContext):
        """Handle authentication credentials input"""
        data = await state.get_data()
        api_name = data.get("api_name")
        auth_type = data.get("auth_type")
        credentials = message.text.strip()

        if not credentials:
            await message.answer("❌ Credentials cannot be empty. Please try again:")
            return

        try:
            # Update authentication credentials
            success, result_message = await self.admin_service.update_api_authentication(
                api_name, auth_type, credentials
            )

            if success:
                text = f"✅ <b>Authentication Updated: {api_name}</b>\n\n"
                text += f"🔐 Authentication credentials have been updated successfully.\n\n"
                text += f"📋 <b>Details:</b>\n{result_message}\n\n"
                text += f"🧪 <b>Next Steps:</b>\n"
                text += f"• Test the API connection\n"
                text += f"• Verify authentication is working\n"
                text += f"• Update any dependent configurations"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🧪 Test Authentication", callback_data=f"auth_test:{api_name}"),
                        InlineKeyboardButton(text="🧪 Test API", callback_data=f"api_test:{api_name}")
                    ],
                    [
                        InlineKeyboardButton(text="⚙️ Back to Settings", callback_data=f"api_settings:{api_name}")
                    ]
                ])
            else:
                text = f"❌ <b>Authentication Update Failed: {api_name}</b>\n\n"
                text += f"📋 <b>Error:</b>\n{result_message}\n\n"
                text += f"💡 <b>Common Issues:</b>\n"
                text += f"• Invalid token format\n"
                text += f"• Expired credentials\n"
                text += f"• Network connectivity issues\n"
                text += f"• API endpoint changes"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🔄 Try Again", callback_data=f"auth_update:{api_name}"),
                        InlineKeyboardButton(text="⚙️ Back to Settings", callback_data=f"api_settings:{api_name}")
                    ]
                ])

            await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
            await state.clear()

        except Exception as e:
            await message.answer(
                f"❌ Error updating authentication: {str(e)}",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="⚙️ Back to Settings", callback_data=f"api_settings:{api_name}")]
                ]),
                parse_mode="HTML"
            )
            await state.clear()

    @admin_required
    @error_handler
    async def handle_headers_input(self, message: Message, state: FSMContext):
        """Handle custom headers input"""
        data = await state.get_data()
        api_name = data.get("api_name")
        action = data.get("action", "add")
        headers_json = message.text.strip()

        if not headers_json:
            await message.answer("❌ Headers JSON cannot be empty. Please try again:")
            return

        try:
            # Parse the JSON headers
            headers_data = json.loads(headers_json)

            if not isinstance(headers_data, dict):
                await message.answer("❌ Headers must be a JSON object. Please try again:")
                return

            # Update API with custom headers authentication
            success, result_message = await self.admin_service.update_api_authentication(
                api_name, "custom_header", json.dumps({"custom_headers": headers_data})
            )

            if success:
                text = f"✅ <b>Custom Headers Updated: {api_name}</b>\n\n"
                text += f"🔧 Headers have been configured successfully.\n\n"
                text += f"📋 <b>Configured Headers ({len(headers_data)}):</b>\n"
                for header_name, header_value in headers_data.items():
                    masked_value = header_value[:12] + "..." if len(header_value) > 12 else header_value
                    text += f"• <code>{header_name}</code>: {masked_value}\n"

                text += f"\n🧪 <b>Next Steps:</b>\n"
                text += f"• Test the API with new headers\n"
                text += f"• Verify headers are working correctly\n"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🧪 Test Headers", callback_data=f"headers_test:{api_name}"),
                        InlineKeyboardButton(text="🧪 Test API", callback_data=f"api_test:{api_name}")
                    ],
                    [
                        InlineKeyboardButton(text="🔧 Manage Headers", callback_data=f"headers_manage:{api_name}")
                    ]
                ])
            else:
                text = f"❌ <b>Headers Update Failed: {api_name}</b>\n\n"
                text += f"📋 <b>Error:</b>\n{result_message}\n\n"
                text += f"💡 <b>Common Issues:</b>\n"
                text += f"• Invalid JSON format\n"
                text += f"• Invalid header names or values\n"
                text += f"• Network connectivity issues"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🔄 Try Again", callback_data=f"headers_{action}:{api_name}"),
                        InlineKeyboardButton(text="🔧 Back to Headers", callback_data=f"headers_manage:{api_name}")
                    ]
                ])

            await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
            await state.clear()

        except json.JSONDecodeError as e:
            await message.answer(
                f"❌ Invalid JSON format: {str(e)}\n\n"
                f"Please check your JSON syntax and try again:",
                parse_mode="HTML"
            )
        except Exception as e:
            await message.answer(
                f"❌ Error updating headers: {str(e)}",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="🔧 Back to Headers", callback_data=f"headers_manage:{api_name}")]
                ]),
                parse_mode="HTML"
            )
            await state.clear()

    @admin_required
    @error_handler
    async def handle_base_url_edit_input(self, message: Message, state: FSMContext):
        """Handle base URL edit input"""
        data = await state.get_data()
        api_name = data.get("api_name")
        new_url = message.text.strip()

        # Validate URL
        if not new_url.startswith(("http://", "https://")):
            await message.answer("❌ Base URL must start with http:// or https://. Please try again:")
            return

        try:
            # Update base URL
            success, result_message = await self.admin_service.update_api_configuration(
                name=api_name,
                updated_by=str(message.from_user.id),
                base_url=new_url
            )

            if success:
                text = f"✅ <b>Base URL Updated: {api_name}</b>\n\n"
                text += f"🌐 New URL: <code>{new_url}</code>\n\n"
                text += f"📋 <b>Details:</b>\n{result_message}\n\n"
                text += f"🧪 <b>Next Steps:</b>\n"
                text += f"• Test the API with new URL\n"
                text += f"• Verify all endpoints work correctly\n"
                text += f"• Update any hardcoded references"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🧪 Test API", callback_data=f"api_test:{api_name}"),
                        InlineKeyboardButton(text="👁️ View Details", callback_data=f"api_view:{api_name}")
                    ],
                    [
                        InlineKeyboardButton(text="✏️ Back to Edit", callback_data=f"api_edit:{api_name}")
                    ]
                ])
            else:
                text = f"❌ <b>Base URL Update Failed: {api_name}</b>\n\n"
                text += f"📋 <b>Error:</b>\n{result_message}\n\n"
                text += f"💡 <b>Common Issues:</b>\n"
                text += f"• Invalid URL format\n"
                text += f"• Network connectivity issues\n"
                text += f"• API endpoint not accessible"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🔄 Try Again", callback_data=f"edit_base_url:{api_name}"),
                        InlineKeyboardButton(text="✏️ Back to Edit", callback_data=f"api_edit:{api_name}")
                    ]
                ])

            await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
            await state.clear()

        except Exception as e:
            await message.answer(
                f"❌ Error updating base URL: {str(e)}",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="✏️ Back to Edit", callback_data=f"api_edit:{api_name}")]
                ]),
                parse_mode="HTML"
            )
            await state.clear()

    @admin_required
    @error_handler
    async def handle_category_edit_input(self, message: Message, state: FSMContext):
        """Handle category edit input"""
        data = await state.get_data()
        api_name = data.get("api_name")
        new_category = message.text.strip()

        if not new_category:
            await message.answer("❌ Category cannot be empty. Please try again:")
            return

        try:
            # Update category
            success, result_message = await self.admin_service.update_api_configuration(
                name=api_name,
                updated_by=str(message.from_user.id),
                category=new_category
            )

            if success:
                text = f"✅ <b>Category Updated: {api_name}</b>\n\n"
                text += f"🏷️ New Category: {new_category}\n\n"
                text += f"📋 <b>Details:</b>\n{result_message}"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="⚙️ Back to Settings", callback_data=f"api_settings:{api_name}"),
                        InlineKeyboardButton(text="👁️ View Details", callback_data=f"api_view:{api_name}")
                    ]
                ])
            else:
                text = f"❌ <b>Category Update Failed: {api_name}</b>\n\n"
                text += f"📋 <b>Error:</b>\n{result_message}"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🔄 Try Again", callback_data=f"edit_category:{api_name}"),
                        InlineKeyboardButton(text="⚙️ Back to Settings", callback_data=f"api_settings:{api_name}")
                    ]
                ])

            await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
            await state.clear()

        except Exception as e:
            await message.answer(
                f"❌ Error updating category: {str(e)}",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(text="⚙️ Back to Settings", callback_data=f"api_settings:{api_name}")]
                ]),
                parse_mode="HTML"
            )
            await state.clear()

    @admin_required
    @error_handler
    async def callback_cancel(self, callback: CallbackQuery, state: FSMContext):
        """Cancel current operation"""
        await state.clear()
        await self.callback_api_main(callback, state)

    @admin_required
    @error_handler
    async def callback_edit_auth(self, callback: CallbackQuery, state: FSMContext):
        """Edit API authentication configuration"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        # Show authentication management menu
        auth_sync_status = await self.auth_sync_service.get_auth_sync_status()
        text = self.ui.format_auth_management_menu(api_details, auth_sync_status)
        keyboard = self.ui.create_auth_management_keyboard(
            api_name,
            api_details.get("authentication", {}).get("type", "none")
        )

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_auth_update(self, callback: CallbackQuery, state: FSMContext):
        """Update API authentication credentials"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        auth_type = api_details.get("authentication", {}).get("type", "none")

        if auth_type == "none":
            await callback.answer("❌ No authentication configured for this API", show_alert=True)
            return

        # Set state for credential input
        await state.set_state(APIManagementStates.editing_auth_credentials)
        await state.update_data(api_name=api_name, auth_type=auth_type)

        text = self.ui.format_auth_update_prompt(api_name, auth_type)
        keyboard = self.ui.create_cancel_keyboard()

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_auth_test(self, callback: CallbackQuery, state: FSMContext):
        """Test API authentication"""
        api_name = callback.data.split(":", 1)[1]

        await callback.answer("🧪 Testing authentication...")

        try:
            # Test API connection with current authentication
            success, message, test_results = await self.admin_service.test_api_connection(api_name)

            if success:
                text = f"✅ <b>Authentication Test: {api_name}</b>\n\n"
                text += f"🔐 Authentication is working correctly!\n\n"
                text += f"📋 <b>Test Results:</b>\n{message}"
            else:
                text = f"❌ <b>Authentication Test: {api_name}</b>\n\n"
                text += f"🔐 Authentication failed!\n\n"
                text += f"📋 <b>Error Details:</b>\n{message}"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="🔄 Test Again", callback_data=f"auth_test:{api_name}"),
                    InlineKeyboardButton(text="✏️ Update Auth", callback_data=f"auth_update:{api_name}")
                ],
                [
                    InlineKeyboardButton(text="⬅️ Back to Settings", callback_data=f"api_settings:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error testing authentication: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"api_settings:{api_name}"),
                parse_mode="HTML"
            )

    @admin_required
    @error_handler
    async def callback_auth_clear(self, callback: CallbackQuery, state: FSMContext):
        """Clear API authentication credentials"""
        api_name = callback.data.split(":", 1)[1]

        try:
            # Clear authentication configuration
            success, message = await self.admin_service.clear_api_authentication(api_name)

            if success:
                text = f"✅ <b>Authentication Cleared: {api_name}</b>\n\n"
                text += f"🔓 Authentication has been removed from this API.\n\n"
                text += f"📋 <b>Details:</b>\n{message}\n\n"
                text += f"⚠️ <b>Note:</b> API requests may fail without authentication."
            else:
                text = f"❌ <b>Failed to Clear Authentication: {api_name}</b>\n\n"
                text += f"📋 <b>Error:</b>\n{message}"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="🔐 Add Authentication", callback_data=f"edit_auth:{api_name}"),
                    InlineKeyboardButton(text="🧪 Test API", callback_data=f"api_test:{api_name}")
                ],
                [
                    InlineKeyboardButton(text="⬅️ Back to Settings", callback_data=f"api_settings:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error clearing authentication: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"api_settings:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_base_url(self, callback: CallbackQuery, state: FSMContext):
        """Edit API base URL"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        current_url = api_details.get("shared_config", {}).get("base_url", "")

        # Set state for URL input
        await state.set_state(APIManagementStates.editing_base_url_value)
        await state.update_data(api_name=api_name, current_url=current_url)

        text = f"🌐 <b>Edit Base URL: {api_name}</b>\n\n"
        text += f"📋 <b>Current URL:</b>\n<code>{current_url}</code>\n\n"
        text += f"🔧 <b>Enter New Base URL:</b>\n"
        text += f"• Must start with http:// or https://\n"
        text += f"• Should not end with trailing slash\n"
        text += f"• Example: https://api.example.com\n\n"
        text += f"💡 <b>Note:</b> Changing the base URL may affect all API endpoints."

        keyboard = self.ui.create_cancel_keyboard()
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Edit API endpoints configuration"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        endpoints = api_details.get("endpoints", {})

        text = f"📡 <b>Edit Endpoints: {api_name}</b>\n\n"

        if endpoints:
            text += f"🔗 <b>Current Endpoints ({len(endpoints)}):</b>\n\n"
            for name, endpoint in endpoints.items():
                method = endpoint.get("method", "GET")
                path = endpoint.get("path", "/")
                description = endpoint.get("description", "No description")

                method_icon = {
                    "GET": "📥", "POST": "📤", "PUT": "🔄",
                    "DELETE": "🗑️", "PATCH": "✏️"
                }.get(method, "📡")

                text += f"{method_icon} <b>{name}</b> ({method})\n"
                text += f"   <code>{path}</code>\n"
                text += f"   {description}\n\n"
        else:
            text += f"📝 No endpoints configured.\n\n"

        text += f"🔧 <b>Endpoint Management:</b>\n"
        text += f"Choose an action to manage API endpoints."

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="➕ Add Endpoint", callback_data=f"add_endpoint:{api_name}"),
                InlineKeyboardButton(text="📋 Add Basic CRUD", callback_data=f"add_basic_endpoints:{api_name}")
            ],
            [
                InlineKeyboardButton(text="🗑️ Clear All", callback_data=f"clear_endpoints:{api_name}"),
                InlineKeyboardButton(text="📤 Export", callback_data=f"export_endpoints:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Edit", callback_data=f"api_edit:{api_name}")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_settings(self, callback: CallbackQuery, state: FSMContext):
        """Edit API general settings"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        admin_info = api_details.get("admin_info", {})
        shared_config = api_details.get("shared_config", {})

        text = f"⚙️ <b>Edit Settings: {api_name}</b>\n\n"
        text += f"📋 <b>Current Settings:</b>\n"
        text += f"• Display Name: {admin_info.get('display_name', 'N/A')}\n"
        text += f"• Description: {admin_info.get('description', 'N/A')}\n"
        text += f"• Category: {admin_info.get('category', 'general')}\n"
        text += f"• Environment: {admin_info.get('environment', 'development')}\n"
        text += f"• Enabled: {'✅ Yes' if admin_info.get('enabled', True) else '❌ No'}\n\n"
        text += f"🔧 <b>What would you like to edit?</b>"

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📝 Display Name", callback_data=f"edit_display_name:{api_name}"),
                InlineKeyboardButton(text="📄 Description", callback_data=f"edit_description:{api_name}")
            ],
            [
                InlineKeyboardButton(text="🏷️ Category", callback_data=f"edit_category:{api_name}"),
                InlineKeyboardButton(text="🌍 Environment", callback_data=f"edit_environment:{api_name}")
            ],
            [
                InlineKeyboardButton(text="🔄 Toggle Enable/Disable", callback_data=f"toggle_enabled:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Edit", callback_data=f"api_edit:{api_name}")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_timeouts(self, callback: CallbackQuery, state: FSMContext):
        """Edit API timeout settings"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        shared_config = api_details.get("shared_config", {})
        timeout_config = shared_config.get("timeout", {})

        connect_timeout = timeout_config.get("connect", 10)
        read_timeout = timeout_config.get("read", 30)
        total_timeout = timeout_config.get("total", 60)

        text = f"⏱️ <b>Edit Timeouts: {api_name}</b>\n\n"
        text += f"📋 <b>Current Timeout Settings:</b>\n"
        text += f"• Connect Timeout: {connect_timeout}s\n"
        text += f"• Read Timeout: {read_timeout}s\n"
        text += f"• Total Timeout: {total_timeout}s\n\n"
        text += f"🔧 <b>Timeout Configuration:</b>\n"
        text += f"• Connect: Time to establish connection\n"
        text += f"• Read: Time to read response data\n"
        text += f"• Total: Maximum total request time\n\n"
        text += f"💡 <b>Recommended Values:</b>\n"
        text += f"• Fast APIs: 5s, 15s, 30s\n"
        text += f"• Standard APIs: 10s, 30s, 60s\n"
        text += f"• Slow APIs: 15s, 60s, 120s"

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="⚡ Fast (5/15/30)", callback_data=f"set_timeouts:{api_name}:fast"),
                InlineKeyboardButton(text="📊 Standard (10/30/60)", callback_data=f"set_timeouts:{api_name}:standard")
            ],
            [
                InlineKeyboardButton(text="🐌 Slow (15/60/120)", callback_data=f"set_timeouts:{api_name}:slow"),
                InlineKeyboardButton(text="🔧 Custom", callback_data=f"custom_timeouts:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Settings", callback_data=f"api_settings:{api_name}")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_retry(self, callback: CallbackQuery, state: FSMContext):
        """Edit API retry settings"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        shared_config = api_details.get("shared_config", {})
        retry_config = shared_config.get("retry", {})

        max_attempts = retry_config.get("max_attempts", 3)
        delay = retry_config.get("delay", 1.0)
        backoff_factor = retry_config.get("backoff_factor", 2.0)

        text = f"🔄 <b>Edit Retry Settings: {api_name}</b>\n\n"
        text += f"📋 <b>Current Retry Settings:</b>\n"
        text += f"• Max Attempts: {max_attempts}\n"
        text += f"• Initial Delay: {delay}s\n"
        text += f"• Backoff Factor: {backoff_factor}x\n\n"
        text += f"🔧 <b>Retry Configuration:</b>\n"
        text += f"• Max Attempts: Number of retry attempts\n"
        text += f"• Initial Delay: Wait time before first retry\n"
        text += f"• Backoff Factor: Delay multiplier for each retry\n\n"
        text += f"💡 <b>Example:</b> 3 attempts, 1s delay, 2x backoff\n"
        text += f"   Retry delays: 1s, 2s, 4s"

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="⚡ Aggressive (5/0.5/1.5)", callback_data=f"set_retry:{api_name}:aggressive"),
                InlineKeyboardButton(text="📊 Standard (3/1/2)", callback_data=f"set_retry:{api_name}:standard")
            ],
            [
                InlineKeyboardButton(text="🛡️ Conservative (2/2/3)", callback_data=f"set_retry:{api_name}:conservative"),
                InlineKeyboardButton(text="🔧 Custom", callback_data=f"custom_retry:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Settings", callback_data=f"api_settings:{api_name}")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_category(self, callback: CallbackQuery, state: FSMContext):
        """Edit API category"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        current_category = api_details.get("admin_info", {}).get("category", "general")

        # Set state for category input
        await state.set_state(APIManagementStates.editing_category_name)
        await state.update_data(api_name=api_name, current_category=current_category)

        text = f"🏷️ <b>Edit Category: {api_name}</b>\n\n"
        text += f"📋 <b>Current Category:</b> {current_category}\n\n"
        text += f"🔧 <b>Choose New Category:</b>\n"
        text += f"Select from common categories or enter a custom one."

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="💳 bin_cards", callback_data=f"set_category:{api_name}:bin_cards"),
                InlineKeyboardButton(text="🔐 authentication", callback_data=f"set_category:{api_name}:authentication")
            ],
            [
                InlineKeyboardButton(text="💰 payment", callback_data=f"set_category:{api_name}:payment"),
                InlineKeyboardButton(text="📊 analytics", callback_data=f"set_category:{api_name}:analytics")
            ],
            [
                InlineKeyboardButton(text="🌐 general", callback_data=f"set_category:{api_name}:general"),
                InlineKeyboardButton(text="🔧 Custom", callback_data=f"custom_category:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Settings", callback_data=f"edit_settings:{api_name}")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_edit_environment(self, callback: CallbackQuery, state: FSMContext):
        """Edit API environment"""
        api_name = callback.data.split(":", 1)[1]

        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return

        current_env = api_details.get("admin_info", {}).get("environment", "development")

        text = f"🌍 <b>Edit Environment: {api_name}</b>\n\n"
        text += f"📋 <b>Current Environment:</b> {current_env}\n\n"
        text += f"🔧 <b>Choose New Environment:</b>\n"
        text += f"• Development: For testing and development\n"
        text += f"• Staging: For pre-production testing\n"
        text += f"• Production: For live/production use\n\n"
        text += f"⚠️ <b>Note:</b> Environment changes may affect API behavior and access."

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔧 Development", callback_data=f"set_environment:{api_name}:development"),
                InlineKeyboardButton(text="🧪 Staging", callback_data=f"set_environment:{api_name}:staging")
            ],
            [
                InlineKeyboardButton(text="🚀 Production", callback_data=f"set_environment:{api_name}:production")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Settings", callback_data=f"edit_settings:{api_name}")
            ]
        ])

        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_export_config(self, callback: CallbackQuery, state: FSMContext):
        """Export API configuration only"""
        api_name = callback.data.split(":", 1)[1]

        try:
            api_details = await self.admin_service.get_api_details(api_name)
            if not api_details:
                await callback.answer("❌ API configuration not found", show_alert=True)
                return

            # Generate configuration export
            import json
            from datetime import datetime

            export_data = {
                "export_info": {
                    "type": "api_configuration",
                    "api_name": api_name,
                    "exported_at": datetime.utcnow().isoformat(),
                    "exported_by": str(callback.from_user.id),
                    "version": "1.0"
                },
                "configuration": {
                    "admin_info": api_details["admin_info"],
                    "shared_config": api_details["shared_config"],
                    "endpoints": api_details["endpoints"],
                    "authentication": {
                        "type": api_details["authentication"]["type"],
                        "has_credentials": api_details["authentication"]["has_credentials"]
                        # Note: Actual credentials are not exported for security
                    }
                }
            }

            # Format as JSON
            config_json = json.dumps(export_data, indent=2, ensure_ascii=False)

            text = f"📄 <b>Configuration Export: {api_name}</b>\n\n"
            text += f"📋 <b>Export Details:</b>\n"
            text += f"• Type: Configuration Only\n"
            text += f"• Size: {len(config_json)} characters\n"
            text += f"• Endpoints: {len(api_details.get('endpoints', {}))}\n"
            text += f"• Authentication: {api_details['authentication']['type']}\n\n"
            text += f"🔒 <b>Security Note:</b> Credentials are not included in exports.\n\n"
            text += f"📝 <b>Configuration Preview:</b>\n"
            text += f"<pre>{config_json[:500]}{'...' if len(config_json) > 500 else ''}</pre>"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="💾 Download Full", callback_data=f"download_config:{api_name}"),
                    InlineKeyboardButton(text="📋 Copy to Clipboard", callback_data=f"copy_config:{api_name}")
                ],
                [
                    InlineKeyboardButton(text="📤 Other Formats", callback_data=f"api_export:{api_name}"),
                    InlineKeyboardButton(text="⬅️ Back", callback_data=f"api_view:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error exporting configuration: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"api_view:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_export_full(self, callback: CallbackQuery, state: FSMContext):
        """Export API configuration with audit log"""
        api_name = callback.data.split(":", 1)[1]

        try:
            api_details = await self.admin_service.get_api_details(api_name)
            if not api_details:
                await callback.answer("❌ API configuration not found", show_alert=True)
                return

            # Get audit log
            audit_logs = await self.admin_service.get_api_audit_log(api_name)

            # Generate full export
            import json
            from datetime import datetime

            export_data = {
                "export_info": {
                    "type": "full_api_export",
                    "api_name": api_name,
                    "exported_at": datetime.utcnow().isoformat(),
                    "exported_by": str(callback.from_user.id),
                    "version": "1.0"
                },
                "configuration": {
                    "admin_info": api_details["admin_info"],
                    "shared_config": api_details["shared_config"],
                    "endpoints": api_details["endpoints"],
                    "authentication": {
                        "type": api_details["authentication"]["type"],
                        "has_credentials": api_details["authentication"]["has_credentials"]
                    }
                },
                "audit_log": audit_logs[-50:] if audit_logs else []  # Last 50 entries
            }

            # Format as JSON
            full_json = json.dumps(export_data, indent=2, ensure_ascii=False)

            text = f"📋 <b>Full Export: {api_name}</b>\n\n"
            text += f"📊 <b>Export Details:</b>\n"
            text += f"• Type: Configuration + Audit Log\n"
            text += f"• Size: {len(full_json)} characters\n"
            text += f"• Endpoints: {len(api_details.get('endpoints', {}))}\n"
            text += f"• Audit Entries: {len(audit_logs) if audit_logs else 0}\n"
            text += f"• Authentication: {api_details['authentication']['type']}\n\n"
            text += f"🔒 <b>Security Note:</b> Credentials are not included in exports.\n\n"
            text += f"📈 <b>Includes:</b>\n"
            text += f"• Complete API configuration\n"
            text += f"• All endpoint definitions\n"
            text += f"• Recent audit trail (last 50 entries)\n"
            text += f"• Metadata and timestamps"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="💾 Download Full", callback_data=f"download_full:{api_name}"),
                    InlineKeyboardButton(text="📋 Copy JSON", callback_data=f"copy_full:{api_name}")
                ],
                [
                    InlineKeyboardButton(text="📤 Other Formats", callback_data=f"api_export:{api_name}"),
                    InlineKeyboardButton(text="⬅️ Back", callback_data=f"api_view:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error exporting full configuration: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"api_view:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_export_template(self, callback: CallbackQuery, state: FSMContext):
        """Export API configuration as template"""
        api_name = callback.data.split(":", 1)[1]

        try:
            api_details = await self.admin_service.get_api_details(api_name)
            if not api_details:
                await callback.answer("❌ API configuration not found", show_alert=True)
                return

            # Generate template export (sanitized for reuse)
            import json
            from datetime import datetime

            template_data = {
                "template_info": {
                    "type": "api_configuration_template",
                    "based_on": api_name,
                    "created_at": datetime.utcnow().isoformat(),
                    "created_by": str(callback.from_user.id),
                    "version": "1.0",
                    "description": f"Template based on {api_details['admin_info']['display_name']}"
                },
                "template": {
                    "name": "{{API_NAME}}",
                    "display_name": "{{DISPLAY_NAME}}",
                    "description": "{{DESCRIPTION}}",
                    "base_url": "{{BASE_URL}}",
                    "category": api_details["admin_info"].get("category", "general"),
                    "environment": "{{ENVIRONMENT}}",
                    "authentication": {
                        "type": api_details["authentication"]["type"],
                        "credentials": "{{CREDENTIALS}}"
                    },
                    "endpoints": api_details.get("endpoints", {}),
                    "timeout": api_details["shared_config"].get("timeout", {
                        "connect": 10,
                        "read": 30,
                        "total": 60
                    }),
                    "retry": api_details["shared_config"].get("retry", {
                        "max_attempts": 3,
                        "delay": 1.0,
                        "backoff_factor": 2.0
                    })
                },
                "instructions": {
                    "usage": "Replace {{PLACEHOLDER}} values with actual configuration",
                    "placeholders": [
                        "{{API_NAME}} - Unique API identifier",
                        "{{DISPLAY_NAME}} - Human-readable name",
                        "{{DESCRIPTION}} - API description",
                        "{{BASE_URL}} - API base URL",
                        "{{ENVIRONMENT}} - development/staging/production",
                        "{{CREDENTIALS}} - Authentication credentials"
                    ]
                }
            }

            template_json = json.dumps(template_data, indent=2, ensure_ascii=False)

            text = f"📝 <b>Template Export: {api_name}</b>\n\n"
            text += f"🎯 <b>Template Details:</b>\n"
            text += f"• Type: Reusable Configuration Template\n"
            text += f"• Based on: {api_details['admin_info']['display_name']}\n"
            text += f"• Endpoints: {len(api_details.get('endpoints', {}))}\n"
            text += f"• Placeholders: 6 variables to customize\n\n"
            text += f"🔧 <b>Usage:</b>\n"
            text += f"• Replace {{PLACEHOLDER}} values\n"
            text += f"• Import as new API configuration\n"
            text += f"• Customize for different environments\n\n"
            text += f"📋 <b>Template Preview:</b>\n"
            text += f"<pre>{template_json[:400]}{'...' if len(template_json) > 400 else ''}</pre>"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="💾 Download Template", callback_data=f"download_template:{api_name}"),
                    InlineKeyboardButton(text="📋 Copy Template", callback_data=f"copy_template:{api_name}")
                ],
                [
                    InlineKeyboardButton(text="📤 Other Formats", callback_data=f"api_export:{api_name}"),
                    InlineKeyboardButton(text="⬅️ Back", callback_data=f"api_view:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error exporting template: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"api_view:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_export_all(self, callback: CallbackQuery, state: FSMContext):
        """Export all API configurations"""
        try:
            # Get all API configurations
            api_configs = await self.admin_service.list_api_configurations()

            if not api_configs:
                await callback.answer("❌ No API configurations found", show_alert=True)
                return

            # Generate comprehensive export
            import json
            from datetime import datetime

            export_data = {
                "export_info": {
                    "type": "bulk_api_export",
                    "exported_at": datetime.utcnow().isoformat(),
                    "exported_by": str(callback.from_user.id),
                    "version": "1.0",
                    "total_apis": len(api_configs)
                },
                "apis": {}
            }

            # Export each API
            for config in api_configs:
                api_name = config["name"]
                api_details = await self.admin_service.get_api_details(api_name)

                if api_details:
                    export_data["apis"][api_name] = {
                        "admin_info": api_details["admin_info"],
                        "shared_config": api_details["shared_config"],
                        "endpoints": api_details["endpoints"],
                        "authentication": {
                            "type": api_details["authentication"]["type"],
                            "has_credentials": api_details["authentication"]["has_credentials"]
                        }
                    }

            all_json = json.dumps(export_data, indent=2, ensure_ascii=False)

            text = f"💾 <b>Bulk Export: All APIs</b>\n\n"
            text += f"📊 <b>Export Summary:</b>\n"
            text += f"• Total APIs: {len(api_configs)}\n"
            text += f"• Export Size: {len(all_json)} characters\n"
            text += f"• Format: JSON\n\n"
            text += f"🔒 <b>Security Note:</b> Credentials are not included.\n\n"
            text += f"📋 <b>Included APIs:</b>\n"

            for config in api_configs[:5]:  # Show first 5
                status_icon = "🟢" if config.get("enabled") else "🔴"
                text += f"{status_icon} {config['display_name']}\n"

            if len(api_configs) > 5:
                text += f"... and {len(api_configs) - 5} more"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="💾 Download All", callback_data="download_all_apis"),
                    InlineKeyboardButton(text="📋 Copy JSON", callback_data="copy_all_apis")
                ],
                [
                    InlineKeyboardButton(text="📤 Individual Exports", callback_data="api_list"),
                    InlineKeyboardButton(text="⬅️ Back to Main", callback_data="api_main")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error exporting all configurations: {str(e)}",
                reply_markup=self.ui.create_back_keyboard("api_main"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_auth_change_type(self, callback: CallbackQuery, state: FSMContext):
        """Change authentication type for API"""
        api_name = callback.data.split(":", 1)[1]

        try:
            api_details = await self.admin_service.get_api_details(api_name)
            if not api_details:
                await callback.answer("❌ API configuration not found", show_alert=True)
                return

            text = f"🔄 <b>Change Authentication Type: {api_name}</b>\n\n"
            text += f"📋 <b>Current:</b> {api_details['authentication']['type']}\n\n"
            text += f"🔧 <b>Select New Authentication Type:</b>\n"
            text += f"• Bearer Token - For APIs using Authorization header\n"
            text += f"• API Key - For APIs using custom API key headers\n"
            text += f"• Basic Auth - For username/password authentication\n"
            text += f"• None - Remove authentication\n\n"
            text += f"⚠️ <b>Note:</b> Changing type will clear existing credentials."

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="🔑 Bearer Token", callback_data=f"auth_type:{api_name}:bearer_token"),
                    InlineKeyboardButton(text="🗝️ API Key", callback_data=f"auth_type:{api_name}:api_key")
                ],
                [
                    InlineKeyboardButton(text="🔐 Basic Auth", callback_data=f"auth_type:{api_name}:basic_auth"),
                    InlineKeyboardButton(text="❌ None", callback_data=f"auth_type:{api_name}:none")
                ],
                [
                    InlineKeyboardButton(text="⬅️ Back to Auth", callback_data=f"edit_auth:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error changing auth type: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"edit_auth:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_headers_manage(self, callback: CallbackQuery, state: FSMContext):
        """Manage custom headers for API"""
        api_name = callback.data.split(":", 1)[1]

        try:
            api_details = await self.admin_service.get_api_details(api_name)
            if not api_details:
                await callback.answer("❌ API configuration not found", show_alert=True)
                return

            auth_config = api_details.get("authentication", {})
            custom_headers = auth_config.get("custom_headers", {})
            has_headers = bool(custom_headers)

            text = self.ui.format_headers_management_menu(api_details)
            keyboard = self.ui.create_headers_management_keyboard(api_name, has_headers)

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error loading headers management: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"edit_auth:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_headers_add(self, callback: CallbackQuery, state: FSMContext):
        """Add custom headers to API"""
        api_name = callback.data.split(":", 1)[1]

        # Set state for headers input
        await state.set_state(APIManagementStates.editing_headers)
        await state.update_data(api_name=api_name, action="add")

        text = f"🔧 <b>Add Custom Headers: {api_name}</b>\n\n"
        text += f"📋 <b>Headers Format (JSON):</b>\n"
        text += f"<code>{{\n"
        text += f'  "X-API-Key": "your-api-key",\n'
        text += f'  "X-Client-ID": "your-client-id",\n'
        text += f'  "Authorization": "Bearer token",\n'
        text += f'  "Content-Type": "application/json"\n'
        text += f"}}</code>\n\n"
        text += f"💡 <b>Common Headers:</b>\n"
        text += f"• <code>Authorization</code> - Bearer tokens, API keys\n"
        text += f"• <code>X-API-Key</code> - Custom API keys\n"
        text += f"• <code>X-Client-ID</code> - Client identification\n"
        text += f"• <code>User-Agent</code> - Custom user agent\n\n"
        text += f"✏️ <b>Send your headers JSON:</b>"

        keyboard = self.ui.create_cancel_keyboard()
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    @admin_required
    @error_handler
    async def callback_headers_update(self, callback: CallbackQuery, state: FSMContext):
        """Update existing custom headers"""
        api_name = callback.data.split(":", 1)[1]

        try:
            api_details = await self.admin_service.get_api_details(api_name)
            if not api_details:
                await callback.answer("❌ API configuration not found", show_alert=True)
                return

            auth_config = api_details.get("authentication", {})
            current_headers = auth_config.get("custom_headers", {})

            # Set state for headers input
            await state.set_state(APIManagementStates.editing_headers)
            await state.update_data(api_name=api_name, action="update")

            text = f"✏️ <b>Update Custom Headers: {api_name}</b>\n\n"

            if current_headers:
                text += f"📋 <b>Current Headers:</b>\n"
                text += f"<code>{json.dumps(current_headers, indent=2)}</code>\n\n"

            text += f"🔧 <b>Enter Updated Headers (JSON):</b>\n"
            text += f"• Modify the existing headers above\n"
            text += f"• Add new headers as needed\n"
            text += f"• Remove headers by omitting them\n\n"
            text += f"✏️ <b>Send your updated headers JSON:</b>"

            keyboard = self.ui.create_cancel_keyboard()
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error loading current headers: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"headers_manage:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_headers_test(self, callback: CallbackQuery, state: FSMContext):
        """Test API with current headers"""
        api_name = callback.data.split(":", 1)[1]

        await callback.answer("🧪 Testing API with current headers...")

        try:
            # Test API connection with current headers
            success, message, test_results = await self.admin_service.test_api_connection(api_name)

            if success:
                text = f"✅ <b>Headers Test: {api_name}</b>\n\n"
                text += f"🔧 Headers are working correctly!\n\n"
                text += f"📋 <b>Test Results:</b>\n{message}"
            else:
                text = f"❌ <b>Headers Test: {api_name}</b>\n\n"
                text += f"🔧 Headers test failed!\n\n"
                text += f"📋 <b>Error Details:</b>\n{message}"

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(text="🔄 Test Again", callback_data=f"headers_test:{api_name}"),
                    InlineKeyboardButton(text="✏️ Update Headers", callback_data=f"headers_update:{api_name}")
                ],
                [
                    InlineKeyboardButton(text="🔧 Back to Headers", callback_data=f"headers_manage:{api_name}")
                ]
            ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error testing headers: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"headers_manage:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_headers_clear(self, callback: CallbackQuery, state: FSMContext):
        """Clear all custom headers"""
        api_name = callback.data.split(":", 1)[1]

        try:
            # Clear headers by setting empty custom_headers
            success, result_message = await self.admin_service.update_api_authentication(
                api_name, "custom_header", json.dumps({"custom_headers": {}})
            )

            if success:
                text = f"✅ <b>Headers Cleared: {api_name}</b>\n\n"
                text += f"🔧 All custom headers have been removed.\n\n"
                text += f"💡 You can add new headers anytime using the 'Add Headers' button."

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="➕ Add Headers", callback_data=f"headers_add:{api_name}"),
                        InlineKeyboardButton(text="🔐 Back to Auth", callback_data=f"edit_auth:{api_name}")
                    ]
                ])
            else:
                text = f"❌ <b>Failed to Clear Headers: {api_name}</b>\n\n"
                text += f"📋 <b>Error:</b>\n{result_message}"

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [
                        InlineKeyboardButton(text="🔄 Try Again", callback_data=f"headers_clear:{api_name}"),
                        InlineKeyboardButton(text="🔧 Back to Headers", callback_data=f"headers_manage:{api_name}")
                    ]
                ])

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error clearing headers: {str(e)}",
                reply_markup=self.ui.create_back_keyboard(f"headers_manage:{api_name}"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_auth_inherit(self, callback: CallbackQuery, state: FSMContext):
        """Handle authentication inheritance from API v1"""
        try:
            parts = callback.data.split(":", 2)
            api_name = parts[1]
            source_api = parts[2] if len(parts) > 2 else "api1"

            # Perform authentication synchronization
            success, message = await self.auth_sync_service.sync_api2_auth_from_api1(force_update=True)

            if success:
                text = f"✅ <b>Authentication Inherited Successfully</b>\n\n"
                text += f"API v2 now inherits authentication from API v1.\n\n"
                text += f"📋 <b>Details:</b>\n{message}"
            else:
                text = f"❌ <b>Authentication Inheritance Failed</b>\n\n"
                text += f"📋 <b>Error:</b>\n{message}"

            # Show updated authentication management
            api_details = await self.admin_service.get_api_details(api_name)
            auth_sync_status = await self.auth_sync_service.get_auth_sync_status()

            if api_details:
                text += f"\n\n{self.ui.format_auth_management_menu(api_details, auth_sync_status)}"
                keyboard = self.ui.create_auth_management_keyboard(
                    api_name,
                    api_details.get("authentication", {}).get("type", "none")
                )
            else:
                keyboard = self.ui.create_back_keyboard("api_main")

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error inheriting authentication: {str(e)}",
                reply_markup=self.ui.create_back_keyboard("api_main"),
                parse_mode="HTML"
            )

        await callback.answer()

    @admin_required
    @error_handler
    async def callback_auth_independent(self, callback: CallbackQuery, state: FSMContext):
        """Handle setting API v2 to use independent authentication"""
        try:
            api_name = callback.data.split(":", 1)[1]

            # Disable authentication inheritance
            success, message = await self.auth_sync_service.disable_auth_inheritance(api_name)

            if success:
                text = f"✅ <b>Independent Authentication Enabled</b>\n\n"
                text += f"API v2 now uses independent authentication.\n\n"
                text += f"📋 <b>Details:</b>\n{message}"
            else:
                text = f"❌ <b>Failed to Enable Independent Authentication</b>\n\n"
                text += f"📋 <b>Error:</b>\n{message}"

            # Show updated authentication management
            api_details = await self.admin_service.get_api_details(api_name)
            auth_sync_status = await self.auth_sync_service.get_auth_sync_status()

            if api_details:
                text += f"\n\n{self.ui.format_auth_management_menu(api_details, auth_sync_status)}"
                keyboard = self.ui.create_auth_management_keyboard(
                    api_name,
                    api_details.get("authentication", {}).get("type", "none")
                )
            else:
                keyboard = self.ui.create_back_keyboard("api_main")

            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")

        except Exception as e:
            await callback.message.edit_text(
                f"❌ Error setting independent authentication: {str(e)}",
                reply_markup=self.ui.create_back_keyboard("api_main"),
                parse_mode="HTML"
            )

        await callback.answer()


_handlers = APIManagementHandlers()

# Command handlers
router.message.register(_handlers.cmd_api_management, Command("api"))

# Essential callback handlers only
router.callback_query.register(_handlers.callback_api_main, F.data == "api_main")
router.callback_query.register(_handlers.callback_api_list, F.data.startswith("api_list"))
router.callback_query.register(_handlers.callback_api_create, F.data == "api_create")
router.callback_query.register(_handlers.callback_auth_type_select, F.data.startswith("auth_type:"))
router.callback_query.register(_handlers.callback_skip_endpoints, F.data == "skip_endpoints")
router.callback_query.register(_handlers.callback_add_basic_endpoints, F.data == "add_basic_endpoints")
router.callback_query.register(_handlers.callback_api_view, F.data.startswith("api_view:"))
router.callback_query.register(_handlers.callback_api_test, F.data.startswith("api_test:"))
router.callback_query.register(_handlers.callback_api_test_endpoint, F.data.startswith("api_test_endpoint:"))

# Core API management handlers
router.callback_query.register(_handlers.callback_api_edit, F.data.startswith("api_edit:"))

# Core functionality handlers - Authentication, Headers, Base URL, Endpoints
router.callback_query.register(_handlers.callback_edit_auth, F.data.startswith("edit_auth:"))
router.callback_query.register(_handlers.callback_auth_update, F.data.startswith("auth_update:"))
router.callback_query.register(_handlers.callback_auth_test, F.data.startswith("auth_test:"))
router.callback_query.register(_handlers.callback_auth_change_type, F.data.startswith("auth_change_type:"))
router.callback_query.register(_handlers.callback_auth_clear, F.data.startswith("auth_clear:"))
router.callback_query.register(_handlers.callback_headers_manage, F.data.startswith("headers_manage:"))
router.callback_query.register(_handlers.callback_headers_add, F.data.startswith("headers_add:"))
router.callback_query.register(_handlers.callback_headers_update, F.data.startswith("headers_update:"))
router.callback_query.register(_handlers.callback_headers_test, F.data.startswith("headers_test:"))
router.callback_query.register(_handlers.callback_headers_clear, F.data.startswith("headers_clear:"))
router.callback_query.register(_handlers.callback_edit_base_url, F.data.startswith("edit_base_url:"))
router.callback_query.register(_handlers.callback_edit_endpoints, F.data.startswith("edit_endpoints:"))
router.callback_query.register(_handlers.callback_cancel, F.data == "cancel")

# Essential state handlers
router.message.register(_handlers.handle_api_name_input, APIManagementStates.waiting_for_name)
router.message.register(_handlers.handle_base_url_input, APIManagementStates.waiting_for_base_url)
router.message.register(_handlers.handle_auth_credentials_input, APIManagementStates.editing_auth_credentials)
router.message.register(_handlers.handle_headers_input, APIManagementStates.editing_headers)
router.message.register(_handlers.handle_base_url_edit_input, APIManagementStates.editing_base_url_value)
