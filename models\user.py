"""
User and Wallet models for MongoDB
"""

from __future__ import annotations

import datetime as dt
from typing import Optional

from pydantic import Field, field_validator

from models.base import BaseDocument, TimestampMixin


class User(BaseDocument):
    """User document model"""

    telegram_id: int = Field(..., description="Telegram user ID")
    username: Optional[str] = Field(
        default=None, max_length=64, description="Telegram username"
    )
    first_name: Optional[str] = Field(
        default=None, max_length=64, description="User's first name"
    )
    language_code: Optional[str] = Field(
        default=None, max_length=8, description="User's language code"
    )
    role: str = Field(default="user", max_length=16, description="User role")
    consent_ack: bool = Field(default=False, description="User consent acknowledgment")
    active: bool = Field(default=True, description="Whether account is active")

    @field_validator("telegram_id")
    @classmethod
    def validate_telegram_id(cls, v):
        if v <= 0:
            raise ValueError("Telegram ID must be positive")
        return v

    @field_validator("role")
    @classmethod
    def validate_role(cls, v):
        valid_roles = {"user", "admin", "moderator"}
        if v not in valid_roles:
            raise ValueError(f"Role must be one of: {valid_roles}")
        return v

    model_config = {"collection_name": "users"}


class Wallet(BaseDocument):
    """Wallet document model"""

    user_id: str = Field(..., description="Reference to User document ID")
    currency: str = Field(default="USD", max_length=8, description="Wallet currency")
    balance: float = Field(default=0.0, ge=0, description="Current balance")
    daily_cap: float = Field(default=500.0, ge=0, description="Daily spending limit")
    monthly_cap: float = Field(
        default=2000.0, ge=0, description="Monthly spending limit"
    )
    locked: bool = Field(default=False, description="Whether wallet is locked")

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        # Basic currency code validation
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    @field_validator("balance", "daily_cap", "monthly_cap")
    @classmethod
    def validate_amounts(cls, v):
        if v < 0:
            raise ValueError("Amount cannot be negative")
        return round(v, 2)  # Round to 2 decimal places

    def can_spend(self, amount: float) -> bool:
        """Check if wallet can spend the specified amount"""
        return not self.locked and self.balance >= amount

    def debit(self, amount: float) -> None:
        """Debit amount from wallet"""
        if not self.can_spend(amount):
            raise ValueError("Insufficient funds or wallet locked")
        self.balance = round(self.balance - amount, 2)
        self.update_timestamp()

    def credit(self, amount: float) -> None:
        """Credit amount to wallet"""
        if amount <= 0:
            raise ValueError("Credit amount must be positive")
        self.balance = round(self.balance + amount, 2)
        self.update_timestamp()

    model_config = {"collection_name": "wallets"}
