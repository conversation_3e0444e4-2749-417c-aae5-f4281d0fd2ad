"""
Utility functions for payment processing.

This module provides essential utility functions for payment processing,
including metrics tracking, logging, and state management.
"""

import logging
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, Optional, Union, List
from aiogram import Bot
from aiogram.fsm.context import FSMContext

logger = logging.getLogger(__name__)

# In-memory metrics store for real-time stats
_payment_metrics = {
    "daily_totals": {},
    "success_count": 0,
    "failure_count": 0,
    "total_volume": 0.0,
    "last_update": None,
}


async def record_payment_completion(
    user_id: int, amount: float, status: str, metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Record payment completion metrics.

    Args:
        user_id: The user ID associated with the payment
        amount: The payment amount
        status: Status of the payment (success, failed, etc.)
        metadata: Additional metadata about the transaction

    Returns:
        bool: True if metrics were recorded successfully
    """
    try:
        # Get current date for daily aggregation
        today = datetime.now().strftime("%Y-%m-%d")
        
        # Update global metrics
        global _payment_metrics
        _payment_metrics["last_update"] = datetime.now()
        
        if status in ["completed", "success", "verified", "confirmed", "paid"]:
            _payment_metrics["success_count"] += 1
            _payment_metrics["total_volume"] += amount
            
            # Update daily totals
            if today not in _payment_metrics["daily_totals"]:
                _payment_metrics["daily_totals"][today] = {"count": 0, "volume": 0.0}
            _payment_metrics["daily_totals"][today]["count"] += 1
            _payment_metrics["daily_totals"][today]["volume"] += amount
            
        else:
            _payment_metrics["failure_count"] += 1
        
        logger.info(
            f"Recorded payment completion: user={user_id}, amount={amount}, status={status}"
        )
        return True
        
    except Exception as e:
        logger.error(f"Error recording payment completion: {e}")
        return False


async def log_payment(bot: Bot, user_id: int, amount: float, invoice_id: str):
    """
    Log payment information to admin channels and log files.
    
    Args:
        bot: Telegram bot instance
        user_id: User ID who made the payment
        amount: Payment amount
        invoice_id: Payment invoice ID
    """
    try:
        # Create log message
        log_message = (
            f"💰 <b>PAYMENT LOG</b>\n\n"
            f"👤 <b>User ID:</b> <code>{user_id}</code>\n"
            f"💵 <b>Amount:</b> <code>${amount:.2f}</code>\n"
            f"🆔 <b>Invoice ID:</b> <code>{invoice_id}</code>\n"
            f"⏰ <b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        
        # Log to console
        logger.info(f"Payment logged: user={user_id}, amount={amount}, invoice={invoice_id}")
        
        # In a real implementation, you would send this to admin channels
        # For now, we'll just log it
        logger.info(f"Payment log message: {log_message}")
        
    except Exception as e:
        logger.error(f"Error logging payment: {e}")


async def safe_update_data(state: FSMContext, **kwargs) -> None:
    """
    Safely update state data by first checking if keys already exist.
    This prevents the "got multiple values for keyword argument" error.
    
    Args:
        state: The FSM context to update
        **kwargs: Key-value pairs to update in the state data
    """
    try:
        # Get current state data
        current_data = await state.get_data()
        
        # Create a new dictionary with updated values
        new_data = {**current_data, **kwargs}
        
        # Clear state data and set it with the new values
        await state.set_data(new_data)
        
        # Log the update for debugging
        logger.debug(f"Updated state data: {list(kwargs.keys())}")
        
    except Exception as e:
        logger.error(f"Error updating state data: {e}")


async def clear_state_data(state: FSMContext, keys: Optional[List[str]] = None) -> None:
    """
    Clear specific keys from state data or clear all data.
    
    Args:
        state: The FSM context to clear
        keys: Optional list of keys to clear. If None, clears all data.
    """
    try:
        if keys is None:
            # Clear all data
            await state.clear()
            logger.debug("Cleared all state data")
        else:
            # Clear specific keys
            current_data = await state.get_data()
            for key in keys:
                current_data.pop(key, None)
            await state.set_data(current_data)
            logger.debug(f"Cleared state data keys: {keys}")
            
    except Exception as e:
        logger.error(f"Error clearing state data: {e}")


def format_crypto_amount(amount, currency=""):
    """
    Format cryptocurrency amount to avoid scientific notation for small values.

    Args:
        amount: The amount to format (float, int, or string)
        currency: The cryptocurrency code (optional)

    Returns:
        str: Properly formatted amount
    """
    try:
        # Convert to float first
        float_amount = float(amount)

        # For very small values (like BTC), use more decimal places
        if float_amount < 0.0001:
            # Format with up to 10 decimal places, removing trailing zeros
            formatted = f"{float_amount:.10f}".rstrip("0").rstrip(".")
        elif float_amount < 0.01:
            # For small values, use 8 decimal places
            formatted = f"{float_amount:.8f}".rstrip("0").rstrip(".")
        elif float_amount < 1:
            # For medium values, use 6 decimal places
            formatted = f"{float_amount:.6f}".rstrip("0").rstrip(".")
        else:
            # For larger values (like USDT), use 2 decimal places
            formatted = f"{float_amount:.2f}"

        # Add currency code if provided
        if currency:
            return f"{formatted} {currency}"
        return formatted

    except (ValueError, TypeError):
        # Return original value if conversion fails
        if currency:
            return f"{amount} {currency}"
        return str(amount)


def get_payment_metrics() -> Dict[str, Any]:
    """
    Get current payment metrics.
    
    Returns:
        Dict containing current payment metrics
    """
    global _payment_metrics
    return _payment_metrics.copy()


def reset_payment_metrics():
    """Reset payment metrics (useful for testing)."""
    global _payment_metrics
    _payment_metrics = {
        "daily_totals": {},
        "success_count": 0,
        "failure_count": 0,
        "total_volume": 0.0,
        "last_update": None,
    }
    logger.info("Payment metrics reset")


def safe_edit_message(message, text: str, **kwargs):
    """
    Safely edit a message with error handling.
    
    Args:
        message: The message to edit
        text: New text content
        **kwargs: Additional parameters for edit_text
    """
    try:
        return message.edit_text(text, **kwargs)
    except Exception as e:
        logger.error(f"Error editing message: {e}")
        # Fallback: try to send a new message
        try:
            return message.answer(text, **kwargs)
        except Exception as e2:
            logger.error(f"Error sending fallback message: {e2}")
            return None


def sanitize_html(text: str) -> str:
    """
    Sanitize HTML text for safe display in Telegram.
    
    Args:
        text: Text to sanitize
        
    Returns:
        str: Sanitized text
    """
    if not text:
        return ""
    
    # Basic HTML sanitization
    # Replace potentially dangerous characters
    text = text.replace("<script", "&lt;script")
    text = text.replace("</script>", "&lt;/script&gt;")
    text = text.replace("javascript:", "")
    text = text.replace("onclick=", "")
    text = text.replace("onload=", "")
    
    return text

