# Demo Wallet Bot v3 - Log Management Utility
# Simple PowerShell script for viewing log files

Write-Host "=== Demo Wallet Bot v3 - Log Status ===" -ForegroundColor Cyan
Write-Host ""

$logsPath = "logs"
if (-not (Test-Path $logsPath)) {
    Write-Host "❌ Logs directory not found. Run the bot first to initialize logging." -ForegroundColor Red
    exit
}

$logFiles = @(
    "application.log",
    "errors.log",
    "debug.log", 
    "api-requests.log",
    "api-responses.log",
    "http-client.log",
    "telegram-bot.log",
    "transactions.log",
    "user-actions.log",
    "database.log",
    "performance.log",
    "security.log",
    "background-services.log",
    "health-monitor.log",
    "admin-actions.log"
)

foreach ($logFile in $logFiles) {
    $filePath = Join-Path $logsPath $logFile
    if (Test-Path $filePath) {
        $size = (Get-Item $filePath).Length
        $sizeKB = [math]::Round($size / 1KB, 2)
        $lastWrite = (Get-Item $filePath).LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
        Write-Host "✅ $logFile - ${sizeKB} KB - Last: $lastWrite" -ForegroundColor Green
    } else {
        Write-Host "❌ $logFile - Not found" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Recent Application Activity ===" -ForegroundColor Cyan
Write-Host ""

$appLogPath = "logs\application.log"
if (Test-Path $appLogPath) {
    Write-Host "Last 10 lines from application.log:" -ForegroundColor Yellow
    Get-Content $appLogPath -Tail 10 | ForEach-Object {
        if ($_ -match "ERROR|CRITICAL") {
            Write-Host $_ -ForegroundColor Red
        } elseif ($_ -match "WARNING") {
            Write-Host $_ -ForegroundColor Yellow
        } else {
            Write-Host $_ -ForegroundColor White
        }
    }
} else {
    Write-Host "No application log found." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Usage Commands ===" -ForegroundColor Cyan
Write-Host "View recent errors:      Get-Content logs\errors.log -Tail 20" -ForegroundColor Yellow
Write-Host "Watch live logs:         Get-Content logs\application.log -Wait -Tail 10" -ForegroundColor Yellow
Write-Host "View API requests:       Get-Content logs\api-requests.log -Tail 10" -ForegroundColor Yellow
Write-Host "View user actions:       Get-Content logs\user-actions.log -Tail 10" -ForegroundColor Yellow
Write-Host "View transactions:       Get-Content logs\transactions.log -Tail 10" -ForegroundColor Yellow
Write-Host ""