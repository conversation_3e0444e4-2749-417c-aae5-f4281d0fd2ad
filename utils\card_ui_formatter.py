"""
Centralized Card UI Formatter - Single Source of Truth for Card Display
This module ensures consistent card formatting across catalog, cart, and add-to-cart views
"""

from typing import Dict, Any, Optional, List
from utils.product_display import ProductDisplayFormatter
from utils.ui_components import create_message, MessageType, data_formatter
from utils.central_logger import get_logger

logger = get_logger()


class CardUIFormatter:
    """Central formatter for card displays - ensures UI consistency"""

    def __init__(self):
        self.product_formatter = ProductDisplayFormatter()

    def format_card_preview(
        self, card: Dict[str, Any], index: Optional[int] = None
    ) -> str:
        """
        Format a card for catalog or cart preview display.
        This is the single source of truth for card display formatting.

        Args:
            card: Card data dictionary
            index: Optional index number for the card

        Returns:
            str: Formatted card display text
        """
        try:
            # Use the product formatter's compact card method as the base
            return self.product_formatter.format_compact_card(
                card, index=index, device_type="mobile"
            )
        except Exception as e:
            logger.error(f"Error formatting card preview: {e}")
            return self._format_fallback_card(card, index)

    def format_card_detail(
        self, card: Dict[str, Any], show_sensitive: bool = False
    ) -> str:
        """
        Format detailed card information with optional sensitive data.

        Args:
            card: Card data dictionary
            show_sensitive: Whether to show sensitive card data

        Returns:
            str: Formatted detailed card display text
        """
        try:
            lines = []

            # Header with BIN
            bin_value = self._get_bin(card)
            if bin_value:
                lines.append(f"💳 <b>BIN:</b> <code>{bin_value}</code>")

            # Bank and Brand
            bank = card.get("bank")
            brand = card.get("brand")
            if bank:
                lines.append(f"🏛️ <b>Bank:</b> {bank}")
            if brand:
                lines.append(f"💎 <b>Brand:</b> {brand}")

            # Card Type and Level
            card_type = card.get("type")
            level = card.get("level")
            if card_type or level:
                type_level_parts = []
                if card_type:
                    type_level_parts.append(f"📋 {card_type}")
                if level:
                    type_level_parts.append(f"⭐ {level}")
                lines.append(" • ".join(type_level_parts))

            # Location
            location = self._format_location(card)
            if location:
                lines.append(f"🌍 <b>Location:</b> {location}")

            # Expiry
            expiry = self._get_expiry(card)
            if expiry:
                lines.append(f"📅 <b>Expiry:</b> <code>{expiry}</code>")

            # Cardholder name
            cardholder = self._get_cardholder_name(card)
            if cardholder:
                lines.append(f"👤 <b>Cardholder:</b> {cardholder}")

            # Price
            price = self._get_price(card)
            if price is not None:
                formatted_price = data_formatter.format_currency(price)
                lines.append(f"💰 <b>Price:</b> {formatted_price}")

            # Sensitive data section (if requested)
            if show_sensitive:
                sensitive_lines = self._format_sensitive_data(card)
                if sensitive_lines:
                    lines.append("")
                    lines.append("<code>━━━━━━━━━━━━━━━━━━━━━━━</code>")
                    lines.extend(sensitive_lines)

            return "\n".join(lines) if lines else "❌ <i>Card data unavailable</i>"

        except Exception as e:
            logger.error(f"Error formatting card detail: {e}")
            return "❌ <i>Error displaying card details</i>"

    def format_add_to_cart_success_message(
        self, card: Dict[str, Any], cart_count: int, cart_total: float
    ) -> str:
        """
        Format the add-to-cart success message with consistent card display.

        Args:
            card: Card data dictionary
            cart_count: Number of items in cart
            cart_total: Total cart value

        Returns:
            str: Complete formatted success message
        """
        try:
            success_msg = create_message(MessageType.SUCCESS)
            success_msg.set_title("🎉 Added to Cart!", "✅")

            # Cart status
            success_msg.add_content(
                f"📦 <b>Cart Status:</b> {cart_count} item(s) • {data_formatter.format_currency(cart_total)}"
            )

            # Card preview (using consistent formatting)
            card_preview = self.format_card_preview(card)
            success_msg.add_section("Card Details", card_preview, "💳")

            # Next steps
            success_msg.add_section(
                "Next Steps",
                "• Continue browsing to add more cards\n"
                "• View your cart to review items\n"
                "• Proceed to checkout when ready",
                "💡",
            )

            return success_msg.build(add_watermark=False)

        except Exception as e:
            logger.error(f"Error formatting add-to-cart success message: {e}")
            return self._format_fallback_success_message(cart_count, cart_total)

    def format_cart_item_display(
        self, card: Dict[str, Any], quantity: int, line_total: float, index: int
    ) -> str:
        """
        Format a cart item display.

        Args:
            card: Card data dictionary
            quantity: Item quantity
            line_total: Total price for this line item
            index: Item index in cart

        Returns:
            str: Formatted cart item display
        """
        try:
            # Card preview (using consistent formatting with price between separators)
            card_preview = self.format_card_preview(card, index=index)
            
            # Fallback notice if needed
            if card.get("_fallback"):
                return f"{card_preview}\n<i>Card details will refresh automatically during checkout.</i>"
            
            return card_preview

        except Exception as e:
            logger.error(f"Error formatting cart item: {e}")
            return f"❌ <i>Item {index} - Display error</i>"

    # Helper methods

    def _get_bin(self, card: Dict[str, Any]) -> Optional[str]:
        """Extract BIN from card data"""
        bin_fields = ["bin", "card_number"]
        for field in bin_fields:
            value = card.get(field)
            if value and str(value).strip():
                bin_str = str(value).strip()
                if len(bin_str) >= 6:
                    return bin_str[:6]
                elif len(bin_str) >= 4:
                    return bin_str
        return None

    def _get_expiry(self, card: Dict[str, Any]) -> Optional[str]:
        """Extract expiry from card data"""
        expiry_fields = ["expiry", "exp", "expiration"]
        for field in expiry_fields:
            value = card.get(field)
            if value and str(value).strip():
                return str(value).strip()

        # Try month/year combination
        month = card.get("expmonth")
        year = card.get("expyear")
        if month and year:
            try:
                month_int = int(str(month).strip())
                year_str = str(year).strip()
                if 1 <= month_int <= 12:
                    if len(year_str) == 4:
                        year_str = year_str[-2:]
                    return f"{month_int:02d}/{year_str}"
            except (ValueError, TypeError):
                pass

        return None

    def _get_cardholder_name(self, card: Dict[str, Any]) -> Optional[str]:
        """Extract cardholder name from card data"""
        name_fields = ["cardholder_name", "name", "cardholder", "f_name"]
        for field in name_fields:
            value = card.get(field)
            if value and str(value).strip():
                name = str(value).strip()
                # Basic validation - avoid obviously invalid names
                if len(name) > 2 and not name.isdigit():
                    return name
        return None

    def _get_price(self, card: Dict[str, Any]) -> Optional[float]:
        """Extract price from card data"""
        price_fields = ["current_price", "price", "original_price"]
        for field in price_fields:
            value = card.get(field)
            if value is not None:
                try:
                    return float(value)
                except (ValueError, TypeError):
                    continue
        return None

    def _format_location(self, card: Dict[str, Any]) -> Optional[str]:
        """Format location from card data"""
        location_parts = []

        city = card.get("city")
        state = card.get("state")
        country = card.get("country")

        if city:
            location_parts.append(str(city).strip())
        if state:
            location_parts.append(str(state).strip())
        if country:
            location_parts.append(str(country).strip())

        return ", ".join(location_parts) if location_parts else None

    def _format_sensitive_data(self, card: Dict[str, Any]) -> List[str]:
        """Format sensitive card data (CVV, full card number, etc.)"""
        sensitive_lines = []

        # Card number
        card_number = card.get("cc") or card.get("card_number") or card.get("pan")
        if card_number and str(card_number) != "[PAN_REDACTED]":
            sensitive_lines.append(f"💳 <b>Card:</b> <code>{card_number}</code>")

        # Expiry (detailed)
        expiry = self._get_expiry(card)
        if expiry:
            sensitive_lines.append(f"📅 <b>Expiry:</b> <code>{expiry}</code>")

        # CVV
        cvv = card.get("cvv")
        if cvv and str(cvv) != "***":
            sensitive_lines.append(f"🔐 <b>CVV:</b> <code>{cvv}</code>")

        return sensitive_lines

    def _format_fallback_card(
        self, card: Dict[str, Any], index: Optional[int] = None
    ) -> str:
        """Fallback formatting when main formatter fails"""
        prefix = f"<b>{index}.</b> " if index else ""
        bin_value = self._get_bin(card) or "N/A"
        price = self._get_price(card)
        price_str = data_formatter.format_currency(price) if price else "N/A"
        return f"{prefix}BIN: {bin_value} • Price: {price_str}"

    def _format_fallback_success_message(
        self, cart_count: int, cart_total: float
    ) -> str:
        """Fallback success message when main formatter fails"""
        return (
            f"✅ <b>Added to Cart!</b>\n\n"
            f"📦 Cart: {cart_count} item(s) • {data_formatter.format_currency(cart_total)}\n\n"
            f"<i>View your cart to see all items.</i>"
        )


# Global instance for easy import
card_ui_formatter = CardUIFormatter()

