"""
Enhanced Checkout Configuration

This module provides configuration settings for the enhanced checkout features,
allowing for easy customization and feature toggling.
"""

from __future__ import annotations

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from utils.central_logger import get_logger

logger = get_logger()


class FeatureToggle(Enum):
    """Feature toggle options"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    BETA = "beta"


@dataclass
class EnhancedCheckoutConfig:
    """Configuration for enhanced checkout features"""
    
    # Feature toggles
    enhanced_notifications: FeatureToggle = FeatureToggle.ENABLED
    improved_messages: FeatureToggle = FeatureToggle.ENABLED
    progress_tracking: FeatureToggle = FeatureToggle.ENABLED
    card_expiry_alerts: FeatureToggle = FeatureToggle.ENABLED
    wallet_low_alerts: FeatureToggle = FeatureToggle.ENABLED
    follow_up_guidance: FeatureToggle = FeatureToggle.ENABLED
    analytics_tracking: FeatureToggle = FeatureToggle.BETA
    
    # Message customization
    message_tone: str = "celebratory"  # celebratory, informative, professional
    include_emojis: bool = True
    include_pro_tips: bool = True
    include_security_reminders: bool = True
    
    # Notification settings
    send_completion_notifications: bool = True
    send_progress_updates: bool = True
    send_failure_notifications: bool = True
    send_card_expiry_alerts: bool = True
    send_wallet_alerts: bool = True
    
    # Timing settings
    follow_up_delay_seconds: int = 30
    card_expiry_warning_hours: int = 2
    wallet_low_threshold_percentage: float = 0.1  # 10%
    max_tracking_age_hours: int = 24
    
    # UI settings
    show_progress_bars: bool = True
    show_loading_animations: bool = True
    show_celebration_effects: bool = True
    compact_mode: bool = False
    
    # Analytics settings
    track_user_satisfaction: bool = False
    track_completion_rates: bool = True
    track_error_recovery: bool = True
    anonymize_data: bool = True


class EnhancedCheckoutConfigManager:
    """Manager for enhanced checkout configuration"""
    
    def __init__(self):
        self.config = EnhancedCheckoutConfig()
        self._custom_messages = {}
        self._custom_notifications = {}
    
    def get_config(self) -> EnhancedCheckoutConfig:
        """Get current configuration"""
        return self.config
    
    def update_config(self, **kwargs) -> None:
        """Update configuration settings"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    logger.info(f"Updated config: {key} = {value}")
                else:
                    logger.warning(f"Unknown config key: {key}")
        except Exception as e:
            logger.error(f"Error updating config: {e}")
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """Check if a feature is enabled"""
        try:
            feature_value = getattr(self.config, feature_name, None)
            if isinstance(feature_value, FeatureToggle):
                return feature_value == FeatureToggle.ENABLED
            return bool(feature_value)
        except Exception as e:
            logger.error(f"Error checking feature {feature_name}: {e}")
            return False
    
    def set_custom_message(self, message_type: str, message_template: str) -> None:
        """Set custom message template"""
        self._custom_messages[message_type] = message_template
        logger.info(f"Set custom message for: {message_type}")
    
    def get_custom_message(self, message_type: str) -> Optional[str]:
        """Get custom message template"""
        return self._custom_messages.get(message_type)
    
    def set_custom_notification(self, notification_type: str, notification_config: Dict[str, Any]) -> None:
        """Set custom notification configuration"""
        self._custom_notifications[notification_type] = notification_config
        logger.info(f"Set custom notification for: {notification_type}")
    
    def get_custom_notification(self, notification_type: str) -> Optional[Dict[str, Any]]:
        """Get custom notification configuration"""
        return self._custom_notifications.get(notification_type)
    
    def get_message_tone_config(self) -> Dict[str, Any]:
        """Get message tone configuration"""
        tone_configs = {
            "celebratory": {
                "emojis": True,
                "enthusiasm": "high",
                "celebration_effects": True,
                "tone": "excited"
            },
            "informative": {
                "emojis": True,
                "enthusiasm": "medium",
                "celebration_effects": False,
                "tone": "helpful"
            },
            "professional": {
                "emojis": False,
                "enthusiasm": "low",
                "celebration_effects": False,
                "tone": "formal"
            }
        }
        
        return tone_configs.get(self.config.message_tone, tone_configs["informative"])
    
    def get_notification_settings(self) -> Dict[str, Any]:
        """Get notification settings"""
        return {
            "send_completion": self.config.send_completion_notifications,
            "send_progress": self.config.send_progress_updates,
            "send_failures": self.config.send_failure_notifications,
            "send_card_expiry": self.config.send_card_expiry_alerts,
            "send_wallet_alerts": self.config.send_wallet_alerts,
            "follow_up_delay": self.config.follow_up_delay_seconds,
            "card_expiry_warning": self.config.card_expiry_warning_hours,
            "wallet_threshold": self.config.wallet_low_threshold_percentage
        }
    
    def get_ui_settings(self) -> Dict[str, Any]:
        """Get UI settings"""
        return {
            "show_progress_bars": self.config.show_progress_bars,
            "show_loading_animations": self.config.show_loading_animations,
            "show_celebration_effects": self.config.show_celebration_effects,
            "compact_mode": self.config.compact_mode,
            "include_emojis": self.config.include_emojis,
            "include_pro_tips": self.config.include_pro_tips,
            "include_security_reminders": self.config.include_security_reminders
        }
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return any issues"""
        issues = []
        
        # Validate timing settings
        if self.config.follow_up_delay_seconds < 0:
            issues.append("follow_up_delay_seconds must be non-negative")
        
        if self.config.card_expiry_warning_hours < 0:
            issues.append("card_expiry_warning_hours must be non-negative")
        
        if not 0 <= self.config.wallet_low_threshold_percentage <= 1:
            issues.append("wallet_low_threshold_percentage must be between 0 and 1")
        
        if self.config.max_tracking_age_hours <= 0:
            issues.append("max_tracking_age_hours must be positive")
        
        # Validate message tone
        valid_tones = ["celebratory", "informative", "professional"]
        if self.config.message_tone not in valid_tones:
            issues.append(f"message_tone must be one of: {valid_tones}")
        
        return issues
    
    def reset_to_defaults(self) -> None:
        """Reset configuration to defaults"""
        self.config = EnhancedCheckoutConfig()
        self._custom_messages = {}
        self._custom_notifications = {}
        logger.info("Configuration reset to defaults")
    
    def export_config(self) -> Dict[str, Any]:
        """Export configuration as dictionary"""
        return {
            "config": {
                "enhanced_notifications": self.config.enhanced_notifications.value,
                "improved_messages": self.config.improved_messages.value,
                "progress_tracking": self.config.progress_tracking.value,
                "card_expiry_alerts": self.config.card_expiry_alerts.value,
                "wallet_low_alerts": self.config.wallet_low_alerts.value,
                "follow_up_guidance": self.config.follow_up_guidance.value,
                "analytics_tracking": self.config.analytics_tracking.value,
                "message_tone": self.config.message_tone,
                "include_emojis": self.config.include_emojis,
                "include_pro_tips": self.config.include_pro_tips,
                "include_security_reminders": self.config.include_security_reminders,
                "send_completion_notifications": self.config.send_completion_notifications,
                "send_progress_updates": self.config.send_progress_updates,
                "send_failure_notifications": self.config.send_failure_notifications,
                "send_card_expiry_alerts": self.config.send_card_expiry_alerts,
                "send_wallet_alerts": self.config.send_wallet_alerts,
                "follow_up_delay_seconds": self.config.follow_up_delay_seconds,
                "card_expiry_warning_hours": self.config.card_expiry_warning_hours,
                "wallet_low_threshold_percentage": self.config.wallet_low_threshold_percentage,
                "max_tracking_age_hours": self.config.max_tracking_age_hours,
                "show_progress_bars": self.config.show_progress_bars,
                "show_loading_animations": self.config.show_loading_animations,
                "show_celebration_effects": self.config.show_celebration_effects,
                "compact_mode": self.config.compact_mode,
                "track_user_satisfaction": self.config.track_user_satisfaction,
                "track_completion_rates": self.config.track_completion_rates,
                "track_error_recovery": self.config.track_error_recovery,
                "anonymize_data": self.config.anonymize_data,
            },
            "custom_messages": self._custom_messages,
            "custom_notifications": self._custom_notifications
        }
    
    def import_config(self, config_data: Dict[str, Any]) -> bool:
        """Import configuration from dictionary"""
        try:
            if "config" in config_data:
                config_dict = config_data["config"]
                for key, value in config_data["config"].items():
                    if hasattr(self.config, key):
                        # Handle enum values
                        if key in ["enhanced_notifications", "improved_messages", "progress_tracking", 
                                 "card_expiry_alerts", "wallet_low_alerts", "follow_up_guidance", "analytics_tracking"]:
                            if isinstance(value, str):
                                value = FeatureToggle(value)
                        setattr(self.config, key, value)
            
            if "custom_messages" in config_data:
                self._custom_messages = config_data["custom_messages"]
            
            if "custom_notifications" in config_data:
                self._custom_notifications = config_data["custom_notifications"]
            
            # Validate imported config
            issues = self.validate_config()
            if issues:
                logger.warning(f"Configuration validation issues: {issues}")
            
            logger.info("Configuration imported successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error importing configuration: {e}")
            return False


# Global configuration manager instance
config_manager = EnhancedCheckoutConfigManager()


def get_enhanced_checkout_config() -> EnhancedCheckoutConfigManager:
    """Get the global enhanced checkout configuration manager"""
    return config_manager


def is_enhanced_feature_enabled(feature_name: str) -> bool:
    """Check if an enhanced feature is enabled globally"""
    return config_manager.is_feature_enabled(feature_name)


def update_enhanced_checkout_config(**kwargs) -> None:
    """Update global enhanced checkout configuration"""
    config_manager.update_config(**kwargs)

