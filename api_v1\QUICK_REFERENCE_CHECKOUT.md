# API v1 Checkout - Quick Reference

## Import
```python
from api_v1.services.cart_service import get_cart_service
```

## Initialize
```python
cart_service = get_cart_service()
```

## Checkout

### Basic
```python
result = await cart_service.checkout_cart(user_id="user123")
```

### With Options
```python
result = await cart_service.checkout_cart(
    user_id="user123",
    clear_cart_on_success=True  # default
)
```

## Response

### Success
```python
if result.success:
    items_count = result.data['items_count']       # 2
    total_price = result.data['total_price']       # 23.98
    new_balance = result.data['new_balance']       # 76.02
    transaction_id = result.data['transaction_id'] # "64abc..."
    purchases = result.data['purchases']           # [...]
```

### Error
```python
if not result.success:
    error_code = result.error_code  # "INSUFFICIENT_BALANCE"
    message = result.message         # "Insufficient balance..."
```

## Error Codes
- `EMPTY_CART` → Add items
- `INSUFFICIENT_BALANCE` → Add funds
- `WALLET_NOT_FOUND` → Contact support
- `API_CHECKOUT_FAILED` → Retry
- `PROCESSING_ERROR` → Contact support

## Complete Example
```python
try:
    result = await cart_service.checkout_cart(user_id="user123")
    
    if result.success:
        print(f"✅ Purchased {result.data['items_count']} items")
        print(f"💰 New balance: ${result.data['new_balance']:.2f}")
    else:
        if result.error_code == "INSUFFICIENT_BALANCE":
            print("Please add funds")
        else:
            print(f"Error: {result.message}")
except Exception as e:
    print(f"Failed: {e}")
```

## Database Records Created

### Purchase
```python
{
    "_id": ObjectId(),
    "user_id": "user123",
    "sku": "card_12345_timestamp",
    "price": 17.99,
    "external_product_id": "12345"
}
```

### Wallet Update
```python
{
    "user_id": "user123",
    "balance": 76.02  # After deduction
}
```

### Transaction Log
```python
{
    "_id": ObjectId(),
    "user_id": "user123",
    "amount": 23.98,
    "old_balance": 100.00,
    "new_balance": 76.02
}
```

## Documentation
- Full docs: [CHECKOUT_DOCUMENTATION.md](./CHECKOUT_DOCUMENTATION.md)
- Examples: [examples/checkout_example.py](./examples/checkout_example.py)
- API docs: [CART_ENDPOINTS.md](./CART_ENDPOINTS.md)

