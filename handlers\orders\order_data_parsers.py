"""
Order Data Parsing Module

This module contains all data parsing and extraction methods for order and card data.
Handles API v1, v2, and v3 response parsing with comprehensive error handling.
"""

from __future__ import annotations

import logging
import re
import json
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)


class OrderDataParser:
    """Handles parsing and extraction of order and card data from various API responses"""
    
    def __init__(self, filter_data: Optional[Dict[str, Any]] = None):
        """
        Initialize the parser with optional filter configuration
        
        Args:
            filter_data: Filter configuration for BIN parsing
        """
        self.filter_data = filter_data or {}
    
    def extract_comprehensive_api_v3_data(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract comprehensive card information from API v3 order data.
        
        Args:
            order_data: Raw API v3 order response data
            
        Returns:
            Dictionary containing extracted card information
        """
        card_info = {}
        
        try:
            # Extract basic order information
            if "order_id" in order_data:
                card_info["order_id"] = order_data["order_id"]
            
            if "price" in order_data:
                card_info["price"] = order_data["price"]
            
            # Extract card-specific data from API v3 structure
            if "sections" in order_data:
                for section in order_data["sections"]:
                    if "tables" in section:
                        for table in section["tables"]:
                            if "rows" in table:
                                for row in table["rows"]:
                                    parsed_row = self._parse_api_v3_table_row(row)
                                    card_info.update(parsed_row)
            
            # Extract from raw_data if available (new API service format)
            raw_data = order_data.get("raw_data", {})
            if raw_data and "rows" in raw_data:
                headers = raw_data.get("headers", [])
                for row in raw_data["rows"]:
                    parsed_row = self._extract_card_data_from_api_service_row(row, headers)
                    card_info.update(parsed_row)
            
            # Extract status information
            if "status" in order_data:
                card_info["status"] = order_data["status"]
                
        except Exception as e:
            logger.warning(f"Error extracting comprehensive API v3 data: {e}")
        
        return card_info
    
    def _parse_api_v3_table_row(self, row: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Parse a single table row from API v3 response.
        
        Args:
            row: List of cell dictionaries from API v3 table
            
        Returns:
            Dictionary containing parsed data from the row
        """
        parsed_data = {}
        
        try:
            if len(row) >= 6:
                # Parse card information from first cell
                if row[0] and "text" in row[0]:
                    card_text = row[0]["text"]
                    parsed_data.update(self._parse_card_text(card_text))
                
                # Parse BIN information from second cell
                if row[1] and "text" in row[1]:
                    bin_text = row[1]["text"]
                    parsed_data.update(self._parse_bin_info_text(bin_text))
                
                # Parse price from third cell
                if row[2] and "text" in row[2]:
                    price_text = row[2]["text"]
                    if price_text and "$" in price_text:
                        try:
                            price_value = float(price_text.replace("$", "").strip())
                            parsed_data["price"] = price_value
                        except ValueError:
                            pass
                
                # Parse status from fourth cell
                if row[3] and "text" in row[3]:
                    status_text = row[3]["text"]
                    if status_text:
                        parsed_data["status"] = status_text.strip()
                
                # Parse additional info from remaining cells
                if len(row) > 5 and row[5] and "text" in row[5]:
                    address_phone = row[5]["text"]
                    parsed_data.update(self._parse_address_phone_text(address_phone))
                    
        except Exception as e:
            logger.warning(f"Error parsing API v3 table row: {e}")
        
        return parsed_data
    
    def _parse_card_text(self, card_text: str) -> Dict[str, Any]:
        """
        Parse card information from text field.
        
        Args:
            card_text: Raw card text from API response
            
        Returns:
            Dictionary containing parsed card information
        """
        parsed = {}
        
        try:
            if not card_text:
                return parsed
            
            # Extract card number pattern
            card_pattern = r'(\d{4}[\s\*]{1,4}\d{4}[\s\*]{1,4}\d{4}[\s\*]{1,4}\d{4})'
            card_match = re.search(card_pattern, card_text)
            if card_match:
                parsed["card_number"] = card_match.group(1)
            
            # Extract expiry date pattern (MM/YY or MM/YYYY)
            expiry_pattern = r'(\d{2}/\d{2,4})'
            expiry_match = re.search(expiry_pattern, card_text)
            if expiry_match:
                parsed["expiry_date"] = expiry_match.group(1)
            
            # Extract CVV pattern
            cvv_pattern = r'CVV[:\s]*(\d{3,4})'
            cvv_match = re.search(cvv_pattern, card_text, re.IGNORECASE)
            if cvv_match:
                parsed["cvv"] = cvv_match.group(1)
            
            # Extract check time left
            time_pattern = r'(\d+)\s*min'
            time_match = re.search(time_pattern, card_text)
            if time_match:
                parsed["check_time_left"] = int(time_match.group(1))
                
        except Exception as e:
            logger.warning(f"Error parsing card text '{card_text}': {e}")
        
        return parsed
    
    def _parse_address_phone_text(self, address_phone: str) -> Dict[str, Any]:
        """
        Parse address and phone information from text field.
        
        Args:
            address_phone: Raw address/phone text from API response
            
        Returns:
            Dictionary containing parsed address and phone information
        """
        parsed = {}
        
        try:
            if not address_phone:
                return parsed
            
            # Extract phone number pattern
            phone_pattern = r'(\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
            phone_match = re.search(phone_pattern, address_phone)
            if phone_match:
                parsed["phone"] = phone_match.group(1)
            
            # Extract address (everything that's not a phone number)
            address_text = re.sub(phone_pattern, '', address_phone).strip()
            if address_text and address_text.lower() != "no address":
                parsed["address"] = address_text
                
        except Exception as e:
            logger.warning(f"Error parsing address/phone text '{address_phone}': {e}")

        return parsed

    def _parse_bin_info_text(self, bin_info_text: str) -> Dict[str, Any]:
        """
        Parse BIN info text to extract country, brand, level, bank using filter configuration.

        Args:
            bin_info_text: Raw BIN info text from API response

        Returns:
            Dictionary containing parsed BIN information
        """
        result = {"bank": "Unknown Bank"}

        try:
            if not bin_info_text:
                return result

            text_lower = bin_info_text.lower()

            # Extract country information
            countries = self.filter_data.get("countries", {})
            for country_code, country_data in countries.items():
                country_name = country_data.get("name", "").lower()
                if country_name and country_name in text_lower:
                    result["country"] = country_data.get("name", "")
                    break

            # Extract brand information (Visa, Mastercard, etc.)
            brands = self.filter_data.get("brands", {})
            for brand_code, brand_name in brands.items():
                if brand_name.lower() in text_lower:
                    result["brand"] = brand_name
                    break

            # Extract level information (Classic, Gold, Platinum, etc.)
            levels = self.filter_data.get("levels", {})
            for level_code, level_name in levels.items():
                if level_name.lower() in text_lower:
                    result["level"] = level_name
                    break

            # Extract bank information
            banks = self.filter_data.get("banks", {})
            for bank_code, bank_name in banks.items():
                if bank_name.lower() in text_lower:
                    result["bank"] = bank_name
                    break

            # Fallback: try to extract bank from common patterns
            if result["bank"] == "Unknown Bank":
                bank_patterns = [
                    r'bank\s+of\s+([a-zA-Z\s]+)',
                    r'([a-zA-Z\s]+)\s+bank',
                    r'([A-Z][a-zA-Z\s]+)\s+(?:BANK|Bank)',
                ]

                for pattern in bank_patterns:
                    match = re.search(pattern, bin_info_text, re.IGNORECASE)
                    if match:
                        bank_name = match.group(1).strip()
                        if len(bank_name) > 2:  # Avoid single letters
                            result["bank"] = bank_name
                            break

            logger.debug(f"🔵 BIN parsing result: {result}")
            return result

        except Exception as e:
            logger.error(f"❌ Error parsing BIN info: {e}")
            return {"bank": "Unknown Bank"}

    def _extract_card_data_from_api_service_row(self, row: List[Any], headers: List[str]) -> Dict[str, Any]:
        """
        Extract card data from API service row using headers.

        Args:
            row: Row data from API service response
            headers: Column headers for the row

        Returns:
            Dictionary containing extracted card data
        """
        card_data = {}

        try:
            if not row or not headers:
                return card_data

            # Create mapping from headers to row values
            for i, header in enumerate(headers):
                if i < len(row) and row[i] is not None:
                    header_lower = header.lower().strip()
                    value = str(row[i]).strip()

                    # Map common header names to standardized fields
                    if "card" in header_lower and "number" in header_lower:
                        card_data["card_number"] = value
                    elif "expiry" in header_lower or "exp" in header_lower:
                        card_data["expiry_date"] = value
                    elif "cvv" in header_lower or "cvc" in header_lower:
                        card_data["cvv"] = value
                    elif "bank" in header_lower:
                        card_data["bank"] = value
                    elif "brand" in header_lower or "type" in header_lower:
                        card_data["brand"] = value
                    elif "country" in header_lower:
                        card_data["country"] = value
                    elif "level" in header_lower:
                        card_data["level"] = value
                    elif "price" in header_lower:
                        try:
                            card_data["price"] = float(value.replace("$", "").strip())
                        except ValueError:
                            pass
                    elif "status" in header_lower:
                        card_data["status"] = value
                    elif "address" in header_lower:
                        card_data["address"] = value
                    elif "phone" in header_lower:
                        card_data["phone"] = value
                    elif "email" in header_lower:
                        card_data["email"] = value
                    elif "name" in header_lower and "card" not in header_lower:
                        card_data["cardholder_name"] = value

            logger.debug(f"✅ Extracted card data fields: {list(card_data.keys())}")
            return card_data

        except Exception as e:
            logger.error(f"❌ Error extracting card data from API service row: {e}")
            return {}

    def parse_api_v3_card_text(self, text: str) -> Tuple[str, str, str]:
        """
        Parse API v3 card text to extract bank, brand, and level information.

        Args:
            text: Raw card text from API v3 response

        Returns:
            Tuple of (bank, brand, level)
        """
        try:
            if not text:
                return "Unknown Bank", "", ""

            # Try to extract structured information
            lines = text.split('\n')
            bank = brand = level = ""

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for bank information
                if any(keyword in line.lower() for keyword in ['bank', 'credit union', 'financial']):
                    bank = line

                # Look for brand information
                if any(keyword in line.lower() for keyword in ['visa', 'mastercard', 'amex', 'discover']):
                    brand = line

                # Look for level information
                if any(keyword in line.lower() for keyword in ['classic', 'gold', 'platinum', 'premium']):
                    level = line

            # Fallback: try to parse the whole text
            if not bank:
                return self._parse_scheme_type_level_v3(text)

            return bank or "Unknown Bank", brand, level

        except Exception as e:
            logger.warning(f"Error parsing API v3 card text '{text}': {e}")
            return "Unknown Bank", "", ""

    def _parse_scheme_type_level_v3(self, text: str) -> Tuple[str, str, str]:
        """
        Parse scheme_type_level field from API v3 to extract bank, brand, and level.

        Args:
            text: Raw scheme_type_level text

        Returns:
            Tuple of (bank, brand, level)
        """
        try:
            if not text:
                return "Unknown Bank", "", ""

            # Common patterns for parsing scheme_type_level
            patterns = [
                # Pattern: "BANK NAME - VISA CLASSIC"
                r'^([^-]+)\s*-\s*([^-]+)\s*-?\s*(.*)$',
                # Pattern: "VISA CLASSIC - BANK NAME"
                r'^([^-]+)\s+([^-]+)\s*-\s*(.+)$',
                # Pattern: "BANK NAME VISA CLASSIC"
                r'^(.+?)\s+(VISA|MASTERCARD|AMEX|DISCOVER)\s+(.+)$',
            ]

            for pattern in patterns:
                match = re.match(pattern, text.strip(), re.IGNORECASE)
                if match:
                    groups = [g.strip() for g in match.groups() if g]

                    # Determine which group is bank, brand, level
                    bank = brand = level = ""

                    for group in groups:
                        group_lower = group.lower()
                        if any(b in group_lower for b in ['visa', 'mastercard', 'amex', 'discover']):
                            brand = group
                        elif any(l in group_lower for l in ['classic', 'gold', 'platinum', 'premium']):
                            level = group
                        else:
                            bank = group

                    return bank or "Unknown Bank", brand, level

            # Fallback: return the whole text as bank
            return text.strip() or "Unknown Bank", "", ""

        except Exception as e:
            logger.warning(f"Error parsing scheme_type_level: {e}")
            return "Unknown Bank", "", ""

    def parse_comprehensive_api_v3_data(self, api_data: Dict[str, Any], card_id: str) -> Dict[str, Any]:
        """
        Parse comprehensive API v3 data for a specific card.

        Args:
            api_data: Full API v3 response data
            card_id: Target card ID to extract data for

        Returns:
            Dictionary containing comprehensive card information
        """
        try:
            # Check if this is the new API service response format
            raw_data = api_data.get("raw_data", {})
            if raw_data and "headers" in raw_data and "rows" in raw_data:
                logger.debug("Using API service response format")
                return self._parse_api_service_response(raw_data, card_id, api_data)

            # Fallback to demo format (sections structure)
            order_response = api_data.get("order_response", {})
            if not order_response:
                order_response = api_data

            sections = order_response.get("sections", [])
            if not sections:
                logger.warning(f"⚠️ No sections found in API v3 response")
                return {}

            # Look for the main order section with tables
            for section in sections:
                tables = section.get("tables", [])
                if not tables:
                    continue

                for table in tables:
                    rows = table.get("rows", [])
                    if not rows:
                        continue

                    # Find the row containing our card
                    for row in rows:
                        if len(row) > 0 and row[0] and "input_value" in row[0]:
                            row_card_id = row[0]["input_value"]
                            if row_card_id == card_id:
                                return self._extract_comprehensive_card_from_row(row)

            logger.warning(f"⚠️ Card {card_id} not found in API v3 response tables")
            return {}

        except Exception as e:
            logger.error(f"❌ Error parsing comprehensive API v3 data: {e}")
            return {}

    def _parse_api_service_response(self, raw_data: Dict[str, Any], card_id: str, full_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse API service response format.

        Args:
            raw_data: Raw data section from API response
            card_id: Target card ID
            full_response: Full API response for context

        Returns:
            Dictionary containing parsed card data
        """
        try:
            headers = raw_data.get("headers", [])
            rows = raw_data.get("rows", [])

            if not headers or not rows:
                logger.warning("⚠️ Missing headers or rows in API service response")
                return {}

            # Find the row containing our card
            for row in rows:
                if len(row) > 0 and str(row[0]) == card_id:
                    card_data = self._extract_card_data_from_api_service_row(row, headers)

                    # Add order-level data
                    order_data = self._extract_order_data_from_api_service(full_response)
                    card_data.update(order_data)

                    return card_data

            logger.warning(f"⚠️ Card {card_id} not found in API service response rows")
            return {}

        except Exception as e:
            logger.error(f"❌ Error parsing API service response: {e}")
            return {}

    def _extract_order_data_from_api_service(self, full_response: Dict[str, Any]) -> Dict[str, Any]:
        """Extract order-level data from API service response."""
        try:
            return {
                "order_id": full_response.get("order_id", ""),
                "item_count": full_response.get("item_count", 0),
            }
        except Exception as e:
            logger.error(f"❌ Error extracting order data from API service: {e}")
            return {}

    def _extract_comprehensive_card_from_row(self, row: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract comprehensive card data from a table row.

        Args:
            row: Table row from API v3 response

        Returns:
            Dictionary containing comprehensive card data
        """
        try:
            card_data = {}

            if len(row) >= 6:
                # Cell 0: Card ID and basic info
                if row[0] and "text" in row[0]:
                    card_text = row[0]["text"]
                    card_data.update(self._parse_full_card_text(card_text))

                # Cell 1: BIN information
                if row[1] and "text" in row[1]:
                    bin_text = row[1]["text"]
                    card_data.update(self._parse_comprehensive_bin_info(bin_text))

                # Cell 2: Price
                if row[2] and "text" in row[2]:
                    price_text = row[2]["text"]
                    if price_text and "$" in price_text:
                        try:
                            price_value = float(price_text.replace("$", "").strip())
                            card_data["price"] = price_value
                        except ValueError:
                            pass

                # Cell 3: Status
                if row[3] and "text" in row[3]:
                    status_text = row[3]["text"]
                    if status_text:
                        card_data["status"] = status_text.strip()

                # Cell 4: Additional features
                if len(row) > 4 and row[4] and "text" in row[4]:
                    features_text = row[4]["text"]
                    card_data["features"] = self._extract_card_features(features_text)

                # Cell 5: Contact information
                if len(row) > 5 and row[5] and "text" in row[5]:
                    contact_text = row[5]["text"]
                    card_data.update(self._parse_comprehensive_contact_info(contact_text))

            return card_data

        except Exception as e:
            logger.error(f"Error extracting comprehensive card from row: {e}")
            return {}

    def _parse_full_card_text(self, card_text: str) -> Dict[str, Any]:
        """
        Parse full card text with comprehensive information extraction.

        Args:
            card_text: Raw card text from API response

        Returns:
            Dictionary containing parsed card information
        """
        parsed = {}

        try:
            if not card_text:
                return parsed

            # Extract card number (various formats)
            card_patterns = [
                r'(\d{4}[\s\*\-]{1,4}\d{4}[\s\*\-]{1,4}\d{4}[\s\*\-]{1,4}\d{4})',
                r'(\d{16})',
                r'(\d{4}\s\d{4}\s\d{4}\s\d{4})',
            ]

            for pattern in card_patterns:
                match = re.search(pattern, card_text)
                if match:
                    parsed["card_number"] = match.group(1)
                    break

            # Extract expiry date (various formats)
            expiry_patterns = [
                r'(\d{2}/\d{2,4})',
                r'(\d{2}-\d{2,4})',
                r'EXP[:\s]*(\d{2}/\d{2,4})',
            ]

            for pattern in expiry_patterns:
                match = re.search(pattern, card_text, re.IGNORECASE)
                if match:
                    parsed["expiry_date"] = match.group(1)
                    break

            # Extract CVV
            cvv_patterns = [
                r'CVV[:\s]*(\d{3,4})',
                r'CVC[:\s]*(\d{3,4})',
                r'Security[:\s]*(\d{3,4})',
            ]

            for pattern in cvv_patterns:
                match = re.search(pattern, card_text, re.IGNORECASE)
                if match:
                    parsed["cvv"] = match.group(1)
                    break

            # Extract cardholder name
            name_patterns = [
                r'Name[:\s]*([A-Z\s]+)',
                r'Holder[:\s]*([A-Z\s]+)',
                r'Cardholder[:\s]*([A-Z\s]+)',
            ]

            for pattern in name_patterns:
                match = re.search(pattern, card_text, re.IGNORECASE)
                if match:
                    name = match.group(1).strip()
                    if len(name) > 2:  # Avoid single letters
                        parsed["cardholder_name"] = name
                    break

            return parsed

        except Exception as e:
            logger.error(f"Error parsing full card text '{card_text}': {e}")
            return {}

    def _parse_comprehensive_bin_info(self, bin_text: str) -> Dict[str, Any]:
        """
        Parse comprehensive BIN information with enhanced extraction.

        Args:
            bin_text: Raw BIN text from API response

        Returns:
            Dictionary containing parsed BIN information
        """
        parsed = {}

        try:
            if not bin_text:
                return parsed

            # Use filter data for structured parsing
            result = self._parse_bin_info_text(bin_text)
            parsed.update(result)

            # Additional extraction for comprehensive data
            text_lower = bin_text.lower()

            # Extract BIN number
            bin_pattern = r'BIN[:\s]*(\d{6,8})'
            bin_match = re.search(bin_pattern, bin_text, re.IGNORECASE)
            if bin_match:
                parsed["bin"] = bin_match.group(1)

            # Extract issuer information
            if "issuer" in text_lower:
                issuer_pattern = r'issuer[:\s]*([^\n\r]+)'
                issuer_match = re.search(issuer_pattern, bin_text, re.IGNORECASE)
                if issuer_match:
                    parsed["issuer"] = issuer_match.group(1).strip()

            return parsed

        except Exception as e:
            logger.error(f"Error parsing comprehensive BIN info '{bin_text}': {e}")
            return {}

    def _parse_comprehensive_contact_info(self, contact_text: str) -> Dict[str, Any]:
        """
        Parse comprehensive contact information.

        Args:
            contact_text: Raw contact text from API response

        Returns:
            Dictionary containing parsed contact information
        """
        parsed = {}

        try:
            if not contact_text:
                return parsed

            # Extract email
            email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            email_match = re.search(email_pattern, contact_text)
            if email_match:
                parsed["email"] = email_match.group(1)

            # Extract phone number (various formats)
            phone_patterns = [
                r'(\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                r'(\d{3}-\d{3}-\d{4})',
                r'(\(\d{3}\)\s?\d{3}-\d{4})',
            ]

            for pattern in phone_patterns:
                match = re.search(pattern, contact_text)
                if match:
                    parsed["phone"] = match.group(1)
                    break

            # Extract address (remaining text after removing email and phone)
            address_text = contact_text
            if "email" in parsed:
                address_text = address_text.replace(parsed["email"], "")
            if "phone" in parsed:
                address_text = address_text.replace(parsed["phone"], "")

            address_text = address_text.strip()
            if address_text and len(address_text) > 5:  # Meaningful address
                parsed["address"] = address_text

            return parsed

        except Exception as e:
            logger.error(f"Error parsing comprehensive contact info '{contact_text}': {e}")
            return {}

    def _extract_card_features(self, features_text: str) -> List[str]:
        """
        Extract card features from text.

        Args:
            features_text: Raw features text from API response

        Returns:
            List of extracted features
        """
        try:
            if not features_text:
                return []

            # Common feature keywords
            feature_keywords = [
                "zip", "address", "phone", "email", "cvv", "refundable",
                "3d secure", "verified", "premium", "instant", "express"
            ]

            features = []
            text_lower = features_text.lower()

            for keyword in feature_keywords:
                if keyword in text_lower:
                    features.append(keyword.title())

            return features

        except Exception as e:
            logger.error(f"Error extracting card features: {e}")
            return []
