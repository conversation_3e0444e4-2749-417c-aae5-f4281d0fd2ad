# Python cache files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Session and cache files
session_cookies.json
*.session

# API response files (temporary data)
*_response.json
*_result.json
*_output.txt

# Large data files (scraped content)
*_forms.json
shop_form.json

# Logs
*.log
logs/

# OS files
.DS_Store
Thumbs.db

# Test files
test.txt
temp.*