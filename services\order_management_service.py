"""
Order Management Service - Centralized database operations for user orders

This service provides a clean, maintainable interface for all order-related
database operations, ensuring consistency and performance.
"""

from __future__ import annotations

from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from bson import ObjectId

from database.connection import get_collection, database_transaction
from models import Purchase, PurchaseStatus

from utils.central_logger import get_logger

logger = get_logger()


class OrderManagementService:
    """
    Centralized service for managing user orders and purchases.
    
    Benefits:
    - Single source of truth for order queries
    - Optimized database operations
    - Consistent error handling
    - Easy to test and maintain
    - Separation of concerns
    """
    
    def __init__(self):
        self.purchases = get_collection("purchases")
        logger.info("OrderManagementService initialized")
    
    async def get_user_orders_paginated(
        self,
        user_id: str,
        page: int = 1,
        page_size: int = 5,
        status_filter: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: int = -1  # -1 for descending, 1 for ascending
    ) -> Dict[str, Any]:
        """
        Get paginated user orders with optional filtering.
        
        Args:
            user_id: User's database ID
            page: Page number (1-indexed)
            page_size: Number of orders per page
            status_filter: Optional status to filter by (e.g., "Live", "Refunded")
            sort_by: Field to sort by
            sort_order: Sort direction (-1 desc, 1 asc)
            
        Returns:
            {
                "orders": [...],  # List of orders for current page
                "total_orders": int,  # Total count
                "total_pages": int,  # Total pages
                "current_page": int,  # Current page number
                "has_next": bool,  # Has next page
                "has_prev": bool,  # Has previous page
            }
        """
        try:
            # Build query
            query = {"user_id": user_id}
            if status_filter:
                query["status"] = status_filter
            
            # Get total count (optimized with count_documents)
            total_orders = await self.purchases.count_documents(query)
            
            if total_orders == 0:
                return {
                    "orders": [],
                    "total_orders": 0,
                    "total_pages": 0,
                    "current_page": page,
                    "has_next": False,
                    "has_prev": False,
                }
            
            # Calculate pagination
            total_pages = max(1, (total_orders + page_size - 1) // page_size)
            page = max(1, min(page, total_pages))  # Clamp page to valid range
            skip = (page - 1) * page_size
            
            # Fetch orders for current page
            cursor = (
                self.purchases.find(query)
                .sort(sort_by, sort_order)
                .skip(skip)
                .limit(page_size)
            )
            orders = await cursor.to_list(page_size)
            
            return {
                "orders": orders,
                "total_orders": total_orders,
                "total_pages": total_pages,
                "current_page": page,
                "has_next": page < total_pages,
                "has_prev": page > 1,
            }
            
        except Exception as e:
            logger.error(f"Error fetching paginated orders for user {user_id}: {e}")
            raise
    
    async def get_user_order_statistics(self, user_id: str) -> Dict[str, Any]:
        """
        Get aggregated statistics for user's orders.
        
        Uses MongoDB aggregation pipeline for efficiency (no need to fetch all docs).
        
        Returns:
            {
                "total_orders": int,
                "total_spent": float,
                "active_cards": int,
                "refunded_cards": int,
                "status_breakdown": {"Live": 5, "Refunded": 2, ...},
                "avg_order_value": float,
            }
        """
        try:
            pipeline = [
                {"$match": {"user_id": user_id}},
                {
                    "$group": {
                        "_id": None,
                        "total_orders": {"$sum": 1},
                        "total_spent": {"$sum": "$price"},
                        "statuses": {"$push": "$status"},
                        "prices": {"$push": "$price"},
                    }
                },
            ]
            
            result = await self.purchases.aggregate(pipeline).to_list(1)
            
            if not result:
                return {
                    "total_orders": 0,
                    "total_spent": 0.0,
                    "active_cards": 0,
                    "refunded_cards": 0,
                    "status_breakdown": {},
                    "avg_order_value": 0.0,
                }
            
            data = result[0]
            statuses = data.get("statuses", [])
            prices = data.get("prices", [])
            
            # Count status breakdown
            status_breakdown = {}
            active_count = 0
            refunded_count = 0
            
            for status in statuses:
                status_lower = (status or "unknown").lower()
                status_breakdown[status] = status_breakdown.get(status, 0) + 1
                
                if status_lower in ["refunded", "expired", "invalid"]:
                    refunded_count += 1
                else:
                    active_count += 1
            
            total_orders = data.get("total_orders", 0)
            total_spent = float(data.get("total_spent", 0.0))
            avg_order_value = total_spent / total_orders if total_orders > 0 else 0.0
            
            return {
                "total_orders": total_orders,
                "total_spent": total_spent,
                "active_cards": active_count,
                "refunded_cards": refunded_count,
                "status_breakdown": status_breakdown,
                "avg_order_value": avg_order_value,
            }
            
        except Exception as e:
            logger.error(f"Error fetching order statistics for user {user_id}: {e}")
            raise
    
    async def get_order_by_card_id(
        self,
        user_id: str,
        card_id: str | int
    ) -> Optional[Dict[str, Any]]:
        """
        Get order by card ID (user-specific) with enhanced field support.

        Args:
            user_id: User's database ID
            card_id: Card ID (can be string or int for different API versions)

        Returns:
            Order document or None if not found
        """
        try:
            logger.debug(f"Searching for order with card_id={card_id}, user_id={user_id}")
            
            # Enhanced query that checks both new and legacy fields
            try:
                card_id_int = int(card_id)
                query = {
                    "user_id": user_id,
                    "$or": [
                        # New enhanced fields
                        {"external_product_id": str(card_id)},
                        {"external_product_id": card_id_int},
                        # Legacy metadata fields
                        {"metadata.card_id": card_id_int},
                        {"metadata.card_id": card_id},
                        # SKU-based lookup
                        {"sku": f"card_{card_id}"},
                        {"sku": f"dump_{card_id}"},  # Support dumps too
                    ],
                }
                logger.debug(f"Using integer card_id query: {query}")
            except (ValueError, TypeError):
                query = {
                    "user_id": user_id,
                    "$or": [
                        # New enhanced fields
                        {"external_product_id": str(card_id)},
                        # Legacy metadata fields
                        {"metadata.card_id": card_id},
                        # SKU-based lookup
                        {"sku": f"card_{card_id}"},
                        {"sku": f"dump_{card_id}"},  # Support dumps too
                    ],
                }
                logger.debug(f"Using string card_id query: {query}")

            order = await self.purchases.find_one(query, sort=[("created_at", -1)])
            
            if order:
                logger.debug(f"Found order: {order.get('_id')} with external_product_id={order.get('external_product_id')}")
            else:
                logger.debug(f"No order found for card_id={card_id}, user_id={user_id}")
                # Log some debugging info about what orders exist for this user
                user_orders = await self.purchases.find({"user_id": user_id}).limit(5).to_list(5)
                if user_orders:
                    logger.debug(f"User {user_id} has {len(user_orders)} orders with external_product_ids: {[o.get('external_product_id') for o in user_orders]}")
                else:
                    logger.debug(f"User {user_id} has no orders at all")
            
            return order

        except Exception as e:
            logger.error(f"Error fetching order for card {card_id}: {e}")
            return None
    
    async def get_order_by_external_id(
        self,
        user_id: str,
        external_order_id: str | int
    ) -> Optional[Dict[str, Any]]:
        """
        Get order by external order ID (from the API).

        Args:
            user_id: User's database ID
            external_order_id: External order ID from the API (e.g., product_id from API v1)

        Returns:
            Order document or None if not found
        """
        try:
            logger.debug(f"Searching for order with external_order_id={external_order_id}, user_id={user_id}")
            
            # Try to convert to int if possible for better matching
            try:
                external_order_id_int = int(external_order_id)
                query = {
                    "user_id": user_id,
                    "$or": [
                        {"external_order_id": str(external_order_id)},
                        {"external_order_id": external_order_id_int},
                        {"external_product_id": str(external_order_id)},
                        {"external_product_id": external_order_id_int},
                        {"metadata.external_order_id": str(external_order_id)},
                        {"metadata.external_order_id": external_order_id_int},
                    ],
                }
            except (ValueError, TypeError):
                query = {
                    "user_id": user_id,
                    "$or": [
                        {"external_order_id": str(external_order_id)},
                        {"external_product_id": str(external_order_id)},
                        {"metadata.external_order_id": str(external_order_id)},
                    ],
                }
            
            order = await self.purchases.find_one(query, sort=[("created_at", -1)])
            
            if order:
                logger.debug(f"Found order: {order.get('_id')} with external_order_id={order.get('external_order_id')}")
            else:
                logger.debug(f"No order found for external_order_id={external_order_id}, user_id={user_id}")
            
            return order

        except Exception as e:
            logger.error(f"Error fetching order by external_order_id {external_order_id}: {e}")
            return None
    
    async def update_order_status(
        self,
        user_id: str,
        card_id: str | int,
        status: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update order status and optionally add more data.
        
        Args:
            user_id: User's database ID
            card_id: Card ID
            status: New status (e.g., "Live", "Refunded", "Checked")
            additional_data: Optional dict of additional fields to update
            
        Returns:
            True if updated, False if not found
        """
        try:
            # Build query (same as get_order_by_card_id)
            try:
                card_id_int = int(card_id)
                query = {
                    "user_id": user_id,
                    "$or": [
                        {"metadata.card_id": card_id_int},
                        {"metadata.card_id": card_id},
                        {"sku": f"card_{card_id}"},
                    ],
                }
            except (ValueError, TypeError):
                query = {
                    "user_id": user_id,
                    "$or": [
                        {"metadata.card_id": card_id},
                        {"sku": f"card_{card_id}"},
                    ],
                }
            
            # Build update document
            update_doc = {
                "status": status,
                "updated_at": datetime.now(timezone.utc),
            }
            
            if additional_data:
                update_doc.update(additional_data)
            
            result = await self.purchases.update_one(
                query,
                {"$set": update_doc}
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated order status for card {card_id} to {status}")
                return True
            else:
                logger.warning(f"No order found to update for card {card_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating order status for card {card_id}: {e}")
            return False
    
    async def mark_card_as_viewed(
        self,
        user_id: str,
        card_id: str | int
    ) -> bool:
        """
        Mark a card as viewed by the user.
        
        Sets:
        - isviewed = 1
        - viewedAt = current timestamp
        - metadata.is_viewed = True
        - metadata.viewed_at = current timestamp
        
        Returns:
            True if marked, False if not found
        """
        try:
            now = datetime.now(timezone.utc)
            
            additional_data = {
                "isviewed": 1,
                "viewedAt": now.isoformat(),
                "is_viewed": True,  # Add root level flag
                "viewed_at": now.isoformat(),
                "metadata.is_viewed": True,
                "metadata.viewed_at": now.isoformat(),
            }
            
            # Use the existing update method (but don't change status)
            try:
                card_id_int = int(card_id)
                query = {
                    "user_id": user_id,
                    "$or": [
                        {"metadata.card_id": card_id_int},
                        {"metadata.card_id": card_id},
                        {"sku": f"card_{card_id}"},
                    ],
                }
            except (ValueError, TypeError):
                query = {
                    "user_id": user_id,
                    "$or": [
                        {"metadata.card_id": card_id},
                        {"sku": f"card_{card_id}"},
                    ],
                }
            
            result = await self.purchases.update_one(
                query,
                {"$set": additional_data}
            )
            
            if result.modified_count > 0:
                logger.debug(f"Marked card {card_id} as viewed for user {user_id}")
                return True
            else:
                logger.warning(f"No order found to mark as viewed for card {card_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error marking card as viewed: {e}")
            return False
    
    async def mark_card_as_checked(
        self,
        user_id: str,
        card_id: str | int,
        check_status: str
    ) -> bool:
        """
        Mark a card as checked with the result.
        
        Sets:
        - checkedAt = current timestamp
        - metadata.checked_at = current timestamp
        - metadata.check_status = check result
        - status = check result
        
        Returns:
            True if marked, False if not found
        """
        try:
            now = datetime.now(timezone.utc)
            
            additional_data = {
                "checkedAt": now.isoformat(),
                "metadata.checked_at": now.isoformat(),
                "metadata.check_status": check_status,
                "status": check_status,  # Update main status field
            }
            
            return await self.update_order_status(
                user_id=user_id,
                card_id=card_id,
                status=check_status,
                additional_data=additional_data
            )
            
        except Exception as e:
            logger.error(f"Error marking card as checked: {e}")
            return False
    
    async def get_unviewed_orders(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all unviewed orders for a user.
        
        Returns:
            List of unviewed order documents
        """
        try:
            query = {
                "user_id": user_id,
                "$or": [
                    {"isviewed": {"$ne": 1}},
                    {"isviewed": {"$exists": False}},
                    {"viewedAt": {"$exists": False}},
                ]
            }
            
            cursor = self.purchases.find(query).sort("created_at", -1)
            unviewed = await cursor.to_list(None)
            
            logger.debug(f"Found {len(unviewed)} unviewed orders for user {user_id}")
            return unviewed
            
        except Exception as e:
            logger.error(f"Error fetching unviewed orders: {e}")
            return []
    
    async def get_orders_by_status(
        self,
        user_id: str,
        status: str
    ) -> List[Dict[str, Any]]:
        """
        Get all orders with a specific status.

        Args:
            user_id: User's database ID
            status: Status to filter by

        Returns:
            List of matching order documents
        """
        try:
            query = {
                "user_id": user_id,
                "status": status
            }

            cursor = self.purchases.find(query).sort("created_at", -1)
            orders = await cursor.to_list(None)

            return orders

        except Exception as e:
            logger.error(f"Error fetching orders by status {status}: {e}")
            return []

    async def get_orders_by_api_version(
        self,
        user_id: str,
        api_version: str
    ) -> List[Dict[str, Any]]:
        """
        Get all orders for a specific API version.

        Args:
            user_id: User's database ID
            api_version: API version to filter by (v1, v2, v3)

        Returns:
            List of matching order documents
        """
        try:
            # Normalize API version
            normalized_version = self._normalize_api_version(api_version)

            query = {
                "user_id": user_id,
                "$or": [
                    {"api_version": normalized_version},
                    {"api_version": api_version},  # Also check original format
                    {"metadata.api_version": normalized_version},
                    {"metadata.api_version": api_version},
                ]
            }

            cursor = self.purchases.find(query).sort("created_at", -1)
            orders = await cursor.to_list(None)

            logger.info(f"Found {len(orders)} orders for API version {api_version}")
            return orders

        except Exception as e:
            logger.error(f"Error fetching orders by API version {api_version}: {e}")
            return []

    async def get_orders_by_product_type(
        self,
        user_id: str,
        product_type: str
    ) -> List[Dict[str, Any]]:
        """
        Get all orders for a specific product type (card or dump).

        Args:
            user_id: User's database ID
            product_type: Product type to filter by ("card" or "dump")

        Returns:
            List of matching order documents
        """
        try:
            query = {
                "user_id": user_id,
                "$or": [
                    {"product_type": product_type},
                    {"metadata.product_type": product_type},
                    {"sku": {"$regex": f"^{product_type}_"}},  # SKU-based detection
                ]
            }

            cursor = self.purchases.find(query).sort("created_at", -1)
            orders = await cursor.to_list(None)

            logger.info(f"Found {len(orders)} orders for product type {product_type}")
            return orders

        except Exception as e:
            logger.error(f"Error fetching orders by product type {product_type}: {e}")
            return []

    def _normalize_api_version(self, api_version: str) -> str:
        """Normalize API version string to v1, v2, or v3"""
        if not api_version:
            return "v1"

        version_map = {
            "v1": "v1", "base1": "v1", "api_v1": "v1",
            "v2": "v2", "base2": "v2", "api_v2": "v2",
            "v3": "v3", "base3": "v3", "api_v3": "v3"
        }
        return version_map.get(api_version.lower(), "v1")


# Singleton instance
_order_service_instance: Optional[OrderManagementService] = None


def get_order_management_service() -> OrderManagementService:
    """Get singleton instance of OrderManagementService"""
    global _order_service_instance
    if _order_service_instance is None:
        _order_service_instance = OrderManagementService()
    return _order_service_instance
