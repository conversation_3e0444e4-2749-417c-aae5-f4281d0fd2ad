#!/usr/bin/env python3
"""
SOCKS Connection Recovery Utility

Provides utilities to handle SOCKS connection failures and recovery.
"""

import time
import socket
from typing import Optional
import requests

from utils.central_logger import get_logger

logger = get_logger()


class SOCKSConnectionRecovery:
    """
    Utility class to handle SOCKS connection recovery and diagnostics.

    This class provides methods to detect and recover from SOCKS connection
    failures that can occur with Tor connections.
    """

    def __init__(self, socks_url: str = "socks5h://127.0.0.1:9150"):
        """
        Initialize SOCKS recovery utility.

        Args:
            socks_url: SOCKS proxy URL to use
        """
        self.socks_url = socks_url
        # Parse host and port from SOCKS URL
        if "://" in socks_url:
            parts = socks_url.split("://")[1].split(":")
            self.socks_host = parts[0]
            self.socks_port = int(parts[1])
        else:
            self.socks_host = "127.0.0.1"
            self.socks_port = 9150

    def is_socks_accessible(self, timeout: float = 2.0) -> bool:
        """
        Check if SOCKS proxy is accessible.

        Args:
            timeout: Connection timeout

        Returns:
            True if SOCKS proxy is accessible
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((self.socks_host, self.socks_port))
            sock.close()
            return result == 0
        except Exception:
            return False

    def recover_session(self, session: requests.Session, base_url: str) -> bool:
        """
        Attempt to recover a failed SOCKS session.

        Args:
            session: Session that needs recovery
            base_url: Base URL for testing

        Returns:
            True if recovery successful
        """
        logger.info(f"🔄 Attempting SOCKS connection recovery...")

        # Step 1: Check if SOCKS is accessible
        if not self.is_socks_accessible():
            logger.error(f"❌ SOCKS proxy {self.socks_url} is not accessible")
            return False

        # Step 2: Clear any existing adapters and create new ones
        try:
            session.adapters.clear()

            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry

            # More aggressive retry strategy for recovery
            retry_strategy = Retry(
                total=5,
                status_forcelist=[429, 500, 502, 503, 504],
                backoff_factor=2,
                raise_on_status=False,
            )

            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=5,  # Smaller pool during recovery
                pool_maxsize=10,
                pool_block=False,
            )

            session.mount("http://", adapter)
            session.mount("https://", adapter)

            # Step 3: Reconfigure proxy settings
            session.proxies.update({"http": self.socks_url, "https": self.socks_url})
            session.trust_env = False

            # Step 4: Test with a simple request
            test_url = f"{base_url}/login"
            logger.info(f"🔍 Testing recovery with: {test_url}")
            
            # Use longer timeout for .onion domains
            timeout = 30 if ".onion" in base_url else 15
            response = session.get(test_url, timeout=timeout)

            if response.status_code == 200:
                logger.info(f"✅ SOCKS connection recovery successful!")
                return True
            else:
                logger.warning(
                    f"⚠️ Recovery test returned status: {response.status_code}"
                )
                return False

        except Exception as e:
            logger.error(f"❌ SOCKS connection recovery failed: {e}")
            return False

    def wait_for_tor_recovery(self, max_wait: int = 30) -> bool:
        """
        Wait for Tor to become accessible.

        Args:
            max_wait: Maximum wait time in seconds

        Returns:
            True if Tor becomes accessible
        """
        logger.info(f"⏳ Waiting for Tor to become accessible (max {max_wait}s)...")

        start_time = time.time()
        while time.time() - start_time < max_wait:
            if self.is_socks_accessible():
                logger.info(f"✅ Tor is now accessible")
                return True

            # Use asyncio.sleep for non-blocking wait
            import asyncio

            try:
                asyncio.sleep(0.5)  # Shorter wait time for better responsiveness
            except RuntimeError:
                time.sleep(0.5)  # Fallback for non-async contexts

        logger.error(f"❌ Tor did not become accessible within {max_wait}s")
        return False


def handle_socks_error(
    session: requests.Session, base_url: str, socks_url: str
) -> bool:
    """
    Handle SOCKS connection errors with automatic recovery.

    Args:
        session: Session that encountered the error
        base_url: Base URL for testing
        socks_url: SOCKS proxy URL

    Returns:
        True if recovery was successful
    """
    recovery = SOCKSConnectionRecovery(socks_url)

    # First, wait a bit for Tor to recover
    if not recovery.is_socks_accessible():
        logger.info("🔄 SOCKS proxy not accessible, waiting for recovery...")
        if not recovery.wait_for_tor_recovery(15):
            return False

    # Attempt session recovery
    return recovery.recover_session(session, base_url)


# Add this to the shared session manager for automatic recovery
def add_socks_recovery_to_session_manager():
    """
    Monkey-patch the SharedSessionManager to add automatic SOCKS recovery.
    """
    from api_v3.auth.shared_session import SharedSessionManager

    original_get_or_create = SharedSessionManager.get_or_create_session

    def get_or_create_session_with_recovery(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        """Enhanced version with SOCKS recovery."""
        try:
            return original_get_or_create(
                self, base_url, username, password, use_socks_proxy, socks_url
            )
        except Exception as e:
            if "SOCKS" in str(e) and use_socks_proxy:
                logger.warning(f"🔄 SOCKS error detected, attempting recovery: {e}")

                # Clear any cached sessions that might be broken
                session_key = self.get_session_key(base_url, username)
                if session_key in self._sessions:
                    del self._sessions[session_key]

                # Wait a moment and retry - optimized timing
                time.sleep(1.5)  # Reduced from 2 seconds for better performance
                try:
                    return original_get_or_create(
                        self, base_url, username, password, use_socks_proxy, socks_url
                    )
                except Exception as e2:
                    logger.error(f"❌ SOCKS recovery failed: {e2}")
                    raise
            else:
                raise

    # Apply the monkey patch
    SharedSessionManager.get_or_create_session = get_or_create_session_with_recovery
    logger.info("🔧 Enhanced SharedSessionManager with SOCKS recovery")


if __name__ == "__main__":
    # Test SOCKS recovery
    recovery = SOCKSConnectionRecovery()
    logger.info(f"SOCKS accessible: {recovery.is_socks_accessible()}")
