"""
Modern Loading Animation System for Telegram Bot

Provides sophisticated loading screens and animations for enhanced user experience
during API calls, data processing, and other async operations.

This module focuses on the LoadingStages class for concurrent loading animations
that show smooth progress while actual work is being performed in the background.
"""

import asyncio
from typing import List, Dict, Any, Awaitable, TypeVar
from dataclasses import dataclass

from aiogram.types import CallbackQuery
from utils.central_logger import get_logger

logger = get_logger()

# Type variables for better type safety
T = TypeVar('T')

# Export all stage configurations and utility functions for easy importing
__all__ = [
    'LoadingStages', 'AnimationConfig', 'get_animation_runner',
    'UNMASK_STAGES', 'CHECK_STAGES', 'ADMIN_STAGES', 'BROWSE_STAGES', 
    'FILTER_STAGES', 'PURCHASE_STAGES', 'CART_STAGES', 'DOWNLOAD_STAGES',
    'LOGIN_STAGES', 'WALLET_STAGES', 'SEARCH_STAGES', 'API_MANAGEMENT_STAGES',
    'PRODUCT_STAGES', 'HISTORY_STAGES', 'GENERAL_STAGES',
    'ORDER_VIEW_STAGES', 'ORDER_LIST_STAGES', 'CARD_DOWNLOAD_STAGES',
    'CARD_CHECK_STAGES', 'RECENT_CARDS_STAGES',
    'get_stages_for_operation', 'run_loading_animation'
]


@dataclass
class AnimationConfig:
    """
    Configuration for loading animations.
    
    Note: Currently used for compatibility with existing imports.
    The main loading system uses LoadingStages.run_concurrent_loading()
    which doesn't require this configuration class.
    """
    duration: float = 3.0
    frame_interval: float = 0.5
    style: str = "modern"
    show_progress: bool = True
    auto_cleanup: bool = True


def get_animation_runner():
    """
    Compatibility function for existing imports.
    
    Note: AnimationRunner has been removed in favor of the simplified
    LoadingStages.run_concurrent_loading() method.
    """
    raise NotImplementedError(
        "AnimationRunner has been deprecated. Use LoadingStages.run_concurrent_loading() instead."
    )


class LoadingStages:

    """
    Multi-stage loading system with realistic progress visualization.
    
    Provides smooth loading animations that run concurrently with actual work,
    ensuring users see consistent progress regardless of work completion time.
    """
    
    @staticmethod
    async def run_concurrent_loading(
        callback: CallbackQuery,
        stages: List[Dict[str, Any]],
        work_coroutine: Awaitable[T],
        operation_name: str = "Operation",
        fallback_message: str = "Processing...",
        send_new_message: bool = True
    ) -> T:
        """
        Execute work with simple progress animation (10% + 5% per second).
        
        Args:
            callback: Telegram callback query
            stages: List of stage dicts with 'text' and 'emoji'
            work_coroutine: Async operation to execute
            operation_name: Name for logging
            fallback_message: Error message if animation fails
            send_new_message: If True, send new message; if False, edit existing
            
        Returns:
            Result from work_coroutine
        """
        if not stages:
            return await work_coroutine
        
        # Answer callback to prevent timeout
        try:
            await callback.answer()
        except:
            pass
        
        # Start work
        work_task = asyncio.create_task(work_coroutine)
        
        # Create initial loading message
        loading_message = None
        if send_new_message:
            try:
                text = f"{stages[0]['emoji']} **{stages[0]['text']}**\n\n`[█░░░░░░░░░]` 10%"
                loading_message = await callback.message.answer(text, parse_mode="Markdown")
            except Exception as e:
                logger.error(f"{operation_name}: Failed to create message: {e}")
                return await work_task
        
        try:
            start_time = asyncio.get_event_loop().time()
            last_update = start_time
            updates = 0
            
            # Animation loop
            while True:
                await asyncio.sleep(0.1)  # Check every 100ms
                
                now = asyncio.get_event_loop().time()
                elapsed = now - start_time
                
                # Update every 1 second OR first update after 0.5s
                should_update = (updates == 0 and elapsed >= 0.5) or (now - last_update >= 1.0)
                
                if should_update:
                    updates += 1
                    
                    # Progress: 10% + 5% per second, capped at 95%
                    progress = min(0.95, 0.10 + (0.05 * elapsed))
                    percentage = int(progress * 100)
                    
                    # Select stage based on progress
                    stage_idx = min(int((progress - 0.10) / 0.85 * len(stages)), len(stages) - 1)
                    stage = stages[max(0, stage_idx)]
                    
                    # Create progress bar
                    filled = int(progress * 10)
                    bar = '█' * filled + '░' * (10 - filled)
                    
                    # Update message
                    text = f"{stage['emoji']} **{stage['text']}**\n\n`[{bar}]` {percentage}%"
                    msg = loading_message if loading_message else callback.message
                    
                    try:
                        await msg.edit_text(text, parse_mode="Markdown")
                        last_update = now
                    except:
                        pass  # Ignore update errors
                
                # Exit after at least one update and work is done
                if work_task.done() and updates > 0:
                    break
            
            # Get result
            result = await work_task
            
            # Clean up loading message
            if loading_message:
                try:
                    await loading_message.delete()
                except:
                    pass
            
            return result
            
        except Exception as e:
            logger.error(f"{operation_name}: Error: {e}")
            
            # Cancel work if needed
            if not work_task.done():
                work_task.cancel()
                try:
                    await work_task
                except:
                    pass
            
            # Clean up
            if loading_message:
                try:
                    await loading_message.delete()
                except:
                    pass
            
            raise



# Predefined Stage Configurations
# Each stage contains: text (display message), emoji (visual indicator), duration_ratio (timing weight)

# Security & Authentication Operations
UNMASK_STAGES = [
    {"text": "Authenticating Session", "emoji": "🔐", "duration_ratio": 1.0},
    {"text": "Connecting to Secure Vault", "emoji": "🌐", "duration_ratio": 1.1},
    {"text": "Decrypting Card Data", "emoji": "🔑", "duration_ratio": 1.3},
    {"text": "Preparing Secure Display", "emoji": "✨", "duration_ratio": 0.8}
]

CHECK_STAGES = [
    {"text": "Initiating Verification", "emoji": "🎯", "duration_ratio": 1.0},
    {"text": "Analyzing Card Status", "emoji": "🔬", "duration_ratio": 1.2},
    {"text": "Checking Network Response", "emoji": "📡", "duration_ratio": 1.1},
    {"text": "Compiling Status Report", "emoji": "📊", "duration_ratio": 0.9}
]

ADMIN_STAGES = [
    {"text": "Authenticating Admin", "emoji": "🔐", "duration_ratio": 0.8},
    {"text": "Accessing System", "emoji": "⚡", "duration_ratio": 1.0},
    {"text": "Loading Configuration", "emoji": "⚙️", "duration_ratio": 1.3},
    {"text": "Preparing Interface", "emoji": "🖥️", "duration_ratio": 0.9}
]

# Catalog & Data Operations  
BROWSE_STAGES = [
    {"text": "Initializing Catalog", "emoji": "📚", "duration_ratio": 0.8},
    {"text": "Connecting to API", "emoji": "🌐", "duration_ratio": 1.1},
    {"text": "Loading Product Data", "emoji": "📦", "duration_ratio": 1.3},
    {"text": "Formatting Display", "emoji": "✨", "duration_ratio": 0.9}
]

FILTER_STAGES = [
    {"text": "Processing Filters", "emoji": "🔍", "duration_ratio": 1.0},
    {"text": "Querying Database", "emoji": "🗄️", "duration_ratio": 1.2},
    {"text": "Applying Criteria", "emoji": "⚙️", "duration_ratio": 1.1},
    {"text": "Organizing Results", "emoji": "📋", "duration_ratio": 0.8}
]

# Transaction Operations
PURCHASE_STAGES = [
    {"text": "Validating Cart Contents", "emoji": "✅", "duration_ratio": 0.9},
    {"text": "Processing Secure Payment", "emoji": "💳", "duration_ratio": 1.4},
    {"text": "Securing Transaction Data", "emoji": "🔒", "duration_ratio": 1.2},
    {"text": "Finalizing Your Order", "emoji": "🎉", "duration_ratio": 0.7}
]

CART_STAGES = [
    {"text": "Loading Cart Data", "emoji": "🛒", "duration_ratio": 1.0},
    {"text": "Calculating Totals", "emoji": "🧮", "duration_ratio": 0.8},
    {"text": "Validating Items", "emoji": "✓", "duration_ratio": 1.1},
    {"text": "Preparing Display", "emoji": "📱", "duration_ratio": 0.7}
]

# File Operations
DOWNLOAD_STAGES = [
    {"text": "Preparing Data", "emoji": "📥", "duration_ratio": 1.0},
    {"text": "Formatting Content", "emoji": "📄", "duration_ratio": 1.2},
    {"text": "Generating File", "emoji": "⚙️", "duration_ratio": 1.1},
    {"text": "Ready for Download", "emoji": "✨", "duration_ratio": 0.6}
]

# Additional Stage Configurations for Comprehensive Coverage

# User & Authentication Operations
LOGIN_STAGES = [
    {"text": "Validating Credentials", "emoji": "🔐", "duration_ratio": 1.0},
    {"text": "Establishing Session", "emoji": "🤝", "duration_ratio": 1.2},
    {"text": "Loading User Profile", "emoji": "👤", "duration_ratio": 1.1},
    {"text": "Preparing Interface", "emoji": "🎯", "duration_ratio": 0.8}
]

# Wallet & Financial Operations
WALLET_STAGES = [
    {"text": "Accessing Wallet", "emoji": "💰", "duration_ratio": 1.0},
    {"text": "Calculating Balance", "emoji": "🧮", "duration_ratio": 1.1},
    {"text": "Validating Transaction", "emoji": "✅", "duration_ratio": 1.2},
    {"text": "Updating Records", "emoji": "📊", "duration_ratio": 0.9}
]

# Search & Discovery Operations
SEARCH_STAGES = [
    {"text": "Analyzing Query", "emoji": "🔍", "duration_ratio": 0.8},
    {"text": "Searching Database", "emoji": "🗄️", "duration_ratio": 1.3},
    {"text": "Ranking Results", "emoji": "📈", "duration_ratio": 1.1},
    {"text": "Preparing Display", "emoji": "📋", "duration_ratio": 0.9}
]

# API Management Operations
API_MANAGEMENT_STAGES = [
    {"text": "Connecting to API", "emoji": "🌐", "duration_ratio": 1.0},
    {"text": "Testing Endpoints", "emoji": "🔧", "duration_ratio": 1.3},
    {"text": "Validating Response", "emoji": "✅", "duration_ratio": 1.1},
    {"text": "Updating Configuration", "emoji": "⚙️", "duration_ratio": 0.8}
]

# Product Management Operations
PRODUCT_STAGES = [
    {"text": "Loading Product Data", "emoji": "📦", "duration_ratio": 1.0},
    {"text": "Validating Selection", "emoji": "✓", "duration_ratio": 0.9},
    {"text": "Updating Preferences", "emoji": "⚙️", "duration_ratio": 1.1},
    {"text": "Refreshing Interface", "emoji": "🔄", "duration_ratio": 0.8}
]

# History & Analytics Operations
HISTORY_STAGES = [
    {"text": "Retrieving History", "emoji": "📚", "duration_ratio": 1.0},
    {"text": "Processing Data", "emoji": "⚙️", "duration_ratio": 1.2},
    {"text": "Generating Analytics", "emoji": "📊", "duration_ratio": 1.3},
    {"text": "Formatting Results", "emoji": "📋", "duration_ratio": 0.8}
]

# General Operations
GENERAL_STAGES = [
    {"text": "Initializing", "emoji": "🚀", "duration_ratio": 0.8},
    {"text": "Processing Request", "emoji": "⚙️", "duration_ratio": 1.2},
    {"text": "Validating Data", "emoji": "✅", "duration_ratio": 1.0},
    {"text": "Completing Task", "emoji": "✨", "duration_ratio": 0.7}
]

# Order Management Operations
ORDER_VIEW_STAGES = [
    {"text": "Fetching Card Details", "emoji": "🔍", "duration_ratio": 1.0},
    {"text": "Decrypting Data", "emoji": "🔐", "duration_ratio": 1.2},
    {"text": "Formatting Display", "emoji": "✨", "duration_ratio": 1.0},
    {"text": "Preparing Interface", "emoji": "🎨", "duration_ratio": 0.8}
]

ORDER_LIST_STAGES = [
    {"text": "Querying Order History", "emoji": "📚", "duration_ratio": 1.0},
    {"text": "Processing Results", "emoji": "⚙️", "duration_ratio": 1.2},
    {"text": "Calculating Statistics", "emoji": "📊", "duration_ratio": 1.1},
    {"text": "Building Display", "emoji": "🎨", "duration_ratio": 0.9}
]

CARD_DOWNLOAD_STAGES = [
    {"text": "Retrieving Card Data", "emoji": "📥", "duration_ratio": 1.0},
    {"text": "Validating Information", "emoji": "✅", "duration_ratio": 1.1},
    {"text": "Formatting Content", "emoji": "📄", "duration_ratio": 1.2},
    {"text": "Preparing Download", "emoji": "✨", "duration_ratio": 0.8}
]

CARD_CHECK_STAGES = [
    {"text": "Initiating Check", "emoji": "🎯", "duration_ratio": 1.0},
    {"text": "Contacting Gateway", "emoji": "🌐", "duration_ratio": 1.3},
    {"text": "Analyzing Response", "emoji": "🔬", "duration_ratio": 1.2},
    {"text": "Compiling Results", "emoji": "📊", "duration_ratio": 0.9}
]

RECENT_CARDS_STAGES = [
    {"text": "Loading Recent Orders", "emoji": "🔍", "duration_ratio": 1.0},
    {"text": "Fetching Card Data", "emoji": "🃏", "duration_ratio": 1.2},
    {"text": "Organizing Cards", "emoji": "📋", "duration_ratio": 1.1},
    {"text": "Preparing Display", "emoji": "✨", "duration_ratio": 0.8}
]


# Utility Functions for Easy Loading Animation Integration

def get_stages_for_operation(operation_type: str) -> List[Dict[str, Any]]:
    """
    Get appropriate loading stages for a given operation type.
    
    Args:
        operation_type: Type of operation (e.g., 'browse', 'unmask', 'cart', etc.)
        
    Returns:
        List of stage dictionaries for the operation
    """
    stage_mapping = {
        'browse': BROWSE_STAGES,
        'filter': FILTER_STAGES,
        'search': SEARCH_STAGES,
        'unmask': UNMASK_STAGES,
        'check': CHECK_STAGES,
        'download': DOWNLOAD_STAGES,
        'cart': CART_STAGES,
        'purchase': PURCHASE_STAGES,
        'wallet': WALLET_STAGES,
        'login': LOGIN_STAGES,
        'admin': ADMIN_STAGES,
        'api_management': API_MANAGEMENT_STAGES,
        'product': PRODUCT_STAGES,
        'history': HISTORY_STAGES,
        'order_view': ORDER_VIEW_STAGES,
        'order_list': ORDER_LIST_STAGES,
        'card_download': CARD_DOWNLOAD_STAGES,
        'card_check': CARD_CHECK_STAGES,
        'recent_cards': RECENT_CARDS_STAGES,
    }
    
    return stage_mapping.get(operation_type.lower(), GENERAL_STAGES)


async def run_loading_animation(
    callback: CallbackQuery,
    work_coroutine: Awaitable[T],
    operation_type: str = "general",
    operation_name: str = None,
    fallback_message: str = "Processing..."
) -> T:
    """
    Convenience function to run loading animation with automatic stage selection.
    
    Args:
        callback: Telegram callback query for message updates
        work_coroutine: The async operation to execute
        operation_type: Type of operation for stage selection
        operation_name: Custom operation name for logging
        fallback_message: Error message if animation fails
        
    Returns:
        Result from the work_coroutine execution
    """
    stages = get_stages_for_operation(operation_type)
    operation_name = operation_name or f"{operation_type.title()} Operation"
    
    return await LoadingStages.run_concurrent_loading(
        callback=callback,
        stages=stages,
        work_coroutine=work_coroutine,
        operation_name=operation_name,
        fallback_message=fallback_message
    )