"""
Checkout Balance Service

Handles automatic balance deduction during checkout with rollback functionality.
This service ensures atomic operations and proper error handling.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Tuple
from contextlib import asynccontextmanager
from dataclasses import dataclass

from utils.central_logger import get_logger
from services.user_service import UserService
from database.connection import database_transaction

logger = get_logger()


@dataclass
class BalanceDeductionResult:
    """Result of balance deduction operation"""
    success: bool
    transaction_id: Optional[str] = None
    original_balance: float = 0.0
    new_balance: float = 0.0
    error_message: Optional[str] = None


@dataclass
class CheckoutTransaction:
    """Represents a checkout transaction with rollback capability"""
    user_id: str
    amount: float
    currency: str
    transaction_id: str
    original_balance: float
    new_balance: float
    checkout_id: str
    timestamp: datetime
    status: str = "pending"  # pending, completed, rolled_back, failed


class CheckoutBalanceService:
    """Service for handling checkout balance operations with rollback"""
    
    def __init__(self):
        self.user_service = UserService()
        self.active_transactions: Dict[str, CheckoutTransaction] = {}
    
    async def deduct_balance_for_checkout(
        self,
        user_id: str,
        amount: float,
        currency: str = "USD",
        checkout_id: str = None,
        reference: str = None
    ) -> BalanceDeductionResult:
        """
        Deduct balance for checkout with automatic rollback on failure.
        
        Args:
            user_id: User ID
            amount: Amount to deduct
            currency: Currency (default: USD)
            checkout_id: Unique checkout identifier
            reference: Transaction reference
            
        Returns:
            BalanceDeductionResult with operation details
        """
        try:
            # Generate checkout ID if not provided
            if not checkout_id:
                checkout_id = f"checkout_{user_id}_{int(datetime.now().timestamp())}"
            
            # Get current wallet balance
            wallet = await self.user_service.get_wallet_by_user_id(user_id)
            if not wallet:
                return BalanceDeductionResult(
                    success=False,
                    error_message="Wallet not found"
                )
            
            original_balance = wallet.balance
            
            # Check if user has sufficient funds
            if not wallet.can_spend(amount):
                return BalanceDeductionResult(
                    success=False,
                    original_balance=original_balance,
                    error_message=f"Insufficient funds. Need ${amount:.2f}, have ${original_balance:.2f}"
                )
            
            # Create checkout transaction record
            transaction_id = f"checkout_{checkout_id}_{int(datetime.now().timestamp())}"
            new_balance = original_balance - amount
            
            checkout_transaction = CheckoutTransaction(
                user_id=user_id,
                amount=amount,
                currency=currency,
                transaction_id=transaction_id,
                original_balance=original_balance,
                new_balance=new_balance,
                checkout_id=checkout_id,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Store transaction for potential rollback
            self.active_transactions[checkout_id] = checkout_transaction
            
            # Perform balance deduction with database transaction
            async with database_transaction():
                # Debit wallet
                wallet, transaction = await self.user_service.debit_wallet(
                    user_id=user_id,
                    amount=amount,
                    currency=currency,
                    reference=reference or f"checkout_{checkout_id}",
                    metadata={
                        "checkout_id": checkout_id,
                        "transaction_type": "checkout_deduction",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    },
                    idempotency_key=transaction_id
                )
                
                # Update transaction status
                checkout_transaction.status = "completed"
                
                logger.info(f"✅ Balance deducted for checkout {checkout_id}: ${amount:.2f} from user {user_id}")
                
                return BalanceDeductionResult(
                    success=True,
                    transaction_id=transaction_id,
                    original_balance=original_balance,
                    new_balance=new_balance
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to deduct balance for checkout {checkout_id}: {e}")
            return BalanceDeductionResult(
                success=False,
                original_balance=original_balance,
                error_message=f"Balance deduction failed: {str(e)}"
            )
    
    async def rollback_checkout_balance(
        self,
        checkout_id: str,
        reason: str = "Checkout failed"
    ) -> bool:
        """
        Rollback balance deduction for failed checkout.
        
        Args:
            checkout_id: Checkout identifier
            reason: Reason for rollback
            
        Returns:
            bool: True if rollback successful
        """
        try:
            # Get transaction record
            transaction = self.active_transactions.get(checkout_id)
            if not transaction:
                logger.warning(f"⚠️ No active transaction found for checkout {checkout_id}")
                return False
            
            # Check if already rolled back
            if transaction.status == "rolled_back":
                logger.info(f"ℹ️ Transaction {checkout_id} already rolled back")
                return True
            
            # Perform rollback with database transaction
            async with database_transaction():
                # Credit wallet back
                wallet = await self.user_service.get_wallet_by_user_id(transaction.user_id)
                if wallet:
                    wallet.credit(transaction.amount)
                    await self.user_service.update_wallet(wallet)
                    
                    # Create rollback transaction record
                    await self.user_service.add_funds(
                        user_id=transaction.user_id,
                        amount=transaction.amount,
                        currency=transaction.currency
                    )
                    
                    # Update transaction status
                    transaction.status = "rolled_back"
                    
                    logger.info(f"✅ Balance rolled back for checkout {checkout_id}: ${transaction.amount:.2f} to user {transaction.user_id}")
                    logger.info(f"📝 Rollback reason: {reason}")
                    
                    return True
                else:
                    logger.error(f"❌ Wallet not found for rollback: {transaction.user_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Failed to rollback balance for checkout {checkout_id}: {e}")
            return False
    
    async def complete_checkout_balance(
        self,
        checkout_id: str
    ) -> bool:
        """
        Mark checkout as completed (no rollback needed).
        
        Args:
            checkout_id: Checkout identifier
            
        Returns:
            bool: True if marked as completed
        """
        try:
            transaction = self.active_transactions.get(checkout_id)
            if not transaction:
                logger.warning(f"⚠️ No active transaction found for checkout {checkout_id}")
                return False
            
            # Update status to completed
            transaction.status = "completed"
            
            # Clean up transaction record (optional - keep for audit)
            # self.active_transactions.pop(checkout_id, None)
            
            logger.info(f"✅ Checkout {checkout_id} marked as completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to complete checkout {checkout_id}: {e}")
            return False
    
    async def get_checkout_transaction(
        self,
        checkout_id: str
    ) -> Optional[CheckoutTransaction]:
        """Get checkout transaction details"""
        return self.active_transactions.get(checkout_id)
    
    async def cleanup_old_transactions(self, max_age_hours: int = 24):
        """Clean up old transaction records"""
        try:
            current_time = datetime.now(timezone.utc)
            cutoff_time = current_time - timedelta(hours=max_age_hours)
            
            to_remove = []
            for checkout_id, transaction in self.active_transactions.items():
                if transaction.timestamp < cutoff_time:
                    to_remove.append(checkout_id)
            
            for checkout_id in to_remove:
                self.active_transactions.pop(checkout_id, None)
                logger.info(f"🧹 Cleaned up old transaction: {checkout_id}")
                
        except Exception as e:
            logger.error(f"❌ Failed to cleanup old transactions: {e}")


# Global instance
checkout_balance_service = CheckoutBalanceService()
