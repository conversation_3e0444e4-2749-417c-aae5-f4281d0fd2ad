"""
Shared Session Manager for API v3
Thin wrapper around the main session manager to provide singleton access.
Simplified to avoid code duplication.
"""

import time
from typing import Optional
from threading import Lock

from utils.central_logger import get_logger

logger = get_logger()


class SharedSessionManager:
    """
    Singleton wrapper around the main session manager.
    Provides a simple interface for sharing sessions across services.
    """

    _instance: Optional["SharedSessionManager"] = None
    _lock = Lock()

    def __new__(cls):
        """Ensure only one instance exists (Singleton pattern)"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    logger.debug("SharedSessionManager singleton created")
        return cls._instance

    @classmethod
    def get_instance(cls) -> "SharedSessionManager":
        """Get the singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def get_session_key(self, base_url: str, username: str) -> str:
        """
        Generate unique session key for caching.
        """
        return f"{base_url}:{username}"

    def get_or_create_session(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        """
        Get authenticated session by delegating to main session manager.
        This ensures consistency and avoids code duplication.
        """
        from api_v3.auth.session_manager import get_authenticated_session
        
        return get_authenticated_session(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )

    def invalidate_session(self, base_url: str, username: str) -> None:
        """
        Invalidate a session by delegating to main session manager.
        """
        from api_v3.auth.session_manager import clear_session_cache
        
        logger.info(f"Invalidating session for {username}")
        # For now, clear all sessions (could be made more granular later)
        clear_session_cache()

    def clear_all_sessions(self) -> None:
        """
        Clear all cached sessions by delegating to main session manager.
        """
        from api_v3.auth.session_manager import clear_session_cache
        
        logger.info("Clearing all cached sessions")
        clear_session_cache()

    def get_session_info(self) -> dict:
        """
        Get information about cached sessions.
        Simplified to show basic info.
        """
        from api_v3.auth.session_manager import _cached_sessions, _cached_session_times
        
        info = {}
        for cache_key in _cached_sessions.keys():
            cache_time = _cached_session_times.get(cache_key, 0)
            age = int(time.time() - cache_time) if cache_time else 0
            info[cache_key] = {
                "age_seconds": age,
                "status": "cached"
            }
        return info


def get_shared_session_manager() -> SharedSessionManager:
    """
    Get the shared session manager singleton instance.
    """
    return SharedSessionManager.get_instance()