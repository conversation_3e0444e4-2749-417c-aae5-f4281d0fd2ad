"""
Enhanced Multi-File Logging System for Demo Wallet Bot v3

This module extends the central logging system to support multiple specialized 
log files following industry standards for comprehensive monitoring and debugging.

Features:
- Multiple specialized log files for different concerns
- Configurable log levels per file type
- Automatic log rotation and compression
- Performance-optimized async logging
- Industry-standard log organization
- Enhanced security and PII filtering
"""

from __future__ import annotations

import asyncio
import json
import logging
import logging.handlers
import time
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, Optional, Union
from concurrent.futures import ThreadPoolExecutor

from utils.central_logger import CentralLogger, SecurityFilter, CentralFormatter, get_logger


class LogCategory(Enum):
    """Log categories for specialized logging"""
    APPLICATION = "application"
    ERRORS = "errors"
    DEBUG = "debug"
    API_REQUESTS = "api-requests"
    API_RESPONSES = "api-responses"
    HTTP_CLIENT = "http-client"
    TELEGRAM_BOT = "telegram-bot"
    TRANSACTIONS = "transactions"
    USER_ACTIONS = "user-actions"
    DATABASE = "database"
    PERFORMANCE = "performance"
    SECURITY = "security"
    BACKGROUND_SERVICES = "background-services"
    HEALTH_MONITOR = "health-monitor"
    ADMIN_ACTIONS = "admin-actions"


class LogLevel(Enum):
    """Standard log levels"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class MultiFileLogger:
    """Enhanced multi-file logging system"""
    
    def __init__(self, base_path: str = "logs"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # Thread pool for async logging
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="logger")
        
        # Configure log files with appropriate levels
        self.log_configs = {
            LogCategory.APPLICATION: {"level": LogLevel.INFO, "max_size": 50},
            LogCategory.ERRORS: {"level": LogLevel.ERROR, "max_size": 25},
            LogCategory.DEBUG: {"level": LogLevel.DEBUG, "max_size": 100},
            LogCategory.API_REQUESTS: {"level": LogLevel.DEBUG, "max_size": 75},
            LogCategory.API_RESPONSES: {"level": LogLevel.DEBUG, "max_size": 75},
            LogCategory.HTTP_CLIENT: {"level": LogLevel.DEBUG, "max_size": 50},
            LogCategory.TELEGRAM_BOT: {"level": LogLevel.INFO, "max_size": 50},
            LogCategory.TRANSACTIONS: {"level": LogLevel.INFO, "max_size": 50},
            LogCategory.USER_ACTIONS: {"level": LogLevel.INFO, "max_size": 50},
            LogCategory.DATABASE: {"level": LogLevel.DEBUG, "max_size": 50},
            LogCategory.PERFORMANCE: {"level": LogLevel.DEBUG, "max_size": 50},
            LogCategory.SECURITY: {"level": LogLevel.WARNING, "max_size": 25},
            LogCategory.BACKGROUND_SERVICES: {"level": LogLevel.INFO, "max_size": 50},
            LogCategory.HEALTH_MONITOR: {"level": LogLevel.INFO, "max_size": 25},
            LogCategory.ADMIN_ACTIONS: {"level": LogLevel.INFO, "max_size": 25},
        }
        
        # Store loggers for each category
        self.loggers: Dict[LogCategory, logging.Logger] = {}
        self.category_handlers: Dict[LogCategory, logging.Handler] = {}
        self.setup_loggers()
    
    def setup_loggers(self):
        """Setup specialized loggers for each category"""
        for category, config in self.log_configs.items():
            logger_name = f"bot_{category.value.replace('-', '_')}"
            logger = logging.getLogger(logger_name)
            logger.setLevel(config["level"].value)
            logger.propagate = False
            
            # Clear existing handlers
            logger.handlers.clear()
            
            # Setup rotating file handler
            log_file = self.base_path / f"{category.value}.log"
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=config["max_size"] * 1024 * 1024,  # Convert MB to bytes
                backupCount=10,
                encoding='utf-8'
            )
            
            file_handler.setLevel(config["level"].value)
            
            # Use specialized formatter for structured logs
            formatter = self._get_formatter(category)
            file_handler.setFormatter(formatter)
            
            # Add security filter
            file_handler.addFilter(SecurityFilter())
            
            logger.addHandler(file_handler)
            self.category_handlers[category] = file_handler

            # Attach consolidated handler if available to mirror terminal output
            central_logger = get_logger()
            consolidated_handler = getattr(central_logger, "get_consolidated_handler", None)
            if callable(consolidated_handler):
                handler = consolidated_handler()
                if handler and handler not in logger.handlers:
                    logger.addHandler(handler)
            self.loggers[category] = logger
    
    def _get_formatter(self, category: LogCategory) -> logging.Formatter:
        """Get appropriate formatter for the category"""
        if category in [LogCategory.API_REQUESTS, LogCategory.API_RESPONSES, LogCategory.PERFORMANCE]:
            # Structured format for technical logs
            return StructuredFormatter()
        else:
            # Standard format for general logs
            return CentralFormatter(colored=False, include_module=True)
    
    def log(self, category: LogCategory, level: LogLevel, message: str, 
            extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log message to specific category"""
        if category not in self.loggers:
            # Fallback to central logger
            central_logger = get_logger()
            getattr(central_logger, level.name.lower())(f"[{category.value}] {message}")
            return
        
        logger = self.loggers[category]
        log_method = getattr(logger, level.name.lower())
        
        if extra:
            # Create LogRecord with extra data
            record = logging.LogRecord(
                name=logger.name,
                level=level.value,
                pathname="",
                lineno=0,
                msg=message,
                args=(),
                exc_info=kwargs.get('exc_info')
            )
            
            # Add extra fields
            for key, value in extra.items():
                setattr(record, key, value)
            
            # Process through handlers
            if logger.isEnabledFor(level.value):
                logger.handle(record)
        else:
            log_method(message, **kwargs)
    
    async def log_async(self, category: LogCategory, level: LogLevel, message: str,
                       extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Async logging to prevent blocking"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self.executor, 
            self.log, 
            category, level, message, extra, kwargs
        )
    
    def log_api_request(self, method: str, url: str, headers: Optional[Dict] = None,
                       data: Optional[Dict] = None, params: Optional[Dict] = None,
                       duration: Optional[float] = None, payload: Optional[str] = None,
                       applied_filters: Optional[str] = None, query_string: Optional[str] = None,
                       full_url: Optional[str] = None):
        """Log API request with comprehensive structured data including headers, payload, and filters"""
        extra = {
            "method": method,
            "url": url,
            "duration_ms": duration * 1000 if duration else None,
            "request_size": len(str(data)) if data else 0,
            "headers": headers if headers else {},
            "params": params if params else {},
            "data": data if data else {},
            "payload": payload if payload else None,
            "applied_filters": applied_filters if applied_filters else "None",
            "query_string": query_string if query_string else "",
            "full_url": full_url if full_url else url
        }
        
        message = f"{method} {url}"
        if duration:
            message += f" [{duration:.3f}s]"
        
        self.log(LogCategory.API_REQUESTS, LogLevel.DEBUG, message, extra)
    
    def log_api_response(self, status_code: int, url: str, response_size: int = 0,
                        duration: Optional[float] = None, success: bool = True,
                        headers: Optional[Dict] = None, response_body: Optional[str] = None,
                        applied_filters: Optional[str] = None, request_params: Optional[Dict] = None):
        """Log API response with comprehensive structured data including headers, body, and request context"""
        extra = {
            "status_code": status_code,
            "url": url,
            "response_size": response_size,
            "duration_ms": duration * 1000 if duration else None,
            "success": success,
            "headers": headers if headers else {},
            "response_body": response_body if response_body else None,
            "applied_filters": applied_filters if applied_filters else "None",
            "request_params": request_params if request_params else {}
        }
        
        level = LogLevel.INFO if success else LogLevel.ERROR
        message = f"Response {status_code} from {url}"
        if duration:
            message += f" [{duration:.3f}s]"
        
        self.log(LogCategory.API_RESPONSES, level, message, extra)
    
    def log_transaction(self, user_id: int, transaction_type: str, amount: Optional[float] = None,
                       status: str = "success", transaction_id: Optional[str] = None):
        """Log transaction with structured data"""
        extra = {
            "user_id": user_id,
            "transaction_type": transaction_type,
            "amount": amount,
            "status": status,
            "transaction_id": transaction_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        message = f"Transaction {transaction_type} for user {user_id}"
        if amount:
            message += f" (${amount:.2f})"
        message += f" - {status}"
        
        level = LogLevel.INFO if status == "success" else LogLevel.ERROR
        self.log(LogCategory.TRANSACTIONS, level, message, extra)
    
    def log_user_action(self, user_id: int, action: str, details: Optional[Dict] = None):
        """Log user action for analytics"""
        extra = {
            "user_id": user_id,
            "action": action,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if details:
            extra.update(details)
        
        message = f"User {user_id} performed action: {action}"
        self.log(LogCategory.USER_ACTIONS, LogLevel.INFO, message, extra)
    
    def log_security_event(self, event_type: str, user_id: Optional[int] = None,
                          ip_address: Optional[str] = None, details: Optional[Dict] = None):
        """Log security-related events"""
        extra = {
            "event_type": event_type,
            "user_id": user_id,
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if details:
            extra.update(details)
        
        message = f"Security event: {event_type}"
        if user_id:
            message += f" (user: {user_id})"
        
        self.log(LogCategory.SECURITY, LogLevel.WARNING, message, extra)
    
    def log_performance(self, operation: str, duration: float, success: bool = True,
                       details: Optional[Dict] = None):
        """Log performance metrics"""
        extra = {
            "operation": operation,
            "duration_ms": duration * 1000,
            "success": success,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if details:
            extra.update(details)
        
        message = f"Operation '{operation}' completed in {duration:.3f}s"
        level = LogLevel.DEBUG
        
        # Log slow operations as warnings
        if duration > 5.0:
            level = LogLevel.WARNING
            message += " (SLOW)"
        
        self.log(LogCategory.PERFORMANCE, level, message, extra)
    
    def log_admin_action(self, admin_id: int, action: str, target: Optional[str] = None,
                        details: Optional[Dict] = None):
        """Log administrative actions for audit trail"""
        extra = {
            "admin_id": admin_id,
            "action": action,
            "target": target,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if details:
            extra.update(details)
        
        message = f"Admin {admin_id} performed: {action}"
        if target:
            message += f" on {target}"
        
        self.log(LogCategory.ADMIN_ACTIONS, LogLevel.INFO, message, extra)
    
    def close(self):
        """Close all loggers and cleanup resources"""
        for logger in self.loggers.values():
            central_handler = get_logger().get_consolidated_handler()
            for handler in list(logger.handlers):
                if handler is central_handler:
                    logger.removeHandler(handler)
                    continue
                logger.removeHandler(handler)
                handler.close()
        
        self.executor.shutdown(wait=True)


class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter for technical logs"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format record as structured JSON"""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "module": record.name,
            "message": record.getMessage()
        }
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)


# Global multi-file logger instance
_multi_logger: Optional[MultiFileLogger] = None


def get_multi_logger() -> MultiFileLogger:
    """Get the global multi-file logger instance"""
    global _multi_logger
    if _multi_logger is None:
        _multi_logger = MultiFileLogger()
    return _multi_logger


def setup_multi_file_logging(base_path: str = "logs") -> MultiFileLogger:
    """Setup multi-file logging system"""
    global _multi_logger
    from utils.central_logger import setup_logging
    central_logger = get_logger()
    consolidated_handler = central_logger.get_consolidated_handler()
    consolidated_path = (
        getattr(consolidated_handler, "baseFilename", None)
        if consolidated_handler
        else f"{base_path}/all.log"
    )
    setup_logging(
        level="DEBUG",
        log_file=f"{base_path}/application.log",
        max_file_size=50 * 1024 * 1024,
        backup_count=10,
        consolidated_log_file=consolidated_path,
    )
    
    _multi_logger = MultiFileLogger(base_path)
    
    central_logger = get_logger()
    central_logger.info("Multi-file logging system initialized")
    central_logger.info(f"Logging to {len(_multi_logger.log_configs)} specialized log files")
    
    return _multi_logger


# Convenience functions for common logging operations
def log_api_request(method: str, url: str, **kwargs):
    """Convenience function for API request logging"""
    get_multi_logger().log_api_request(method, url, **kwargs)


def log_api_response(status_code: int, url: str, **kwargs):
    """Convenience function for API response logging"""
    get_multi_logger().log_api_response(status_code, url, **kwargs)


def log_transaction(user_id: int, transaction_type: str, **kwargs):
    """Convenience function for transaction logging"""
    get_multi_logger().log_transaction(user_id, transaction_type, **kwargs)


def log_user_action(user_id: int, action: str, **kwargs):
    """Convenience function for user action logging"""
    get_multi_logger().log_user_action(user_id, action, **kwargs)


def log_security_event(event_type: str, **kwargs):
    """Convenience function for security event logging"""
    get_multi_logger().log_security_event(event_type, **kwargs)


def log_performance(operation: str, duration: float, success: bool = True, details: Optional[Dict] = None):
    """Convenience function for performance logging"""
    get_multi_logger().log_performance(operation, duration, success, details)


def log_admin_action(admin_id: int, action: str, **kwargs):
    """Convenience function for admin action logging"""
    get_multi_logger().log_admin_action(admin_id, action, **kwargs)


# Context manager for performance timing
class PerformanceTimer:
    """Context manager for automatic performance logging"""
    
    def __init__(self, operation: str, details: Optional[Dict] = None):
        self.operation = operation
        self.details = details or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        success = exc_type is None
        
        if exc_type:
            self.details["error"] = str(exc_val)
        
        log_performance(self.operation, duration, success, self.details)


# Decorator for automatic performance logging
def log_performance_decorator(operation_name: Optional[str] = None):
    """Decorator for automatic performance logging"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            with PerformanceTimer(op_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator


async def log_performance_decorator_async(operation_name: Optional[str] = None):
    """Async decorator for automatic performance logging"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            with PerformanceTimer(op_name):
                return await func(*args, **kwargs)
        return wrapper
    return decorator
