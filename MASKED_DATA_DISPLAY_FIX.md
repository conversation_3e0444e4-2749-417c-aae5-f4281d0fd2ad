# Masked Data Display Fix - Show Masked Cards When Not Actually Unmasked

## Issue
When clicking on a card that was marked as "unmasked" in the database (has `is_unmasked=True` flag), the system was showing masked card data (e.g., `3752************`) instead of checking if the card data actually contained unmasked information.

### Root Cause
The system was trusting the `is_unmasked` flag in the database without validating that the actual card data contained unmasked information (full CC number without asterisks). This happened because:

1. Initial card view/checkout saved card with masked data
2. Database flag `is_unmasked` was set incorrectly or prematurely
3. When viewing the card, system set `show_sensitive=True` based on flag alone
4. Card data still contained masked numbers like `3752************`

### Symptoms
- Clicking on a card showed message: "🔓 UNMASKING: show_sensitive=True"
- But card number displayed as: `3752************` (masked)
- Logs showed: `cc=✅ cvv=❌ exp=✅` (found cc, but it was masked)
- User expected to see full unmasked card number

## Solution

Added validation to check if card data **ACTUALLY** contains unmasked information before showing it as sensitive data.

### Files Modified

**handlers/orders_handlers.py**

#### 1. `cb_view_purchased_card` - Lines 2484-2504

**Before**:
```python
message = await self._create_full_card_view(
    order, str(db_user.id), show_sensitive=is_unmasked
)
```

**After**:
```python
# Check if card data ACTUALLY has unmasked data (not just the flag)
actual_is_unmasked = is_unmasked
if is_unmasked and "extracted_cards" in order and order["extracted_cards"]:
    card_data = order["extracted_cards"][0]
    card_number = card_data.get("card_number") or card_data.get("cc", "")
    # Check if card number is actually unmasked (has full digits, no asterisks)
    if card_number:
        has_asterisks = "*" in card_number
        is_redacted = "[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number
        # If card number has asterisks or is redacted, it's NOT actually unmasked
        if has_asterisks or is_redacted:
            actual_is_unmasked = False
            logger.warning(f"⚠️ [View Card] is_unmasked flag set but card data is masked: {card_number[:12]}...")
    else:
        actual_is_unmasked = False
        logger.warning(f"⚠️ [View Card] is_unmasked flag set but no card number found")

message = await self._create_full_card_view(
    order, str(db_user.id), show_sensitive=actual_is_unmasked
)
```

#### 2. `cb_view_card_details` - Lines 3683-3763

**Before**:
```python
is_unmasked = self._is_unmask_data(order)
message = await self._create_full_card_view(order, str(db_user.id))

# Show download button if unmasked
elif is_viewed and is_unmasked:
    kb.add_button("📥 Download Card Data", ...)
elif not is_unmasked:
    kb.add_button("🔓 Unmask Card", ...)
```

**After**:
```python
is_unmasked = self._is_unmask_data(order)

# Check if card data ACTUALLY has unmasked data (not just the flag)
actual_is_unmasked = is_unmasked
if is_unmasked and "extracted_cards" in order and order["extracted_cards"]:
    card_data = order["extracted_cards"][0]
    card_number = card_data.get("card_number") or card_data.get("cc", "")
    # Check if card number is actually unmasked (has full digits, no asterisks)
    if card_number:
        has_asterisks = "*" in card_number
        is_redacted = "[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number
        # If card number has asterisks or is redacted, it's NOT actually unmasked
        if has_asterisks or is_redacted:
            actual_is_unmasked = False
            logger.warning(f"⚠️ [My Orders] is_unmasked flag set but card data is masked: {card_number[:12]}...")
    else:
        actual_is_unmasked = False
        logger.warning(f"⚠️ [My Orders] is_unmasked flag set but no card number found")

message = await self._create_full_card_view(order, str(db_user.id), show_sensitive=actual_is_unmasked)

# Show download button if ACTUALLY unmasked
elif is_viewed and actual_is_unmasked:
    kb.add_button("📥 Download Card Data", ...)
elif not actual_is_unmasked:
    kb.add_button("🔓 Unmask Card", ...)
```

## Validation Logic

The fix validates card data by checking:

1. **Has asterisks**: `"*" in card_number`
   - Masked cards contain asterisks like `3752************`
   
2. **Is redacted**: `"[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number`
   - API returns `[PAN_REDACTED]` for masked cards
   - System uses `[UNMASKED - DOWNLOAD REQUIRED]` for backend-unmasked cards
   
3. **No card number**: Missing `card_number` or `cc` field

If any of these conditions are true, the card is NOT actually unmasked, regardless of the database flag.

## New Log Messages

When validation detects masked data with unmasked flag set:

1. `⚠️ [View Card] is_unmasked flag set but card data is masked: {first_12_chars}...`
2. `⚠️ [View Card] is_unmasked flag set but no card number found`
3. `⚠️ [My Orders] is_unmasked flag set but card data is masked: {first_12_chars}...`
4. `⚠️ [My Orders] is_unmasked flag set but no card number found`

## Benefits

1. **Correct Display**: Shows masked cards as masked, not as "unmasked"
2. **Proper Button Logic**: Shows "Unmask Card" button instead of "Download" when card is not actually unmasked
3. **Better UX**: No confusion about card state - clear when card needs to be unmasked
4. **Data Validation**: Catches incorrect database flags and corrects behavior
5. **Clear Logging**: Warns when database flags don't match actual data state

## Testing Scenarios

### Scenario 1: Card Just Purchased (Masked Data)
**Before Fix**:
- Database: `is_unmasked=True` (incorrectly set)
- Card Data: `3752************` (masked)
- Display: Showed as "unmasked" with masked number
- Button: "Download Card Data" (wrong)

**After Fix**:
- Database: `is_unmasked=True`
- Card Data: `3752************` (masked)
- Validation: Detects asterisks, sets `actual_is_unmasked=False`
- Display: Shows correctly as masked
- Button: "🔓 Unmask Card" (correct)
- Log: `⚠️ [View Card] is_unmasked flag set but card data is masked`

### Scenario 2: Card Actually Unmasked (Full Data)
**Before Fix**:
- Database: `is_unmasked=True`
- Card Data: `3752567890123456` (full number, no asterisks)
- Display: Showed full number correctly
- Button: "Download Card Data" (correct)

**After Fix**:
- Database: `is_unmasked=True`
- Card Data: `3752567890123456` (full number)
- Validation: No asterisks, no redaction → `actual_is_unmasked=True`
- Display: Shows full number (same behavior)
- Button: "📥 Download Card Data" (same)
- No warning log

### Scenario 3: Card with [PAN_REDACTED]
**Before Fix**:
- Database: `is_unmasked=True`
- Card Data: `[PAN_REDACTED]`
- Display: Showed "[PAN_REDACTED]" as "unmasked"
- Button: "Download Card Data"

**After Fix**:
- Database: `is_unmasked=True`
- Card Data: `[PAN_REDACTED]`
- Validation: Detects redaction marker → `actual_is_unmasked=False`
- Display: Shows as masked
- Button: "🔓 Unmask Card"
- Log: `⚠️ [View Card] is_unmasked flag set but card data is masked`

## Related Issues

This fix complements the previous fixes:
- **CARD_DISPLAY_IMPROVEMENTS.md**: Shows updated data when card is clicked
- **API_V3_POST_CHECKOUT_FIXES.md**: Proper data storage in database

Together, these ensure:
1. Complete data is stored (POST_CHECKOUT_FIXES)
2. Updated data is shown (DISPLAY_IMPROVEMENTS)  
3. Masked data is shown as masked (THIS FIX)

## Implementation Date
October 26, 2025

## Summary

The fix ensures that the system validates actual card data content, not just database flags, before displaying sensitive information. This prevents confusion where masked cards were shown as "unmasked" simply because a database flag was set incorrectly.

**Key Principle**: Don't trust database flags alone - validate the actual data content.

