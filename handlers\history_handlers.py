"""
History-related Telegram bot handlers
"""

from __future__ import annotations

from datetime import datetime
from typing import List

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from services.user_service import UserService
from database.connection import get_collection
from utils.keyboards import back_keyboard
from utils.texts import (
    HISTORY_TEMPLATE,
    TRANSACTION_ITEM_TEMPLATE,
    format_transaction_type,
    format_currency,
)
from middleware import attach_common_middlewares

from utils.central_logger import get_logger

logger = get_logger()


class HistoryHandlers:
    """Transaction history handlers"""

    def __init__(self):
        self.user_service = UserService()
        self.tx_collection = get_collection("transactions")

    async def cb_history_menu(self, callback: CallbackQuery) -> None:
        """Show user's transaction history with styled pagination (page 1)."""
        try:
            from utils.ui_manager import ui_manager
            from utils.post_checkout_ui import PaginationHelpers

            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            cursor = self.tx_collection.find({"user_id": str(db_user.id)}).sort(
                "created_at", -1
            )
            tx_docs = await cursor.limit(100).to_list(100)

            message, keyboard = PaginationHelpers.create_transactions_page(
                transactions=tx_docs,
                current_page=1,
                items_per_page=10,
                title="💳 Transaction History",
            )

            await ui_manager.edit_message_safely(
                callback,
                message,
                keyboard.build(),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error showing enhanced transaction history: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_all_transactions(self, callback: CallbackQuery) -> None:
        """Show all user transactions with enhanced pagination"""
        try:
            from utils.ui_manager import ui_manager

            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Parse page number from callback data
            from utils.post_checkout_ui import PaginationHelpers

            current_page = PaginationHelpers.parse_page_from_callback(
                callback.data, default=1
            )

            # Fetch all transactions (increased limit for pagination)
            cursor = self.tx_collection.find({"user_id": str(db_user.id)}).sort(
                "created_at", -1
            )
            tx_docs = await cursor.limit(100).to_list(100)

            if not tx_docs:
                await callback.answer("No transactions found", show_alert=True)
                return

            # Use pagination helper to create the message and keyboard
            message, keyboard = PaginationHelpers.create_transactions_page(
                transactions=tx_docs,
                current_page=current_page,
                items_per_page=10,  # Show 10 transactions per page
                title="💳 All Transaction History",
            )

            await ui_manager.edit_message_safely(
                callback,
                message,
                keyboard.build(),
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error showing all transactions: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_history_refresh(self, callback: CallbackQuery) -> None:
        """Refresh transaction history (page 1)."""
        await self.cb_all_transactions(callback)


def get_history_router() -> Router:
    """Create and return history router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = HistoryHandlers()

    router.callback_query.register(handlers.cb_history_menu, F.data == "menu:history")
    router.callback_query.register(
        handlers.cb_all_transactions, F.data == "history:all"
    )
    router.callback_query.register(
        handlers.cb_all_transactions, F.data.startswith("history:all:page:")
    )
    router.callback_query.register(
        handlers.cb_history_refresh, F.data == "history:refresh"
    )
    router.callback_query.register(
        handlers.cb_view_receipt, F.data.startswith("history:receipt:")
    )

    logger.debug("History handlers registered")
    return router


async def _fetch_receipt_text(user_id: str, tx_id: str) -> str:
    """Helper to compose receipt text for a transaction."""
    from database.connection import get_collection
    from datetime import datetime

    tx_col = get_collection("transactions")
    pur_col = get_collection("purchases")

    tx = await tx_col.find_one({"_id": tx_id})
    if tx is None:
        # try ObjectId
        try:
            from bson import ObjectId

            if isinstance(tx_id, str) and ObjectId.is_valid(tx_id):
                tx = await tx_col.find_one({"_id": ObjectId(tx_id)})
        except Exception:
            pass
    if not tx:
        return "❌ Receipt not found."

    created = tx.get("created_at")
    if isinstance(created, datetime):
        created_str = created.strftime("%Y-%m-%d %H:%M:%S")
    else:
        created_str = str(created)

    amount = float(tx.get("amount", 0))
    lines = [
        "dY'3 <b>Receipt</b>",
        f"Transaction: <code>{str(tx.get('_id'))}</code>",
        f"Date: {created_str}",
        f"Amount: ${amount:.2f}",
        "",
        "<b>Items</b>",
    ]

    # Fetch related purchases via tx_id in metadata
    cursor = pur_col.find({"user_id": user_id, "metadata.tx_id": str(tx.get("_id"))})
    items = await cursor.to_list(100)
    if not items:
        lines.append("(No purchase line items found)")
    else:
        total = 0.0
        for i, p in enumerate(items, 1):
            price = float(p.get("price", 0))
            meta = p.get("metadata", {}) or {}
            qty = meta.get("quantity", 1)
            sku = p.get("sku", "")
            lines.append(f"{i}. {sku} x{qty} - ${price:.2f}")
            total += price
        lines += ["", f"Total: <b>${total:.2f}</b>"]

    return "\n".join(lines)


class HistoryHandlers(HistoryHandlers):
    async def cb_view_receipt(self, callback: CallbackQuery) -> None:
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid receipt request", show_alert=True)
                return
            tx_id = parts[2]
            text = await _fetch_receipt_text(str(db_user.id), tx_id)
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="Back", callback_data="menu:history")]
                ]
            )
            await callback.message.edit_text(text, reply_markup=kb)
            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing receipt: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
