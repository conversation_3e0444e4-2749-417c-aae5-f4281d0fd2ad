# Telegram HTML Formatting Documentation

## Overview

Telegram Bot API supports HTML-style formatting to enhance message presentation. This document provides a comprehensive guide to all supported HTML tags and their usage.

---

## Supported HTML Tags

### 1. Text Formatting

#### Bold Text
```html
<b>bold text</b>
<strong>bold text</strong>
```
**Result:** **bold text**

#### Italic Text
```html
<i>italic text</i>
<em>italic text</em>
```
**Result:** *italic text*

#### Underlined Text
```html
<u>underlined text</u>
<ins>underlined text</ins>
```
**Result:** <u>underlined text</u>

#### Strikethrough Text
```html
<s>strikethrough text</s>
<strike>strikethrough text</strike>
<del>strikethrough text</del>
```
**Result:** ~~strikethrough text~~

#### Spoiler Text
```html
<span class="tg-spoiler">spoiler text</span>
<tg-spoiler>spoiler text</tg-spoiler>
```
**Result:** Text is hidden until clicked by the user

---

### 2. Code Formatting

#### Inline Code
For displaying inline code snippets within text:

```html
<code>inline fixed-width code</code>
```

**Example:**
```html
Use the <code>print()</code> function to display output.
```

**Use Cases:**
- Variable names
- Function names
- Short commands
- API endpoints

#### Pre-Formatted Code Block
For multi-line code blocks without syntax highlighting:

```html
<pre>pre-formatted fixed-width code block
line 2
line 3</pre>
```

**Use Cases:**
- Plain text code
- Configuration files
- Log outputs
- Multi-line data

#### Pre-Formatted Code Block with Syntax Highlighting
For code blocks with language-specific syntax highlighting:

```html
<pre><code class="language-python">
def hello_world():
    print("Hello, World!")
    return True
</code></pre>
```

**Supported Languages:**
- `language-python`
- `language-javascript`
- `language-java`
- `language-cpp`
- `language-c`
- `language-php`
- `language-ruby`
- `language-go`
- `language-swift`
- `language-kotlin`
- `language-rust`
- `language-sql`
- `language-bash`
- `language-shell`
- And many more...

**Example with Python:**
```html
<pre><code class="language-python">
import requests

response = requests.get("https://api.example.com")
data = response.json()
</code></pre>
```

---

### 3. Quotations

#### Basic Block Quote
For standard quotations:

```html
<blockquote>
Block quotation started
Block quotation continued
The last line of the block quotation
</blockquote>
```

**Use Cases:**
- Citing messages
- Highlighting important information
- User feedback
- Documentation quotes

#### Expandable Block Quote
For quotations that are collapsed by default and expandable by the user:

```html
<blockquote expandable>
Expandable block quotation started
Expandable block quotation continued
Expandable block quotation continued
Hidden by default part of the block quotation started
Expandable block quotation continued
The last line of the block quotation
</blockquote>
```

**Use Cases:**
- Long quotes that might clutter the chat
- Detailed information that's optional to read
- Verbose error messages
- Extended documentation sections

---

### 4. Links

#### Text Links
```html
<a href="https://example.com">inline URL</a>
```

#### User Mentions
```html
<a href="tg://user?id=123456789">inline mention of a user</a>
```

**Note:** User ID must be a valid Telegram user ID.

---

### 5. Emojis

#### Custom Emojis
```html
<tg-emoji emoji-id="5368324170671202286">👍</tg-emoji>
```

**Note:** The emoji-id can be obtained from the `getCustomEmojiStickers` method.

---

## Important Rules and Restrictions

### 1. Tag Nesting
❌ **Not Allowed:** Tags must not be nested
```html
<!-- WRONG -->
<b><i>bold italic</i></b>
```

✅ **Allowed:** Separate formatting
```html
<!-- CORRECT -->
<b>bold text</b> and <i>italic text</i>
```

### 2. Special Characters
All `<`, `>`, and `&` symbols that are **not part of a tag or HTML entity** must be escaped:

| Character | HTML Entity |
|-----------|-------------|
| `<`       | `&lt;`      |
| `>`       | `&gt;`      |
| `&`       | `&amp;`     |
| `"`       | `&quot;`    |

**Example:**
```html
<!-- WRONG -->
<code>if x > 5 && y < 10</code>

<!-- CORRECT -->
<code>if x &gt; 5 &amp;&amp; y &lt; 10</code>
```

### 3. Supported HTML Entities

**Numerical Entities:** All numerical HTML entities are supported
```html
&#60; &#62; &#38; &#128512;
```

**Named Entities:** Only these are supported:
- `&lt;` - Less than (<)
- `&gt;` - Greater than (>)
- `&amp;` - Ampersand (&)
- `&quot;` - Double quote (")

### 4. Message Length Limits
- Maximum message length: 4096 characters (including HTML tags)
- For messages exceeding this limit, split into multiple messages

---

## Best Practices

### 1. Code Formatting
```html
<!-- For inline code -->
The <code>sendMessage</code> method sends a text message.

<!-- For code blocks -->
<pre><code class="language-python">
async def send_formatted_message(chat_id: int, text: str):
    await bot.send_message(chat_id, text, parse_mode="HTML")
</code></pre>
```

### 2. Combining Formats
```html
<!-- Correct way to combine formats -->
<b>Important:</b> Use <code>parse_mode="HTML"</code> parameter.

<!-- Not recommended (nested tags) -->
<b>Important: <code>parse_mode="HTML"</code></b>
```

### 3. Quotations
```html
<!-- Short quotes -->
<blockquote>User feedback: Great bot!</blockquote>

<!-- Long or optional content -->
<blockquote expandable>
Detailed error trace:
File "main.py", line 42
  Error details...
  Stack trace...
</blockquote>
```

### 4. Links
```html
<!-- External links -->
Visit <a href="https://telegram.org">Telegram</a> for more info.

<!-- User mentions -->
Thanks to <a href="tg://user?id=123456789">John</a> for the report!
```

---

## Common Use Cases

### 1. Error Messages
```html
<b>⚠️ Error Occurred</b>

<blockquote expandable>
<code>Error: Connection timeout</code>

Stack trace:
<pre>
File: api_client.py, line 156
Function: make_request()
Timestamp: 2025-10-25 14:32:01
</pre>
</blockquote>

Please try again later.
```

### 2. Code Tutorials
```html
<b>📚 Python Tutorial: Hello World</b>

Create a new file and add this code:

<pre><code class="language-python">
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
</code></pre>

Run it using: <code>python main.py</code>
```

### 3. Status Updates
```html
<b>✅ Order #12345 Confirmed</b>

<blockquote>
Order Total: <b>$99.99</b>
Status: <i>Processing</i>
Estimated Delivery: <u>Oct 28, 2025</u>
</blockquote>

Track your order: <a href="https://example.com/track/12345">Click Here</a>
```

### 4. User Profiles
```html
<b>👤 User Profile</b>

Name: <b>John Doe</b>
Username: <code>@johndoe</code>
Member Since: <i>January 2024</i>
Status: <span class="tg-spoiler">Premium Member</span>

<blockquote>
"I love using this bot!"
</blockquote>
```

---

## Python Implementation Examples

### Basic HTML Message
```python
from aiogram import Bot

async def send_formatted_message(bot: Bot, chat_id: int):
    text = """
<b>Welcome!</b>

This is an <i>italic text</i> and this is <code>inline code</code>.

<blockquote>This is a quote</blockquote>
"""
    await bot.send_message(chat_id, text, parse_mode="HTML")
```

### Code Block with Syntax Highlighting
```python
async def send_code_example(bot: Bot, chat_id: int):
    code = """
<b>Python Example:</b>

<pre><code class="language-python">
def calculate_sum(a: int, b: int) -> int:
    return a + b

result = calculate_sum(5, 10)
print(f"Result: {result}")
</code></pre>
"""
    await bot.send_message(chat_id, code, parse_mode="HTML")
```

### Escaping Special Characters
```python
import html

async def send_user_input(bot: Bot, chat_id: int, user_input: str):
    # Escape special characters
    escaped_input = html.escape(user_input)
    
    text = f"""
<b>Your input:</b>
<code>{escaped_input}</code>
"""
    await bot.send_message(chat_id, text, parse_mode="HTML")
```

### Expandable Error Details
```python
async def send_error_report(bot: Bot, chat_id: int, error_details: str):
    text = f"""
<b>⚠️ Error Occurred</b>

<blockquote expandable>
<pre>{html.escape(error_details)}</pre>
</blockquote>

Please contact support if the issue persists.
"""
    await bot.send_message(chat_id, text, parse_mode="HTML")
```

---

## Testing Your Formatting

### Quick Test Function
```python
async def test_html_formatting(bot: Bot, chat_id: int):
    """Test all HTML formatting options"""
    
    test_messages = [
        "<b>Bold</b> and <i>Italic</i>",
        "<u>Underlined</u> and <s>Strikethrough</s>",
        "Inline <code>code</code> example",
        "<pre>Multi-line\ncode\nblock</pre>",
        '<pre><code class="language-python">print("Hello")</code></pre>',
        "<blockquote>Simple quote</blockquote>",
        "<blockquote expandable>Expandable\nquote\nwith\nmultiple\nlines</blockquote>",
        '<a href="https://telegram.org">Link</a>',
        "<span class=\"tg-spoiler\">Spoiler text</span>",
    ]
    
    for msg in test_messages:
        await bot.send_message(chat_id, msg, parse_mode="HTML")
        await asyncio.sleep(0.5)  # Small delay between messages
```

---

## Troubleshooting

### Common Issues

#### 1. Tags Not Working
**Problem:** HTML tags appear as plain text
**Solution:** Ensure `parse_mode="HTML"` is set in your send_message call

```python
# Wrong
await bot.send_message(chat_id, "<b>Bold</b>")

# Correct
await bot.send_message(chat_id, "<b>Bold</b>", parse_mode="HTML")
```

#### 2. Special Characters Breaking Formatting
**Problem:** Messages fail when containing `<`, `>`, or `&`
**Solution:** Escape special characters using `html.escape()`

```python
import html

user_text = "x > 5 && y < 10"
safe_text = html.escape(user_text)
message = f"<code>{safe_text}</code>"
```

#### 3. Nested Tags Not Working
**Problem:** Nested formatting doesn't render
**Solution:** Telegram doesn't support nested tags. Use separate formatting.

```python
# Wrong
"<b><i>Bold Italic</i></b>"

# Correct (use Markdown mode if you need combined formatting)
# Or apply styles separately
"<b>Bold</b> <i>Italic</i>"
```

#### 4. Code Block Language Not Highlighting
**Problem:** Syntax highlighting not working
**Solution:** Ensure correct language class name with `language-` prefix

```python
# Wrong
'<pre><code class="python">code</code></pre>'

# Correct
'<pre><code class="language-python">code</code></pre>'
```

---

## Comparison: HTML vs Markdown

Telegram also supports Markdown formatting. Here's when to use each:

### Use HTML When:
- ✅ Need precise control over formatting
- ✅ Working with code blocks and syntax highlighting
- ✅ Using expandable blockquotes
- ✅ Need to escape special characters reliably
- ✅ Integrating with HTML-based systems

### Use Markdown When:
- ✅ Need combined formatting (bold + italic)
- ✅ Simpler, more readable code
- ✅ Quick formatting without complex nesting

---

## Resources

- **Official Telegram Bot API Documentation:** [https://core.telegram.org/bots/api#formatting-options](https://core.telegram.org/bots/api#formatting-options)
- **HTML Entity Reference:** [https://dev.w3.org/html5/html-author/charref](https://dev.w3.org/html5/html-author/charref)

---

## Summary

Telegram's HTML formatting provides powerful options for enhancing bot messages:

| Feature | Tag | Use Case |
|---------|-----|----------|
| Bold | `<b>` or `<strong>` | Emphasis |
| Italic | `<i>` or `<em>` | Subtle emphasis |
| Underline | `<u>` or `<ins>` | Highlighting |
| Strikethrough | `<s>`, `<strike>`, `<del>` | Corrections |
| Spoiler | `<span class="tg-spoiler">` | Hidden content |
| Inline Code | `<code>` | Variable/function names |
| Code Block | `<pre>` | Multi-line code |
| Syntax Highlight | `<pre><code class="language-*">` | Programming code |
| Quote | `<blockquote>` | Citations |
| Expandable Quote | `<blockquote expandable>` | Long optional content |
| Link | `<a href="">` | URLs and mentions |

Remember to always escape special characters and set `parse_mode="HTML"` when sending formatted messages!

