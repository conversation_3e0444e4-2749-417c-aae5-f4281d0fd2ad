"""
Enhanced Timer-Based UI Components with Modern Animations
Provides sophisticated timer functionality with dynamic emoji changes and loading animations
"""

import asyncio
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Callable, List
from dataclasses import dataclass

from aiogram.types import CallbackQuery, InlineKeyboardMarkup
from utils.enhanced_keyboards import create_enhanced_keyboard, KeyboardStyle, ButtonPriority
from utils.ui_components import create_message, MessageType
from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class TimerConfig:
    """Configuration for timer-based components"""
    duration: int = 30  # Total duration in seconds
    update_interval: int = 2  # Update frequency in seconds
    warning_threshold: int = 10  # When to show warning colors
    critical_threshold: int = 5  # When to show critical colors
    disable_on_expire: bool = True  # Disable button when timer expires


class TimerEmojis:
    """Dynamic emoji sets for timer-based components"""
    
    # Check button emojis with urgency progression
    CHECK_NORMAL = "🔍"  # 30-21 seconds
    CHECK_WARNING = "⚡"  # 20-11 seconds  
    CHECK_URGENT = "🚨"   # 10-6 seconds
    CHECK_CRITICAL = "💥" # 5-1 seconds
    CHECK_EXPIRED = "⏰"  # 0 seconds
    CHECK_DISABLED = "🚫" # Permanently disabled
    
    # Loading animation sequence
    LOADING_FRAMES = ["⏳", "⌛", "⏳", "⌛"]
    
    # Status indicators
    PROCESSING = "🔄"
    SUCCESS = "✅"
    ERROR = "❌"
    TIMEOUT = "⏰"


class LoadingAnimation:
    """Manages loading animations for better UX"""
    
    @staticmethod
    def get_loading_message(operation: str, style: str = "dots") -> str:
        """Generate loading message with animation"""
        animations = {
            "dots": ["⏳ Loading", "⏳ Loading.", "⏳ Loading..", "⏳ Loading..."],
            "spinner": ["🔄 Processing", "⚡ Processing", "💫 Processing", "✨ Processing"],
            "pulse": ["💓 Working", "💗 Working", "💓 Working", "💗 Working"],
            "wave": ["🌊 Checking", "🌀 Checking", "🌊 Checking", "🌀 Checking"]
        }
        
        frames = animations.get(style, animations["dots"])
        # Return a random frame for now - in practice this would cycle
        return f"{frames[0]} {operation}..."
    
    @staticmethod
    def create_checking_message() -> str:
        """Create an animated checking message"""
        msg = create_message(MessageType.INFO)
        msg.set_title("🔍 Checking Card Status", "⚡")
        
        msg.add_section(
            "🔄 Processing",
            "🌊 Connecting to verification system...\n"
            "⚡ Analyzing card data...\n"
            "🎯 Fetching real-time status...\n"
            "✨ Almost done...",
            "🚀"
        )
        
        msg.add_footer("Please wait while we verify your card...")
        return msg.build(add_watermark=False)


class TimerKeyboard:
    """Enhanced keyboard with timer-based check button"""
    
    @staticmethod
    def create_enhanced_card_keyboard(
        card_id: str,
        order_id: str,
        is_unmasked: bool = False,
        timer_config: Optional[TimerConfig] = None,
        current_time: Optional[int] = None,
        check_enabled: bool = True,
        check_cooldown_remaining: int = 0
    ) -> InlineKeyboardMarkup:
        """
        Create enhanced keyboard with timer-based check button
        
        Args:
            card_id: Card identifier
            order_id: Order identifier  
            is_unmasked: Whether card is already unmasked
            timer_config: Timer configuration
            current_time: Current timestamp
            check_enabled: Whether check functionality is enabled
            check_cooldown_remaining: Remaining cooldown time in seconds
            
        Returns:
            Enhanced keyboard with timer functionality
        """
        kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
        
        # Primary action: Unmask or Download
        if not is_unmasked:
            kb.add_button(
                "🔓 Unmask Card",
                f"orders:unmask:{order_id}:{card_id}",
                ButtonPriority.PRIMARY
            )
        else:
            kb.add_button(
                "📥 Download",
                f"orders:download:{order_id}:{card_id}",
                ButtonPriority.PRIMARY
            )
        
        # Enhanced timer-based check button
        if is_unmasked and check_enabled and timer_config:
            check_button = TimerKeyboard._create_timer_check_button(
                card_id, order_id, timer_config, current_time, check_cooldown_remaining
            )
            if check_button:
                kb.add_button(
                    check_button["text"],
                    check_button["callback_data"],
                    check_button["priority"]
                )
        
        # Navigation buttons
        kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
        kb.add_navigation_row(back_text="🏠 Main", back_callback="menu:main")
        
        return kb.build()
    
    @staticmethod
    def _create_timer_check_button(
        card_id: str,
        order_id: str, 
        timer_config: TimerConfig,
        current_time: Optional[int] = None,
        cooldown_remaining: int = 0
    ) -> Optional[Dict[str, Any]]:
        """Create timer-based check button with dynamic styling"""
        
        if cooldown_remaining > 0:
            return {
                "text": f"⏳ Check in {cooldown_remaining}s",
                "callback_data": f"orders:check_cooldown:{order_id}:{card_id}",
                "priority": ButtonPriority.TERTIARY
            }
        
        if not current_time:
            current_time = int(datetime.now(timezone.utc).timestamp())
        
        # Calculate expiry time (30 seconds from unmask)
        expiry_time = current_time + timer_config.duration
        remaining = expiry_time - current_time
        
        if remaining <= 0:
            if timer_config.disable_on_expire:
                return {
                    "text": f"{TimerEmojis.CHECK_EXPIRED} Check Expired",
                    "callback_data": f"orders:check_expired:{order_id}:{card_id}",
                    "priority": ButtonPriority.TERTIARY
                }
            else:
                return {
                    "text": f"{TimerEmojis.CHECK_NORMAL} Check Card",
                    "callback_data": f"orders:check:{order_id}:{card_id}",
                    "priority": ButtonPriority.SECONDARY
                }
        
        # Dynamic emoji and priority based on remaining time
        if remaining > timer_config.warning_threshold:
            emoji = TimerEmojis.CHECK_NORMAL
            priority = ButtonPriority.SECONDARY
        elif remaining > timer_config.critical_threshold:
            emoji = TimerEmojis.CHECK_WARNING  
            priority = ButtonPriority.SUCCESS
        else:
            emoji = TimerEmojis.CHECK_CRITICAL
            priority = ButtonPriority.DANGER
        
        return {
            "text": f"{emoji} Check ({remaining}s)",
            "callback_data": f"orders:check:{order_id}:{card_id}:{expiry_time}",
            "priority": priority
        }


class TimerManager:
    """Manages timer updates and animations"""
    
    def __init__(self):
        self._active_timers: Dict[str, asyncio.Task] = {}
        self._timer_configs: Dict[str, TimerConfig] = {}
    
    async def start_timer(
        self,
        timer_id: str,
        callback: CallbackQuery,
        card_id: str,
        order_id: str,
        timer_config: TimerConfig,
        update_callback: Callable
    ) -> None:
        """Start a new timer with automatic updates"""
        
        # Cancel existing timer if any
        await self.stop_timer(timer_id)
        
        # Store configuration
        self._timer_configs[timer_id] = timer_config
        
        # Create and start timer task
        task = asyncio.create_task(
            self._timer_loop(timer_id, callback, card_id, order_id, timer_config, update_callback)
        )
        self._active_timers[timer_id] = task
        
        logger.info(f"Started timer {timer_id} for {timer_config.duration}s")
    
    async def stop_timer(self, timer_id: str) -> None:
        """Stop and cleanup timer"""
        if timer_id in self._active_timers:
            task = self._active_timers[timer_id]
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            del self._active_timers[timer_id]
            
        if timer_id in self._timer_configs:
            del self._timer_configs[timer_id]
            
        logger.debug(f"Stopped timer {timer_id}")
    
    async def _timer_loop(
        self,
        timer_id: str,
        callback: CallbackQuery,
        card_id: str,
        order_id: str,
        timer_config: TimerConfig,
        update_callback: Callable
    ) -> None:
        """Main timer loop with updates"""
        try:
            start_time = int(datetime.now(timezone.utc).timestamp())
            
            while True:
                current_time = int(datetime.now(timezone.utc).timestamp())
                elapsed = current_time - start_time
                remaining = timer_config.duration - elapsed
                
                if remaining <= 0:
                    # Timer expired
                    await update_callback(callback, card_id, order_id, 0, expired=True)
                    break
                
                # Update keyboard
                await update_callback(callback, card_id, order_id, remaining, expired=False)
                
                # Wait for next update
                await asyncio.sleep(timer_config.update_interval)
                
        except asyncio.CancelledError:
            logger.debug(f"Timer {timer_id} cancelled")
        except Exception as e:
            logger.error(f"Timer {timer_id} error: {e}")
        finally:
            # Cleanup
            if timer_id in self._active_timers:
                del self._active_timers[timer_id]
            if timer_id in self._timer_configs:
                del self._timer_configs[timer_id]
    
    def get_active_timers(self) -> List[str]:
        """Get list of active timer IDs"""
        return list(self._active_timers.keys())
    
    async def cleanup_all(self) -> None:
        """Stop all active timers"""
        timer_ids = list(self._active_timers.keys())
        for timer_id in timer_ids:
            await self.stop_timer(timer_id)
        logger.info("Cleaned up all timers")


# Global timer manager instance
_timer_manager = None

def get_timer_manager() -> TimerManager:
    """Get global timer manager instance"""
    global _timer_manager
    if _timer_manager is None:
        _timer_manager = TimerManager()
    return _timer_manager


class AnimatedMessages:
    """Animated message templates for better UX"""
    
    @staticmethod
    def create_unmask_loading() -> str:
        """Create animated unmask loading message"""
        msg = create_message(MessageType.INFO)
        msg.set_title("🔓 Unmasking Card", "⚡")
        
        msg.add_section(
            "🔄 Processing",
            "🌊 Connecting to secure vault...\n"
            "🔑 Authenticating access...\n" 
            "🎯 Decrypting card data...\n"
            "✨ Preparing display...",
            "🚀"
        )
        
        msg.add_footer("🔒 Your data is encrypted end-to-end")
        return msg.build(add_watermark=False)
    
    @staticmethod
    def create_check_processing() -> str:
        """Create animated check processing message"""
        return LoadingAnimation.create_checking_message()
    
    @staticmethod
    def create_timer_expired_message() -> str:
        """Create message for expired timer"""
        msg = create_message(MessageType.WARNING)
        msg.set_title("⏰ Check Timer Expired", "⚠️")
        
        msg.add_section(
            "🚫 Access Expired", 
            "The 30-second check window has expired.\n"
            "For security reasons, card checking is now disabled.\n\n"
            "💡 <b>Next time:</b> Use the check button within 30 seconds of unmasking.",
            "⏰"
        )
        
        return msg.build(add_watermark=False)