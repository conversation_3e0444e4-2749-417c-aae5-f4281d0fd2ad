# Developer Guide: Multi-Item Checkout

## Quick Reference for Working with Multi-Item Checkouts

### Key Principles

1. **Always account for quantity** when calculating prices
2. **Handle partial failures gracefully** - some items may fail while others succeed
3. **Use proper transactions** for data consistency
4. **Log comprehensively** for debugging and monitoring

---

## Common Patterns

### 1. Calculating Total Price

**❌ WRONG - Missing Quantity:**
```python
total_price = sum(
    float(item.get('price', 0)) * (1 - float(item.get('discount', 0)) / 100)
    for item in cart_items
)
```

**✅ CORRECT - Including Quantity:**
```python
total_price = sum(
    float(item.get('price', 0)) * (1 - float(item.get('discount', 0)) / 100) * int(item.get('quantity', 1))
    for item in cart_items
)
```

### 2. Creating Purchase Records

**❌ WRONG - Failing on First Error:**
```python
for item in cart_items:
    try:
        purchase = create_purchase(item)
        await save_purchase(purchase)
    except Exception as e:
        logger.error(f"Failed: {e}")
        raise  # This kills the entire checkout!
```

**✅ CORRECT - Continue on Errors:**
```python
created = 0
failed = 0

for item in cart_items:
    try:
        purchase = create_purchase(item)
        await save_purchase(purchase)
        created += 1
    except Exception as e:
        logger.error(f"Failed item {item.get('id')}: {e}")
        failed += 1
        continue  # Keep processing other items

if created == 0:
    raise Exception("All items failed")
elif failed > 0:
    logger.warning(f"Partial success: {created} succeeded, {failed} failed")
```

### 3. Using Database Transactions

**❌ WRONG - No Transaction Protection:**
```python
async def checkout(user_id, items):
    # Deduct balance
    await deduct_balance(user_id, total)
    
    # Create purchases (might fail!)
    await create_purchases(items)
```

**✅ CORRECT - Using Transactions:**
```python
from database.connection import database_transaction

async def checkout(user_id, items):
    async with database_transaction():
        # Both operations are now atomic
        await deduct_balance(user_id, total)
        await create_purchases(items)
        # Auto-commits if no exception
        # Auto-rollbacks if exception
```

### 4. Handling Quantities in Metadata

**✅ BEST PRACTICE:**
```python
purchase_metadata = {
    "quantity": quantity,
    "unit_price": base_price * (1 - discount / 100),
    "total_price": base_price * (1 - discount / 100) * quantity,
    "discount": discount,
    "original_price": base_price
}
```

This gives you:
- Full audit trail
- Easy refund calculations
- Clear price breakdown
- Historical accuracy

---

## Validation Checklist

Before processing a cart item, validate:

```python
def validate_cart_item(item: Dict[str, Any]) -> bool:
    """Validate a cart item before processing"""
    
    # Check required fields
    if not item.get('product_id') and not item.get('_id'):
        logger.warning("Missing product ID")
        return False
    
    # Check quantity
    try:
        quantity = int(item.get('quantity', 1))
        if quantity <= 0:
            logger.warning(f"Invalid quantity: {quantity}")
            return False
    except (ValueError, TypeError):
        logger.warning("Quantity is not a number")
        return False
    
    # Check price
    try:
        price = float(item.get('price', 0))
        if price < 0:
            logger.warning(f"Negative price: {price}")
            return False
    except (ValueError, TypeError):
        logger.warning("Price is not a number")
        return False
    
    # Check discount
    try:
        discount = float(item.get('discount', 0))
        if discount < 0 or discount > 100:
            logger.warning(f"Invalid discount: {discount}")
            return False
    except (ValueError, TypeError):
        logger.warning("Discount is not a number")
        return False
    
    return True
```

---

## Error Handling Best Practices

### 1. Categorize Errors

```python
class CheckoutError(Exception):
    """Base checkout error"""
    pass

class ValidationError(CheckoutError):
    """Invalid item data"""
    pass

class PaymentError(CheckoutError):
    """Payment processing failed"""
    pass

class InventoryError(CheckoutError):
    """Item out of stock"""
    pass
```

### 2. Provide Context

```python
try:
    await create_purchase(item)
except Exception as e:
    logger.error(
        f"Purchase creation failed",
        extra={
            "item_id": item.get('product_id'),
            "user_id": user_id,
            "error": str(e),
            "error_type": type(e).__name__
        }
    )
```

### 3. User Notifications

```python
# For partial success
if failed_items > 0 and successful_items > 0:
    await notify_user(
        user_id,
        f"⚠️ Partial Success\n"
        f"✅ {successful_items} items processed\n"
        f"❌ {failed_items} items failed\n"
        f"You were charged only for successful items."
    )

# For complete failure
elif successful_items == 0:
    await notify_user(
        user_id,
        f"❌ Checkout Failed\n"
        f"None of the items could be processed.\n"
        f"Your balance was not charged.\n"
        f"Please contact support if this persists."
    )

# For complete success
else:
    await notify_user(
        user_id,
        f"✅ Checkout Successful\n"
        f"{successful_items} items processed.\n"
        f"Total: ${total_amount:.2f}"
    )
```

---

## Testing Guidelines

### Unit Tests

```python
@pytest.mark.asyncio
async def test_checkout_with_multiple_items():
    """Test checkout with multiple valid items"""
    items = [
        {"product_id": "1", "price": 10, "quantity": 2, "discount": 0},
        {"product_id": "2", "price": 20, "quantity": 1, "discount": 10},
        {"product_id": "3", "price": 15, "quantity": 3, "discount": 5},
    ]
    
    result = await checkout_service.process(user_id="test", items=items)
    
    assert result.success is True
    assert len(result.purchases) == 3
    # 10*2 + 20*0.9 + 15*3*0.95 = 20 + 18 + 42.75 = 80.75
    assert result.total_amount == 80.75
```

### Integration Tests

```python
@pytest.mark.asyncio
async def test_checkout_with_partial_failure():
    """Test checkout handles partial failures correctly"""
    items = [
        {"product_id": "1", "price": 10, "quantity": 1, "discount": 0},
        {"product_id": None, "price": 20, "quantity": 1, "discount": 0},  # Invalid
        {"product_id": "3", "price": 15, "quantity": 1, "discount": 0},
    ]
    
    result = await checkout_service.process(user_id="test", items=items)
    
    assert result.success is True  # Partial success
    assert len(result.purchases) == 2  # Only valid items
    assert result.failed_items == 1
```

---

## Monitoring and Alerts

### Key Metrics to Track

1. **Success Rate**
   ```python
   success_rate = successful_items / total_items * 100
   ```
   Alert if < 95%

2. **Partial Failure Rate**
   ```python
   partial_failure_rate = checkouts_with_failures / total_checkouts * 100
   ```
   Alert if > 5%

3. **Average Items Per Checkout**
   ```python
   avg_items = total_items / total_checkouts
   ```
   Track trend over time

4. **Transaction Rollback Rate**
   ```python
   rollback_rate = rolled_back_transactions / total_transactions * 100
   ```
   Alert if > 1%

### Logging Strategy

```python
# At start of checkout
logger.info(
    f"Starting checkout",
    extra={
        "user_id": user_id,
        "item_count": len(items),
        "total_amount": total_amount,
        "checkout_id": checkout_id
    }
)

# During processing
for i, item in enumerate(items):
    logger.debug(
        f"Processing item {i+1}/{len(items)}",
        extra={
            "item_id": item.get('product_id'),
            "quantity": item.get('quantity'),
            "price": item.get('price')
        }
    )

# At end of checkout
logger.info(
    f"Checkout completed",
    extra={
        "checkout_id": checkout_id,
        "successful_items": successful_items,
        "failed_items": failed_items,
        "total_charged": total_charged,
        "duration_ms": duration_ms
    }
)
```

---

## Common Pitfalls to Avoid

### 1. ❌ Forgetting Quantities
```python
# Wrong
total = item['price'] * (1 - item['discount']/100)

# Correct
total = item['price'] * (1 - item['discount']/100) * item['quantity']
```

### 2. ❌ Not Handling Partial Failures
```python
# Wrong - all or nothing
if any_item_failed:
    rollback_everything()
    return error

# Correct - allow partial success
if successful_items > 0:
    commit_successful_items()
    notify_user_of_partial_success()
```

### 3. ❌ Inconsistent Data Types
```python
# Wrong - mixing strings and numbers
quantity = item['quantity']  # Might be string "2"
total = price * quantity  # Type error or wrong result

# Correct - always convert
quantity = int(item.get('quantity', 1))
price = float(item.get('price', 0))
total = price * quantity
```

### 4. ❌ Missing Transaction Protection
```python
# Wrong - operations are not atomic
await deduct_balance(user_id, amount)
await create_purchases(items)  # If this fails, money is lost!

# Correct - use transactions
async with database_transaction():
    await deduct_balance(user_id, amount)
    await create_purchases(items)
```

---

## Quick Debugging Tips

### Problem: "Total price is wrong"
✅ **Check**: Are you multiplying by quantity?

### Problem: "Checkout fails with multiple items but works with one"
✅ **Check**: Are you using transactions? Is error handling correct?

### Problem: "User charged but no purchases created"
✅ **Check**: Are you using database transactions? Check rollback logic.

### Problem: "Some items silently disappear"
✅ **Check**: Are you logging when items are skipped? Check validation logic.

### Problem: "Duplicate purchase records"
✅ **Check**: Are you using idempotency keys? Check for retry logic.

---

## File References

- **Transaction Support**: `database/connection.py:374-415`
- **Purchase Creation**: `services/checkout_queue_service.py:2917-3197`
- **Price Calculation**: `api_v1/services/checkout_processor_service.py:81-85`
- **Error Handling**: `api_v1/services/checkout_processor_service.py:275-295`

---

## Support

For questions or issues:
1. Check logs for detailed error messages
2. Review `MULTI_ITEM_CHECKOUT_FIX.md` for comprehensive documentation
3. Run `tests/test_multi_item_checkout.py` to verify functionality
4. Contact the development team if issues persist

---

**Last Updated**: 2025-10-26
**Version**: 1.0

