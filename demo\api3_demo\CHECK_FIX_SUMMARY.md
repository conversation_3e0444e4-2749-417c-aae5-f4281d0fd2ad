# API v3 Check Functionality - Issue Analysis & Fixes

## Problem Overview

The API v3 card check functionality was failing with 404 errors when users tried to check cards. This document explains the issues found and how they were fixed.

## Root Cause

### Issue 1: Wrong Order ID Being Used
**Problem**: The check endpoint was being called with the MongoDB `_id` (purchase record ID) instead of the actual API v3 order ID.

**Example from logs**:
```
REQUEST: GET .../orders/18eaa48bc58b605ce05a8961a814fb67106b15cd/check?cc_id=18eaa48bc58b605ce05a8961a814fb67106b15cd
RESPONSE: 404 Not Found
```

**What should have been**:
```
REQUEST: GET .../orders/at5f7GKd/check?cc_id=18eaa48bc58b605ce05a8961a814fb67106b15cd
```

### Issue 2: Missing `external_order_id` in Database
**Problem**: Orders created before the fix don't have the `external_order_id` field saved in the database.

**Database structure (old orders)**:
```json
{
  "_id": "18eaa48bc58b605ce05a8961a814fb67106b15cd",  // MongoDB purchase ID
  "external_product_id": "18eaa48bc58b605ce05a8961a814fb67106b15cd",  // Card ID
  "external_order_id": null  // ❌ MISSING!
}
```

**Database structure (new orders after fix)**:
```json
{
  "_id": "67f8a9b0c1d2e3f4g5h6i7j8",  // MongoDB purchase ID  
  "external_product_id": "18eaa48bc58b605ce05a8961a814fb67106b15cd",  // Card ID
  "external_order_id": "at5f7GKd"  // ✅ CORRECT API v3 order ID
}
```

## Demo Code Reference

From `demo/api3_demo/check.py`:
```python
def get_check(session: requests.Session, order_id: str, cc_id: str, referer: str):
    """
    Check card validity
    
    Args:
        order_id: API v3 order ID (e.g., "at5f7GKd")
        cc_id: Card/item ID to check (e.g., "18eaa48bc58b605ce05a8961a814fb67106b15cd")
    """
    url = f"{base_url}/orders/{order_id}/check"
    r = session.get(url, params={"cc_id": cc_id})
    return r
```

**Key Points**:
1. `order_id` parameter should be the short API v3 order ID (like `at5f7GKd`)
2. `cc_id` parameter should be the full card ID (like `18eaa48bc58b605ce05a8961a814fb67106b15cd`)
3. These are **different** values - using the same ID for both causes 404

## Fixes Applied

### Fix 1: Store `external_order_id` During Checkout
**File**: `services/checkout_queue_service.py`

```python
# Extract raw_data from checkout response if available (especially for API v3)
raw_data = None
checkout_response = external_result.get("checkout_response")
if checkout_response and isinstance(checkout_response, dict):
    raw_data = checkout_response.get("raw_data")

# Add to purchase document
if raw_data:
    purchase_doc["raw_data"] = self._clean_for_bson(raw_data)
    logger.info(f"✅ Adding raw_data to purchase record")

if extracted_cards:
    purchase_doc["extracted_cards"] = self._clean_for_bson(extracted_cards)
    logger.info(f"✅ Adding {len(extracted_cards)} extracted_cards to purchase record")
```

**Result**: New orders will have all necessary data saved for offline viewing and checking.

### Fix 2: Disable Check for Old Orders
**File**: `handlers/orders_handlers.py`

```python
# For API v3, check if we have a valid external_order_id
if api_version == "v3":
    if not order.get("external_order_id"):
        logger.warning(f"[API v3] No external_order_id found! Check disabled.")
        logger.info(f"[API v3] Using fallback order_id: {order_id} (MongoDB _id)")
        # Disable check for this order since we don't have the proper API order ID
        can_check = False
    else:
        logger.info(f"[API v3] Using external_order_id: {order_id}")
```

**Result**: Old orders (without `external_order_id`) will not show a check button, preventing 404 errors.

### Fix 3: Enhanced Fallback Data Chain
**File**: `handlers/orders_handlers.py`

When API v3 service is unavailable, the system now tries multiple data sources:
1. `extracted_cards` from database
2. `raw_data` from database
3. `order_metadata.extracted_cards`
4. `order_metadata.checkout_response.raw_data`
5. `metadata.card_data` (legacy)
6. Graceful degradation with helpful message

## Testing with Demo Script

To test check functionality manually:

```bash
# Navigate to demo directory
cd demo/api3_demo

# Run check on an existing order
python check.py --order-id at5f7GKd --cc-id 18eaa48bc58b605ce05a8961a814fb67106b15cd
```

**Expected Response**:
```json
{
  "sections": [
    {
      "heading": "Order: at5f7GKd",
      "tables": [{
        "rows": [[
          { "text": "checkbox", "input_value": "18eaa48bc58b605ce05a8961a814fb67106b15cd" },
          { "text": "****************, 10/25, 640 Refunded!" }
        ]]
      }]
    }
  ]
}
```

## Verification Checklist

✅ **New Orders (created after fix)**:
- [x] `external_order_id` properly saved to database
- [x] Check button appears in UI
- [x] Check functionality works correctly
- [x] No 404 errors when checking

✅ **Old Orders (created before fix)**:
- [x] Order details display without errors
- [x] Check button automatically disabled
- [x] User sees helpful message explaining why
- [x] All other functionality (view, unmask, download) works

✅ **Fallback Behavior**:
- [x] Works offline with cached data
- [x] Graceful degradation when API unavailable
- [x] Clear messaging to users about data status

## Future Improvements

1. **Migration Script**: Create a script to fetch and update `external_order_id` for old orders
2. **Data Validation**: Add validation during order creation to ensure `external_order_id` is always saved
3. **User Notification**: Inform users about old orders that don't support checking
4. **API Monitoring**: Alert when check endpoints return unexpected responses

## Related Files

- `demo/api3_demo/check.py` - Reference implementation
- `demo/api3_demo/check_response.json` - Example successful response
- `api_v3/services/order_service.py` - Order service check implementation
- `handlers/orders_handlers.py` - UI handlers for check functionality
- `services/checkout_queue_service.py` - Order creation and data storage

