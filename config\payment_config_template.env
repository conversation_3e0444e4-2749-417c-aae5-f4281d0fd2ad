# Payment Module Configuration
# Add these settings to your .env file

# OXA Pay Configuration (Required for payment functionality)
OXA_PAY_API_KEY="your_oxa_pay_api_key_here"
OXA_PAY_CALLBACK_URL=""  # Leave empty for dynamic generation based on server IP

# Payment Environment Settings
PAYMENT_DEVELOPMENT_MODE=true  # Set to false for production
PAYMENT_TESTING_MODE=false     # Set to true to skip HMAC verification
PAYMENT_DEBUG_MODE=false        # Set to true for debug logging

# Payment Limits
PAYMENT_MIN_AMOUNT=10.0
PAYMENT_MAX_AMOUNT=1000.0

# Payment Callback Server
PAYMENT_CALLBACK_PORT=3000
