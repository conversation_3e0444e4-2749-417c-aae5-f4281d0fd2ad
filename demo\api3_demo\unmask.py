import argparse
import json
import sys
import logging
from typing import List, Optional

import requests
from bs4 import BeautifulSoup

from session_manager import get_authenticated_session, save_session_cookies
from login import REFERER

# Suppress login module logging for cleaner output
logging.getLogger("login_grabber").setLevel(logging.ERROR)

# Import the proper response parser
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


class OrderUnmasker:
    """Simplified class to handle order item unmasking"""

    def __init__(self):
        logger = logging.getLogger("login_grabber")
        self.session = get_authenticated_session(logger)
        self.base_url = REFERER.rstrip("/")

    def get_order_url(self, order_id: str) -> str:
        """Build the order detail page URL"""
        return f"{self.base_url}/orders/{order_id}"

    def get_order_page(self, order_id: str) -> requests.Response:
        """Fetch the order page"""
        url = self.get_order_url(order_id)
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        return response

    def extract_form_data(self, html_content: str) -> tuple[Optional[str], List[str]]:
        """Extract CSRF token and available item IDs from the page"""
        soup = BeautifulSoup(html_content, "html.parser")

        # Find CSRF token
        csrf_token = None
        token_input = soup.find("input", {"name": "_token"})
        if token_input:
            csrf_token = token_input.get("value")

        # Find checkboxes for items to unmask
        checkboxes = soup.find_all("input", {"type": "checkbox", "name": "checked[]"})

        available_items = []
        for checkbox in checkboxes:
            item_id = checkbox.get("value")
            if item_id:
                available_items.append(item_id)

        return csrf_token, available_items

    def unmask_items(
        self, order_id: str, item_ids: List[str], csrf_token: Optional[str]
    ) -> dict:
        """Send the unmask request"""
        url = self.get_order_url(order_id)

        # Prepare the form data
        data = {
            "_token": csrf_token or "",
            "_method": "PUT",
            "target": "Unmask Selected",
        }

        # Add selected items
        if item_ids:
            data["checked[]"] = item_ids

        # Send the request
        response = self.session.post(
            url,
            data=data,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "Referer": url,
            },
            allow_redirects=True,
            timeout=30,
        )

        # Use the proper response parser to get structured data
        parsed_response = _response_to_jsonable(response)

        # Show only sections data
        sections = parsed_response.get("sections", [])
        print(json.dumps(sections, indent=2, ensure_ascii=False))

        return {
            "success": response.status_code in [200, 302],
            "sections": sections,
        }

    def process_order(
        self, order_id: str, specific_items: Optional[List[str]] = None
    ) -> dict:
        """Main process to unmask items in an order"""
        try:
            # Step 1: Get the order page
            page_response = self.get_order_page(order_id)

            # Step 2: Extract form data
            csrf_token, available_items = self.extract_form_data(page_response.text)

            if not available_items:
                return {"success": False, "error": "No unmaskable items found"}

            # Step 3: Determine which items to unmask
            items_to_unmask = []
            if specific_items:
                # Use specific items if provided
                for item in specific_items:
                    if item in available_items:
                        items_to_unmask.append(item)
            else:
                # Auto-select the first item if none specified
                items_to_unmask = [available_items[0]]

            if not items_to_unmask:
                return {"success": False, "error": "No valid items selected"}

            # Step 4: Unmask the items
            result = self.unmask_items(order_id, items_to_unmask, csrf_token)

            # Step 5: Save result - only sections data
            output_file = f"unmask_{order_id}_result.json"
            sections_data = result.get("sections", [])
            
            # Print JSON response to console
            print("\n" + "=" * 60)
            print(f"UNMASK {order_id} RESPONSE (JSON)")
            print("=" * 60)
            print(json.dumps(sections_data, ensure_ascii=False, indent=2))
            print("=" * 60 + "\n")
            
            # Save to file
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(sections_data, f, indent=2, ensure_ascii=False)

            # Save session cookies for future use
            logger = logging.getLogger("login_grabber")
            save_session_cookies(self.session, logger=logger)

            return result

        except requests.RequestException as e:
            error_msg = f"Network error: {str(e)}"
            return {"success": False, "error": error_msg, "sections": []}
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            return {"success": False, "error": error_msg, "sections": []}


def parse_item_list(item_string: str) -> List[str]:
    """Parse comma-separated item IDs"""
    if not item_string:
        return []
    return [item.strip() for item in item_string.split(",") if item.strip()]


def main():
    parser = argparse.ArgumentParser(
        description="Unmask items in an order",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python unmask_simple.py ORDER123
  python unmask_simple.py ORDER123 --item-id ITEM001
  python unmask_simple.py ORDER123 --item-id ITEM001,ITEM002,ITEM003
        """,
    )

    parser.add_argument("order_id", help="Order ID to process")
    parser.add_argument(
        "--item-id", help="Specific item IDs to unmask (comma-separated)", default=None
    )

    args = parser.parse_args()

    # Parse item IDs if provided
    specific_items = None
    if args.item_id:
        specific_items = parse_item_list(args.item_id)
        if not specific_items:
            sys.exit(1)

    # Create unmasker and process the order
    unmasker = OrderUnmasker()
    result = unmasker.process_order(args.order_id, specific_items)

    # Always exit with success since we're only interested in the data
    sys.exit(0)


if __name__ == "__main__":
    main()
