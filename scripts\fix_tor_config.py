#!/usr/bin/env python3
"""
Tor Configuration Fix Utility

This script automatically detects Tor configuration issues and provides
fixes for API v3 connectivity problems.
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from api_v3.utils.tor_detector import TorPortDetector
from dotenv import load_dotenv


def check_env_file():
    """Check if .env file exists and load it."""
    env_path = project_root / ".env"
    if not env_path.exists():
        print("❌ .env file not found!")
        print(f"   Expected location: {env_path}")
        print("   Please create .env file with API v3 configuration")
        return False
    
    load_dotenv(env_path)
    print(f"✅ Found .env file: {env_path}")
    return True


def analyze_current_config():
    """Analyze current SOCKS configuration."""
    print("\n🔍 Analyzing Current Configuration")
    print("=" * 50)
    
    socks_url = os.getenv("SOCKS_URL")
    external_socks_url = os.getenv("EXTERNAL_V3_SOCKS_URL")
    use_socks = os.getenv("USE_SOCKS_PROXY", "").lower() in ("true", "1", "yes")
    
    print(f"USE_SOCKS_PROXY: {use_socks}")
    print(f"SOCKS_URL: {socks_url or 'Not set (auto-detection enabled)'}")
    print(f"EXTERNAL_V3_SOCKS_URL: {external_socks_url or 'Not set'}")
    
    return socks_url, use_socks


def detect_tor_status():
    """Detect current Tor status."""
    print("\n🔍 Detecting Tor Status")
    print("=" * 50)
    
    detector = TorPortDetector()
    
    # Check individual ports
    tor_9150 = detector.check_port(port=9150)
    tor_9050 = detector.check_port(port=9050)
    
    print(f"Port 9150 (Tor Browser): {'✅ OPEN' if tor_9150 else '❌ CLOSED'}")
    print(f"Port 9050 (System Tor):  {'✅ OPEN' if tor_9050 else '❌ CLOSED'}")
    
    # Auto-detection
    detected_url = detector.get_socks_url()
    if detected_url:
        print(f"\n🎯 Auto-detected: {detected_url}")
    else:
        print("\n❌ No Tor SOCKS proxy detected")
    
    return tor_9150, tor_9050, detected_url


def provide_recommendations(socks_url, use_socks, tor_9150, tor_9050, detected_url):
    """Provide configuration recommendations."""
    print("\n💡 Recommendations")
    print("=" * 50)
    
    if not use_socks:
        print("⚠️  SOCKS proxy is disabled")
        print("   For .onion domains, you need: USE_SOCKS_PROXY=true")
        return
    
    if detected_url:
        if socks_url:
            # Manual configuration exists
            configured_port = "9150" if "9150" in socks_url else "9050" if "9050" in socks_url else "unknown"
            detected_port = "9150" if "9150" in detected_url else "9050"
            
            if configured_port == detected_port:
                print("✅ Configuration matches detected Tor port")
                print(f"   Using: {socks_url}")
            else:
                print("⚠️  Configuration mismatch detected!")
                print(f"   Configured: {socks_url} (port {configured_port})")
                print(f"   Detected:   {detected_url} (port {detected_port})")
                print("\n🔧 Recommended fix:")
                print(f"   Update .env: SOCKS_URL={detected_url}")
        else:
            # Auto-detection mode
            print("✅ Auto-detection is working correctly")
            print(f"   Will use: {detected_url}")
            print("   No manual configuration needed!")
    else:
        # No Tor detected
        print("❌ No Tor SOCKS proxy is accessible")
        print("\n🔧 To fix this:")
        
        if not tor_9150 and not tor_9050:
            print("\n   Option 1: Start Tor Browser")
            print("     1. Download: https://www.torproject.org/download/")
            print("     2. Open Tor Browser")
            print("     3. Wait for connection")
            print("     4. Keep running in background")
            
            print("\n   Option 2: Install System Tor")
            print("     Linux:")
            print("       sudo apt install tor")
            print("       sudo systemctl start tor")
            print("     macOS:")
            print("       brew install tor")
            print("       brew services start tor")


def generate_env_fix(detected_url):
    """Generate .env file fix."""
    if not detected_url:
        return None
    
    print("\n🔧 Generating .env Fix")
    print("=" * 50)
    
    env_path = project_root / ".env"
    backup_path = project_root / ".env.backup"
    
    try:
        # Read current .env
        with open(env_path, 'r') as f:
            lines = f.readlines()
        
        # Create backup
        with open(backup_path, 'w') as f:
            f.writelines(lines)
        print(f"✅ Created backup: {backup_path}")
        
        # Update lines
        updated_lines = []
        socks_url_updated = False
        
        for line in lines:
            if line.startswith("SOCKS_URL=") and not line.startswith("#"):
                # Update existing SOCKS_URL
                updated_lines.append(f"SOCKS_URL={detected_url}\n")
                socks_url_updated = True
                print(f"   Updated: SOCKS_URL={detected_url}")
            elif line.startswith("# SOCKS_URL="):
                # Uncomment and update
                updated_lines.append(f"SOCKS_URL={detected_url}\n")
                socks_url_updated = True
                print(f"   Uncommented and updated: SOCKS_URL={detected_url}")
            else:
                updated_lines.append(line)
        
        # Add SOCKS_URL if not found
        if not socks_url_updated:
            updated_lines.append(f"\n# Auto-detected Tor configuration\n")
            updated_lines.append(f"SOCKS_URL={detected_url}\n")
            print(f"   Added: SOCKS_URL={detected_url}")
        
        # Write updated .env
        with open(env_path, 'w') as f:
            f.writelines(updated_lines)
        
        print(f"✅ Updated .env file")
        return True
        
    except Exception as e:
        print(f"❌ Error updating .env: {e}")
        return False


def test_configuration():
    """Test the configuration after fixes."""
    print("\n🧪 Testing Configuration")
    print("=" * 50)
    
    try:
        # Reload environment
        load_dotenv(project_root / ".env", override=True)
        
        # Test auto-detection
        detector = TorPortDetector()
        detected_url = detector.get_socks_url()
        
        if detected_url:
            print(f"✅ Auto-detection working: {detected_url}")
            
            # Test API v3 config loading
            try:
                from api_v3.config import get_api_v3_config_from_env
                config = get_api_v3_config_from_env()
                
                if config and config.use_socks_proxy:
                    print(f"✅ API v3 config loaded: {config.socks_url}")
                    print("🎉 Configuration is ready for API v3!")
                    return True
                else:
                    print("⚠️  API v3 configuration incomplete")
                    
            except Exception as e:
                print(f"⚠️  API v3 config test failed: {e}")
        else:
            print("❌ Auto-detection still not working")
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
    
    return False


def main():
    """Main fix utility."""
    print("🔧 Tor Configuration Fix Utility")
    print("=" * 60)
    
    # Check .env file
    if not check_env_file():
        return 1
    
    # Analyze current config
    socks_url, use_socks = analyze_current_config()
    
    # Detect Tor status
    tor_9150, tor_9050, detected_url = detect_tor_status()
    
    # Provide recommendations
    provide_recommendations(socks_url, use_socks, tor_9150, tor_9050, detected_url)
    
    # Offer to fix configuration
    if detected_url and (not socks_url or input("\n🔧 Apply automatic fix? (y/N): ").lower() == 'y'):
        if generate_env_fix(detected_url):
            # Test the fix
            if test_configuration():
                print("\n" + "=" * 60)
                print("🎉 Tor configuration fixed successfully!")
                print("   You can now use API v3 with automatic Tor detection")
                print("=" * 60)
                return 0
    
    print("\n" + "=" * 60)
    if detected_url:
        print("✅ Tor is accessible - manual configuration may be needed")
    else:
        print("❌ Please start Tor Browser or system Tor service")
    print("=" * 60)
    
    return 1 if not detected_url else 0


if __name__ == "__main__":
    sys.exit(main())
