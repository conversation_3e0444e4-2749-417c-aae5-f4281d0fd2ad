"""
Validation decorators for callback and message handlers

This module provides decorators to validate input data before processing,
improving security and reducing boilerplate validation code.
"""

from functools import wraps
from typing import Callable, Optional, TypeVar, Any, List
from aiogram.types import CallbackQuery, Message
from utils.central_logger import get_logger

logger = get_logger()

F = TypeVar('F', bound=Callable[..., Any])


def validate_callback_data(
    expected_pattern: Optional[str] = None,
    min_parts: int = 2,
    max_parts: Optional[int] = None
) -> Callable[[F], F]:
    """
    Validate callback data format before processing
    
    Args:
        expected_pattern: Expected prefix pattern (e.g. "local:cart:")
        min_parts: Minimum number of parts after splitting by ":"
        max_parts: Maximum number of parts (None = no limit)
        
    Returns:
        Decorated function with validation
        
    Example:
        @validate_callback_data("local:cart:edit_item:", min_parts=4)
        async def edit_item_handler(self, callback: CallbackQuery):
            parts = callback.data.split(":")
            card_id = parts[3]  # Safe to access now
            # ... handler logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, callback: CallbackQuery, *args, **kwargs):
            # Validate callback data exists
            if not callback.data:
                logger.warning(
                    f"Empty callback data in {func.__name__}",
                    extra={"handler": func.__name__}
                )
                await callback.answer("❌ Invalid request", show_alert=True)
                return
            
            # Check pattern match
            if expected_pattern and not callback.data.startswith(expected_pattern):
                logger.warning(
                    f"Callback data pattern mismatch in {func.__name__}: expected '{expected_pattern}', got '{callback.data}'",
                    extra={
                        "handler": func.__name__,
                        "expected_pattern": expected_pattern,
                        "actual_data": callback.data
                    }
                )
                await callback.answer("❌ Invalid request format", show_alert=True)
                return
            
            # Validate part count
            parts = callback.data.split(":")
            if len(parts) < min_parts:
                logger.warning(
                    f"Callback data has too few parts in {func.__name__}: expected >={min_parts}, got {len(parts)}",
                    extra={
                        "handler": func.__name__,
                        "min_parts": min_parts,
                        "actual_parts": len(parts),
                        "callback_data": callback.data
                    }
                )
                await callback.answer("❌ Invalid request structure", show_alert=True)
                return
            
            if max_parts and len(parts) > max_parts:
                logger.warning(
                    f"Callback data has too many parts in {func.__name__}: expected <={max_parts}, got {len(parts)}",
                    extra={
                        "handler": func.__name__,
                        "max_parts": max_parts,
                        "actual_parts": len(parts),
                        "callback_data": callback.data
                    }
                )
                await callback.answer("❌ Invalid request structure", show_alert=True)
                return
            
            # Validation passed, call the handler
            return await func(self, callback, *args, **kwargs)
            
        return wrapper
    return decorator


def validate_numeric_input(
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    error_message: str = "Invalid number. Please enter a valid value."
) -> Callable[[F], F]:
    """
    Validate that message text is a valid number within range
    
    Args:
        min_value: Minimum allowed value (inclusive)
        max_value: Maximum allowed value (inclusive)
        error_message: Custom error message for validation failure
        
    Returns:
        Decorated function with validation
        
    Example:
        @validate_numeric_input(min_value=1, max_value=1000, error_message="Amount must be between $1 and $1000")
        async def handle_custom_amount(self, message: Message):
            amount = float(message.text)
            # ... handler logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, message: Message, *args, **kwargs):
            if not message.text:
                await message.answer(f"❌ {error_message}")
                return
            
            try:
                value = float(message.text)
                
                # Check range
                if min_value is not None and value < min_value:
                    await message.answer(f"❌ Value must be at least {min_value}")
                    return
                
                if max_value is not None and value > max_value:
                    await message.answer(f"❌ Value must not exceed {max_value}")
                    return
                
                # Validation passed
                return await func(self, message, *args, **kwargs)
                
            except (ValueError, TypeError):
                logger.debug(
                    f"Non-numeric input in {func.__name__}: {message.text}",
                    extra={
                        "handler": func.__name__,
                        "input_text": message.text,
                        "user_id": message.from_user.id if message.from_user else None
                    }
                )
                await message.answer(f"❌ {error_message}")
                return
                
        return wrapper
    return decorator


def validate_text_length(
    min_length: int = 1,
    max_length: int = 1000,
    error_message: str = "Text length is invalid"
) -> Callable[[F], F]:
    """
    Validate that message text length is within specified range
    
    Args:
        min_length: Minimum text length
        max_length: Maximum text length
        error_message: Custom error message for validation failure
        
    Returns:
        Decorated function with validation
        
    Example:
        @validate_text_length(min_length=3, max_length=100, error_message="Search query must be 3-100 characters")
        async def handle_search(self, message: Message):
            query = message.text
            # ... search logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, message: Message, *args, **kwargs):
            if not message.text:
                await message.answer(f"❌ Please provide text input")
                return
            
            text_length = len(message.text)
            
            if text_length < min_length:
                await message.answer(f"❌ Text must be at least {min_length} characters")
                return
            
            if text_length > max_length:
                await message.answer(f"❌ Text must not exceed {max_length} characters")
                return
            
            # Validation passed
            return await func(self, message, *args, **kwargs)
            
        return wrapper
    return decorator


def require_user_auth() -> Callable[[F], F]:
    """
    Ensure user is authenticated before processing
    
    Validates that the user object exists and has necessary attributes.
    
    Returns:
        Decorated function with user validation
        
    Example:
        @require_user_auth()
        async def protected_handler(self, callback: CallbackQuery):
            user = callback.from_user  # Guaranteed to exist
            # ... handler logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, update, *args, **kwargs):
            # Determine update type
            is_callback = isinstance(update, CallbackQuery)
            is_message = isinstance(update, Message)
            
            # Get user
            user = update.from_user if (is_callback or is_message) else None
            
            if not user:
                logger.warning(
                    f"No user found in {func.__name__}",
                    extra={"handler": func.__name__}
                )
                
                try:
                    if is_callback:
                        await update.answer("❌ Unable to identify user", show_alert=True)
                    elif is_message:
                        await update.answer("❌ Unable to identify user")
                except:
                    pass
                    
                return
            
            # User exists, proceed
            return await func(self, update, *args, **kwargs)
            
        return wrapper
    return decorator


def validate_allowed_values(
    allowed_values: List[str],
    error_message: str = "Invalid value provided"
) -> Callable[[F], F]:
    """
    Validate that input is one of the allowed values
    
    Args:
        allowed_values: List of acceptable values
        error_message: Custom error message for validation failure
        
    Returns:
        Decorated function with validation
        
    Example:
        @validate_allowed_values(["BIN", "BANK", "COUNTRY"], error_message="Invalid filter type")
        async def handle_filter_selection(self, message: Message):
            filter_type = message.text
            # ... filter logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, message: Message, *args, **kwargs):
            if not message.text:
                await message.answer(f"❌ Please provide input")
                return
            
            if message.text not in allowed_values:
                logger.debug(
                    f"Invalid value in {func.__name__}: {message.text}",
                    extra={
                        "handler": func.__name__,
                        "provided_value": message.text,
                        "allowed_values": allowed_values
                    }
                )
                await message.answer(
                    f"❌ {error_message}\n\nAllowed values: {', '.join(allowed_values)}"
                )
                return
            
            # Validation passed
            return await func(self, message, *args, **kwargs)
            
        return wrapper
    return decorator


def sanitize_input(
    strip_whitespace: bool = True,
    lowercase: bool = False,
    remove_special_chars: bool = False
) -> Callable[[F], F]:
    """
    Sanitize input text before processing
    
    Args:
        strip_whitespace: Remove leading/trailing whitespace
        lowercase: Convert to lowercase
        remove_special_chars: Remove special characters (keep alphanumeric and spaces)
        
    Returns:
        Decorated function with input sanitization
        
    Note: Modifies message.text in place before calling handler
    
    Example:
        @sanitize_input(strip_whitespace=True, lowercase=True)
        async def handle_username(self, message: Message):
            username = message.text  # Already sanitized
            # ... handler logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, message: Message, *args, **kwargs):
            if message.text:
                text = message.text
                
                if strip_whitespace:
                    text = text.strip()
                
                if lowercase:
                    text = text.lower()
                
                if remove_special_chars:
                    # Keep only alphanumeric, spaces, and basic punctuation
                    text = ''.join(c for c in text if c.isalnum() or c.isspace() or c in '.,!?-_')
                
                # Update message.text with sanitized version
                message.text = text
            
            # Call handler with sanitized input
            return await func(self, message, *args, **kwargs)
            
        return wrapper
    return decorator

