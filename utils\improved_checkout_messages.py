"""
Improved Checkout Processing Messages

This module provides enhanced user-friendly messages for all stages of the checkout process,
replacing technical jargon with clear, actionable communication.
"""

from __future__ import annotations

from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from enum import Enum

from utils.central_logger import get_logger

logger = get_logger()


class MessageTone(Enum):
    """Different tones for messages to match user expectations"""
    CELEBRATORY = "celebratory"
    INFORMATIVE = "informative" 
    WARNING = "warning"
    ERROR = "error"
    GUIDANCE = "guidance"


class ImprovedCheckoutMessages:
    """Enhanced checkout processing messages with better UX"""
    
    @staticmethod
    def get_cart_validation_message(cart_status: Dict[str, Any]) -> str:
        """Generate user-friendly cart validation message"""
        if cart_status.get("is_empty", True):
            return """
🛒 <b>Your Cart is Empty</b>

It looks like you haven't added any cards yet. Here's how to get started:

🔍 <b>Browse Cards:</b>
• Use filters to find cards by country, bank, or price
• Tap any card to see details and add to cart
• Check ratings and reviews for quality assurance

💡 <b>Pro Tip:</b>
• Add multiple cards to save on processing
• Check card availability before adding
• Read card descriptions carefully

<i>Ready to shop? Let's find some great cards!</i>
"""
        
        items = cart_status.get("items", [])
        total_items = cart_status.get("total_items", len(items))
        total_amount = cart_status.get("total_amount", 0.0)
        
        message = f"""
🛒 <b>Cart Summary</b>

📦 <b>Items:</b> {total_items} card{'s' if total_items != 1 else ''}
💰 <b>Total:</b> ${total_amount:.2f}

✅ <b>Your cart is ready for checkout!</b>
"""
        
        # Add item preview
        if items:
            message += "\n🃏 <b>Your Cards:</b>\n"
            for i, item in enumerate(items[:3], 1):
                card_data = dict(item.card_data or {})
                bank = card_data.get("bank", "Unknown")
                brand = card_data.get("brand", "")
                level = card_data.get("level", "")
                price = item.price_at_add
                
                card_name = f"{bank} {brand} {level}".strip()
                if not card_name or card_name == "Unknown":
                    card_name = f"Card #{i}"
                
                message += f"• {card_name} - ${price:.2f}\n"
            
            if len(items) > 3:
                message += f"• ... and {len(items) - 3} more cards\n"
        
        # Add cart expiry info
        cart = cart_status.get("cart")
        if cart and hasattr(cart, "expires_at") and cart.expires_at:
            expires_at = cart.expires_at
            if expires_at.tzinfo is None:
                expires_at = expires_at.replace(tzinfo=timezone.utc)
            
            now = datetime.now(timezone.utc)
            delta = expires_at - now
            total_seconds = int(delta.total_seconds())
            
            if total_seconds > 0:
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                
                if hours > 0:
                    expiry_text = f"{hours}h {minutes}m"
                else:
                    expiry_text = f"{minutes} minutes"
                
                message += f"\n⏰ <b>Cart expires in:</b> {expiry_text}"
        
        message += "\n\n💡 <b>Ready to checkout?</b>\nTap 'Proceed to Checkout' when you're ready!"
        
        return message
    
    @staticmethod
    def get_wallet_validation_message(
        current_balance: float, 
        order_total: float,
        has_sufficient_funds: bool
    ) -> str:
        """Generate wallet validation message"""
        if has_sufficient_funds:
            remaining = current_balance - order_total
            return f"""
💰 <b>Payment Verification</b>

✅ <b>Sufficient funds available!</b>

💵 <b>Current Balance:</b> ${current_balance:.2f}
🛒 <b>Order Total:</b> ${order_total:.2f}
💚 <b>Remaining After Purchase:</b> ${remaining:.2f}

🔒 <b>Payment Method:</b> Wallet Balance
⚡ <b>Processing:</b> Instant & Secure

<i>Ready to complete your order!</i>
"""
        else:
            shortfall = order_total - current_balance
            return f"""
💰 <b>Insufficient Funds</b>

❌ <b>Cannot complete order</b>

💵 <b>Current Balance:</b> ${current_balance:.2f}
🛒 <b>Order Total:</b> ${order_total:.2f}
⚠️ <b>Shortfall:</b> ${shortfall:.2f}

💡 <b>To complete your order:</b>
• Add ${shortfall:.2f} or more to your wallet
• Use secure payment methods
• Your cart will be saved automatically

🔄 <b>Quick Add Funds:</b>
• Instant bank transfer
• Credit/debit card
• Cryptocurrency options

<i>Your cart is safe and waiting!</i>
"""
    
    @staticmethod
    def get_checkout_progress_message(stage: str, progress_data: Dict[str, Any]) -> str:
        """Generate checkout progress messages for different stages"""
        
        stage_messages = {
            "validating_cart": """
🔍 <b>Validating Your Cart</b>

⚡ <b>Step 1 of 5:</b> Cart Validation

• ✅ Checking item availability
• 🔍 Verifying card details
• 💰 Confirming prices
• 📋 Validating quantities

<i>Almost ready to process...</i>
""",
            
            removing_items": """
🧹 <b>Cleaning Your Cart</b>

⚡ <b>Step 2 of 5:</b> Cart Optimization

• 🗑️ Removing outdated items
• 🔄 Refreshing card data
• ✅ Ensuring accuracy
• 📦 Preparing for checkout

<i>Making sure everything is perfect...</i>
""",
            
            "synchronizing_cart": """
🔄 <b>Synchronizing Cart</b>

⚡ <b>Step 3 of 5:</b> Cart Synchronization

• 🌐 Connecting to secure servers
• 📤 Transferring cart items
• ✅ Verifying transfer
• 🔒 Securing your data

<i>Your items are being securely transferred...</i>
""",
            
            "processing_payment": """
💳 <b>Processing Payment</b>

⚡ <b>Step 4 of 5:</b> Payment Processing

• 🔐 Verifying wallet balance
• 💰 Capturing payment
• ✅ Confirming transaction
• 🧾 Generating receipt

<i>Payment is being processed securely...</i>
""",
            
            "creating_order": """
📦 <b>Creating Your Order</b>

⚡ <b>Step 5 of 5:</b> Order Creation

• 🎯 Generating digital cards
• 📋 Creating order record
• 🔒 Securing card data
• ✅ Finalizing purchase

<i>Your cards are being created...</i>
"""
        }
        
        return stage_messages.get(stage, """
⚡ <b>Processing Your Order</b>

Please wait while we securely process your purchase...

<i>This usually takes 30-60 seconds.</i>
""")
    
    @staticmethod
    def get_order_completion_message(
        order_data: Dict[str, Any], 
        tone: MessageTone = MessageTone.CELEBRATORY
    ) -> str:
        """Generate order completion message with appropriate tone"""
        
        order_id = order_data.get("order_id", "Unknown")
        transaction_id = order_data.get("transaction_id", "Unknown")
        total_amount = order_data.get("total_amount", 0.0)
        item_count = order_data.get("item_count", 0)
        remaining_balance = order_data.get("remaining_balance", 0.0)
        purchased_cards = order_data.get("purchased_cards", [])
        
        if tone == MessageTone.CELEBRATORY:
            message = f"""
🎉 <b>Order Completed Successfully!</b>

🎊 <b>Congratulations!</b> Your order has been processed and your cards are ready!

📦 <b>Order Details:</b>
• Order ID: #{order_id[:8]}...
• Transaction: #{transaction_id[:8]}...
• Total Paid: ${total_amount:.2f}
• Cards Purchased: {item_count}

💰 <b>Your Balance:</b> ${remaining_balance:.2f}

✅ <b>Your {item_count} card{'s are' if item_count != 1 else ' is'} now available!</b>
"""
            
            if purchased_cards:
                message += "\n🃏 <b>Your New Cards:</b>\n"
                for i, card in enumerate(purchased_cards[:3], 1):
                    bank = card.get("bank", "Unknown")
                    brand = card.get("brand", "")
                    level = card.get("level", "")
                    price = card.get("price", 0.0)
                    
                    card_name = f"{bank} {brand} {level}".strip()
                    if not card_name or card_name == "Unknown":
                        card_name = f"Card #{i}"
                    
                    message += f"• 🎯 {card_name} - ${price:.2f}\n"
                
                if len(purchased_cards) > 3:
                    message += f"• ... and {len(purchased_cards) - 3} more cards\n"
            
            message += """
🚀 <b>What's Next?</b>
• 🃏 View your cards to unlock details
• 🔍 Check card status within 60 seconds  
• 📥 Download card data when ready
• 🔒 Keep your information secure

💡 <b>Pro Tips:</b>
• Test with small amounts first
• Check status before major purchases
• Save card details securely

<i>Thank you for your purchase! Enjoy your new cards!</i>
"""
        
        elif tone == MessageTone.INFORMATIVE:
            message = f"""
✅ <b>Order Processed</b>

📦 <b>Order ID:</b> #{order_id[:8]}...
💳 <b>Transaction:</b> #{transaction_id[:8]}...
💰 <b>Amount:</b> ${total_amount:.2f}
🃏 <b>Cards:</b> {item_count} card{'s' if item_count != 1 else ''}

💵 <b>Remaining Balance:</b> ${remaining_balance:.2f}

<i>Your cards are ready for use.</i>
"""
        
        return message
    
    @staticmethod
    def get_error_message(
        error_type: str, 
        error_details: str,
        recovery_options: List[str] = None
    ) -> str:
        """Generate user-friendly error messages with recovery options"""
        
        error_templates = {
            "cart_empty": """
🛒 <b>Cart is Empty</b>

❌ <b>Issue:</b> No items in cart

💡 <b>Solution:</b>
• Browse the catalog
• Add cards to your cart
• Return to checkout when ready

<i>Your cart is waiting for items!</i>
""",
            
            "insufficient_funds": """
💰 <b>Insufficient Funds</b>

❌ <b>Issue:</b> Not enough wallet balance

💡 <b>Solutions:</b>
• Add funds to your wallet
• Remove items to reduce total
• Check your balance first

<i>Your cart is saved and waiting!</i>
""",
            
            "payment_failed": """
💳 <b>Payment Failed</b>

❌ <b>Issue:</b> Payment processing error

💡 <b>Try This:</b>
• Check your wallet balance
• Verify payment method
• Try again in a few minutes
• Contact support if needed

<i>Your funds are safe and protected!</i>
""",
            
            "cart_sync_failed": """
🔄 <b>Cart Sync Failed</b>

❌ <b>Issue:</b> Could not synchronize cart

💡 <b>Solutions:</b>
• Try checkout again
• Clear cart and re-add items
• Check your internet connection
• Contact support if persistent

<i>Your cart is preserved!</i>
""",
            
            "order_creation_failed": """
📦 <b>Order Creation Failed</b>

❌ <b>Issue:</b> Could not create order

💡 <b>Try This:</b>
• Check your cart contents
• Verify payment method
• Try again in a few minutes
• Contact support for help

<i>No charges were made!</i>
""",
            
            "network_error": """
🌐 <b>Network Error</b>

❌ <b>Issue:</b> Connection problem

💡 <b>Solutions:</b>
• Check your internet connection
• Try again in a moment
• Use a stable network
• Contact support if needed

<i>Your progress is saved!</i>
""",
            
            "server_error": """
🖥️ <b>Server Error</b>

❌ <b>Issue:</b> Temporary server problem

💡 <b>What to do:</b>
• Wait a few minutes and try again
• Check our status page
• Contact support if urgent
• Your data is safe

<i>We're working to fix this quickly!</i>
"""
        }
        
        # Use specific template if available
        if error_type in error_templates:
            return error_templates[error_type]
        
        # Generic error message
        message = f"""
❌ <b>Something Went Wrong</b>

🔍 <b>Issue:</b> {error_details}

💡 <b>What you can do:</b>
"""
        
        if recovery_options:
            for option in recovery_options:
                message += f"• {option}\n"
        else:
            message += """• Try the operation again
• Check your connection
• Contact support if needed"""
        
        message += "\n\n<i>We're here to help!</i>"
        
        return message
    
    @staticmethod
    def get_guidance_message(context: str, user_level: str = "beginner") -> str:
        """Generate contextual guidance messages"""
        
        guidance_templates = {
            "first_checkout": """
🎯 <b>First Time Checkout Guide</b>

👋 <b>Welcome!</b> Here's how to complete your first order:

📋 <b>Step-by-Step:</b>
1. Review your cart items
2. Verify your wallet balance
3. Tap "Proceed to Checkout"
4. Wait for processing (30-60 seconds)
5. View your new cards!

💡 <b>Pro Tips:</b>
• Check card status within 60 seconds
• Download card data when ready
• Keep information secure
• Start with small amounts

<i>Need help? We're here for you!</i>
""",
            
            "cart_management": """
🛒 <b>Cart Management Tips</b>

💡 <b>Managing Your Cart:</b>
• Add multiple cards to save on processing
• Remove items by tapping the edit button
• Check cart expiry time
• Clear cart if needed

⏰ <b>Cart Expiry:</b>
• Carts expire after 30 minutes of inactivity
• You'll get a warning before expiry
• Items are preserved during checkout

<i>Your cart is always saved!</i>
""",
            
            "card_usage": """
🃏 <b>Card Usage Guide</b>

🔓 <b>After Purchase:</b>
• View card to unlock full details
• Check status within 60 seconds for best results
• Download data when ready to use
• Keep details secure and private

⏰ <b>Important:</b>
• Cards may expire after 24-48 hours
• Check status before major purchases
• Test with small amounts first

<i>Use cards responsibly and legally!</i>
""",
            
            "wallet_management": """
💰 <b>Wallet Management</b>

💵 <b>Managing Your Balance:</b>
• Add funds using secure methods
• Monitor your spending
• Set spending limits if needed
• Keep track of transactions

🔒 <b>Security:</b>
• Never share wallet details
• Use strong passwords
• Enable 2FA if available
• Report suspicious activity

<i>Your funds are always secure!</i>
"""
        }
        
        return guidance_templates.get(context, """
💡 <b>Help & Support</b>

Need assistance? We're here to help!

• Browse our help center
• Contact support 24/7
• Check status updates
• Join our community

<i>We want you to have the best experience!</i>
""")
    
    @staticmethod
    def get_loading_message(stage: str, progress_percent: int = 0) -> str:
        """Generate loading messages with progress indicators"""
        
        progress_bar = "█" * (progress_percent // 10) + "░" * (10 - progress_percent // 10)
        
        stage_messages = {
            "initializing": f"""
🔄 <b>Initializing Checkout</b>

⚡ <b>Setting up your order...</b>

{progress_bar} {progress_percent}%

<i>Preparing secure checkout environment...</i>
""",
            
            "validating": f"""
🔍 <b>Validating Order</b>

⚡ <b>Checking your items...</b>

{progress_bar} {progress_percent}%

<i>Verifying cart contents and prices...</i>
""",
            
            "processing": f"""
💳 <b>Processing Payment</b>

⚡ <b>Handling your payment...</b>

{progress_bar} {progress_percent}%

<i>Securely processing your transaction...</i>
""",
            
            "finalizing": f"""
📦 <b>Finalizing Order</b>

⚡ <b>Creating your cards...</b>

{progress_bar} {progress_percent}%

<i>Generating your digital cards...</i>
"""
        }
        
        return stage_messages.get(stage, f"""
🔄 <b>Processing</b>

⚡ <b>Please wait...</b>

{progress_bar} {progress_percent}%

<i>Working on your request...</i>
""")

