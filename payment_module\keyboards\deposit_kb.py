from aiogram.types import Inline<PERSON>eyboardMarkup, InlineKeyboardButton


def deposit_amount_keyboard():
    """Keyboard with preset deposit amounts and cancel button."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="$10",
                    callback_data="select_amount:10",
                ),
                InlineKeyboardButton(
                    text="$20",
                    callback_data="select_amount:20",
                ),
                InlineKeyboardButton(
                    text="$50",
                    callback_data="select_amount:50",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="$100",
                    callback_data="select_amount:100",
                ),
                InlineKeyboardButton(
                    text="$200",
                    callback_data="select_amount:200",
                ),
                InlineKeyboardButton(
                    text="$500",
                    callback_data="select_amount:500",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="Custom Amount",
                    callback_data="custom_amount",
                )
            ],
            [
                InlineKeyboardButton(
                    text="Cancel",
                    callback_data="cancel_deposit",
                )
            ],
        ]
    )


def deposit_pay_keyboard(amount):
    """Keyboard for payment confirmation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Pay Now",
                    callback_data=f"pay_deposit:{amount}",
                )
            ],
            [
                InlineKeyboardButton(
                    text="Cancel",
                    callback_data="cancel_deposit",
                )
            ],
        ]
    )


def deposit_cancel_keyboard():
    """Keyboard for canceled deposit."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Return to Main",
                    callback_data="return_to_main",
                )
            ]
        ]
    )


def custom_amount_cancel_keyboard():
    """Keyboard for canceling custom amount input."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Cancel",
                    callback_data="cancel_deposit",
                )
            ]
        ]
    )


def payment_verification_keyboard():
    """Keyboard for verifying payments."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ I've Completed Payment",
                    callback_data="verify_latest_payment",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Check Payment Status",
                    callback_data="check_payment_status",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Return to Wallet",
                    callback_data="return_to_wallet",
                )
            ]
        ]
    )


def payment_success_keyboard():
    """Keyboard for successful payment."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="💰 View Balance",
                    callback_data="menu:wallet",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Return to Wallet",
                    callback_data="return_to_wallet",
                )
            ]
        ]
    )


def payment_processing_keyboard(track_id):
    """Keyboard for payment still processing."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Check Again",
                    callback_data=f"check_payment:{track_id}",
                ),
                InlineKeyboardButton(
                    text="Cancel",
                    callback_data="cancel_deposit",
                )
            ]
        ]
    )

