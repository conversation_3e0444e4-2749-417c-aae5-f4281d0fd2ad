"""
Admin handlers module initialization.
Exports all admin handler routers for the bot application.
"""

# Import routers only when needed to avoid circular dependencies
def get_api_management_router():
    """Get API management router."""
    from .api_management_handlers import get_api_management_router
    return get_api_management_router()

def get_response_processor_admin_router():
    """Get response processor admin router."""
    from .response_processor_admin import get_response_processor_admin_router
    return get_response_processor_admin_router()

__all__ = [
    "get_api_management_router",
    "get_response_processor_admin_router"
]