"""
Wallet Manual Payment Verification Handlers
Handles manual payment verification when callbacks fail
"""

from __future__ import annotations

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import Command

from utils.central_logger import get_logger
from utils.texts import DEMO_WATERMARK
from utils.keyboards import wallet_menu_keyboard

logger = get_logger()


class ManualVerificationStates(StatesGroup):
    """States for manual payment verification"""
    waiting_for_track_id = State()
    verifying_payment = State()
    handling_verification_result = State()


class WalletManualVerificationHandlers:
    """Handlers for manual payment verification in wallet"""

    def __init__(self):
        self.payment_service = None
        self._initialize_payment_service()

    def _initialize_payment_service(self):
        """Initialize payment service if available"""
        try:
            from services.payment_service import get_payment_service
            self.payment_service = get_payment_service()
        except ImportError:
            logger.warning("Payment service not available")
            self.payment_service = None

    async def cb_verify_payment(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle manual payment verification request"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Check if payment service is available
            if not self.payment_service or not self.payment_service.is_available():
                await callback.message.edit_text(
                    "🚫 Payment verification is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK,
                    reply_markup=wallet_menu_keyboard()
                )
                await callback.answer("Payment system unavailable", show_alert=True)
                return

            # Show verification options
            verification_text = (
                "🔍 <b>Manual Payment Verification</b>\n\n"
                "If your payment callback failed, you can manually verify your payment here.\n\n"
                "📋 <b>Choose verification method:</b>\n"
                "• <b>Track ID:</b> Enter your payment Track ID\n"
                "• <b>Recent Payments:</b> Select from your recent payments\n"
                "• <b>Payment History:</b> View all your payments\n\n"
                "💡 <b>Tip:</b> You can find your Track ID in the payment confirmation email or on the payment page."
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔢 Enter Track ID",
                            callback_data="wallet:verify:track_id"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📋 Recent Payments",
                            callback_data="wallet:verify:recent"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📊 Payment History",
                            callback_data="wallet:verify:history"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel",
                            callback_data="wallet:verify:cancel"
                        )
                    ]
                ]
            )

            await callback.message.edit_text(
                verification_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in manual verification request: {e}")
            await callback.answer("❌ Error starting verification", show_alert=True)

    async def cb_verify_track_id(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle track ID verification"""
        try:
            verification_text = (
                "🔢 <b>Enter Track ID</b>\n\n"
                "Please enter your payment Track ID to verify:\n\n"
                "📝 <b>Format:</b> Enter the Track ID (e.g., ABC123XYZ)\n"
                "💡 <b>Tip:</b> You can find this in your payment confirmation\n\n"
                "⚠️ <b>Note:</b> This will check the payment status and process any pending payments"
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel",
                            callback_data="wallet:verify:cancel"
                        )
                    ]
                ]
            )

            await callback.message.edit_text(
                verification_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )

            await state.set_state(ManualVerificationStates.waiting_for_track_id)
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in track ID verification: {e}")
            await callback.answer("❌ Error starting track ID verification", show_alert=True)

    async def cb_verify_recent(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle recent payments verification"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get recent payments
            success, payments, error_msg = await self.payment_service.get_user_payment_history(user.id)
            
            if not success or not payments:
                await callback.message.edit_text(
                    "❌ <b>No recent payments found</b>\n\n"
                    "You don't have any recent payments to verify.\n"
                    "Please try entering a Track ID manually." + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔢 Enter Track ID",
                                    callback_data="wallet:verify:track_id"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                await callback.answer("No recent payments found", show_alert=True)
                return

            # Show recent payments (last 5)
            recent_payments = payments[:5]
            buttons = []
            
            for payment in recent_payments:
                track_id = payment.get('track_id', 'Unknown')
                amount = payment.get('amount', 0)
                status = payment.get('status', 'Unknown')
                created_at = payment.get('created_at', 'Unknown')
                
                # Format date
                if isinstance(created_at, str):
                    try:
                        created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    except:
                        pass
                
                if isinstance(created_at, datetime):
                    date_str = created_at.strftime("%m/%d %H:%M")
                else:
                    date_str = "Unknown"
                
                button_text = f"${amount:.2f} - {status} - {date_str}"
                buttons.append([
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=f"wallet:verify:payment:{track_id}"
                    )
                ])
            
            # Add navigation buttons
            buttons.append([
                InlineKeyboardButton(
                    text="🔢 Enter Track ID",
                    callback_data="wallet:verify:track_id"
                )
            ])
            buttons.append([
                InlineKeyboardButton(
                    text="🔙 Back to Wallet",
                    callback_data="menu:wallet"
                )
            ])

            keyboard = InlineKeyboardMarkup(inline_keyboard=buttons)

            recent_text = (
                "📋 <b>Recent Payments</b>\n\n"
                "Select a payment to verify:\n\n"
                "💡 <b>Tip:</b> Only pending or failed payments need verification"
            )

            await callback.message.edit_text(
                recent_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in recent payments verification: {e}")
            await callback.answer("❌ Error loading recent payments", show_alert=True)

    async def cb_verify_payment_by_id(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle verification of specific payment by track ID"""
        try:
            # Extract track_id from callback data
            track_id = callback.data.split(":")[-1]
            
            if not track_id:
                await callback.answer("❌ Invalid payment ID", show_alert=True)
                return

            await callback.answer("Verifying payment...")
            
            # Show processing message
            await callback.message.edit_text(
                f"⏳ <b>Verifying Payment...</b>\n\n"
                f"Track ID: <code>{track_id}</code>\n"
                f"Please wait while we check the payment status.",
                parse_mode="HTML"
            )

            # Verify payment
            success, verification_data, error_msg = await self.payment_service.verify_payment(track_id)
            
            if not success:
                await callback.message.edit_text(
                    f"❌ <b>Verification Failed</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Error: {error_msg}\n\n"
                    f"Please check your Track ID and try again.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Try Again",
                                    callback_data=f"wallet:verify:payment:{track_id}"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                return

            # Process verification result
            await self._process_verification_result(callback.message, track_id, verification_data)

        except Exception as e:
            logger.error(f"Error verifying payment by ID: {e}")
            await callback.answer("❌ Error verifying payment", show_alert=True)

    async def process_track_id_input(self, message: Message, state: FSMContext) -> None:
        """Process track ID input from user"""
        try:
            track_id = message.text.strip()
            
            if not track_id:
                await message.answer(
                    "❌ Please enter a valid Track ID.\n"
                    "Use /cancel to stop verification.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="❌ Cancel",
                                    callback_data="wallet:verify:cancel"
                                )
                            ]
                        ]
                    )
                )
                return

            # Show processing message
            processing_msg = await message.answer(
                f"⏳ <b>Verifying Payment...</b>\n\n"
                f"Track ID: <code>{track_id}</code>\n"
                f"Please wait while we check the payment status.",
                parse_mode="HTML"
            )

            # Verify payment
            success, verification_data, error_msg = await self.payment_service.verify_payment(track_id)
            
            if not success:
                await processing_msg.edit_text(
                    f"❌ <b>Verification Failed</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Error: {error_msg}\n\n"
                    f"Please check your Track ID and try again.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Try Again",
                                    callback_data="wallet:verify:track_id"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                return

            # Process verification result
            await self._process_verification_result(processing_msg, track_id, verification_data)

        except Exception as e:
            logger.error(f"Error processing track ID input: {e}")
            await message.answer("❌ Error processing verification", show_alert=True)

    async def _process_verification_result(self, target: Message, track_id: str, verification_data: Dict[str, Any]) -> None:
        """Process verification result and show appropriate message"""
        try:
            status = verification_data.get('status', 'unknown')
            amount = verification_data.get('amount', 0)
            
            if status == 'completed':
                # Payment is completed
                await target.edit_text(
                    f"✅ <b>Payment Verified Successfully!</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Amount: ${amount:.2f}\n"
                    f"Status: Completed\n\n"
                    f"Your payment has been processed and your balance has been updated.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="💰 Check Balance",
                                    callback_data="wallet:balance"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                
            elif status == 'pending':
                # Payment is still pending
                await target.edit_text(
                    f"⏳ <b>Payment Still Pending</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Amount: ${amount:.2f}\n"
                    f"Status: Pending\n\n"
                    f"Please complete the payment in your crypto wallet.\n"
                    f"You can check again later.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Check Again",
                                    callback_data=f"wallet:verify:payment:{track_id}"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                
            elif status == 'failed':
                # Payment failed
                await target.edit_text(
                    f"❌ <b>Payment Failed</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Amount: ${amount:.2f}\n"
                    f"Status: Failed\n\n"
                    f"Your payment could not be processed.\n"
                    f"Please try again or contact support.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Try Again",
                                    callback_data="wallet:verify:track_id"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                
            else:
                # Unknown status
                await target.edit_text(
                    f"🔄 <b>Payment Processing</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Amount: ${amount:.2f}\n"
                    f"Status: {status.title()}\n\n"
                    f"Please wait for the payment to be processed.\n"
                    f"You can check again later.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Check Again",
                                    callback_data=f"wallet:verify:payment:{track_id}"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )

        except Exception as e:
            logger.error(f"Error processing verification result: {e}")
            await target.edit_text(
                "❌ <b>Error Processing Result</b>\n\n"
                "An error occurred while processing the verification result.\n"
                "Please try again or contact support.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Wallet",
                                callback_data="menu:wallet"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML"
            )

    async def cmd_verify(self, message: Message, state: FSMContext) -> None:
        """Handle /verify command - direct access to manual verification"""
        try:
            user = message.from_user
            if not user:
                await message.answer("❌ User not found" + DEMO_WATERMARK)
                return

            # Check if payment service is available
            if not self.payment_service or not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment verification is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK
                )
                return

            # Show verification options
            verification_text = (
                "🔍 <b>Manual Payment Verification</b>\n\n"
                "If your payment callback failed, you can manually verify your payment here.\n\n"
                "📋 <b>Choose verification method:</b>\n"
                "• <b>Track ID:</b> Enter your payment Track ID\n"
                "• <b>Recent Payments:</b> Select from your recent payments\n"
                "• <b>Payment History:</b> View all your payments\n\n"
                "💡 <b>Tip:</b> You can find your Track ID in the payment confirmation email or on the payment page."
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔢 Enter Track ID",
                            callback_data="wallet:verify:track_id"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📋 Recent Payments",
                            callback_data="wallet:verify:recent"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📊 Payment History",
                            callback_data="wallet:verify:history"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel",
                            callback_data="wallet:verify:cancel"
                        )
                    ]
                ]
            )

            await message.answer(
                verification_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )

        except Exception as e:
            logger.error(f"Error in /verify command: {e}")
            await message.answer("❌ Error starting verification" + DEMO_WATERMARK)

    async def cb_completed_payment(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle 'I've Completed Payment' button - verify latest payment"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Check if payment service is available
            if not self.payment_service or not self.payment_service.is_available():
                await callback.message.edit_text(
                    "🚫 Payment verification is currently unavailable.\n"
                    "Please try again later or contact support." + DEMO_WATERMARK,
                    reply_markup=wallet_menu_keyboard()
                )
                await callback.answer("Payment system unavailable", show_alert=True)
                return

            # Get user's latest payment
            success, payments, error_msg = await self.payment_service.get_user_payment_history(user.id)
            
            if not success or not payments:
                await callback.message.edit_text(
                    "❌ <b>No recent payments found</b>\n\n"
                    "You don't have any recent payments to verify.\n"
                    "Please make a payment first or use manual verification." + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔍 Manual Verification",
                                    callback_data="wallet:verify_payment"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                await callback.answer("No recent payments found", show_alert=True)
                return

            # Get the latest payment
            latest_payment = payments[0]
            track_id = latest_payment.get('track_id')
            
            if not track_id:
                await callback.message.edit_text(
                    "❌ <b>Invalid payment data</b>\n\n"
                    "The latest payment doesn't have a valid Track ID.\n"
                    "Please use manual verification instead." + DEMO_WATERMARK,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔍 Manual Verification",
                                    callback_data="wallet:verify_payment"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                await callback.answer("Invalid payment data", show_alert=True)
                return

            # Show processing message
            await callback.message.edit_text(
                f"⏳ <b>Verifying Your Payment...</b>\n\n"
                f"Track ID: <code>{track_id}</code>\n"
                f"Please wait while we check the payment status.",
                parse_mode="HTML"
            )

            # Verify payment
            success, verification_data, error_msg = await self.payment_service.verify_payment(track_id)
            
            if not success:
                await callback.message.edit_text(
                    f"❌ <b>Verification Failed</b>\n\n"
                    f"Track ID: <code>{track_id}</code>\n"
                    f"Error: {error_msg}\n\n"
                    f"Please try again or use manual verification.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Try Again",
                                    callback_data="wallet:completed_payment"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔍 Manual Verification",
                                    callback_data="wallet:verify_payment"
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Wallet",
                                    callback_data="menu:wallet"
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML"
                )
                return

            # Process verification result
            await self._process_verification_result(callback.message, track_id, verification_data)

        except Exception as e:
            logger.error(f"Error in completed payment verification: {e}")
            await callback.answer("❌ Error verifying payment", show_alert=True)

    async def cb_cancel_verification(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Cancel verification and return to wallet"""
        try:
            await state.clear()
            
            await callback.message.edit_text(
                "💰 <b>Wallet Menu</b>\n\n"
                "Choose an option below:" + DEMO_WATERMARK,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer("Verification cancelled")

        except Exception as e:
            logger.error(f"Error cancelling verification: {e}")
            await callback.answer("❌ Error cancelling verification", show_alert=True)


def get_wallet_manual_verification_router() -> Router:
    """Get wallet manual verification router"""
    router = Router()
    handlers = WalletManualVerificationHandlers()
    
    # Command handlers
    router.message.register(
        handlers.cmd_verify,
        Command("verify")
    )
    
    # Callbacks
    router.callback_query.register(
        handlers.cb_verify_payment,
        F.data == "wallet:verify_payment"
    )
    router.callback_query.register(
        handlers.cb_completed_payment,
        F.data == "wallet:completed_payment"
    )
    router.callback_query.register(
        handlers.cb_verify_track_id,
        F.data == "wallet:verify:track_id"
    )
    router.callback_query.register(
        handlers.cb_verify_recent,
        F.data == "wallet:verify:recent"
    )
    router.callback_query.register(
        handlers.cb_verify_payment_by_id,
        F.data.startswith("wallet:verify:payment:")
    )
    router.callback_query.register(
        handlers.cb_cancel_verification,
        F.data == "wallet:verify:cancel"
    )
    
    # Message handlers
    router.message.register(
        handlers.process_track_id_input,
        ManualVerificationStates.waiting_for_track_id
    )
    
    return router
