"""
Configuration management for the Demo Wallet Bot
"""

from __future__ import annotations

from typing import List, Optional

from pydantic import Field, ValidationError, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

from utils.central_logger import get_logger

logger = get_logger()


class Settings(BaseSettings):
    """Application settings with environment variable support"""

    model_config = SettingsConfigDict(
        env_file=(".env", "config.example.env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",  # Allow extra fields to prevent validation errors
    )

    # Bot & API Configuration
    BOT_TOKEN: str = Field(default="", description="Telegram Bot API token")
    DEMO_API_BASE: str = Field(
        default="https://demo.api.example", description="Demo API base URL"
    )
    DEMO_API_TOKEN: str = Field(default="", description="Demo API authentication token")

    # Database Configuration
    DATABASE_URL: str = Field(
        default="sqlite:///demo_wallet_bot.db", description="Database connection URL"
    )
    DATABASE_NAME: str = Field(default="demo_wallet_bot", description="Database name")
    USE_MONGODB: bool = Field(
        default=False, description="Use MongoDB instead of SQLite"
    )
    MONGODB_URL: str = Field(
        default="mongodb://localhost:27017", description="MongoDB connection URL"
    )
    MONGODB_AUTH_SOURCE: str = Field(
        default="admin", description="MongoDB authentication database"
    )
    MONGODB_AUTH_MECHANISM: str = Field(
        default="SCRAM-SHA-256", description="MongoDB authentication mechanism"
    )

    # Wallet Settings
    DEFAULT_CURRENCY: str = Field(default="USD", description="Default currency code")

    # Compliance & Security
    SANCTIONED_COUNTRIES: str = Field(
        default="CU,IR,KP,SY,UA-CRIMEA",
        description="Comma-separated sanctioned country codes",
    )
    AML_HOURLY_LIMIT: float = Field(
        default=200.0, ge=0, description="AML hourly transaction limit"
    )
    DAILY_SPEND_CAP: float = Field(
        default=500.0, ge=0, description="Daily spending cap"
    )
    MONTHLY_SPEND_CAP: float = Field(
        default=2000.0, ge=0, description="Monthly spending cap"
    )

    # Rate Limiting
    PURCHASES_PER_MINUTE: int = Field(
        default=3, ge=1, description="Purchase rate limit per minute"
    )
    PURCHASES_PER_DAY: int = Field(
        default=50, ge=1, description="Purchase rate limit per day"
    )
    SEARCHES_PER_MINUTE: int = Field(
        default=20, ge=1, description="Search rate limit per minute"
    )
    MESSAGES_PER_MINUTE: int = Field(
        default=50, ge=1, description="Generic message rate limit per minute"
    )
    CALLBACKS_PER_MINUTE: int = Field(
        default=300, ge=1, description="Generic callback rate limit per minute"
    )

    # Payment Module Configuration
    OXA_PAY_API_KEY: str = Field(default="", description="OXA Pay API key for cryptocurrency payments")
    OXA_PAY_CALLBACK_URL: str = Field(default="", description="OXA Pay callback URL for payment notifications (leave empty for dynamic generation)")
    PAYMENT_DEVELOPMENT_MODE: bool = Field(default=True, description="Enable payment sandbox mode (SANDBOX=TRUE for safe testing)")
    PAYMENT_TESTING_MODE: bool = Field(default=False, description="Skip HMAC verification for testing")
    PAYMENT_DEBUG_MODE: bool = Field(default=False, description="Enable payment debug logging")
    PAYMENT_MIN_AMOUNT: float = Field(default=10.0, ge=0, description="Minimum payment amount")
    PAYMENT_MAX_AMOUNT: float = Field(default=1000.0, ge=0, description="Maximum payment amount")
    PAYMENT_CALLBACK_PORT: int = Field(default=3000, ge=1000, le=65535, description="Payment callback server port")

    # Feature Flags
    FALLBACK_ENABLED: bool = Field(
        default=False,
        description="Enable fallback card generation (disabled by default)",
    )
    METRICS_ENABLED: bool = Field(default=True, description="Enable Prometheus metrics")
    RETENTION_ENABLED: bool = Field(
        default=True, description="Enable data retention cleanup"
    )

    # Observability
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    ENVIRONMENT: str = Field(
        default="development",
        description="App environment: development|production|test",
    )
    LOG_TO_FILE: bool = Field(default=True, description="Enable file logging")
    LOG_FILE_PATH: str = Field(
        default="logs/application.log", description="Main log file path"
    )
    LOG_MAX_SIZE: int = Field(
        default=50 * 1024 * 1024, ge=1024, description="Max log file size in bytes"
    )
    LOG_BACKUP_COUNT: int = Field(
        default=10, ge=1, description="Number of rotated log files to keep"
    )
    LOG_COLOR: bool = Field(default=True, description="Colorize console logs")
    LOG_STRUCTURED: bool = Field(
        default=True, description="Use structured log formatter"
    )
    LOG_SHOW_CATEGORY: bool = Field(
        default=False, description="Show category tags like <SYSTEM>/<USER>"
    )
    LOG_FULL_API_RESPONSES: bool = Field(
        default=False, description="Show API response bodies (can be very verbose - disabled by default)"
    )
    LOG_API_RESPONSES_MAX_ITEMS: int = Field(
        default=3, description="Maximum number of items to show from API responses (3 = first 3 items only)"
    )
    LOG_CONSOLIDATED_FILE: str = Field(
        default="logs/all.log",
        description="Consolidated log file mirroring console output",
    )

    # Multi-File Logging Configuration
    LOG_MULTI_FILE_ENABLED: bool = Field(
        default=True, description="Enable multi-file logging system"
    )
    LOG_BASE_PATH: str = Field(
        default="logs", description="Base directory for all log files"
    )
    LOG_API_REQUESTS: bool = Field(
        default=True, description="Enable detailed API request logging"
    )
    LOG_API_RESPONSES: bool = Field(
        default=True, description="Enable detailed API response logging"
    )
    LOG_PERFORMANCE_TRACKING: bool = Field(
        default=True, description="Enable performance timing logs"
    )
    LOG_USER_ACTIONS: bool = Field(
        default=True, description="Enable user action analytics logging"
    )
    LOG_SECURITY_EVENTS: bool = Field(
        default=True, description="Enable security event logging"
    )
    LOG_TRANSACTION_DETAILS: bool = Field(
        default=True, description="Enable detailed transaction logging"
    )
    LOG_ADMIN_AUDIT: bool = Field(
        default=True, description="Enable admin action audit trail"
    )
    METRICS_PORT: int = Field(
        default=8000, ge=1024, le=65535, description="Prometheus metrics server port"
    )

    # Data Retention
    RETENTION_DAYS: int = Field(
        default=45, ge=1, description="Data retention period in days"
    )

    # Admin Configuration
    ADMIN_USER_IDS: str = Field(
        default="", description="Comma-separated Telegram user IDs for admin access"
    )
    # Centralized API Config encryption key (Fernet, URL-safe base64)
    API_CONFIG_ENCRYPTION_KEY: str | None = Field(
        default=None, description="Fernet key for API config credential encryption"
    )

    # External API auth (ronaldo-club) — used by services/external_api_service.py
    # These are intentionally plain strings; do not commit real values.
    EXTERNAL_LOGIN_TOKEN: str = Field(
        default="", description="JWT loginToken cookie for external API"
    )
    EXTERNAL_COOKIES: str = Field(
        default="", description="External API session cookies (format: key1=value1;key2=value2)"
    )
    EXTERNAL_DDG1: str = Field(default="", description="__ddg1_ session cookie value")
    EXTERNAL_DDG8: str = Field(default="", description="__ddg8_ session cookie value")
    EXTERNAL_DDG9: str = Field(
        default="", description="__ddg9_ IP/session cookie value"
    )
    EXTERNAL_DDG10: str = Field(
        default="", description="__ddg10_ timestamp/session cookie value"
    )
    EXTERNAL_GA: str = Field(
        default="", description="_ga Google Analytics cookie value"
    )
    EXTERNAL_GA_KZWCRF57VT: str = Field(
        default="", description="_ga_KZWCRF57VT GA session cookie value"
    )

    # API integration versioning
    EXTERNAL_API_VERSION: str = Field(
        default="v2",
        description="External API version to use (v1, v2, or v3)",
    )

    # API v3 Configuration (session-based authentication)
    API_V3_BASE_URL: str = Field(
        default="",
        description="API v3 base URL (supports .onion domains)",
    )
    API_V3_USERNAME: str = Field(
        default="",
        description="API v3 login username",
    )
    API_V3_PASSWORD: str = Field(
        default="",
        description="API v3 login password",
    )

    # Alternative naming convention (EXTERNAL_V3_*)
    EXTERNAL_V3_BASE_URL: str = Field(
        default="",
        description="API v3 base URL (alternative naming)",
    )
    EXTERNAL_V3_USERNAME: str = Field(
        default="",
        description="API v3 username (alternative naming)",
    )
    EXTERNAL_V3_PASSWORD: str = Field(
        default="",
        description="API v3 password (alternative naming)",
    )
    EXTERNAL_V3_USE_TOR_PROXY: bool = Field(
        default=False,
        description="Use Tor SOCKS proxy for API v3",
    )
    EXTERNAL_V3_SOCKS_URL: str = Field(
        default="socks5h://127.0.0.1:9150",
        description="SOCKS proxy URL for Tor",
    )

    # Backward compatibility
    USE_SOCKS_PROXY: bool = Field(
        default=False,
        description="Use SOCKS proxy (legacy)",
    )
    SOCKS_URL: str = Field(
        default="socks5h://127.0.0.1:9150",
        description="SOCKS proxy URL (legacy)",
    )

    @property
    def admin_ids(self) -> List[int]:
        """Parse admin user IDs from comma-separated string"""
        ids = []
        for part in self.ADMIN_USER_IDS.split(","):
            part = part.strip()
            if not part:
                continue
            try:
                ids.append(int(part))
            except ValueError:
                logger.warning(f"Invalid admin user ID: {part}")
                continue
        return ids

    @property
    def sanctioned_countries_set(self) -> set[str]:
        """Parse sanctioned countries into a set for fast lookup"""
        return {
            country.strip().upper()
            for country in self.SANCTIONED_COUNTRIES.split(",")
            if country.strip()
        }

    @field_validator("BOT_TOKEN")
    @classmethod
    def validate_bot_token(cls, v: str):
        """Validate Telegram bot token format"""
        if v and ":" not in v:
            raise ValueError("BOT_TOKEN must be in format <bot_id>:<token>")
        return v

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str):
        """Validate logging level"""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of: {valid_levels}")
        return v.upper()

    @field_validator("ENVIRONMENT")
    @classmethod
    def validate_environment(cls, v: str):
        valid_envs = {"development", "production", "test"}
        value = (v or "").lower()
        if value not in valid_envs:
            raise ValueError(f"ENVIRONMENT must be one of: {valid_envs}")
        return value


def load_settings() -> Settings:
    """Load application settings with proper error handling"""
    # Ensure .env file is loaded into environment from bot_v2 directory
    from pathlib import Path

    bot_v2_dir = Path(__file__).parent.parent
    load_dotenv(bot_v2_dir / ".env")
    load_dotenv(bot_v2_dir / "config.example.env")

    try:
        settings = Settings()
        logger.info("Configuration loaded successfully")
        return settings
    except ValidationError as e:
        logger.error(f"Configuration validation failed: {e}")
        raise RuntimeError(f"Invalid configuration: {e}")
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise RuntimeError(f"Configuration loading failed: {e}")


# Global settings instance (lazy loaded)
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get cached settings instance"""
    global _settings
    if _settings is None:
        _settings = load_settings()
    return _settings
