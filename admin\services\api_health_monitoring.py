"""
Real-time API Health Monitoring Service

This service provides comprehensive health monitoring and testing capabilities
for APIs managed through the admin interface.
"""

import asyncio
import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

from shared_api.config.registry import api_registry
from shared_api.config.client_factory import api_client_factory
from admin.models.api_config_storage import get_admin_api_service
from database.connection import get_collection
from models.api import APIUsageMetrics
from models.base import now_utc

from utils.central_logger import get_logger

logger = get_logger()


class HealthStatus(str, Enum):
    """Health status enumeration"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check operation"""
    api_name: str
    status: HealthStatus
    response_time_ms: Optional[float]
    timestamp: datetime
    endpoint_tested: Optional[str]
    error_message: Optional[str] = None
    status_code: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class EndpointTestResult:
    """Result of testing a specific endpoint"""
    endpoint_name: str
    success: bool
    response_time_ms: Optional[float]
    status_code: Optional[int]
    error_message: Optional[str] = None
    response_data: Optional[Dict[str, Any]] = None


class APIHealthMonitoringService:
    """
    Service for monitoring API health and performing real-time tests
    
    Provides comprehensive health monitoring capabilities including:
    - Individual API health checks
    - Bulk health monitoring
    - Endpoint-specific testing
    - Historical health data tracking
    - Real-time status updates
    """
    
    def __init__(self):
        self.admin_storage = get_admin_api_service()
        self.health_collection = get_collection("api_health_history")
        self.request_logs_collection = get_collection("api_request_logs")
        self.metrics_collection = get_collection("api_usage_metrics")
        self._monitoring_tasks = {}
        self._health_cache = {}
        self._cache_ttl = 60  # 1 minute cache TTL
    
    async def check_api_health(
        self,
        api_name: str,
        endpoint_name: Optional[str] = None,
        timeout: float = 30.0
    ) -> HealthCheckResult:
        """
        Perform a health check on a specific API
        
        Args:
            api_name: Name of the API to check
            endpoint_name: Specific endpoint to test (optional)
            timeout: Request timeout in seconds
            
        Returns:
            HealthCheckResult with test results
        """
        try:
            # Get API configuration
            admin_config = await self.admin_storage.get_api_config(api_name)
            if not admin_config:
                return HealthCheckResult(
                    api_name=api_name,
                    status=HealthStatus.UNKNOWN,
                    response_time_ms=None,
                    timestamp=datetime.utcnow(),
                    endpoint_tested=endpoint_name,
                    error_message="API configuration not found"
                )
            
            shared_config = admin_config.to_shared_config()
            
            # Create client for testing
            try:
                client = api_client_factory.create_client(shared_config)
            except Exception as e:
                return HealthCheckResult(
                    api_name=api_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=None,
                    timestamp=datetime.utcnow(),
                    endpoint_tested=endpoint_name,
                    error_message=f"Failed to create client: {str(e)}"
                )
            
            # Perform the health check
            start_time = asyncio.get_event_loop().time()
            
            try:
                async with asyncio.timeout(timeout):
                    async with client:
                        if endpoint_name:
                            # Test specific endpoint
                            if endpoint_name not in shared_config.endpoints:
                                return HealthCheckResult(
                                    api_name=api_name,
                                    status=HealthStatus.UNKNOWN,
                                    response_time_ms=None,
                                    timestamp=datetime.utcnow(),
                                    endpoint_tested=endpoint_name,
                                    error_message=f"Endpoint '{endpoint_name}' not found"
                                )
                            
                            response = await client.get(endpoint_name)
                            status = HealthStatus.HEALTHY
                            
                        else:
                            # General health check
                            is_healthy = await client.health_check()
                            status = HealthStatus.HEALTHY if is_healthy else HealthStatus.UNHEALTHY
                
                response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                
                result = HealthCheckResult(
                    api_name=api_name,
                    status=status,
                    response_time_ms=response_time,
                    timestamp=datetime.utcnow(),
                    endpoint_tested=endpoint_name,
                    status_code=200  # Assume success if no exception
                )
                
                # Update admin configuration health status
                admin_config.update_health_status(
                    status.value,
                    response_time_ms=response_time
                )
                
                # Store health check result
                await self._store_health_result(result)
                
                return result
                
            except asyncio.TimeoutError:
                return HealthCheckResult(
                    api_name=api_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=None,
                    timestamp=datetime.utcnow(),
                    endpoint_tested=endpoint_name,
                    error_message=f"Request timed out after {timeout}s"
                )
                
            except Exception as e:
                response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                
                result = HealthCheckResult(
                    api_name=api_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=response_time if response_time < timeout * 1000 else None,
                    timestamp=datetime.utcnow(),
                    endpoint_tested=endpoint_name,
                    error_message=str(e)
                )
                
                # Update admin configuration health status
                admin_config.update_health_status(
                    HealthStatus.UNHEALTHY.value,
                    error_message=str(e)
                )
                
                # Store health check result
                await self._store_health_result(result)
                
                return result
            
        except Exception as e:
            logger.error(f"Failed to check health for API '{api_name}': {e}")
            return HealthCheckResult(
                api_name=api_name,
                status=HealthStatus.UNKNOWN,
                response_time_ms=None,
                timestamp=datetime.utcnow(),
                endpoint_tested=endpoint_name,
                error_message=f"Health check failed: {str(e)}"
            )
    
    async def check_all_apis_health(
        self,
        environment: Optional[str] = None,
        enabled_only: bool = True
    ) -> List[HealthCheckResult]:
        """
        Check health of all APIs
        
        Args:
            environment: Filter by environment
            enabled_only: Only check enabled APIs
            
        Returns:
            List of health check results
        """
        try:
            # Get API configurations
            admin_configs = await self.admin_storage.list_api_configs(
                environment=environment,
                enabled_only=enabled_only
            )
            
            # Perform health checks concurrently
            tasks = []
            for config in admin_configs:
                task = self.check_api_health(config.name)
                tasks.append(task)
            
            # Wait for all health checks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions and return valid results
            health_results = []
            for result in results:
                if isinstance(result, HealthCheckResult):
                    health_results.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"Health check failed with exception: {result}")
            
            return health_results
            
        except Exception as e:
            logger.error(f"Failed to check health of all APIs: {e}")
            return []
    
    async def test_api_endpoints(
        self,
        api_name: str,
        endpoint_names: Optional[List[str]] = None
    ) -> List[EndpointTestResult]:
        """
        Test specific endpoints of an API
        
        Args:
            api_name: Name of the API
            endpoint_names: List of endpoint names to test (all if None)
            
        Returns:
            List of endpoint test results
        """
        try:
            # Get API configuration
            admin_config = await self.admin_storage.get_api_config(api_name)
            if not admin_config:
                return []
            
            shared_config = admin_config.to_shared_config()
            
            # Determine which endpoints to test
            if endpoint_names is None:
                endpoint_names = list(shared_config.endpoints.keys())
            
            # Create client for testing
            try:
                client = api_client_factory.create_client(shared_config)
            except Exception as e:
                logger.error(f"Failed to create client for API '{api_name}': {e}")
                return []
            
            # Test each endpoint
            results = []
            
            async with client:
                for endpoint_name in endpoint_names:
                    if endpoint_name not in shared_config.endpoints:
                        results.append(EndpointTestResult(
                            endpoint_name=endpoint_name,
                            success=False,
                            response_time_ms=None,
                            status_code=None,
                            error_message=f"Endpoint '{endpoint_name}' not found in configuration"
                        ))
                        continue
                    
                    start_time = asyncio.get_event_loop().time()
                    
                    try:
                        response = await client.get(endpoint_name)
                        response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                        
                        results.append(EndpointTestResult(
                            endpoint_name=endpoint_name,
                            success=True,
                            response_time_ms=response_time,
                            status_code=200,
                            response_data=response if isinstance(response, dict) else None
                        ))
                        
                    except Exception as e:
                        response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                        
                        results.append(EndpointTestResult(
                            endpoint_name=endpoint_name,
                            success=False,
                            response_time_ms=response_time,
                            status_code=None,
                            error_message=str(e)
                        ))
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to test endpoints for API '{api_name}': {e}")
            return []
    
    async def get_health_history(
        self,
        api_name: str,
        hours: int = 24
    ) -> List[HealthCheckResult]:
        """
        Get health check history for an API
        
        Args:
            api_name: Name of the API
            hours: Number of hours of history to retrieve
            
        Returns:
            List of historical health check results
        """
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            cursor = self.health_collection.find({
                "api_name": api_name,
                "timestamp": {"$gte": since}
            }).sort("timestamp", -1)
            
            docs = await cursor.to_list(None)  # Get all documents
            results = []
            for doc in docs:
                result = HealthCheckResult(
                    api_name=doc["api_name"],
                    status=HealthStatus(doc["status"]),
                    response_time_ms=doc.get("response_time_ms"),
                    timestamp=doc["timestamp"],
                    endpoint_tested=doc.get("endpoint_tested"),
                    error_message=doc.get("error_message"),
                    status_code=doc.get("status_code"),
                    additional_data=doc.get("additional_data")
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get health history for API '{api_name}': {e}")
            return []
    
    async def get_health_summary(self) -> Dict[str, Any]:
        """
        Get overall health summary for all APIs
        
        Returns:
            Dictionary with health summary statistics
        """
        try:
            # Get all enabled APIs
            admin_configs = await self.admin_storage.list_api_configs(enabled_only=True)
            
            total_apis = len(admin_configs)
            healthy_apis = 0
            unhealthy_apis = 0
            unknown_apis = 0
            
            avg_response_time = 0
            response_times = []
            
            for config in admin_configs:
                status = config.health_status
                
                if status == "healthy":
                    healthy_apis += 1
                elif status == "unhealthy":
                    unhealthy_apis += 1
                else:
                    unknown_apis += 1
                
                # Get recent health check for response time
                recent_health = await self.get_health_history(config.name, hours=1)
                if recent_health and recent_health[0].response_time_ms:
                    response_times.append(recent_health[0].response_time_ms)
            
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
            
            return {
                "total_apis": total_apis,
                "healthy_apis": healthy_apis,
                "unhealthy_apis": unhealthy_apis,
                "unknown_apis": unknown_apis,
                "health_percentage": (healthy_apis / total_apis * 100) if total_apis > 0 else 0,
                "average_response_time_ms": avg_response_time,
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get health summary: {e}")
            return {
                "total_apis": 0,
                "healthy_apis": 0,
                "unhealthy_apis": 0,
                "unknown_apis": 0,
                "health_percentage": 0,
                "average_response_time_ms": 0,
                "error": str(e)
            }
    
    async def _store_health_result(self, result: HealthCheckResult) -> None:
        """Store health check result in database"""
        try:
            doc = asdict(result)
            await self.health_collection.insert_one(doc)
            
            # Clean up old records (keep last 7 days)
            cutoff = datetime.utcnow() - timedelta(days=7)
            await self.health_collection.delete_many({
                "api_name": result.api_name,
                "timestamp": {"$lt": cutoff}
            })

        except Exception as e:
            logger.error(f"Failed to store health result: {e}")

    async def collect_usage_metrics(self, period_type: str = "hourly") -> None:
        """Aggregate usage metrics for all APIs within the specified period."""
        try:
            current_time = now_utc()

            if period_type == "hourly":
                period_start = current_time.replace(minute=0, second=0, microsecond=0)
                period_end = period_start + timedelta(hours=1)
            elif period_type == "daily":
                period_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
                period_end = period_start + timedelta(days=1)
            else:
                logger.warning(f"Unsupported metrics period type: {period_type}")
                return

            configs = await self.admin_storage.list_api_configs(enabled_only=True)
            if not configs:
                return

            for config in configs:
                config_id = self._get_config_identifier(config)
                try:
                    await self._collect_api_metrics(
                        config_id,
                        period_start,
                        period_end,
                        period_type
                    )
                except Exception as collect_error:
                    logger.error(
                        "Failed to collect %s metrics for API %s: %s",
                        period_type,
                        config.name,
                        collect_error
                    )

        except Exception as e:
            logger.error(f"Failed to collect usage metrics: {e}")

    async def cleanup_old_data(self, days_to_keep: int = 90) -> None:
        """Cleanup aged monitoring data to keep collections manageable."""
        try:
            cutoff_date = now_utc() - timedelta(days=days_to_keep)

            logs_result = await self.request_logs_collection.delete_many({
                "created_at": {"$lt": cutoff_date}
            })
            logger.info(
                "Removed %s request log documents older than %s days",
                getattr(logs_result, "deleted_count", 0),
                days_to_keep
            )

            metrics_cutoff = now_utc() - timedelta(days=days_to_keep * 2)
            metrics_result = await self.metrics_collection.delete_many({
                "period_start": {"$lt": metrics_cutoff}
            })
            logger.info(
                "Removed %s usage metric documents older than %s days",
                getattr(metrics_result, "deleted_count", 0),
                days_to_keep * 2
            )

        except Exception as e:
            logger.error(f"Failed to cleanup old health monitoring data: {e}")

    async def _collect_api_metrics(
        self,
        config_id: str,
        period_start: datetime,
        period_end: datetime,
        period_type: str,
    ) -> None:
        """Collect metrics for a single API within the given period."""
        try:
            cursor = self.request_logs_collection.find({
                "api_config_id": config_id,
                "created_at": {"$gte": period_start, "$lt": period_end}
            })

            logs = await cursor.to_list(length=None)
            if not logs:
                return

            total_requests = len(logs)
            successful_requests = sum(
                1 for log in logs if 200 <= (log.get("status_code") or 0) < 300
            )
            failed_requests = total_requests - successful_requests

            response_times = [
                log.get("response_time_ms")
                for log in logs
                if log.get("response_time_ms") is not None
            ]

            avg_response_time = (
                float(sum(response_times)) / len(response_times)
                if response_times else 0.0
            )
            min_response_time = min(response_times) if response_times else 0
            max_response_time = max(response_times) if response_times else 0
            p95_response_time = self._percentile(response_times, 0.95)
            p99_response_time = self._percentile(response_times, 0.99)

            status_2xx = sum(
                1 for log in logs if 200 <= (log.get("status_code") or 0) < 300
            )
            status_3xx = sum(
                1 for log in logs if 300 <= (log.get("status_code") or 0) < 400
            )
            status_4xx = sum(
                1 for log in logs if 400 <= (log.get("status_code") or 0) < 500
            )
            status_5xx = sum(
                1 for log in logs if 500 <= (log.get("status_code") or 0) < 600
            )

            error_rate = (
                (failed_requests / total_requests) * 100 if total_requests else 0.0
            )
            timeout_count = sum(
                1
                for log in logs
                if "timeout" in (log.get("error_message") or "").lower()
            )
            connection_error_count = sum(
                1
                for log in logs
                if "connection" in (log.get("error_message") or "").lower()
            )

            total_bytes_sent = sum(log.get("request_size_bytes", 0) or 0 for log in logs)
            total_bytes_received = sum(log.get("response_size_bytes", 0) or 0 for log in logs)

            metrics = APIUsageMetrics(
                api_config_id=config_id,
                period_start=period_start,
                period_end=period_end,
                period_type=period_type,
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                avg_response_time_ms=avg_response_time,
                min_response_time_ms=min_response_time,
                max_response_time_ms=max_response_time,
                p95_response_time_ms=p95_response_time,
                p99_response_time_ms=p99_response_time,
                error_rate=error_rate,
                timeout_count=timeout_count,
                connection_error_count=connection_error_count,
                status_2xx_count=status_2xx,
                status_3xx_count=status_3xx,
                status_4xx_count=status_4xx,
                status_5xx_count=status_5xx,
                total_bytes_sent=total_bytes_sent,
                total_bytes_received=total_bytes_received,
            )

            await self.metrics_collection.update_one(
                {
                    "api_config_id": config_id,
                    "period_start": period_start,
                    "period_type": period_type,
                },
                {"$set": metrics.to_mongo()},
                upsert=True,
            )

        except Exception as e:
            logger.error(f"Failed to collect metrics for API {config_id}: {e}")

    @staticmethod
    def _percentile(values: List[int], percentile: float) -> float:
        """Calculate percentile for a list of numeric values."""
        if not values:
            return 0.0

        sorted_values = sorted(values)
        if len(sorted_values) == 1:
            return float(sorted_values[0])

        k = (len(sorted_values) - 1) * percentile
        f = math.floor(k)
        c = math.ceil(k)

        if f == c:
            return float(sorted_values[int(k)])

        d0 = sorted_values[f] * (c - k)
        d1 = sorted_values[c] * (k - f)
        return float(d0 + d1)

    @staticmethod
    def _get_config_identifier(config: Any) -> str:
        """Resolve a stable identifier for admin API configurations."""
        config_id = getattr(config, "id", None)
        if config_id:
            return str(config_id)
        return getattr(config, "name", "unknown")


# Global service instance
_health_monitoring_service = None


def get_api_health_monitoring_service() -> APIHealthMonitoringService:
    """Get the global API health monitoring service instance"""
    global _health_monitoring_service
    if _health_monitoring_service is None:
        _health_monitoring_service = APIHealthMonitoringService()
    return _health_monitoring_service
