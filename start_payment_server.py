#!/usr/bin/env python3
"""
Payment Callback Server
Starts the payment callback server for handling OXA Pay webhooks
"""

import sys
import os
import logging
from pathlib import Path

# Add current directory to Python path
sys.path.append('.')

# Set up environment
os.environ["OXA_PAY_API_KEY"] = os.environ.get("OXA_PAY_API_KEY", "test_key")
os.environ["DEVELOPMENT_MODE"] = os.environ.get("PAYMENT_DEVELOPMENT_MODE", "true").lower()
os.environ["TESTING_MODE"] = os.environ.get("PAYMENT_TESTING_MODE", "false").lower()
os.environ["DEBUG_MODE"] = os.environ.get("PAYMENT_DEBUG_MODE", "false").lower()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Start the payment callback server"""
    try:
        logger.info("🚀 Starting Payment Callback Server...")
        
        # Initialize database connection (required for payment module)
        import asyncio
        from database.connection import init_database
        
        async def setup_database():
            """Initialize database connection"""
            try:
                await init_database()
                logger.info("✅ Database connection established for payment server")
            except Exception as e:
                logger.warning(f"⚠️ Database connection failed: {e}")
                logger.warning("Payment module will use in-memory storage")
        
        # Run database initialization
        asyncio.run(setup_database())
        
        # Import and start the callback server
        from payment_module.core.flask_server import run_callback_server
        
        # Get configuration
        from config.settings import get_settings
        settings = get_settings()
        
        host = "0.0.0.0"
        port = settings.PAYMENT_CALLBACK_PORT
        debug = settings.PAYMENT_DEBUG_MODE
        
        logger.info(f"📡 Server configuration:")
        logger.info(f"   Host: {host}")
        logger.info(f"   Port: {port}")
        logger.info(f"   Debug: {debug}")
        logger.info(f"   Development Mode: {settings.PAYMENT_DEVELOPMENT_MODE}")
        
        logger.info("✅ Payment callback server starting...")
        
        # Start the server
        run_callback_server(host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        logger.info("🛑 Payment callback server stopped by user")
    except Exception as e:
        logger.error(f"❌ Failed to start payment callback server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
