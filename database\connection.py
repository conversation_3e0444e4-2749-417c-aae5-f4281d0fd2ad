"""
MongoDB connection and database management
"""

from __future__ import annotations

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorDatabase,
    AsyncIOMotorCollection,
)
from pymongo.errors import (
    ConnectionFailure,
    ServerSelectionTimeoutError,
    OperationFailure,
)

from config.settings import get_settings

from utils.central_logger import get_logger

logger = get_logger()


class DatabaseManager:
    """MongoDB database manager with connection pooling"""

    def __init__(self):
        self._client: Optional[AsyncIOMotorClient] = None
        self._database: Optional[AsyncIOMotorDatabase] = None
        self._settings = get_settings()

    async def connect(self) -> None:
        """Establish connection to MongoDB database"""
        if self._client is not None:
            return

        logger.info("Connecting to MongoDB...")
        
        # Minimal connection options - let the URL handle timeouts
        connection_options = {
            "retryWrites": True,
            "retryReads": True,
        }

        # Add production SSL settings only if needed
        if self._settings.ENVIRONMENT == "production" and "tls=true" not in self._settings.MONGODB_URL.lower():
            connection_options.update({
                "tls": True,
                "tlsAllowInvalidCertificates": False,
                "tlsAllowInvalidHostnames": False,
            })

        try:
            # Create client - let MongoDB driver use URL timeouts
            self._client = AsyncIOMotorClient(self._settings.MONGODB_URL, **connection_options)

            # Simple connection test - no additional timeout since URL has them
            await self._client.admin.command("ping")

            # Set database
            self._database = self._client[self._settings.DATABASE_NAME]
            
            # Create indexes
            await self._create_indexes()
            
            logger.info(f"Successfully connected to MongoDB: {self._settings.DATABASE_NAME}")

        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            if self._client:
                self._client.close()
                self._client = None
                self._database = None
            raise RuntimeError(f"Failed to connect to MongoDB: {e}")

    async def disconnect(self) -> None:
        """Close MongoDB connection"""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None
            logger.info("Disconnected from MongoDB")

    @property
    def database(self) -> AsyncIOMotorDatabase:
        """Get database instance"""
        if self._database is None:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self._database
    
    @property
    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self._database is not None

    def get_collection(self, name: str) -> AsyncIOMotorCollection:
        """Get collection by name"""
        return self.database[name]

    async def health_check(self) -> bool:
        """Check database connection health"""
        try:
            if self._client is None:
                return False

            # Ping with timeout
            await asyncio.wait_for(self._client.admin.command("ping"), timeout=5.0)
            return True
        except Exception as e:
            logger.warning(f"Database health check failed: {e}")
            return False

    async def _create_indexes(self) -> None:
        """Create database indexes for optimal performance"""
        try:
            # Users collection indexes
            users = self.get_collection("users")
            await users.create_index("telegram_id", unique=True)
            await users.create_index("username")
            await users.create_index("created_at")

            # Wallets collection indexes
            wallets = self.get_collection("wallets")
            await wallets.create_index("user_id", unique=True)
            await wallets.create_index("updated_at")

            # Purchases collection indexes
            purchases = self.get_collection("purchases")
            await purchases.create_index("user_id")
            await purchases.create_index("created_at")
            await purchases.create_index([("user_id", 1), ("created_at", -1)])
            # Create idempotency_key as unique + sparse, reconcile conflicts if index exists without sparse/unique
            try:
                await purchases.create_index(
                    "idempotency_key",
                    unique=True,
                    sparse=True,
                    name="idempotency_key_1",
                )
            except OperationFailure as ie:
                if getattr(ie, "code", None) == 86:  # IndexKeySpecsConflict
                    try:
                        indexes = await purchases.list_indexes().to_list(None)
                        for idx in indexes:
                            if idx.get("name") == "idempotency_key_1":
                                if not idx.get("sparse", False) or not idx.get(
                                    "unique", False
                                ):
                                    await purchases.drop_index(idx["name"])
                                    await purchases.create_index(
                                        "idempotency_key",
                                        unique=True,
                                        sparse=True,
                                        name="idempotency_key_1",
                                    )
                                break
                    except Exception as fix_err:
                        logger.warning(
                            f"Could not reconcile purchases.idempotency_key index: {fix_err}"
                        )
                else:
                    raise
            await purchases.create_index("api_req_id", sparse=True)

            # Enhanced indexes for API version tracking
            await purchases.create_index("api_version")
            await purchases.create_index("product_type")
            await purchases.create_index("external_product_id", sparse=True)
            await purchases.create_index("external_order_id", sparse=True)
            await purchases.create_index([("user_id", 1), ("api_version", 1), ("created_at", -1)])
            await purchases.create_index([("user_id", 1), ("product_type", 1), ("created_at", -1)])
            await purchases.create_index([("user_id", 1), ("external_product_id", 1)], sparse=True)

            # Transactions collection indexes
            transactions = self.get_collection("transactions")
            await transactions.create_index("user_id")
            await transactions.create_index("created_at")
            await transactions.create_index(
                [("user_id", 1), ("type", 1), ("created_at", -1)]
            )
            # Ensure unique + sparse index on hash. Resolve existing conflicting index if present.
            try:
                await transactions.create_index(
                    "hash", unique=True, sparse=True, name="hash_1"
                )
            except OperationFailure as ie:
                # Handle IndexKeySpecsConflict when an existing non-sparse index named hash_1 exists
                if getattr(ie, "code", None) == 86:
                    try:
                        indexes = await transactions.list_indexes().to_list(None)
                        for idx in indexes:
                            # Identify index on {hash:1}
                            key = idx.get("key") or {}
                            # key can be an OrderedDict-like; normalize to list of tuples
                            items = (
                                list(getattr(key, "items", lambda: key.items())())
                                if hasattr(key, "items")
                                else []
                            )
                            if idx.get("name") == "hash_1" or items == [("hash", 1)]:
                                # If not sparse or not unique, drop and recreate
                                if not idx.get("sparse", False) or not idx.get(
                                    "unique", False
                                ):
                                    await transactions.drop_index(idx["name"])
                                    await transactions.create_index(
                                        "hash", unique=True, sparse=True, name="hash_1"
                                    )
                                break
                    except Exception as fix_err:
                        logger.warning(
                            f"Could not reconcile transactions.hash index: {fix_err}"
                        )
                else:
                    raise

            # Cards collection indexes
            cards = self.get_collection("cards")
            await cards.create_index("purchase_id")
            await cards.create_index("created_at")

            # Audit logs collection indexes
            audit_logs = self.get_collection("audit_logs")
            await audit_logs.create_index("actor_id")
            await audit_logs.create_index("created_at")
            await audit_logs.create_index("action")
            await audit_logs.create_index("hash", unique=True)

            # Catalog items collection indexes
            catalog_items = self.get_collection("catalog_items")
            await catalog_items.create_index("sku", unique=True)
            await catalog_items.create_index("active")
            await catalog_items.create_index([("active", 1), ("price", 1)])
            await catalog_items.create_index("country")
            await catalog_items.create_index("brand")
            await catalog_items.create_index("type")
            await catalog_items.create_index("bin")

            # Saved filters collection indexes
            saved_filters = self.get_collection("saved_filters")
            await saved_filters.create_index("user_id")
            await saved_filters.create_index("created_at")

            # App settings collection indexes
            app_settings = self.get_collection("app_settings")
            await app_settings.create_index("key", unique=True)

            # API Management collection indexes
            api_configurations = self.get_collection("api_configurations")
            await api_configurations.create_index("name")
            await api_configurations.create_index("status")
            await api_configurations.create_index("environment")
            await api_configurations.create_index("created_by")
            await api_configurations.create_index("created_at")
            await api_configurations.create_index("is_deleted")

            api_health_status = self.get_collection("api_health_status")
            await api_health_status.create_index("api_config_id", unique=True)
            await api_health_status.create_index("is_healthy")
            await api_health_status.create_index("last_check_at")
            await api_health_status.create_index("next_check_at")

            api_usage_metrics = self.get_collection("api_usage_metrics")
            await api_usage_metrics.create_index("api_config_id")
            await api_usage_metrics.create_index("period_type")
            await api_usage_metrics.create_index("period_start")
            await api_usage_metrics.create_index(
                [("api_config_id", 1), ("period_start", -1)]
            )

            api_request_logs = self.get_collection("api_request_logs")
            await api_request_logs.create_index("api_config_id")
            await api_request_logs.create_index("created_at")
            await api_request_logs.create_index("status_code")
            await api_request_logs.create_index(
                [("api_config_id", 1), ("created_at", -1)]
            )

            api_credentials = self.get_collection("api_credentials")
            await api_credentials.create_index("api_config_id")
            await api_credentials.create_index("environment")
            await api_credentials.create_index("is_active")
            await api_credentials.create_index("expires_at")

            api_audit_logs = self.get_collection("api_audit_logs")
            await api_audit_logs.create_index("resource_type")
            await api_audit_logs.create_index("resource_id")
            await api_audit_logs.create_index("actor_id")
            await api_audit_logs.create_index("timestamp")
            await api_audit_logs.create_index("operation")

            # Checkout jobs collection indexes (prevent duplicate active jobs)
            checkout_jobs = self.get_collection("checkout_jobs")
            await checkout_jobs.create_index("created_at")
            await checkout_jobs.create_index("status")
            # Unique idempotency per user for active jobs (queued or processing)
            try:
                await checkout_jobs.create_index(
                    [("user_id", 1), ("idempotency_key", 1)],
                    unique=True,
                    name="uniq_active_idem_per_user",
                    partialFilterExpression={
                        "status": {"$in": ["queued", "processing"]}
                    },
                )
            except OperationFailure as ie:
                # Best effort: if index exists with incompatible options, drop and recreate
                if getattr(ie, "code", None) == 86:  # IndexKeySpecsConflict
                    try:
                        idxs = await checkout_jobs.list_indexes().to_list(None)
                        for idx in idxs:
                            if idx.get("name") == "uniq_active_idem_per_user":
                                await checkout_jobs.drop_index(idx["name"])
                                await checkout_jobs.create_index(
                                    [("user_id", 1), ("idempotency_key", 1)],
                                    unique=True,
                                    name="uniq_active_idem_per_user",
                                    partialFilterExpression={
                                        "status": {"$in": ["queued", "processing"]}
                                    },
                                )
                                break
                    except Exception as fix_err:
                        logger.warning(
                            f"Could not reconcile checkout_jobs unique index: {fix_err}"
                        )
                else:
                    raise

            logger.info("Database indexes created successfully")

        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            # Don't raise - allow database connection to succeed even if indexes fail
            # This ensures the application can still function with reduced performance
            logger.warning("Database connection will proceed without all indexes - performance may be affected")


# Global database manager instance
db_manager = DatabaseManager()


async def init_database() -> None:
    """Initialize database connection"""
    await db_manager.connect()


async def close_database() -> None:
    """Close database connection"""
    await db_manager.disconnect()


def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    return db_manager.database


def get_collection(name: str) -> AsyncIOMotorCollection:
    """Get collection by name"""
    return db_manager.get_collection(name)


def is_database_connected() -> bool:
    """Check if database is connected"""
    return db_manager.is_connected


@asynccontextmanager
async def database_transaction() -> AsyncGenerator[AsyncIOMotorDatabase, None]:
    """
    Context manager for database transactions with MongoDB support
    
    Provides proper ACID transactions for MongoDB 4.0+.
    Falls back to non-transactional operations for older MongoDB versions or standalone servers.
    """
    client = db_manager._client
    
    # Check if transactions are supported (requires replica set or sharded cluster)
    if client is None:
        logger.warning("Database client not initialized, falling back to non-transactional operation")
        try:
            yield get_database()
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            raise
        return
    
    # Try to use a session with transaction support
    session = None
    try:
        # Start a client session for transaction support
        async with await client.start_session() as session:
            # Check if we can use transactions (requires replica set)
            try:
                # Try to start a transaction
                async with session.start_transaction():
                    yield get_database()
                    # Transaction will auto-commit if no exception
            except Exception as transaction_error:
                # If transactions aren't supported (standalone MongoDB), fall back
                if "Transaction numbers" in str(transaction_error) or "no replication" in str(transaction_error).lower():
                    logger.debug("MongoDB transactions not supported (standalone server), using non-transactional operations")
                    yield get_database()
                else:
                    # Real transaction error, abort
                    logger.error(f"Transaction failed: {transaction_error}")
                    raise
    except Exception as e:
        logger.error(f"Database operation failed: {e}")
        raise
