#!/usr/bin/env python3
"""
Debug API Routing

This script helps debug why API v3 requests might be falling back to API v2.
"""

import sys
import os
from pathlib import Path

# Add the bot root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_settings_loading():
    """Test settings loading and API version detection"""
    print("🔍 Testing Settings Loading")
    print("=" * 50)
    
    try:
        from config.settings import get_settings
        settings = get_settings()
        
        print(f"✅ Settings loaded successfully")
        
        # Check API version
        api_version = getattr(settings, 'EXTERNAL_API_VERSION', 'NOT_SET')
        print(f"📋 EXTERNAL_API_VERSION: '{api_version}'")
        
        # Check if it starts with v3
        is_v3 = api_version.lower().startswith("v3")
        print(f"🔍 API version starts with 'v3': {is_v3}")
        
        # Check all API v3 settings
        v3_settings = {
            'EXTERNAL_V3_BASE_URL': getattr(settings, 'EXTERNAL_V3_BASE_URL', 'NOT_SET'),
            'EXTERNAL_V3_USERNAME': getattr(settings, 'EXTERNAL_V3_USERNAME', 'NOT_SET'),
            'EXTERNAL_V3_PASSWORD': '***' if getattr(settings, 'EXTERNAL_V3_PASSWORD', '') else 'NOT_SET',
            'EXTERNAL_V3_USE_TOR_PROXY': getattr(settings, 'EXTERNAL_V3_USE_TOR_PROXY', 'NOT_SET'),
            'EXTERNAL_V3_SOCKS_URL': getattr(settings, 'EXTERNAL_V3_SOCKS_URL', 'NOT_SET'),
        }
        
        print(f"\n📋 API v3 Settings:")
        for key, value in v3_settings.items():
            print(f"   {key}: {value}")
        
        return is_v3, settings
        
    except Exception as e:
        print(f"❌ Settings loading failed: {e}")
        return False, None


def test_external_api_service_initialization():
    """Test external API service initialization"""
    print("\n🔧 Testing External API Service Initialization")
    print("=" * 50)
    
    try:
        # Try to import without aiogram dependencies
        import importlib.util
        
        # Load the module manually to avoid aiogram import
        service_path = Path(__file__).parent.parent / "services" / "external_api_service.py"
        
        with open(service_path, 'r') as f:
            service_content = f.read()
        
        # Check for the key initialization logic
        if "_use_api_v3 = (" in service_content:
            print("✅ Found _use_api_v3 initialization logic")
            
            # Extract the logic
            lines = service_content.split('\n')
            for i, line in enumerate(lines):
                if "_use_api_v3 = (" in line:
                    print(f"📋 Initialization logic found at line {i+1}:")
                    for j in range(max(0, i-2), min(len(lines), i+5)):
                        marker = ">>> " if j == i else "    "
                        print(f"{marker}{j+1:3}: {lines[j]}")
                    break
        else:
            print("❌ _use_api_v3 initialization logic not found")
        
        # Check for routing logic in list_items
        if "if self._use_api_v3:" in service_content:
            print("✅ Found API v3 routing logic")
            
            # Count occurrences
            v3_checks = service_content.count("if self._use_api_v3:")
            print(f"📊 Found {v3_checks} API v3 routing checks")
        else:
            print("❌ API v3 routing logic not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Service analysis failed: {e}")
        return False


def test_api_v3_client_availability():
    """Test if API v3 client can be imported"""
    print("\n🔌 Testing API v3 Client Availability")
    print("=" * 50)
    
    try:
        # Check if api_v3 module exists
        api_v3_path = Path(__file__).parent.parent / "api_v3"
        if not api_v3_path.exists():
            print("❌ api_v3 directory not found")
            return False
        
        print("✅ api_v3 directory exists")
        
        # Check key files
        key_files = ["__init__.py", "client.py", "config.py"]
        for file_name in key_files:
            file_path = api_v3_path / file_name
            if file_path.exists():
                print(f"✅ {file_name} exists")
            else:
                print(f"❌ {file_name} missing")
                return False
        
        # Try to check imports without actually importing (to avoid dependency issues)
        init_path = api_v3_path / "__init__.py"
        with open(init_path, 'r') as f:
            init_content = f.read()
        
        if "APIV3Client" in init_content:
            print("✅ APIV3Client export found in __init__.py")
        else:
            print("❌ APIV3Client export not found in __init__.py")
        
        if "APIV3Config" in init_content:
            print("✅ APIV3Config export found in __init__.py")
        else:
            print("❌ APIV3Config export not found in __init__.py")
        
        return True
        
    except Exception as e:
        print(f"❌ API v3 client check failed: {e}")
        return False


def test_environment_variables():
    """Test environment variables directly"""
    print("\n🌍 Testing Environment Variables")
    print("=" * 50)
    
    try:
        # Load .env file directly
        env_path = Path(__file__).parent.parent / ".env"
        
        if not env_path.exists():
            print("❌ .env file not found")
            return False
        
        print("✅ .env file exists")
        
        # Read and parse .env
        env_vars = {}
        with open(env_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
        
        # Check API version
        api_version = env_vars.get('EXTERNAL_API_VERSION', 'NOT_SET')
        print(f"📋 EXTERNAL_API_VERSION from .env: '{api_version}'")
        
        # Check if it would trigger v3
        would_use_v3 = api_version.lower().startswith("v3")
        print(f"🔍 Would use API v3: {would_use_v3}")
        
        # Check other v3 settings
        v3_keys = [
            'EXTERNAL_V3_BASE_URL',
            'EXTERNAL_V3_USERNAME', 
            'EXTERNAL_V3_PASSWORD',
            'EXTERNAL_V3_USE_TOR_PROXY'
        ]
        
        print(f"\n📋 API v3 Environment Variables:")
        for key in v3_keys:
            value = env_vars.get(key, 'NOT_SET')
            if 'PASSWORD' in key and value != 'NOT_SET':
                value = '***'
            print(f"   {key}: {value}")
        
        return would_use_v3
        
    except Exception as e:
        print(f"❌ Environment variable check failed: {e}")
        return False


def test_routing_simulation():
    """Simulate the routing logic"""
    print("\n🔀 Testing Routing Logic Simulation")
    print("=" * 50)
    
    try:
        # Simulate the exact logic from ExternalAPIService.__init__
        from config.settings import get_settings
        settings = get_settings()
        
        # Replicate the exact logic
        use_api_v3 = (
            getattr(settings, "EXTERNAL_API_VERSION", "").lower().startswith("v3")
        )
        
        print(f"🔍 Simulated _use_api_v3: {use_api_v3}")
        
        # Show the exact values used
        api_version_raw = getattr(settings, "EXTERNAL_API_VERSION", "")
        api_version_lower = api_version_raw.lower()
        starts_with_v3 = api_version_lower.startswith("v3")
        
        print(f"📋 Raw EXTERNAL_API_VERSION: '{api_version_raw}'")
        print(f"📋 Lowercased: '{api_version_lower}'")
        print(f"📋 Starts with 'v3': {starts_with_v3}")
        
        # Test routing decision
        if use_api_v3:
            print("✅ Would route to API v3 methods")
        else:
            print("❌ Would route to API v2 methods")
        
        return use_api_v3
        
    except Exception as e:
        print(f"❌ Routing simulation failed: {e}")
        return False


def main():
    """Run all debug tests"""
    print("🐛 API Routing Debug Tool")
    print("=" * 70)
    
    results = {}
    
    # Test 1: Settings loading
    is_v3, settings = test_settings_loading()
    results['settings_v3'] = is_v3
    
    # Test 2: Service initialization
    service_ok = test_external_api_service_initialization()
    results['service_ok'] = service_ok
    
    # Test 3: API v3 client availability
    client_ok = test_api_v3_client_availability()
    results['client_ok'] = client_ok
    
    # Test 4: Environment variables
    env_v3 = test_environment_variables()
    results['env_v3'] = env_v3
    
    # Test 5: Routing simulation
    routing_v3 = test_routing_simulation()
    results['routing_v3'] = routing_v3
    
    # Summary
    print("\n📊 Debug Summary")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}: {result}")
    
    # Overall assessment
    all_good = all(results.values())
    
    if all_good:
        print(f"\n🎉 All checks passed - API v3 should be working!")
        print(f"💡 If you're still seeing API v2 responses, the issue might be:")
        print(f"   1. Missing dependencies (beautifulsoup4, requests[socks])")
        print(f"   2. API v3 client failing and falling back silently")
        print(f"   3. Response formatting issues")
    else:
        print(f"\n❌ Found issues that could cause API v2 fallback:")
        failed_tests = [name for name, result in results.items() if not result]
        for test in failed_tests:
            print(f"   • {test}")
    
    return 0 if all_good else 1


if __name__ == "__main__":
    sys.exit(main())
