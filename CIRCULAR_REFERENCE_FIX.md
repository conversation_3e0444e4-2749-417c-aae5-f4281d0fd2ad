# Circular Reference Fix - API v3 Database Save Issue

## Critical Issue
API v3 checkout was failing with:
- `Error extracting from API response: Circular reference detected`
- `maximum recursion depth exceeded while encoding an object to BSON`

This prevented cards from being saved to the database after successful checkout.

## Root Cause

The API v3 raw_data response contains circular references (objects that reference themselves), causing:

1. **Card Data Extractor**: Failed with "Circular reference detected"
2. **BSON Encoder**: Failed with "maximum recursion depth exceeded"
3. **Database Save**: Purchase record couldn't be saved

### Error Log
```
2025-10-26 03:53:58 [ERROR] card_data_extractor: Error extracting from API response: Circular reference detected
2025-10-26 03:53:58 [WARNING] checkout_queue_service: ⚠️ No cards extracted from checkout response
2025-10-26 03:53:58 [INFO] checkout_queue_service: ✅ Adding raw_data to purchase record for card f000d392...
2025-10-26 03:53:58 [ERROR] checkout_queue_service: ❌ Failed to create purchase record: maximum recursion depth exceeded while encoding an object to BSON
```

## Solution

Enhanced `_clean_for_bson` methods to detect and handle circular references using a **visited set** to track object IDs.

### Files Modified

#### 1. utils/card_data_extractor.py (Lines 234-244)

**Root Cause of Card Extraction Failure**:
The extraction was failing when trying to create a cache key using `json.dumps(api_data)`.

**Before**:
```python
# Generate hash for cache key to detect identical data
import time
data_str = json.dumps(api_data, sort_keys=True)  # ❌ FAILS on circular refs!
data_hash = hashlib.md5(data_str.encode()).hexdigest()
cache_key = f"{endpoint or 'unknown'}:{data_hash}"
```

**Problem**: `json.dumps()` throws an exception when encountering circular references.

**After**:
```python
# Generate hash for cache key to detect identical data
import time
try:
    # Try to serialize to JSON for cache key
    data_str = json.dumps(api_data, sort_keys=True)
    data_hash = hashlib.md5(data_str.encode()).hexdigest()
except (TypeError, ValueError) as e:
    # If JSON serialization fails (e.g., circular references), use a simpler cache key
    logger.debug(f"⚠️ Cannot serialize API data for cache key (circular refs): {e}")
    # Use id() of the api_data object as a simple cache key
    data_hash = str(id(api_data))
    logger.debug(f"Using object ID for cache key: {data_hash}")

cache_key = f"{endpoint or 'unknown'}:{data_hash}"
```

**Solution**: Gracefully handle JSON serialization failure by falling back to using the object ID.

#### 2. services/checkout_queue_service.py (Lines 895-961)

**Before**:
```python
def _clean_for_bson(self, data: any, max_depth: int = 10, current_depth: int = 0) -> any:
    """Clean data structure to prevent BSON encoding errors."""
    if current_depth >= max_depth:
        return "<<MAX_DEPTH_REACHED>>"
    if isinstance(data, dict):
        clean_dict = {}
        for key, value in data.items():
            # ... no circular reference detection
            clean_dict[key] = self._clean_for_bson(value, max_depth, current_depth + 1)
        return clean_dict
    # ... rest of method
```

**Problem**: No tracking of visited objects, causing infinite recursion on circular references.

**After**:
```python
def _clean_for_bson(self, data: any, max_depth: int = 10, current_depth: int = 0, visited: set = None) -> any:
    """Clean data structure to prevent BSON encoding errors from circular references."""
    # Initialize visited set on first call
    if visited is None:
        visited = set()
    
    # Check depth limit
    if current_depth >= max_depth:
        return "<<MAX_DEPTH_REACHED>>"
    
    # Handle dictionaries
    if isinstance(data, dict):
        # Use object id to detect circular references
        obj_id = id(data)
        if obj_id in visited:
            return "<<CIRCULAR_REFERENCE>>"
        
        visited.add(obj_id)
        clean_dict = {}
        for key, value in data.items():
            # Skip problematic keys
            if key in ["_parent", "parent", "self", "__dict__", "__class__", "session", "connection", "client"]:
                continue
            clean_dict[key] = self._clean_for_bson(value, max_depth, current_depth + 1, visited)
        visited.remove(obj_id)
        return clean_dict
    
    # Handle lists
    elif isinstance(data, list):
        obj_id = id(data)
        if obj_id in visited:
            return "<<CIRCULAR_REFERENCE>>"
        
        visited.add(obj_id)
        clean_list = []
        for item in data:
            clean_list.append(self._clean_for_bson(item, max_depth, current_depth + 1, visited))
        visited.remove(obj_id)
        return clean_list
    
    # Handle primitives and other types
    elif isinstance(data, (str, int, float, bool)) or data is None:
        return data
    else:
        # For datetime objects, use isoformat()
        if hasattr(data, 'isoformat'):
            return data.isoformat()
        return str(data)
```

#### 2. handlers/orders_handlers.py (Lines 981-1018)

Applied the same circular reference detection logic to `_clean_data_for_bson` method.

## How It Works

### Circular Reference Detection

1. **Visited Set**: Tracks object IDs of all dictionaries and lists being processed
2. **Check Before Processing**: If object ID is already in visited set → circular reference detected
3. **Mark as Visited**: Add object ID to set before processing
4. **Clean Up**: Remove object ID from set after processing (allows same object in different branches)

### Example

```python
# Circular reference example:
obj_a = {"name": "A"}
obj_b = {"name": "B", "ref": obj_a}
obj_a["ref"] = obj_b  # Circular!

# Without fix: Infinite recursion
# With fix: Detects obj_a is already being processed → returns "<<CIRCULAR_REFERENCE>>"
```

### Skipped Keys

Added more keys to skip list to avoid processing potentially circular objects:
- `_parent`, `parent`, `self` (object references)
- `__dict__`, `__class__` (Python internals)
- `session`, `connection`, `client` (database/HTTP objects)

## Benefits

1. **Prevents Crashes**: No more recursion depth errors
2. **Saves Data**: API v3 raw_data can now be saved to database
3. **Clear Markers**: Circular references replaced with `<<CIRCULAR_REFERENCE>>` string
4. **Safe Fallback**: Any problematic data gets safe string representation
5. **Preserves Structure**: Non-circular data remains intact

## Testing

### Before Fix
```
✅ API v3 checkout succeeded
❌ Card extraction failed: Circular reference detected
❌ Database save failed: maximum recursion depth exceeded
❌ Purchase record NOT created
```

### After Fix
```
✅ API v3 checkout succeeded
✅ Card extraction handles circular references
✅ Database save succeeds (circular refs replaced with markers)
✅ Purchase record created successfully
```

## Error Markers

When circular references or errors are detected, data is replaced with clear markers:

- `<<CIRCULAR_REFERENCE>>`: Circular reference detected
- `<<MAX_DEPTH_REACHED>>`: Max nesting depth (10 levels) reached
- `<<BSON_ERROR: ...>>`: Error encoding specific value
- `<<UNCONVERTIBLE>>`: Unable to convert type to string

These markers:
- Prevent crashes
- Indicate where problems occurred
- Allow data to be saved
- Can be debugged if needed

## Implementation Date
October 26, 2025

## Related Issues

This fix complements:
- **API_V3_POST_CHECKOUT_FIXES.md**: Complete data storage
- **API_V3_MASKED_DATA_FIX.md**: Proper masked/unmasked detection

Together ensuring:
- ✅ Checkout completes successfully
- ✅ Data is saved to database (even with circular refs)
- ✅ Cards can be viewed and managed

## Summary

The circular reference detection prevents infinite recursion when processing complex API v3 responses. By tracking visited objects and replacing circular references with safe markers, the system can now:

1. Successfully save API v3 order data to database
2. Handle complex nested structures
3. Prevent crashes from circular references
4. Provide clear indicators of problematic data

**Key Improvement**: API v3 checkouts now complete successfully end-to-end! 🎉

