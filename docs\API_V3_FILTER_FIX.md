# API v3 Filter & Search Auto-Detection Fix

## Problem

When API v3 was configured with credentials (`EXTERNAL_V3_BASE_URL`, `EXTERNAL_V3_USERNAME`, `EXTERNAL_V3_PASSWORD`) but the `EXTERNAL_API_VERSION` setting was not explicitly set to "v3" in the `.env` file, the system would default to v2. This caused the following issues:

1. **Wrong Filter UI**: Users saw v1/v2 filter options instead of v3-specific filters
2. **Missing v3 Features**: API v3 features like:
   - Continent, Region, Postal Code filters
   - Quality & Status filters (Show Medium Valid, Expiring Soon, etc.)
   - Advanced filters (CC Per BIN, Ethnicity, etc.)
   - Bank search functionality
   - Extended contact data filters

## Root Cause

The `_get_user_api_version()` method in `handlers/catalog_handlers.py` was reading `EXTERNAL_API_VERSION` from settings, which defaults to "v2" (see `config/settings.py` line 213). There was no auto-detection logic to check if API v3 credentials were configured.

## Solution

Added auto-detection logic to `_get_user_api_version()` that:

1. Checks if `EXTERNAL_API_VERSION` is explicitly set to "v3" or "base3"
2. If not, checks whether API v3 credentials are configured:
   - `EXTERNAL_V3_BASE_URL` or `API_V3_BASE_URL`
   - `EXTERNAL_V3_USERNAME` or `API_V3_USERNAME`
   - `EXTERNAL_V3_PASSWORD` or `API_V3_PASSWORD`
3. If credentials are found, automatically sets API version to "v3"
4. Logs the auto-detection for debugging purposes

## Code Changes

### File: `handlers/catalog_handlers.py`

**Location**: Lines 465-514 (method `_get_user_api_version`)

**Changes**:
- Added credential check before normalizing version strings
- Automatically detects v3 when credentials are present
- Maintains backward compatibility with explicit `EXTERNAL_API_VERSION` setting

### File: `services/external_api_service.py`

**Location**: Lines 261-295 (method `__init__`)

**Changes**:
- Added auto-detection logic when `api_version` parameter is not explicitly provided
- Checks for v3 credentials and sets `self.api_version = "v3"` accordingly
- Logs auto-detection for debugging

### File: `services/card_service.py`

**Location**: Lines 113-148 (method `__init__`)

**Changes**:
- Added auto-detection logic when no external service is provided
- Checks for v3 credentials before setting API version flags
- Ensures CardService uses correct API version

## Testing

To verify the fix works:

1. **With API v3 credentials configured**:
   - Ensure you have `EXTERNAL_V3_BASE_URL`, `EXTERNAL_V3_USERNAME`, and `EXTERNAL_V3_PASSWORD` in your `.env` file
   - Open the bot and navigate to "Search & Filter Cards" or "Filters"
   - You should see:
     - Title shows "(API V3)"
     - Filter categories include: Location, Card Details, Contact Data, Quality & Status, Advanced
     - Location section has: Continent, Country, Region, City, Postal Code
     - Card section has: BINs, Scheme, Card Type, Level, Bank, Search Bank
     - Contact section has: Billing Address, Phone Number, Date of Birth
     - Quality section has: Show Medium Valid, Expiring Soon, Expiring Next Month
     - Advanced section has: CC Per BIN, Ethnicity

2. **Check logs**:
   - Look for: `🔍 Auto-detected API v3 configuration for user <user_id>`
   - This confirms auto-detection is working

## Alternative Configuration

If you prefer explicit configuration over auto-detection, you can still set:

```env
EXTERNAL_API_VERSION=v3
```

in your `.env` file. This will bypass the auto-detection logic.

## Benefits

1. **User-Friendly**: No need to manually set `EXTERNAL_API_VERSION` when v3 credentials are present
2. **Backward Compatible**: Existing configurations with explicit version settings continue to work
3. **Flexible**: Supports both naming conventions (`EXTERNAL_V3_*` and `API_V3_*`)
4. **Debugging**: Logs auto-detection for easier troubleshooting

## Related Files

- `handlers/catalog_handlers.py` - Main fix location
- `utils/enhanced_keyboards.py` - Contains v3-specific filter keyboards
- `config/settings.py` - Defines `EXTERNAL_API_VERSION` and v3 credential fields
- `api_v3/config/api_config.py` - Loads v3 configuration from environment

## Implementation Complete

Auto-detection has been implemented across all critical services:
- ✅ `handlers/catalog_handlers.py` - Filter and search UI detection
- ✅ `services/external_api_service.py` - Service initialization
- ✅ `services/card_service.py` - Card service API version detection

This creates a consistent auto-detection experience across the entire application.

