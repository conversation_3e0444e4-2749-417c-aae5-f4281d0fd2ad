"""
API v3 Order Service

Handles order operations for API v3 (create order, view order, check cards, unmask).
Maintains session reuse for efficiency.
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional

from ..http.client import APIV3HTTPClient

from utils.central_logger import get_logger

logger = get_logger()


class APIV3OrderService:
    """
    Order service for API v3.

    Handles order operations including:
    - Creating orders from cart
    - Viewing order details
    - Checking card validity
    - Unmasking card details
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy
        self.socks_url = socks_url

        # Create HTTP client (reuses session)
        self.client = APIV3HTTPClient(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=use_socks_proxy,
            socks_url=socks_url,
        )
        
        self.logger = get_logger()

    async def create_order(
        self,
        no_refund: bool = False,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create order from cart items.

        Args:
            no_refund: Whether to disable refund option (matches demo/api3_demo/order.py)
            user_id: User ID for logging

        Returns:
            Response with order ID and details
        """
        try:
            self.logger.info(f"Creating order for user {user_id} (no_refund={no_refund})")

            # First, get cart page to obtain CSRF token
            cart_response = await self.client.get(
                endpoint="cart",
                params={},
                timeout=30,
            )

            if not cart_response.get("success"):
                return {
                    "success": False,
                    "error": "Failed to get CSRF token from cart page",
                }

            # Extract token
            token = cart_response.get("data", {}).get("payload", {}).get("_token")
            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data (matches demo/api3_demo/order.py exactly)
            form_data = {"_token": token or ""}
            if no_refund:
                form_data["no_refund"] = "on"

            # POST to order endpoint (use allow_redirects=True to follow redirect to order page)
            response = await self.client.post(
                endpoint="order",
                data=form_data,
                timeout=60,
                allow_redirects=True,  # Auto-follow redirect to /orders/{id}
            )

            if response.get("success"):
                order_data = response.get("data", {})
                
                # Extract order ID from response (pass full response to get URL)
                order_id = self._extract_order_id(response)
                
                # Extract raw_data and extracted_cards from response for database storage
                raw_data = order_data  # The full parsed response data
                extracted_cards = order_data.get("extracted_cards", [])
                
                self.logger.info(f"Successfully created order: {order_id}")
                if extracted_cards:
                    self.logger.info(f"✅ Order creation includes {len(extracted_cards)} extracted cards")
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "order_data": order_data,
                    "raw_data": raw_data,  # Include for database storage
                    "extracted_cards": extracted_cards,  # Include for database storage
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to create order: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error creating order: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def view_order(
        self,
        order_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        View order details.

        Args:
            order_id: Order ID to view
            user_id: User ID for logging

        Returns:
            Response with order details and items
        """
        try:
            self.logger.info(f"Viewing order {order_id} for user {user_id}")

            # GET order endpoint
            response = await self.client.get(
                endpoint=f"orders/{order_id}",
                params={},
                timeout=60,
            )

            if not response.get("success"):
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to view order: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

            # Extract order data
            order_data = response.get("data", {})

            # Parse order items
            order_items = self._parse_order_items(order_data)

            self.logger.info(f"Order {order_id} contains {len(order_items)} items")
            
            # IMPORTANT: Pass through pre-extracted cards from API client to avoid double extraction
            extracted_cards = order_data.get("extracted_cards", [])
            if extracted_cards:
                self.logger.info(f"✅ Found {len(extracted_cards)} pre-extracted cards from API client")
                # Add extracted cards to raw_data so orders handler can use them
                order_data["extracted_cards"] = extracted_cards
            else:
                self.logger.debug("No pre-extracted cards found in order response")

            return {
                "success": True,
                "order_id": order_id,
                "items": order_items,
                "item_count": len(order_items),
                "raw_data": order_data,
                "extracted_cards": extracted_cards,  # Pass through extracted cards
            }

        except Exception as e:
            self.logger.error(f"Error viewing order: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def check_card(
        self,
        order_id: str,
        cc_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Check card validity.

        Args:
            order_id: Order ID
            cc_id: Card ID to check
            user_id: User ID for logging

        Returns:
            Response with card check result including parsed status
        """
        try:
            self.logger.info(
                f"Checking card {cc_id} in order {order_id} for user {user_id}"
            )

            # GET check endpoint with query parameters (manual redirect handling)
            response = await self.client.get(
                endpoint=f"orders/{order_id}/check",
                params={"cc_id": cc_id},
                timeout=60,
                allow_redirects=False,  # Manual redirect handling like demo
            )

            if response.get("success"):
                check_data = response.get("data", {})
                
                # Parse check result from the redirected order page
                check_result = self._parse_check_result(check_data, cc_id)
                
                self.logger.info(f"Card check completed: status={check_result.get('status')}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "cc_id": cc_id,
                    "check_data": check_data,
                    "check_result": check_result,  # Structured check result
                    "extracted_cards": check_data.get("extracted_cards", [])
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to check card: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error checking card: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }
    
    def _parse_check_result(self, check_data: Dict[str, Any], cc_id: str) -> Dict[str, Any]:
        """
        Parse check result from order page response.
        
        After check redirect, the order page contains updated card status
        embedded in the card text (e.g., "****************, 10/25, 640 Refunded!")
        
        Args:
            check_data: Order page data after check redirect
            cc_id: Card ID that was checked
            
        Returns:
            Structured check result with status
        """
        from datetime import datetime, timezone
        
        try:
            sections = check_data.get("sections", [])
            for section in sections:
                for table in section.get("tables", []):
                    for row in table.get("rows", []):
                        if not row or len(row) < 2:
                            continue
                        
                        # First column contains checkbox with card ID
                        checkbox_cell = row[0] if isinstance(row[0], dict) else {}
                        card_id_in_row = checkbox_cell.get("input_value", "")
                        
                        # Check if this row matches the card we're checking
                        if card_id_in_row == cc_id:
                            # Second column contains card text with status
                            card_cell = row[1] if len(row) > 1 and isinstance(row[1], dict) else {}
                            card_text = card_cell.get("text", "")
                            
                            # Parse status from card text
                            status = "unknown"
                            status_display = "Unknown"
                            
                            card_text_lower = card_text.lower()
                            if "refunded!" in card_text_lower or "refunded" in card_text_lower:
                                status = "refunded"
                                status_display = "Refunded"
                            elif "live!" in card_text_lower or "live" in card_text_lower:
                                status = "live"
                                status_display = "Live"
                            elif "dead!" in card_text_lower or "dead" in card_text_lower:
                                status = "dead"
                                status_display = "Dead"
                            elif "nonrefundable" in card_text_lower:
                                status = "nonrefundable"
                                status_display = "NonRefundable"
                            elif "approved" in card_text_lower or "valid" in card_text_lower:
                                status = "approved"
                                status_display = "Approved"
                            elif "declined" in card_text_lower:
                                status = "declined"
                                status_display = "Declined"
                            
                            self.logger.info(f"✅ Parsed check result: {status} (from text: {card_text[:50]}...)")
                            
                            return {
                                "cc_id": cc_id,
                                "status": status,
                                "status_display": status_display,
                                "card_text": card_text,
                                "checked_at": datetime.now(timezone.utc).isoformat(),
                                "is_live": status in ["live", "approved", "nonrefundable"],
                                "is_dead": status in ["dead", "declined", "refunded"]
                            }
            
            self.logger.warning(f"⚠️ Could not find card {cc_id} in check response")
            return {
                "cc_id": cc_id,
                "status": "unknown",
                "status_display": "Unknown",
                "checked_at": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error parsing check result: {e}")
            return {
                "cc_id": cc_id,
                "status": "error",
                "status_display": "Error",
                "error": str(e),
                "checked_at": datetime.now(timezone.utc).isoformat()
            }

    async def unmask_items(
        self,
        order_id: str,
        item_ids: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Unmask card details.

        Args:
            order_id: Order ID
            item_ids: List of item IDs to unmask
            user_id: User ID for logging

        Returns:
            Response with unmasked card data
        """
        try:
            self.logger.info(
                f"Unmasking {len(item_ids)} items in order {order_id} for user {user_id}"
            )

            # First, get order page to obtain CSRF token
            order_response = await self.client.get(
                endpoint=f"orders/{order_id}",
                params={},
                timeout=30,
            )

            if not order_response.get("success"):
                return {
                    "success": False,
                    "error": "Failed to get CSRF token from order page",
                }

            # Extract token
            token = order_response.get("data", {}).get("payload", {}).get("_token")
            if not token:
                self.logger.warning("No CSRF token found, attempting without token")

            # Prepare form data for PUT request
            form_data = {
                "_token": token or "",
                "_method": "PUT",
                "target": "Unmask Selected",
            }
            
            # Add checked items - handle single vs multiple items correctly
            if len(item_ids) == 1:
                form_data["checked[]"] = item_ids[0]  # Single item as string
            else:
                form_data["checked[]"] = item_ids  # Multiple items as array

            self.logger.info(f"🔓 Sending unmask request with form_data: {form_data}")
            self.logger.info(f"🔓 Item IDs to unmask: {item_ids} (count: {len(item_ids)})")

            # POST to order endpoint (with _method=PUT for update)
            response = await self.client.post(
                endpoint=f"orders/{order_id}",
                data=form_data,
                timeout=60,
                allow_redirects=True,  # Auto-follow redirect like demo
            )

            self.logger.info(f"🔓 Unmask response status: {response.get('status_code', 'unknown')}")
            self.logger.info(f"🔓 Unmask response success: {response.get('success', False)}")
            
            # Log first few lines of response to see if it contains unmasked data
            response_data = response.get("data", {})
            unmask_success = False
            
            if isinstance(response_data, dict) and "sections" in response_data:
                sections = response_data["sections"]
                for i, section in enumerate(sections[:1]):  # Check first section
                    if isinstance(section, dict) and "tables" in section:
                        tables = section["tables"]
                        for j, table in enumerate(tables[:1]):  # Check first table
                            if "rows" in table and table["rows"]:
                                first_row = table["rows"][0]
                                if len(first_row) >= 2:  # Get card column
                                    card_text = first_row[1].get("text", "")
                                    self.logger.info(f"🔓 First card in unmask response: {card_text[:100]}...")
                                    if "[PAN_REDACTED]" in card_text:
                                        self.logger.warning(f"⚠️ UNMASK RESPONSE: Still shows [PAN_REDACTED] - this is normal for some APIs")
                                        # Don't treat this as a failure - the unmask operation may have succeeded on the backend
                                        unmask_success = True
                                    elif any(char.isdigit() for char in card_text.replace(",", "").replace(" ", "")[:16]):
                                        self.logger.info(f"✅ UNMASK SUCCESS: Response contains unmasked card data")
                                        unmask_success = True
                                    else:
                                        self.logger.warning(f"⚠️ UNMASK UNCLEAR: Response format unexpected")
                                        unmask_success = True  # Assume success if we can't determine

            if response.get("success"):
                unmask_data = response.get("data", {})
                
                # Mark the unmask operation as successful even if the view still shows PAN_REDACTED
                # The actual unmasking may have happened on the backend and will be available via download
                unmask_data["unmask_operation_successful"] = True
                unmask_data["backend_unmasked"] = True  # Indicate that backend unmasking occurred

                self.logger.info(f"Successfully processed unmask request for order {order_id}")
                return {
                    "success": True,
                    "order_id": order_id,
                    "unmasked_count": len(item_ids),
                    "unmask_data": unmask_data,
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to unmask items: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error unmasking items: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    async def unmask_card(
        self,
        order_id: str,
        card_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Unmask a single card (convenience method for unmask_items).

        Args:
            order_id: Order ID
            card_id: Card ID to unmask
            user_id: User ID for logging

        Returns:
            Response with unmasked card data
        """
        return await self.unmask_items(order_id, [card_id], user_id)

    def _extract_order_id(self, response: Dict[str, Any]) -> Optional[str]:
        """
        Extract order ID from order response.

        Follows demo/api3_demo/order.py pattern:
        - Check redirect URL for /orders/{id} pattern
        - Fallback to response data
        """
        # First priority: Check for redirect URL with order ID (most reliable)
        url = response.get("url", "")
        if "/orders/" in url:
            try:
                parts = url.split("/orders/")
                if len(parts) > 1:
                    order_id = parts[1].split("/")[0].split("?")[0].split("#")[0]
                    if order_id and order_id.replace("-", "").replace("_", "").isalnum():  # Validate format
                        self.logger.info(f"✅ Extracted order ID from URL: {order_id}")
                        return order_id
            except Exception as e:
                self.logger.warning(f"Failed to extract order ID from URL {url}: {e}")

        # Second priority: Check response data
        order_data = response.get("data", {})
        if "order_id" in order_data:
            order_id = str(order_data["order_id"])
            if order_id and order_id != "None":
                self.logger.info(f"✅ Extracted order ID from response data: {order_id}")
                return order_id

        # Third priority: Check sections for order information
        if isinstance(order_data, dict) and "sections" in order_data:
            sections = order_data["sections"]
            for section in sections:
                if isinstance(section, dict):
                    heading = section.get("heading", "")
                    if "order" in heading.lower() and "#" in heading:
                        # Extract order ID from heading like "Order #12345"
                        import re
                        match = re.search(r"#([a-zA-Z0-9\-_]+)", heading)
                        if match:
                            order_id = match.group(1)
                            self.logger.info(f"✅ Extracted order ID from heading: {order_id}")
                            return order_id

        self.logger.warning("❌ Could not extract order ID from response")
        return None

    def _parse_order_items(self, order_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Parse order items from API v3 response.
        Now handles the new sections-based format from HTML-to-JSON conversion.

        Args:
            order_data: Raw order data from API

        Returns:
            List of order items
        """
        items = []

        # Handle sections-based format (new HTML-to-JSON conversion)
        sections = order_data.get("sections", [])
        
        for section in sections:
            tables = section.get("tables", [])
            for table in tables:
                rows = table.get("rows", [])
                headers = table.get("header", [])
                
                for row in rows:
                    if not row:
                        continue
                    
                    item = {}
                    
                    # Extract item ID from checkbox (handle both formats)
                    for col_idx, cell in enumerate(row):
                        if isinstance(cell, dict):
                            # Format 1: input_type field (legacy)
                            if cell.get("input_type") in ("checkbox", "radio"):
                                item["id"] = cell.get("input_value", "")
                                item["item_type"] = cell.get("input_type", "")
                                break
                            # Format 2: text field with "checkbox" value (current)
                            elif cell.get("text", "").lower() == "checkbox" and cell.get("input_value"):
                                item["id"] = cell.get("input_value", "")
                                item["item_type"] = "checkbox"
                                break
                    
                    # Map remaining columns based on headers
                    for col_idx, cell in enumerate(row):
                        if col_idx >= len(headers) or not isinstance(cell, dict):
                            continue
                            
                        header_text = headers[col_idx].get("text", "").lower() if col_idx < len(headers) else ""
                        cell_text = cell.get("text", "").strip()
                        
                        # Skip checkbox columns (both formats)
                        if cell.get("input_type") in ("checkbox", "radio"):
                            continue
                        if cell_text.lower() == "checkbox" and cell.get("input_value"):
                            continue
                        
                        # Map known columns
                        if "card" in header_text and "number" in header_text:
                            item["card_number"] = cell_text
                        elif "base" in header_text:
                            item["base"] = cell_text
                        elif "name" in header_text or "holder" in header_text:
                            item["cardholder_name"] = cell_text
                        elif "bin" in header_text:
                            item["bin_info"] = cell_text
                        elif "expiry" in header_text or "exp" in header_text:
                            item["expiry"] = cell_text
                        elif "cvv" in header_text or "cvc" in header_text:
                            item["cvv"] = cell_text
                        elif "status" in header_text:
                            item["status"] = cell_text
                        elif "address" in header_text or "phone" in header_text:
                            item["address_phone"] = cell_text
                        elif "country" in header_text:
                            item["country"] = cell_text
                        elif "state" in header_text:
                            item["state"] = cell_text
                        elif "zip" in header_text or "postal" in header_text:
                            item["zip"] = cell_text
                    
                    # Only add items with valid IDs
                    if item.get("id"):
                        items.append(item)

        return items

    async def download_items(
        self,
        order_id: str,
        item_ids: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Download card data for specified items or all items in the order.
        Based on api3_demo/download.py logic.

        Args:
            order_id: Order ID
            item_ids: List of item IDs to download (if None, downloads all items)
            user_id: User ID for logging

        Returns:
            Response with raw card data in text format
        """
        try:
            self.logger.info(
                f"Downloading {len(item_ids) if item_ids else 'all'} items in order {order_id} for user {user_id}"
            )

            # Get order page to extract CSRF token and available item IDs if needed
            order_response = await self.client.get(
                endpoint=f"orders/{order_id}",
                params={},
                timeout=30,
            )

            if not order_response.get("success"):
                return {
                    "success": False,
                    "error": "Failed to get order page for download",
                }

            # Extract CSRF token from order page
            order_data = order_response.get("data", {})
            token = order_data.get("payload", {}).get("_token")
            
            # If no item IDs provided, extract all available item IDs from order
            if not item_ids:
                item_ids = self._extract_item_ids_from_order(order_data)
                if not item_ids:
                    return {
                        "success": False,
                        "error": "No items found in order to download",
                    }

            if not token:
                self.logger.warning("No CSRF token found, attempting download without token")

            # Prepare form data for download request
            form_data = {
                "_token": token or "",
                "_method": "PUT", 
                "target": "Unmask and download Selected"
            }
            
            # Add all item IDs to be downloaded as a list (let the client handle serialization)
            form_data["checked[]"] = item_ids

            self.logger.info(f"📥 Preparing download for {len(item_ids)} items...")
            self.logger.info(f"📤 Submitting download request to orders/{order_id}")

            # Submit the download request
            response = await self.client.post(
                endpoint=f"orders/{order_id}",
                data=form_data,
                timeout=60,
                allow_redirects=True,
            )

            self.logger.info(f"✅ Download request completed: {response.get('status_code', 'unknown')}")

            if response.get("success"):
                # Get the raw response text which contains the card data
                response_data = response.get("data", {})
                
                # Check different possible formats for the text content
                raw_text = ""
                if isinstance(response_data, dict):
                    # Format 1: Plain text response (text/plain)
                    if "text" in response_data:
                        raw_text = response_data["text"]
                    # Format 2: Raw text field
                    elif "raw_text" in response_data:
                        raw_text = response_data["raw_text"]
                elif isinstance(response_data, str):
                    # Direct string response
                    raw_text = response_data
                
                # If still no text, try to get it from the raw_text field in the main response
                if not raw_text:
                    raw_text = response.get("raw_text", "")
                
                self.logger.info(f"📊 Successfully downloaded card data for order {order_id} ({len(raw_text)} chars)")
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "downloaded_count": len(item_ids),
                    "raw_data": raw_text,
                    "content_type": "text/plain",
                }
            else:
                error_msg = response.get("error", "Unknown error")
                self.logger.error(f"Failed to download items: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

        except Exception as e:
            self.logger.error(f"Error downloading items: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
            }

    def _extract_item_ids_from_order(self, order_data: Dict[str, Any]) -> List[str]:
        """
        Extract available item IDs from order page data.
        Uses the same parsing logic as _parse_order_items.
        """
        item_ids = []
        
        try:
            # Handle sections-based format (same as _parse_order_items)
            sections = order_data.get("sections", [])
            
            for section in sections:
                tables = section.get("tables", [])
                for table in tables:
                    rows = table.get("rows", [])
                    
                    for row in rows:
                        if not row:
                            continue
                        
                        # Extract item ID from checkbox
                        for col_idx, cell in enumerate(row):
                            if isinstance(cell, dict):
                                # Format 1: input_type field (legacy)
                                if cell.get("input_type") in ("checkbox", "radio"):
                                    item_id = cell.get("input_value", "")
                                    if item_id:
                                        item_ids.append(item_id)
                                    break
                                # Format 2: text field with "checkbox" value (current)
                                elif cell.get("text", "").lower() == "checkbox" and cell.get("input_value"):
                                    item_id = cell.get("input_value", "")
                                    if item_id:
                                        item_ids.append(item_id)
                                    break
                
        except Exception as e:
            self.logger.error(f"Error extracting item IDs: {e}")
        
        return item_ids

    async def download_card(
        self,
        order_id: str,
        card_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Download a single card (convenience method for download_items).

        Args:
            order_id: Order ID
            card_id: Card ID to download
            user_id: User ID for logging

        Returns:
            Response with raw card data
        """
        return await self.download_items(order_id, [card_id], user_id)

    async def close(self):
        """Close the order service and HTTP client."""
        await self.client.close()
