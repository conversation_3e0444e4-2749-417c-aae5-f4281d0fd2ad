"""
Payment Module - Standalone Payment System for Telegram Bots

This module provides a complete payment system using OXA Pay gateway with:
- Multi-currency support (cryptocurrency payments)
- Automatic currency conversion to USDT
- Secure HMAC verification
- Real-time payment verification
- Comprehensive error handling
- Flask callback server for payment notifications
- Database operations for payment storage
- VIP payment hooks and bonus calculation
- Transaction management with rollback
- Template system for customizable messages

Author: AI Assistant
Version: 1.0.0
"""

import os
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class PaymentModule:
    """
    Main payment module class that provides easy integration.
    """
    
    def __init__(self, api_key: str, callback_url: Optional[str] = None):
        """
        Initialize the payment module.
        
        Args:
            api_key: OXA Pay API key
            callback_url: Optional custom callback URL
        """
        self.api_key = api_key
        self.callback_url = callback_url
        
        # Set environment variables
        os.environ["OXA_PAY_API_KEY"] = api_key
        if callback_url:
            os.environ["OXA_PAY_CALLBACK_URL"] = callback_url
        
        # Initialize default templates
        from .utils.template_helpers import create_default_templates
        create_default_templates()
    
    def get_handlers(self):
        """
        Get all payment handlers for registration with aiogram dispatcher.
        
        Returns:
            list: List of handler modules
        """
        from .handlers import deposit, payment_verification, manual_verification
        return [deposit, payment_verification, manual_verification]
    
    def get_keyboards(self):
        """
        Get all payment keyboards.
        
        Returns:
            dict: Dictionary of available keyboards
        """
        from .keyboards import deposit_kb
        return {
            'deposit_amount': deposit_kb.deposit_amount_keyboard,
            'deposit_pay': deposit_kb.deposit_pay_keyboard,
            'deposit_cancel': deposit_kb.deposit_cancel_keyboard,
            'custom_amount_cancel': deposit_kb.custom_amount_cancel_keyboard,
            'payment_verification': deposit_kb.payment_verification_keyboard,
            'payment_success': deposit_kb.payment_success_keyboard,
            'payment_processing': deposit_kb.payment_processing_keyboard,
        }
    
    def get_core_functions(self):
        """
        Get core payment functions.
        
        Returns:
            dict: Dictionary of core functions
        """
        from .core import payment_link, currency_converter, oxa_verify, flask_server
        return {
            'create_payment_link': payment_link.create_payment_link,
            'create_payment_link_sync': payment_link.create_payment_link_sync,
            'convert_currency': currency_converter.convert_currency,
            'process_payment_with_conversion': currency_converter.process_payment_with_conversion,
            'format_conversion_display': currency_converter.format_conversion_display,
            'get_usdt_equivalent_amount': currency_converter.get_usdt_equivalent_amount,
            'check_payment': oxa_verify.check_oxapay_payment,
            'run_callback_server': flask_server.run_callback_server,
        }
    
    def get_database_operations(self):
        """
        Get database operations for payment processing.
        
        Returns:
            dict: Dictionary of database functions
        """
        from .database import payment_operations, user_operations
        return {
            # Payment operations
            'save_payment_details': payment_operations.save_payment_details,
            'get_payment_by_track_id': payment_operations.get_payment_by_track_id,
            'update_payment_status': payment_operations.update_payment_status,
            'update_payment_status_atomic': payment_operations.update_payment_status_atomic,
            'get_user_payments': payment_operations.get_user_payments,
            'get_user_pending_payments': payment_operations.get_user_pending_payments,
            'get_latest_payment': payment_operations.get_latest_payment,
            'ensure_payment_indexes': payment_operations.ensure_payment_indexes,
            # User operations
            'get_user': user_operations.get_user,
            'get_or_create_user': user_operations.get_or_create_user,
            'get_user_balance': user_operations.get_user_balance,
            'update_user_balance': user_operations.update_user_balance,
            'get_user_balance_async': user_operations.get_user_balance_async,
            'update_user_balance_async': user_operations.update_user_balance_async,
            # Transaction operations
            'add_transaction': user_operations.add_transaction,
            'add_transaction_async': user_operations.add_transaction_async,
            'get_user_transactions': user_operations.get_user_transactions,
            # Enhanced database operations
            'get_payment_statistics': payment_operations.get_payment_statistics,
            'get_payments_by_date_range': payment_operations.get_payments_by_date_range,
            'search_payments': payment_operations.search_payments,
            'get_payment_analytics': payment_operations.get_payment_analytics,
            'cleanup_old_payments': payment_operations.cleanup_old_payments,
            'backup_payments': payment_operations.backup_payments,
            'restore_payments': payment_operations.restore_payments,
        }
    
    def get_utility_functions(self):
        """
        Get utility functions for payment processing.
        
        Returns:
            dict: Dictionary of utility functions
        """
        from .utils import (
            payment_utils, callback_factories, template_helpers, vip_payment_hooks, 
            bonus_calculator, transaction_manager, payment_amount_handler,
            hmac_verification, auto_verification, button_flow_documentation
        )
        return {
            'record_payment_completion': payment_utils.record_payment_completion,
            'log_payment': payment_utils.log_payment,
            'safe_update_data': payment_utils.safe_update_data,
            'clear_state_data': payment_utils.clear_state_data,
            'format_crypto_amount': payment_utils.format_crypto_amount,
            'get_payment_metrics': payment_utils.get_payment_metrics,
            'reset_payment_metrics': payment_utils.reset_payment_metrics,
            'safe_edit_message': payment_utils.safe_edit_message,
            'sanitize_html': payment_utils.sanitize_html,
            'format_text': template_helpers.format_text,
            'update_text': template_helpers.update_text,
            'trigger_vip_check_on_payment': vip_payment_hooks.trigger_vip_check_on_payment,
            'trigger_vip_check_on_verification': vip_payment_hooks.trigger_vip_check_on_verification,
            'calculate_deposit_bonus': bonus_calculator.calculate_deposit_bonus,
            'calculate_deposit_bonus_async': bonus_calculator.calculate_deposit_bonus_async,
            'create_payment_transaction': transaction_manager.create_payment_transaction,
            # Payment amount handling functions
            'check_payment_amounts': payment_amount_handler.check_payment_amounts,
            'handle_underpayment': payment_amount_handler.handle_underpayment,
            'handle_overpayment': payment_amount_handler.handle_overpayment,
            'handle_payment_completion': payment_amount_handler.handle_payment_completion,
            'log_payment_amount_analysis': payment_amount_handler.log_payment_amount_analysis,
            'create_underpayment_message': payment_amount_handler.create_underpayment_message,
            'create_overpayment_message': payment_amount_handler.create_overpayment_message,
            'create_payment_completion_message': payment_amount_handler.create_payment_completion_message,
            # HMAC verification functions
            'create_hmac_verifier': hmac_verification.create_hmac_verifier,
            'verify_payment_callback': hmac_verification.verify_payment_callback,
            'generate_payment_signature': hmac_verification.generate_payment_signature,
            'setup_flask_hmac_verification': hmac_verification.setup_flask_hmac_verification,
            'secure_compare': hmac_verification.secure_compare,
            'generate_secure_token': hmac_verification.generate_secure_token,
            'hash_sensitive_data': hmac_verification.hash_sensitive_data,
            # Auto verification functions
            'get_verification_manager': auto_verification.get_verification_manager,
            'start_auto_verification': auto_verification.start_auto_verification,
            'stop_auto_verification': auto_verification.stop_auto_verification,
            'add_payment_for_verification': auto_verification.add_payment_for_verification,
            'get_payment_verification_status': auto_verification.get_payment_verification_status,
            'get_verification_statistics': auto_verification.get_verification_statistics,
            # Button flow documentation functions
            'get_button_flow_documentation': button_flow_documentation.get_button_flow_documentation,
            'get_button_action_info': button_flow_documentation.get_button_action_info,
            'validate_button_action': button_flow_documentation.validate_button_action,
        }
    
    def get_callback_factories(self):
        """
        Get callback factories for payment operations.
        
        Returns:
            dict: Dictionary of callback factories
        """
        from .utils import callback_factories
        return {
            'DepositCallback': callback_factories.DepositCallback,
            'PaymentVerificationCallback': callback_factories.PaymentVerificationCallback,
            'PaymentCallback': callback_factories.PaymentCallback,
        }
    
    def get_states(self):
        """
        Get FSM states for payment operations.
        
        Returns:
            dict: Dictionary of state classes
        """
        from .states import payment_states
        return {
            'DepositStates': payment_states.DepositStates,
            'PaymentVerificationStates': payment_states.PaymentVerificationStates,
            'PaymentStates': payment_states.PaymentStates,
        }
    
    def get_all_functions(self):
        """
        Get all available functions in the payment module.
        
        Returns:
            dict: Dictionary containing all function categories
        """
        return {
            'core': self.get_core_functions(),
            'database': self.get_database_operations(),
            'utilities': self.get_utility_functions(),
            'callbacks': self.get_callback_factories(),
            'states': self.get_states(),
            'keyboards': self.get_keyboards(),
        }


def create_payment_module(api_key: str, callback_url: Optional[str] = None) -> PaymentModule:
    """
    Create a payment module instance.
    
    Args:
        api_key: OXA Pay API key
        callback_url: Optional custom callback URL
        
    Returns:
        PaymentModule: Configured payment module instance
    """
    return PaymentModule(api_key, callback_url)


# Export main classes and functions
__all__ = [
    'PaymentModule',
    'create_payment_module',
]
