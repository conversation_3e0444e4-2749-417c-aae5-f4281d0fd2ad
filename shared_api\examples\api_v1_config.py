"""
API v1 Configuration Example

Shows how to configure the shared API system to work with the existing
API v1/BASE 1 implementation, maintaining full backward compatibility.
"""

from typing import Optional, Dict, Any

from ..config.api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from ..core.constants import HTTPMethod, AuthenticationType


def create_api_v1_configuration(
    base_url: str = "https://ronaldo-club.to/api",
    login_token: Optional[str] = None,
    session_cookies: Optional[Dict[str, str]] = None,
    environment: str = "production"
) -> APIConfiguration:
    """
    Create API v1 configuration that matches the existing implementation
    
    Args:
        base_url: Base URL for the API
        login_token: Bearer token for authentication
        session_cookies: Session cookies for authentication
        environment: Environment (development, staging, production)
        
    Returns:
        Configured APIConfiguration for API v1
    """
    
    # Define all endpoints from the existing API v1 implementation
    endpoints = {
        "list_items": EndpointConfiguration(
            name="list_items",
            path="/cards/hq/list",
            method=HTTPMethod.POST,
            description="List available items with filtering and pagination"
        ),
        "cart_view": EndpointConfiguration(
            name="cart_view",
            path="/cart/",
            method=HTTPMethod.GET,
            description="View current cart contents"
        ),
        "cart_add": EndpointConfiguration(
            name="cart_add",
            path="/cart/",
            method=HTTPMethod.POST,
            description="Add item to cart"
        ),
        "cart_remove": EndpointConfiguration(
            name="cart_remove",
            path="/cart/",
            method=HTTPMethod.DELETE,
            description="Remove item from cart"
        ),
        "user_info": EndpointConfiguration(
            name="user_info",
            path="/user/getme",
            method=HTTPMethod.GET,
            description="Get current user information and authentication status"
        ),
        "checkout": EndpointConfiguration(
            name="checkout",
            path="/checkout/",
            method=HTTPMethod.POST,
            description="Process checkout and complete purchase"
        ),
        "check_order": EndpointConfiguration(
            name="check_order",
            path="/cards/hq/check",
            method=HTTPMethod.POST,
            description="Check order status and details"
        ),
        "orders_history": EndpointConfiguration(
            name="orders_history",
            path="/orders/",
            method=HTTPMethod.GET,
            description="Get user's order history"
        ),
    }
    
    # Set up authentication based on available credentials
    if login_token:
        auth = AuthenticationConfiguration(
            type=AuthenticationType.BEARER_TOKEN,
            bearer_token=login_token,
        )
    else:
        auth = AuthenticationConfiguration(
            type=AuthenticationType.NONE,
        )
    
    # Default headers that match the existing implementation
    default_headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.6",
        "content-type": "application/json",
        "origin": base_url.replace("/api", ""),
        "referer": base_url.replace("/api", "/store/cards/hq"),
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36"
    }
    
    # Add session cookies to custom headers if provided
    if session_cookies:
        # Convert cookies to a cookie header string
        cookie_string = "; ".join([f"{k}={v}" for k, v in session_cookies.items()])
        default_headers["cookie"] = cookie_string
    
    # Timeout configuration matching existing implementation
    timeout = TimeoutConfiguration(
        connect=10,
        read=30,
        total=60
    )
    
    # Retry configuration matching existing implementation
    retry = RetryConfiguration(
        max_attempts=3,
        delay=1.0,
        backoff_factor=1.0,
        retry_on_status=[500, 502, 503, 504, 429]  # Include rate limiting
    )
    
    # Create the configuration
    config = APIConfiguration(
        name="api_v1",
        base_url=base_url,
        endpoints=endpoints,
        authentication=auth,
        default_headers=default_headers,
        timeout=timeout,
        retry=retry,
        description="API v1 - External Cart API (Ronaldo Club) - Backward Compatible Configuration",
        version="1.0",
        environment=environment,
    )
    
    return config


# Example configuration dictionary for reference
API_V1_CONFIG_EXAMPLE = {
    "name": "api_v1",
    "base_url": "https://ronaldo-club.to/api",
    "description": "API v1 - External Cart API (Ronaldo Club)",
    "version": "1.0",
    "environment": "production",
    "endpoints": {
        "list_items": {
            "name": "list_items",
            "path": "/cards/hq/list",
            "method": "POST",
            "description": "List available items with filtering"
        },
        "cart_view": {
            "name": "cart_view",
            "path": "/cart/",
            "method": "GET",
            "description": "View cart contents"
        },
        "cart_add": {
            "name": "cart_add",
            "path": "/cart/",
            "method": "POST",
            "description": "Add item to cart"
        },
        "cart_remove": {
            "name": "cart_remove",
            "path": "/cart/",
            "method": "DELETE",
            "description": "Remove item from cart"
        },
        "user_info": {
            "name": "user_info",
            "path": "/user/getme",
            "method": "GET",
            "description": "Get user information"
        },
        "checkout": {
            "name": "checkout",
            "path": "/checkout/",
            "method": "POST",
            "description": "Process checkout"
        },
        "check_order": {
            "name": "check_order",
            "path": "/cards/hq/check",
            "method": "POST",
            "description": "Check order status"
        }
    },
    "authentication": {
        "type": "bearer_token",
        "api_key_header": "X-API-Key",
        "custom_headers": {}
    },
    "default_headers": {
        "accept": "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.6",
        "content-type": "application/json",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin"
    },
    "timeout": {
        "connect": 10,
        "read": 30,
        "total": 60
    },
    "retry": {
        "max_attempts": 3,
        "delay": 1.0,
        "backoff_factor": 1.0,
        "retry_on_status": [500, 502, 503, 504]
    }
}


def create_api_v1_client_example():
    """
    Example of how to create an API v1 client using the shared system
    
    This shows how existing code can be migrated to use the new shared API system.
    """
    from ..config.client_factory import api_client_factory
    
    # Method 1: Create configuration and then client
    config = create_api_v1_configuration(
        base_url="https://ronaldo-club.to/api",
        login_token="your-bearer-token-here"
    )
    client = api_client_factory.create_client(config)
    
    # Method 2: Create client from dictionary
    client = api_client_factory.create_client(API_V1_CONFIG_EXAMPLE)
    
    # Method 3: Use the factory's built-in API v1 configuration
    config = api_client_factory.create_api_v1_configuration(
        login_token="your-bearer-token-here"
    )
    client = api_client_factory.create_client(config)
    
    return client


async def api_v1_usage_example():
    """
    Example of using the API v1 client with the shared system
    
    This demonstrates how existing API v1 functionality works with the new system.
    """
    from ..config.client_factory import api_client_factory
    
    # Create client
    config = create_api_v1_configuration(
        login_token="your-bearer-token-here"
    )
    
    async with api_client_factory.create_client(config) as client:
        # List items (equivalent to existing list_items functionality)
        items_response = await client.post("list_items", data={
            "page": 1,
            "limit": 10,
            "priceFrom": 0,
            "priceTo": 500
        })
        
        # Get user info (equivalent to existing user_info functionality)
        user_response = await client.get("user_info")
        
        # Add item to cart (equivalent to existing cart_add functionality)
        cart_response = await client.post("cart_add", data={
            "item_id": "1818704",
            "quantity": 1
        })
        
        # View cart (equivalent to existing cart_view functionality)
        cart_view_response = await client.get("cart_view")
        
        # Check order status (equivalent to existing check_order functionality)
        order_response = await client.post("check_order", data={
            "id": 12345
        })
        
        return {
            "items": items_response,
            "user": user_response,
            "cart_add": cart_response,
            "cart_view": cart_view_response,
            "order_check": order_response
        }
