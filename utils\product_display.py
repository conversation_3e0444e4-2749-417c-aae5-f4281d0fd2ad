"""
Professional Product Display Components
Optimized layouts for displaying multiple products with enhanced visual hierarchy

This module provides comprehensive card catalogue display functionality with:
- Modern emoji-based UI with clean bullet separators (•) and proper breathing room
- Responsive design for mobile, tablet, and desktop devices  
- Comprehensive pagination system with Previous/Next and page indicators
- Accessible button layouts with proper touch targets (48px mobile, 44px desktop)
- Clean code with removed deprecated functions and commented-out code
- Enhanced mobile responsiveness with CSS media queries
- Intelligent field filtering for cleaner displays

Key Features:
- Card catalogue format with emojis (💳🏦📋📍✅💰♻️)
- Single-line sections with bullet separators (•) and breathing room spacing
- "Included" indicators for available data (📱 Phone included, 🎂 DOB included)
- Bold formatting for important values like BIN numbers
- Responsive button layouts (1 button/row mobile, 2 tablet, 3 desktop)
- Comprehensive pagination with jump buttons for long lists
- Mobile-first CSS media queries for proper responsive behavior
"""

from __future__ import annotations

import re
from typing import Dict, List, Optional, Any, Tuple
from numbers import Number

from textwrap import shorten

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from utils.central_logger import get_logger

logger = get_logger()


class ProductDisplayFormatter:
    """Enhanced product display formatting with professional layouts"""

    # Enhanced visual separators and styling with improved aesthetics
    ITEM_SEPARATOR = "—" * 25
    
    # Mobile-friendly separators
    MOBILE_SEPARATOR = "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    DESKTOP_SEPARATOR = "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    def _get_price_separator(self, device_type: str = "mobile") -> str:
        """Get appropriate separator based on device type"""
        if device_type == "mobile":
            return "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"  # Shorter for mobile
        else:
            return "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"  # Full length for desktop

    # Optimized card borders with clean, professional appearance
    CARD_CORNER = "╭─────────────────────────╮"
    CARD_BOTTOM = "╰─────────────────────────╯"
    CARD_DIVIDER = "├─────────────────────────┤"
    CARD_SECTION_BREAK = "│ ┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈ │"

    # Enhanced emojis for different data types with better visual hierarchy
    FIELD_EMOJIS = {
        "bank": "🏦",
        "country": "🌍",
        "brand": "💳",
        "scheme": "💳",
        "type": "📋",
        "level": "⭐",
        "quality": "💎",  # Changed from 💠 for better visibility
        "bin": "🔢",
        "location": "📍",
        "price": "💰",
        "expiry": "⏳",
        "exp": "⏳",
        "address": "🏠",  # Changed from 📍 to differentiate from location
        "refundable": "♻️",
        "zip": "📮",  # Changed from 📍 to differentiate
        "state": "🏙️",
        "city": "🏘️",
        "status": "📊",
        "id": "🆔",
        "availability": "✅",
        "verification": "🔐",
        "name": "👤",
        "f_name": "👤",
        "cardholder": "👤",
        "cardholder_name": "👤",
        "phone": "📱",
        "dob": "🎂",
        "dob_available": "🎂",
        "continent": "🌎",
        "ethnicity": "🌐",
        "original_price": "💰",
        "current_price": "💸",
        "discount_percent": "🏷️",
        "discounted": "🏷️",
        "expiring_soon": "⚠️",
        "expiry_warning": "⚠️",
    }

    FIELD_LABELS = {
        "bin": "BIN",
        "bank": "Bank",
        "country": "Country",
        "location": "Location",
        "brand": "Brand",
        "scheme": "Scheme",
        "type": "Type",
        "level": "Level",
        "quality": "Quality",
        "price": "Price",
        "expiry": "Expiry",
        "exp": "Expiry",
        "address": "Address",
        "refundable": "Refundable",
        "state": "State",
        "city": "City",
        "zip": "ZIP",
        "status": "Status",
        "name": "Cardholder",
        "f_name": "First Name",
        "cardholder": "Cardholder",
        "cardholder_name": "Cardholder",
        "phone": "Phone",
        "dob": "DOB",
        "dob_available": "DOB Available",
        "continent": "Continent",
        "ethnicity": "Ethnicity",
        "original_price": "Original Price",
        "current_price": "Current Price",
        "discount_percent": "Discount",
        "discounted": "Discounted",
        "expiring_soon": "Expiring Soon",
        "expiry_warning": "Expiry Warning",
    }

    PRIMARY_FIELD_ORDER = [
        "bin",
        "bank",
        "brand",
        "scheme",
        "type",
        "level",
        "quality",
        "expiry",
        "name",
        "cardholder",
        "cardholder_name",
        "location",
        "country",
        "address",
        "phone",
        "dob",
        "dob_available",
        "price",
        "original_price",
        "current_price",
        "discount_percent",
        "refundable",
        "expiring_soon",
    ]

    # Fields to hide from display - includes "base" as per requirements
    # Note: _id is handled specially in card header display
    HIDDEN_FIELDS = {
        "id",
        "card_id",  # Hide card_id as requested
        "base",  # Hide base field as per requirements
        "continent",  # Hide continent for compact display
        "sellerusername",
        "sellerUsername",
        "seller_username",
        "seller",
        "refund_rate",
        "isfirsthand",
        "expmonth",
        "expyear",
    }

    FIELD_ALIASES = {"basequality": "quality", "exp": "expiry"}
    
    def _is_truthy_value(self, value) -> bool:
        """Check if a value should be considered True (handles bool, str, int)"""
        if value is True or value == 1:
            return True
        if isinstance(value, str):
            return value.lower() in ['true', 'yes', '1', 'on']
        return False

    VERIFIED_FIELDS = {
        "address": "Address",
        "ip": "IP",
        "email": "Email",
        "phone": "Phone",
        "dob": "DOB",
        "ssn": "SSN",
        "ua": "User Agent",
        "dl": "Driver License",
        "mmn": "MMN",
    }

    def __init__(self):
        self.display_cache: Dict[str, str] = {}
        self.responsive_settings = {
            "mobile": {
                "max_line_length": 60,
                "buttons_per_row": 1,
                "compact_mode": True,
                "wrap_long_lines": True,
            },
            "tablet": {
                "max_line_length": 70,
                "buttons_per_row": 2,
                "compact_mode": False,
                "wrap_long_lines": True,
            },
            "desktop": {
                "max_line_length": 80,
                "buttons_per_row": 3,
                "compact_mode": False,
                "wrap_long_lines": False,
            },
        }

        # Simplified field filtering
        self.filtered_patterns = [
            "id",
            "_id",
            "uuid",
            "token",
            "key",
            "secret",
            "password",
            "auth",
            "session",
            "cookie",
            "created",
            "updated",
            "timestamp",
        ]

    def _wrap_text_intelligently(
        self, text: str, max_length: int, device_type: str = "mobile"
    ) -> List[str]:
        """
        Intelligently wrap text to prevent lines from exceeding specified length

        Args:
            text: Text to wrap
            max_length: Maximum line length
            device_type: Device type for responsive behavior

        Returns:
            List[str]: List of wrapped lines
        """
        if not text or len(text) <= max_length:
            return [text] if text else []

        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )

        # Don't wrap on desktop unless absolutely necessary
        if device_type == "desktop" and not settings.get("wrap_long_lines", False):
            return [text]

        lines = []
        current_line = ""

        # Split by logical separators first
        parts = text.split(" • ")

        for i, part in enumerate(parts):
            separator = " • " if i > 0 else ""
            test_line = current_line + separator + part

            if len(test_line) <= max_length:
                current_line = test_line
            else:
                # Current line is full, start new line
                if current_line:
                    lines.append(current_line)

                # Check if part itself is too long
                if len(part) > max_length:
                    # Break long part at word boundaries
                    words = part.split()
                    temp_line = ""

                    for word in words:
                        test_word_line = temp_line + (" " if temp_line else "") + word
                        if len(test_word_line) <= max_length:
                            temp_line = test_word_line
                        else:
                            if temp_line:
                                lines.append(temp_line)
                            temp_line = word

                    current_line = temp_line
                else:
                    current_line = part

        if current_line:
            lines.append(current_line)

        return lines

    def _should_filter_field(self, field_key: str, field_value: Any) -> bool:
        """
        Determine if a field should be filtered out from display

        Args:
            field_key: Field name
            field_value: Field value

        Returns:
            bool: True if field should be filtered out
        """
        field_key_lower = field_key.lower()

        # Check filtered patterns
        for pattern in self.filtered_patterns:
            if pattern in field_key_lower:
                return True

        # Filter out email addresses
        if isinstance(field_value, str) and "@" in field_value and "." in field_value:
            return True

        # Filter out very long strings (likely tokens)
        if isinstance(field_value, str) and len(field_value) > 100:
            return True

        return False

    def _create_enhanced_card_button_text(
        self, serial_number: int, card: Dict[str, Any]
    ) -> str:
        """
        Create card button text showing only available data - no fallbacks
        """
        try:
            button_parts = [f"{serial_number}."]

            # BIN - only if actually available
            bin_number = self._get_actual_bin(card)
            if bin_number:
                button_parts.append(bin_number)

            # Location - only if available
            location_part = self._format_button_location(card)
            if location_part:
                button_parts.append(location_part)

            # Price - only if available
            price_part = self._format_button_price(card)
            if price_part:
                button_parts.append(price_part)

            # Refundable status - only if explicitly set
            refund_part = self._format_button_refundable(card)
            if refund_part:
                button_parts.append(refund_part)

            result = " • ".join(button_parts)
            
            # Trim if too long
            if len(result) > 60:
                # Keep only essential parts
                essential_parts = [f"{serial_number}."]
                if bin_number:
                    essential_parts.append(bin_number)
                if location_part:
                    essential_parts.append(location_part[:10])  # Truncate location
                if price_part:
                    essential_parts.append(price_part)
                result = " ".join(essential_parts)
                
            return result
            
        except Exception as e:
            logger.error(f"Error creating button text for card {serial_number}: {e}")
            return f"{serial_number}. Card"
    
    def _format_button_location(self, card: Dict[str, Any]) -> str:
        """Format location for button - only show country flag"""
        country = card.get('country')
        if country and str(country).strip():
            country_str = str(country).strip().upper()
            flag = self._get_country_flag_emoji(country_str)
            return flag
        return ""
    
    def _calculate_discounted_price(self, original_price: float, discount: float) -> float:
        """Calculate final price after discount"""
        if discount <= 0 or discount > 100:
            return original_price
        return round(original_price * (1 - discount / 100), 2)
    
    def _format_button_price(self, card: Dict[str, Any]) -> str:
        """Format price for button - shows current/discounted price (API v3 compatible)"""
        # Extract API v3 price fields
        original_price = card.get('original_price')
        current_price = card.get('current_price')
        
        try:
            orig = float(original_price) if original_price is not None else None
            curr = float(current_price) if current_price is not None else None
        except (ValueError, TypeError):
            orig = curr = None
        
        # Case 1: Both original and current prices exist (API v3 format)
        # Show the current/discounted price in the button
        if orig is not None and curr is not None and orig > curr:
            if curr == 0:
                return "FREE"
            return f"${curr:.2f}"
        
        # Case 2: Fallback to single price (try current_price first, then others)
        price_fields = ['current_price', 'price', 'original_price']
        for field in price_fields:
            price = card.get(field)
            if price is not None:
                try:
                    price_val = float(price)
                    if price_val > 0:
                        return f"${price_val:.2f}"
                    elif price_val == 0:
                        return "FREE"
                except (ValueError, TypeError):
                    continue
        
        return ""
    
    def _format_button_refundable(self, card: Dict[str, Any]) -> str:
        """Format refundable status for button - only if explicitly set"""
        refundable = card.get('refundable')
        if refundable is not None and str(refundable).lower() in ['true', '1', 'yes']:
            return "✓"
        return ""

    def _format_location_for_button(self, country: str, state: str) -> str:
        """Format location information for button display - only show country flag"""
        if not country:
            return ""

        # Get country flag emoji
        country_flag = self._get_country_flag_emoji(country)
        
        # Show only the flag, no state or country name
        return country_flag

    def _format_price_for_button(self, price: Any) -> str:
        """Format price information for button display"""
        if price is None or price == "":
            return ""

        try:
            price_float = float(price)
            if price_float == 0:
                return "FREE"
            elif price_float > 0:
                return f"${price_float:.2f}"
            else:
                return ""  # Don't show negative prices
        except (TypeError, ValueError):
            # If price is not a number, try to format it as string
            price_str = str(price).strip()
            if price_str.lower() in ["free", "0", "0.0", "0.00"]:
                return "FREE"
            elif price_str:
                return price_str
            else:
                return ""

    def _format_refundable_for_button(self, refundable: Any) -> str:
        """Format refundable status for button display"""
        if refundable is None:
            return ""

        # Only show if explicitly True/refundable
        if refundable == 1 or refundable == "1" or refundable is True:
            return "✓"
        else:
            # Check string representations for True values only
            refundable_str = str(refundable).lower()
            if refundable_str in ["yes", "true", "refundable"]:
                return "✓"
            else:
                return ""

    def _create_compact_card_button_text(
        self, serial_number: int, card: Dict[str, Any]
    ) -> str:
        """
        Create compact card button text for mobile devices

        Args:
            serial_number: Card serial number (1, 2, 3, etc.)
            card: Card data dictionary

        Returns:
            str: Compact button text optimized for mobile
        """
        # Get basic card information with improved field handling
        bin_number = self._get_card_value(card, "bin") or "N/A"
        country = self._get_card_value(card, "country") or ""
        state = self._get_card_value(card, "state") or ""
        # Try current_price first, then price, then original_price
        price = (self._get_card_value(card, "current_price") or 
                self._get_card_value(card, "price") or 
                self._get_card_value(card, "original_price"))
        refundable = self._get_card_value(card, "refundable")
        
        # Check for discount and calculate final price
        discount = self._get_card_value(card, "discount")
        if price and discount:
            try:
                original_price = float(price)
                discount_val = float(discount)
                if discount_val > 0:
                    # Calculate discounted price
                    price = self._calculate_discounted_price(original_price, discount_val)
            except (ValueError, TypeError):
                pass

        # Build compact button text
        button_parts = []

        # 1. Serial number and BIN (always included)
        button_parts.append(f"{serial_number}. {bin_number}")

        # 2. Location with flag (show flag only, without state or country name)
        country_flag = self._get_country_flag_emoji(country)
        if country_flag != "📍":  # Only add if we have a proper flag
            button_parts.append(f"{country_flag}")

        # 3. Price (if available)
        price_part = self._format_price_for_button(price)
        if price_part:
            button_parts.append(price_part)

        # 4. Refundable status (if available)
        refund_part = self._format_refundable_for_button(refundable)
        if refund_part:
            button_parts.append(refund_part)

        # Join with spaces for mobile compactness
        return " ".join(button_parts)

    def _format_with_wrapping(self, text: str, device_type: str = "mobile") -> str:
        """
        Format text with intelligent wrapping based on device type

        Args:
            text: Text to format
            device_type: Device type for responsive behavior

        Returns:
            str: Formatted text with appropriate line breaks
        """
        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )
        max_length = settings.get("max_line_length", 60)

        if not settings.get("wrap_long_lines", True):
            return text

        wrapped_lines = self._wrap_text_intelligently(text, max_length, device_type)
        return "\n".join(wrapped_lines)

    def format_compact_card(
        self, card: Dict[str, Any], index: int = None, device_type: str = "mobile"
    ) -> str:
        """
        Format a single card showing only available data - clean modern layout
        """
        import time
        start_time = time.time()
        
        try:
            # Validate input card
            if not card or not isinstance(card, dict):
                logger.warning(f"Invalid card data for index {index}: {type(card)}")
                return f"<b>{index}.</b> ❌ <i>Invalid card data</i>"
            
            # Debug logging for price and address fields
            current_price = card.get('current_price')
            original_price = card.get('original_price') 
            generic_price = card.get('price')
            address = card.get('address')
            logger.debug(f"Card {index} prices: current={current_price}, original={original_price}, generic={generic_price}, address={address}")
            logger.debug(f"Formatting compact card {index}: available fields={list(card.keys())[:10]}")
            
            # Timeout protection for individual card formatting (5 seconds max per card)
            result = self._format_modern_card_layout(card, index, device_type, compact=True)
            
            formatting_time = time.time() - start_time
            if formatting_time > 5.0:
                logger.warning(f"Card {index} formatting took {formatting_time:.2f}s - unusually long")
            
            if not result or not result.strip():
                # Fallback to basic format if empty result
                logger.warning(f"Empty result for card {index}, using fallback")
                return self._format_basic_card_fallback(card, index)
            return result
            
        except Exception as e:
            logger.error(f"Error formatting compact card {index}: {e}")
            # Return minimal card info if main formatting fails - avoid traceback to prevent further issues
            return self._format_basic_card_fallback(card, index)

    def _format_modern_card_layout(
        self,
        card: Dict[str, Any],
        index: int = None,
        device_type: str = "mobile",
        compact: bool = True,
    ) -> str:
        '''Format card with clean, simplified layout focusing on essential information'''
        card_lines = []

        # Line 1: Card Header with BIN and Expiry
        card_header = self._build_card_header_line(card, index)
        if card_header:
            card_lines.append(card_header)

        # Build simplified information sections
        sections = []
        
        # Section 1: Bank and Card Type (Essential info only)
        bank_type_info = self._build_simplified_bank_type_section(card)
        if bank_type_info:
            sections.append(bank_type_info)
        
        # Section 2: Cardholder and Country (Essential personal info)
        holder_country_info = self._build_simplified_holder_section(card)
        if holder_country_info:
            sections.append(holder_country_info)
        
        # Section 3: Additional Details (Phone, DOB, Address, etc.)
        additional_details = self._build_additional_details_section(card)
        if additional_details:
            sections.append(additional_details)
        
        # Section 4: Price (Clean, centered display)
        price_info = self._build_simplified_price_section(card)
        if price_info:
            sections.append(price_info)
        
        # Combine all sections
        card_lines.extend(sections)

        # Check if we have any meaningful content besides the header
        clean_lines = [line for line in card_lines if line and line.strip()]
        
        # If we only have a header (or nothing), add a fallback message
        if len(clean_lines) <= 1:  # Only header or empty
            # Try to show at least some card identifier or fallback
            fallback_info = self._create_fallback_card_info(card)
            if fallback_info:
                clean_lines.append(fallback_info)
            elif len(clean_lines) == 1:  # We have header but no content
                clean_lines.append("📄 <i>Card data loading...</i>")
            elif len(clean_lines) == 0:  # No header, no content
                if index is not None:
                    clean_lines.append(f"<b>{index}.</b> 📄 <i>Card #{index} - Data unavailable</i>")
                else:
                    clean_lines.append("📄 <i>Card data unavailable</i>")
        
        return '\n'.join(clean_lines)

    def _build_card_header_line(self, card: Dict[str, Any], index: int = None) -> str:
        """Build card header showing only available data"""
        header_parts = []
        
        # Add index and BIN together
        if index is not None:
            bin_value = self._get_actual_bin(card)
            if bin_value:
                header_parts.append(f"<b>{index}.</b> ◆ BIN: <b>{bin_value}</b>")
            else:
                header_parts.append(f"<b>{index}.</b>")
        else:
            bin_value = self._get_actual_bin(card)
            if bin_value:
                header_parts.append(f"◆ BIN: <b>{bin_value}</b>")
        
        # Expiry information - only show if available
        expiry_display = self._get_actual_expiry(card)
        if expiry_display:
            header_parts.append(f"EXP: <b>{expiry_display}</b>")
        
        return " • ".join(header_parts) if header_parts else ""
    
    def _get_actual_bin(self, card: Dict[str, Any]) -> str:
        """Get actual BIN value if available, no fallbacks"""
        bin_fields = ['bin', 'card_number']
        
        for field in bin_fields:
            value = card.get(field)
            if value and str(value).strip():
                bin_str = str(value).strip()
                # Extract BIN from card number if needed
                if len(bin_str) >= 6:
                    # Check if it looks like a BIN (digits, possibly with X or *)
                    clean_bin = bin_str.replace('X', '').replace('*', '')
                    if clean_bin.isdigit() or len(clean_bin) >= 4:
                        return bin_str[:6]
                elif len(bin_str) >= 4:
                    return bin_str
        
        return ""
    
    def _get_actual_expiry(self, card: Dict[str, Any]) -> str:
        """Get actual expiry value if available, no fallbacks"""
        expiry_fields = ['expiry', 'exp', 'expiration']
        
        for field in expiry_fields:
            value = card.get(field)
            if value and str(value).strip():
                return self._format_expiry_value(str(value).strip())
        
        # Try month/year combination
        month = card.get('expmonth')
        year = card.get('expyear')
        if month and year:
            try:
                month_int = int(str(month).strip())
                year_str = str(year).strip()
                if 1 <= month_int <= 12:
                    if len(year_str) == 4:
                        year_str = year_str[-2:]
                    return f"{month_int:02d}/{year_str}"
            except (ValueError, TypeError):
                pass
        
        return ""
    
    def _format_expiry_value(self, value: str) -> str:
        """Format expiry value consistently"""
        # Handle MM/YY format
        if '/' in value and len(value) == 5:
            return value.upper()
        
        # Handle MMYY format
        if len(value) == 4 and value.isdigit():
            return f"{value[:2]}/{value[2:]}"
        
        return value
    
    def _build_bank_info(self, card: Dict[str, Any]) -> str:
        """Build bank information if available with smart truncation"""
        bank = card.get('bank')
        if bank and str(bank).strip():
            bank_name = str(bank).strip().upper()
            
            # Smart truncation for very long bank names (over 60 characters)
            if len(bank_name) > 60:
                # Try to truncate at a natural break point
                words = bank_name.split()
                if len(words) > 1:
                    # Take first few words that fit within reasonable limit
                    truncated = ""
                    for word in words:
                        if len(truncated + word) < 50:  # Leave room for "..."
                            truncated += word + " "
                        else:
                            break
                    if truncated:
                        bank_name = truncated.strip() + "..."
                    else:
                        # If even first word is too long, truncate it
                        bank_name = bank_name[:47] + "..."
                else:
                    # Single very long word, truncate it
                    bank_name = bank_name[:57] + "..."
            
            return f"🏛️ {bank_name}"
        return ""
    
    def _build_type_brand_info(self, card: Dict[str, Any]) -> str:
        """Build type and brand information if available"""
        parts = []
        
        # Card type with level
        card_type = card.get('type')
        if card_type and str(card_type).strip():
            type_str = str(card_type).strip().upper()
            
            # Add level if available and different from type
            level = card.get('level')
            if level and str(level).strip():
                level_str = str(level).strip().upper()
                if level_str != type_str and level_str not in ['', 'NULL', 'NONE', 'N/A']:
                    type_str = f"{type_str} ({level_str})"
            
            parts.append(f"💳 {type_str}")
        
        # Brand information
        brand = card.get('brand') or card.get('scheme')
        if brand and str(brand).strip():
            brand_str = str(brand).strip().upper()
            if brand_str not in ['', 'NULL', 'NONE', 'N/A']:
                parts.append(f"🏷️ {brand_str}")
        
        return " • ".join(parts)
    def _build_card_details_section(self, card: Dict[str, Any]) -> list:
        """Build card details section with bank and card type information"""
        section_lines = []
        
        # Bank information
        bank_info = self._build_bank_info(card)
        if bank_info:
            section_lines.append(bank_info)
        
        # Card type and brand information
        type_brand_info = self._build_type_brand_info(card)
        if type_brand_info:
            section_lines.append(type_brand_info)
        
        return section_lines
    
    def _build_personal_info_section(self, card: Dict[str, Any]) -> str:
        """Build personal information section with cardholder name and country if no address"""
        info_parts = []
        
        # Get cardholder info
        holder_info = self._build_holder_info(card)
        if holder_info:
            # Check if there's an address - if no address, add country to same line
            address = card.get('address')
            if not (address and str(address).strip()):
                # No address, so add country to name line
                country = card.get('country')
                if country and str(country).strip():
                    country_str = str(country).strip().upper()
                    # Remove continent if present (e.g., "GERMANY,Europe" -> "GERMANY")
                    if ',' in country_str:
                        country_str = country_str.split(',')[0].strip()
                    flag = self._get_country_flag_emoji(country_str)
                    if flag and flag != "📍":
                        holder_info += f" • {flag} {country_str}"
                    else:
                        holder_info += f" • 📍 {country_str}"
            info_parts.append(holder_info)
        
        return "\n".join(info_parts) if info_parts else ""
    
    def _build_contact_info_section(self, card: Dict[str, Any]) -> str:
        """Build contact and additional information section with enhanced formatting"""
        contact_lines = []
        
        # Address information - show full copyable address in uppercase
        address = card.get('address')
        country = card.get('country')
        
        if address and str(address).strip() and str(address).strip().lower() not in ['', 'null', 'none', 'n/a']:
            addr_str = str(address).strip().upper()  # Make address uppercase
            # Combine address and country in single copyable block
            if country and str(country).strip():
                country_str = str(country).strip().upper()
                # Remove continent if present (e.g., "GERMANY,Europe" -> "GERMANY")
                if ',' in country_str:
                    country_str = country_str.split(',')[0].strip()
                flag = self._get_country_flag_emoji(country_str)
                # Single copyable block with full address including country
                full_address = f"{addr_str}, {country_str}"
                contact_lines.append(f"{flag} <code>{full_address}</code>")
            else:
                contact_lines.append(f"📍 <code>{addr_str}</code>")
        # Note: Country without address is now handled in personal_info_section
        
        # Show actual data values instead of just indicators - all on one line
        additional_info = []
        
        # Phone number (show actual number, not just indicator)
        phone = card.get('phone')
        if phone and str(phone).strip():
            phone_str = str(phone).strip()
            additional_info.append(f"📱 {phone_str}")
        
        # DOB availability (show as checkmark) - handle both boolean and string values
        dob_available = card.get('dob_available')
        if self._is_truthy_value(dob_available):
            additional_info.append("🎂 ✅")
        
        # Expiring status - handle both boolean and string values
        is_expiring = card.get('is_expiring')
        if self._is_truthy_value(is_expiring):
            additional_info.append("⏳ Expiring Soon")
            logger.debug(f"🔍 Card with BIN {card.get('bin', 'unknown')} marked as expiring")
        
        # Add additional info on same line if available
        if additional_info:
            contact_lines.append(" • ".join(additional_info))
        
        # Return items with minimal spacing
        return "\n".join(contact_lines) if contact_lines else ""
    
    def _build_financial_section(self, card: Dict[str, Any]) -> str:
        """Build financial information section with centered, highlighted price display"""
        # Validate input
        if not card or not isinstance(card, dict):
            return ""
            
        # Price information - properly handle API v3 price fields
        # API v3 provides: original_price, current_price, discount_percentage
        # If both original and current exist, they are already calculated - don't recalculate!
        
        original_price = None
        current_price = None
        discount_percentage = 0
        
        # Extract original price
        if card.get('original_price') is not None:
            try:
                original_price = float(card['original_price'])
            except (ValueError, TypeError):
                pass
        
        # Extract current price (already discounted by API)
        if card.get('current_price') is not None:
            try:
                current_price = float(card['current_price'])
            except (ValueError, TypeError):
                pass
        
        # Extract discount percentage
        discount_pct = card.get('discount_percentage', 0)
        try:
            discount_percentage = float(discount_pct)
        except (ValueError, TypeError):
            discount_percentage = 0
        
        # Determine display price
        price_val = None
        final_price = None
        has_discount = False
        
        # Case 1: Both original and current prices exist (API v3 format)
        if original_price is not None and current_price is not None and original_price > current_price:
            # Prices are already calculated by API - use them directly
            price_val = original_price
            final_price = current_price
            has_discount = True
            # Use the provided discount percentage
            if discount_percentage <= 0:
                # Calculate it if not provided
                discount_percentage = ((original_price - current_price) / original_price) * 100
        # Case 2: Only one price with discount percentage (needs calculation)
        elif current_price is not None and discount_percentage > 0:
            # current_price is actually the base price, need to calculate discount
            price_val = current_price
            final_price = self._calculate_discounted_price(price_val, discount_percentage)
            has_discount = True
        # Case 3: Fallback to any available price field
        else:
            price_fields = ['current_price', 'price', 'original_price']
            for field in price_fields:
                price = card.get(field)
                if price is not None:
                    try:
                        price_val = float(price)
                        if price_val >= 0 and not (price_val == float('inf') or price_val != price_val):
                            break
                    except (ValueError, TypeError, OverflowError):
                        continue
            
            # Check for old-style discount field
            if price_val is not None:
                discount = card.get('discount', 0)
                try:
                    discount_val = float(discount)
                    if discount_val > 0:
                        final_price = self._calculate_discounted_price(price_val, discount_val)
                        discount_percentage = discount_val
                        has_discount = True
                except (ValueError, TypeError):
                    pass
        
        # Build price display
        if price_val is not None:
            if has_discount and final_price is not None:
                # Show discount
                if final_price == 0:
                    price_main = "   🎁 <b>FREE</b> 🆓"
                elif final_price < 5:
                    price_main = f"   💵 <s>${price_val:.2f}</s> → <b>${final_price:.2f}</b> <i>(Budget, {discount_percentage:.0f}% OFF)</i>"
                elif final_price < 15:
                    price_main = f"   💰 <s>${price_val:.2f}</s> → <b>${final_price:.2f}</b> <i>(Standard, {discount_percentage:.0f}% OFF)</i>"
                else:
                    price_main = f"   💎 <s>${price_val:.2f}</s> → <b>${final_price:.2f}</b> <i>(Premium, {discount_percentage:.0f}% OFF)</i>"
            else:
                # No discount
                if price_val < 0:
                    price_main = f"❌ <b>${price_val:.2f}</b> <i>(Invalid Price)</i>"
                elif price_val == 0:
                    price_main = "🎁 <b>FREE</b> 🆓"
                elif price_val < 5:
                    price_main = f"💵 <b>${price_val:.2f}</b> <i>(Budget)</i>"
                elif price_val < 15:
                    price_main = f"💰 <b>${price_val:.2f}</b> <i>(Standard)</i>"
                else:
                    price_main = f"💎 <b>${price_val:.2f}</b> <i>(Premium)</i>"
            
            # Add refundable status with enhanced styling
            refundable = card.get('refundable')
            if refundable is not None and str(refundable).lower() in ['true', '1', 'yes']:
                price_main += " ♻️ <i>Refundable</i>"
            
            # Create centered, highlighted price display as the end of card data
            # Use 4 spaces padding for discounted prices (longer text), 17 spaces for regular prices
            separator_line = "━━━━━━━━━━━━━━━━━━━━━━━"
            padding = "    " if (has_discount and final_price is not None) else "                 "
            centered_price = f"<code>{separator_line}</code>\n{padding}{price_main}\n<code>{separator_line}</code>"
            
            return centered_price
        
        return ""

    def _create_fallback_card_info(self, card: Dict[str, Any]) -> str:
        """Create fallback information for cards with minimal or no data"""
        fallback_parts = []
        
        # Try to show any available identifying information
        # Check for any non-empty fields that could provide card identity
        id_fields = ['id', 'card_id', '_id']
        for field in id_fields:
            value = card.get(field)
            if value and str(value).strip():
                fallback_parts.append(f"🆔 ID: <code>{str(value).strip()}</code>")
                break
        
        # Check for any available fields that have meaningful values
        meaningful_fields = {
            'bank': '🏛️',
            'type': '💳', 
            'country': '🌍',
            'cardholder_name': '👤',
            'address': '📍'
        }
        
        found_data = False
        for field, emoji in meaningful_fields.items():
            value = card.get(field)
            if value and str(value).strip() and str(value).strip().lower() not in ['', 'null', 'none', 'unknown']:
                fallback_parts.append(f"{emoji} {str(value).strip()}")
                found_data = True
                break  # Show only one field to keep it compact
        
        # If we found some data, return it
        if fallback_parts:
            return " • ".join(fallback_parts)
        
        # If no meaningful data found, show a helpful message
        if found_data:
            return "📄 <i>Limited card data available</i>"
        else:
            return "📄 <i>Card data pending...</i>"

    def _build_holder_info(self, card: Dict[str, Any]) -> str:
        """Build cardholder information if available and valid"""
        
        # Get cardholder name
        name_fields = ['cardholder_name', 'name', 'cardholder', 'f_name', 'holder']
        for field in name_fields:
            value = card.get(field)
            if value and str(value).strip():
                name = str(value).strip()
                # Allow all names including non-English characters, add LTR mark to prevent RTL issues
                if len(name) >= 2 and not name.upper() in ['UNKNOWN', 'CARDHOLDER', 'HOLDER', 'NAME', 'USER', 'CUSTOMER']:
                    # Add Left-to-Right mark (U+200E) to force LTR display without unsupported HTML
                    return f"👤 \u200E{name}"
        
        return ""
    
    def _is_valid_cardholder_name(self, name: str) -> bool:
        """Check if name looks like a real cardholder name"""
        name_lower = name.lower()
        
        # List of countries and invalid values
        invalid_names = {
            'spain', 'germany', 'france', 'italy', 'usa', 'united states', 
            'united kingdom', 'israel', 'turkey', 'poland', 'brazil', 
            'colombia', 'chile', 'ireland', 'philippines', 'netherlands', 
            'india', 'norway', 'uruguay', 'thailand', 'austria', 'cayman islands',
            'azerbaijan', 'russia', 'ukraine', 'kazakhstan', 'belarus', 'georgia',
            'unknown', 'cardholder', 'holder', 'name', 'user', 'customer'
        }
        
        if name_lower in invalid_names:
            return False
        
        # Basic validation - should have letters and reasonable length
        if len(name) < 2 or len(name) > 50:
            return False
        
        if not any(c.isalpha() for c in name):
            return False
        
        return True
    
    def _build_simplified_bank_type_section(self, card: Dict[str, Any]) -> str:
        """Build bank on one line, then brand, type, and level on next line - only show if available"""
        lines = []
        
        # Bank information (shortened) on its own line - skip if N/A or Unknown
        bank = card.get('bank')
        if bank and str(bank).strip() and str(bank).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            bank_name = str(bank).strip().upper()
            # Truncate very long bank names
            if len(bank_name) > 30:
                bank_name = bank_name[:27] + "..."
            lines.append(f"🏛️ {bank_name}")
        
        # Brand, type, and level on next line - only show if not N/A or Unknown
        brand_type_level_parts = []
        
        # Brand information (simplified)
        brand = card.get('brand') or card.get('scheme')
        if brand and str(brand).strip().upper() not in ['', 'NULL', 'NONE', 'N/A', 'UNKNOWN']:
            brand_str = str(brand).strip()
            brand_type_level_parts.append(f"💳 {brand_str}")
        
        # Card type
        card_type = card.get('type')
        if card_type and str(card_type).strip().upper() not in ['', 'NULL', 'NONE', 'N/A', 'UNKNOWN']:
            type_str = str(card_type).strip().upper()
            brand_type_level_parts.append(f"💳 {type_str}")
        
        # Level information
        level = card.get('level')
        if level and str(level).strip().upper() not in ['', 'NULL', 'NONE', 'N/A', 'UNKNOWN']:
            level_str = str(level).strip().upper()
            brand_type_level_parts.append(f"⭐️ {level_str}")
        
        if brand_type_level_parts:
            lines.append(" • ".join(brand_type_level_parts))
        
        return "\n".join(lines) if lines else ""
    
    def _build_simplified_holder_section(self, card: Dict[str, Any]) -> str:
        """Build simplified cardholder and location information - supports both API v1 (city/state/zip) and API v3 (address string)"""
        import html
        
        # Cardholder name - only show if available
        name = None
        name_fields = ['cardholder_name', 'name', 'cardholder', 'f_name', 'holder']
        for field in name_fields:
            value = card.get(field)
            if value and str(value).strip():
                name_str = str(value).strip()
                if len(name_str) >= 2 and name_str.upper() not in ['UNKNOWN', 'CARDHOLDER', 'HOLDER', 'NAME', 'USER', 'CUSTOMER', 'N/A']:
                    name = name_str
                    break
        
        # Get country for flag
        country = card.get('country')
        
        # Check for direct address field (API v3 style - address as string)
        address_field = card.get('address')
        address_str = None
        
        # If address is a string (API v3), use it directly
        if address_field and isinstance(address_field, str) and str(address_field).strip() and str(address_field).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NONE']:
            address_str = str(address_field).strip()
        else:
            # Otherwise, build from city/state/zip (API v1 style)
            city = card.get('city')
            state = card.get('state') 
            zip_code = card.get('zip')
            
            address_parts = []
            
            # Add city if available
            if city and str(city).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
                address_parts.append(str(city).strip().title())
            
            # Add state if available
            if state and str(state).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
                address_parts.append(str(state).strip().upper())
            
            # Add zip if available
            if zip_code and str(zip_code).strip() not in ['N/A', 'UNKNOWN', 'NONE', '0']:
                address_parts.append(str(zip_code).strip())
            
            if address_parts:
                address_str = ", ".join(address_parts)
        
        # Build final display line
        if name and address_str and country:
            # Both name and address available
            country_str = str(country).strip()
            if ',' in country_str:
                country_str = country_str.split(',')[0].strip()
            flag = self._get_country_flag_emoji(country_str)
            return f"👤 \u200E{html.escape(name)} • {flag} \u200E{html.escape(address_str)}"
        elif name:
            # Only name available
            return f"👤 \u200E{html.escape(name)}"
        elif address_str and country:
            # Only address available
            country_str = str(country).strip()
            if ',' in country_str:
                country_str = country_str.split(',')[0].strip()
            flag = self._get_country_flag_emoji(country_str)
            return f"{flag} \u200E{html.escape(address_str)}"
        elif country and str(country).strip():
            # Only country available
            country_str = str(country).strip()
            if ',' in country_str:
                country_str = country_str.split(',')[0].strip()
            flag = self._get_country_flag_emoji(country_str)
            return f"{flag} \u200E{html.escape(country_str)}"
        
        return ""
    
    def _build_additional_details_section(self, card: Dict[str, Any]) -> str:
        """Build additional details section - supports both API v1 (boolean flags) and API v3 (string values)"""
        details = []
        
        # === PHONE ===
        # Check for actual phone string value first (API v3 style)
        phone = card.get('phone')
        if phone and isinstance(phone, str) and str(phone).strip() and str(phone).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NO', '']:
            # Has actual phone value
            phone_str = str(phone).strip()
            if len(phone_str) > 15:
                phone_str = phone_str[:12] + "..."
            import html
            details.append(f"📱 {html.escape(phone_str)}")
        elif phone == 1 or phone == '1' or phone is True:
            # API v1 style - boolean flag
            details.append("📱")
        
        # === EMAIL ===
        # Check for actual email string value
        email = card.get('email')
        if email and isinstance(email, str) and str(email).strip() and str(email).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NO', '']:
            # Has actual email value
            email_str = str(email).strip()
            if len(email_str) > 25:
                email_str = email_str[:22] + "..."
            import html
            details.append(f"📧 {html.escape(email_str)}")
        elif email == 1 or email == '1' or email is True:
            # API v1 style - boolean flag
            details.append("📧")
        
        # === IP ===
        # Check for actual IP string value
        ip = card.get('ip')
        if ip and isinstance(ip, str) and str(ip).strip() and str(ip).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NO', '']:
            # Has actual IP value
            import html
            details.append(f"🌐 {html.escape(str(ip).strip())}")
        elif ip == 1 or ip == '1' or ip is True:
            # API v1 style - boolean flag
            details.append("🌐")
        
        # === DOB (API v3 style) ===
        dob = card.get('dob')
        dob_available = card.get('dob_available')
        if (dob and str(dob).strip().upper() in ['YES', 'TRUE', 'AVAILABLE', '1']) or self._is_truthy_value(dob_available):
            details.append("🎂")
        
        # === EXPIRING SOON (API v3 style - check both is_expiring and expiring_soon) ===
        is_expiring = card.get('is_expiring')
        expiring_soon = card.get('expiring_soon')
        if self._is_truthy_value(is_expiring) or self._is_truthy_value(expiring_soon):
            details.append("⏳ Expiring Soon")
        
        return " • ".join(details) if details else ""
    
    def _build_simplified_price_section(self, card: Dict[str, Any]) -> str:
        """Build simplified price display with clean separator and discount support"""
        # Extract price fields (API v3 format: original_price, current_price, discount_percentage)
        original_price = None
        current_price = None
        discount_percentage = 0
        
        if card.get('original_price') is not None:
            try:
                original_price = float(card['original_price'])
            except (ValueError, TypeError):
                pass
        
        if card.get('current_price') is not None:
            try:
                current_price = float(card['current_price'])
            except (ValueError, TypeError):
                pass
        
        discount_pct = card.get('discount_percentage', 0)
        try:
            discount_percentage = float(discount_pct)
        except (ValueError, TypeError):
            discount_percentage = 0
        
        # Determine display price
        price_val = None
        final_price = None
        has_discount = False
        
        # Case 1: Both original and current prices exist (API v3 format)
        if original_price is not None and current_price is not None and original_price > current_price:
            price_val = original_price
            final_price = current_price
            has_discount = True
            if discount_percentage <= 0:
                discount_percentage = ((original_price - current_price) / original_price) * 100
        # Case 2: Only one price with discount (needs calculation)
        elif current_price is not None and discount_percentage > 0:
            price_val = current_price
            final_price = self._calculate_discounted_price(price_val, discount_percentage)
            has_discount = True
        # Case 3: Fallback to any available price
        else:
            price_fields = ['current_price', 'price', 'original_price']
            for field in price_fields:
                price = card.get(field)
                if price is not None:
                    try:
                        price_val = float(price)
                        if price_val >= 0 and not (price_val == float('inf') or price_val != price_val):
                            break
                    except (ValueError, TypeError, OverflowError):
                        continue
            
            # Check for old-style discount field
            if price_val is not None:
                discount = card.get('discount', 0)
                try:
                    discount_val = float(discount)
                    if discount_val > 0:
                        final_price = self._calculate_discounted_price(price_val, discount_val)
                        discount_percentage = discount_val
                        has_discount = True
                except (ValueError, TypeError):
                    pass
        
        # Build price display
        if price_val is not None:
            if has_discount and final_price is not None:
                if final_price == 0:
                    price_display = "🎁 <b>FREE</b>"
                else:
                    price_display = f"💰 <s>${price_val:.2f}</s> → <b>${final_price:.2f}</b> <i>({discount_percentage:.0f}% OFF)</i>"
            else:
                # No discount - simple price display
                if price_val == 0:
                    price_display = "🎁 <b>FREE</b>"
                else:
                    price_display = f"💰 <b>${price_val:.2f}</b>"
            
            # Add refundable status if available
            refundable = card.get('refundable')
            if refundable is not None and str(refundable).lower() in ['true', '1', 'yes']:
                price_display += " ♻️"
            
            # Clean separator line - shorter for mobile
            # Use 4 spaces padding for discounted prices (longer text), 17 spaces for regular prices
            separator = "━━━━━━━━━━━━━━━━━━━━━━━"
            padding = "    " if (has_discount and final_price is not None) else "                 "
            return f"<code>{separator}</code>\n{padding}{price_display}\n<code>{separator}</code>"
        
        return ""
    
    def _build_location_info(self, card: Dict[str, Any]) -> str:
        """Build location information if available"""
        country = card.get('country')
        if country and str(country).strip():
            country_str = str(country).strip().upper()
            flag = self._get_country_flag_emoji(country_str)
            return f"{flag} {country_str}"
        return ""
    
    def _build_additional_details(self, card: Dict[str, Any]) -> str:
        """Build additional details like address, phone, DOB availability"""
        details = []
        
        # Address information - show full address in one line
        address = card.get('address')
        if address and str(address).strip():
            addr_str = str(address).strip()
            # Show full address without truncation
            details.append(f"🏠 {addr_str}")
        
        # Phone information
        phone = card.get('phone')
        if phone and str(phone).strip():
            phone_str = str(phone).strip()
            details.append(f"📱 {phone_str}")
        
        # DOB availability - show as tick emoji
        dob_available = card.get('dob_available')
        if dob_available:
            details.append("🎂 ✅")
        
        # Don't show continent for compact display
        
        return " • ".join(details)
    
    def _format_basic_card_fallback(self, card: Dict[str, Any], index: int = None) -> str:
        """Basic card formatting fallback for error cases"""
        try:
            parts = []
            
            # Index and BIN
            if index is not None:
                bin_value = self._get_actual_bin(card)
                if bin_value:
                    parts.append(f"<b>{index}.</b> ◆ BIN: <b>{bin_value}</b>")
                else:
                    parts.append(f"<b>{index}.</b> Card")
            
            # Basic info on separate lines
            info_parts = []
            
            # Bank
            bank = card.get('bank')
            if bank and str(bank).strip():
                info_parts.append(f"🏛️ {str(bank).strip().upper()}")
            
            # Name and country on same line
            name_country_parts = []
            
            # Name
            name_fields = ['cardholder_name', 'name', 'cardholder', 'f_name']
            for field in name_fields:
                name = card.get(field)
                if name and str(name).strip() and self._is_valid_cardholder_name(str(name).strip()):
                    name_country_parts.append(f"👤 {str(name).strip()}")
                    break
            
            # Country - ensure flag is always shown
            country = card.get('country')
            if country and str(country).strip():
                country_str = str(country).strip().upper()
                flag = self._get_country_flag_emoji(country_str)
                # Safety check for flag
                if flag and flag != "📍":
                    name_country_parts.append(f"{flag} {country_str}")
                else:
                    name_country_parts.append(f"📍 {country_str}")
            
            if name_country_parts:
                info_parts.append(" • ".join(name_country_parts))
            
            # Address and phone with enhanced formatting
            address = card.get('address')
            if address and str(address).strip():
                info_parts.append(f"📍 <i>{str(address).strip()}</i>")
            
            phone = card.get('phone')
            if phone and str(phone).strip():
                phone_str = str(phone).strip()
                if not phone_str.startswith('+'):
                    phone_str = f"+{phone_str}" if phone_str.replace('.', '').replace('-', '').isdigit() else phone_str
                info_parts.append(f"📱 <code>{phone_str}</code>")
            
            # DOB with enhanced styling
            dob_available = card.get('dob_available')
            if dob_available:
                info_parts.append("🎂 <b>✅</b>")
            
            # Enhanced price display
            financial_info = self._build_financial_section(card)
            if financial_info:
                info_parts.append(financial_info)
            
            # Combine all parts
            if parts:
                result = "\n".join(parts + info_parts)
            else:
                result = "\n".join(info_parts) if info_parts else "Card Information"
            
            return result
            
        except Exception as e:
            logger.error(f"Error in basic card fallback: {e}")
            return f"{index}. Card" if index else "Card"

    def _get_country_flag_emoji(self, country_code: str) -> str:
        """Get country flag emoji using dynamic detection from filter data"""
        if not country_code or not isinstance(country_code, str):
            return "📍"
        
        try:
            from utils.dynamic_country_flags import get_dynamic_country_flag
            flag = get_dynamic_country_flag(country_code)
            if flag and flag != "🌍":
                return flag
        except ImportError:
            logger.debug("dynamic_country_flags module not available")
        except Exception as e:
            logger.error(f"Error getting dynamic country flag: {e}")
        
        try:
            # Use centralized flag system for comprehensive coverage
            from utils.country_flags import get_country_flag
            flag = get_country_flag(country_code)
            if flag and flag != "🌍":
                return flag
        except ImportError:
            logger.debug("country_flags module not available")
        except Exception as e:
            logger.error(f"Error getting country flag: {e}")
        
        # Provide basic flag emojis for common countries as fallback
        country_upper = country_code.upper().strip()
        basic_flags = {
            'GERMANY': '🇩🇪',
            'USA': '🇺🇸',
            'UNITED STATES': '🇺🇸',
            'FRANCE': '🇫🇷',
            'UK': '🇬🇧',
            'UNITED KINGDOM': '🇬🇧',
            'SPAIN': '🇪🇸',
            'ITALY': '🇮🇹',
            'CANADA': '🇨🇦',
            'AUSTRALIA': '🇦🇺',
            'JAPAN': '🇯🇵',
            'CHINA': '🇨🇳',
            'INDIA': '🇮🇳',
            'BRAZIL': '🇧🇷',
            'MEXICO': '🇲🇽',
            'RUSSIA': '🇷🇺',
            'NETHERLANDS': '🇳🇱',
            'POLAND': '🇵🇱',
            'TURKEY': '🇹🇷',
            'ISRAEL': '🇮🇱',
            'AUSTRIA': '🇦🇹',
            'SWITZERLAND': '🇨🇭',
            'BELGIUM': '🇧🇪',
            'NORWAY': '🇳🇴',
            'SWEDEN': '🇸🇪',
            'DENMARK': '🇩🇰',
            'FINLAND': '🇫🇮'
        }
        
        return basic_flags.get(country_upper, "📍")

    # Removed old methods - replaced with new no-fallback versions above

    def _get_expiry_display(self, card: Dict[str, Any]) -> str:
        """Get expiry display - redirect to new method for consistency"""
        return self._get_actual_expiry(card)

    def _format_expiry_for_display(self, expiry_value: Any) -> str:
        """Format expiry value for consistent MM/YY display"""
        if not expiry_value:
            return ""

        expiry_str = str(expiry_value).strip()

        # Handle MM/YY format (already correct)
        if "/" in expiry_str and len(expiry_str) == 5:
            parts = expiry_str.split("/")
            if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                month, year = parts
                # Ensure MM format for month
                month = month.zfill(2)
                # Ensure YY format for year
                if len(year) == 4:
                    year = year[-2:]
                elif len(year) == 1:
                    year = year.zfill(2)
                return f"{month}/{year}"

        # Handle MMYY format (convert to MM/YY)
        if len(expiry_str) == 4 and expiry_str.isdigit():
            month = expiry_str[:2]
            year = expiry_str[2:]
            return f"{month}/{year}"

        # Handle MMYYYY format (convert to MM/YY)
        if len(expiry_str) == 6 and expiry_str.isdigit():
            month = expiry_str[:2]
            year = expiry_str[4:]
            return f"{month}/{year}"

        # Use existing formatting as fallback and clean it
        formatted = self._format_field_value("expiry", expiry_value)
        cleaned = self._strip_tags(formatted)

        # Try to extract MM/YY pattern from cleaned text
        match = re.search(r"(\d{1,2})[/\-]?(\d{2,4})", cleaned)
        if match:
            month, year = match.groups()
            month = month.zfill(2)
            if len(year) == 4:
                year = year[-2:]
            return f"{month}/{year}"

        return cleaned

    def _create_strategic_detail_line(
        self,
        card: Dict[str, Any],
        entry_map: Dict[str, Tuple[str, str, str]],
        used_keys: set,
    ) -> str:
        """
        Create a strategic additional detail line with the most valuable information

        Args:
            card: Card data dictionary
            entry_map: Available field entries
            used_keys: Set to track used keys

        Returns:
            str: Strategic detail line or empty string
        """
        strategic_parts = []

        # Priority 1: Card Type + Level combination (check card directly if entry_map is empty)
        type_entry = entry_map.get("type")
        level_entry = entry_map.get("level")

        # If entry_map is empty, get values directly from card
        if not entry_map:
            type_value = self._get_card_value(card, "type")
            level_value = self._get_card_value(card, "level")

            if self._is_displayable(type_value) and self._is_displayable(level_value):
                strategic_parts.append(f"📋 <b>{type_value}</b> • <i>{level_value}</i>")
                used_keys.add("type")
                used_keys.add("level")
            elif self._is_displayable(type_value):
                strategic_parts.append(f"📋 <b>{type_value}</b>")
                used_keys.add("type")
            elif self._is_displayable(level_value):
                strategic_parts.append(f"📋 <i>{level_value}</i>")
                used_keys.add("level")
        else:
            # Use entry_map when available
            if type_entry and level_entry:
                strategic_parts.append(
                    f"📋 <b>{type_entry[2]}</b> • <i>{level_entry[2]}</i>"
                )
                used_keys.add("type")
                used_keys.add("level")
            elif type_entry:
                strategic_parts.append(f"📋 <b>{type_entry[2]}</b>")
                used_keys.add("type")
            elif level_entry:
                strategic_parts.append(f"📋 <i>{level_entry[2]}</i>")
                used_keys.add("level")

        # Priority 2: Enhanced verification status with count
        verified_fields = []
        for field_key in self.VERIFIED_FIELDS.keys():
            value = self._get_card_value(card, field_key)
            if self._is_displayable(value):
                verified_fields.append(field_key)

        if verified_fields:
            verification_count = len(verified_fields)
            if verification_count >= 3:
                verification_status = (
                    f"🔐✨ <b>Fully Verified</b> ({verification_count} fields)"
                )
            elif verification_count == 2:
                verification_status = (
                    f"🔐 <b>Well Verified</b> ({verification_count} fields)"
                )
            else:
                verification_status = (
                    f"🔒 <b>Basic Verification</b> ({verification_count} field)"
                )

            if not strategic_parts:  # Only add if no type/level info
                strategic_parts.append(verification_status)

        # Priority 3: Availability indicator (if no other strategic info)
        if not strategic_parts:
            # Check for stock or availability indicators
            stock_indicators = ["stock", "available", "quantity", "inventory"]
            for indicator in stock_indicators:
                value = self._get_card_value(card, indicator)
                if self._is_displayable(value):
                    try:
                        stock_num = float(value)
                        if stock_num > 10:
                            strategic_parts.append("📦 <b>High Stock</b> 🔥")
                        elif stock_num > 5:
                            strategic_parts.append("📦 <b>Available</b> ✅")
                        elif stock_num > 0:
                            strategic_parts.append("📦 <b>Limited Stock</b> ⚡")
                        else:
                            strategic_parts.append("📦 <i>Out of Stock</i> ❌")
                        break
                    except (ValueError, TypeError):
                        strategic_parts.append("📦 <b>Available</b> ✅")
                        break

        return " • ".join(strategic_parts)


    def _strip_tags(self, text: str) -> str:
        """Remove HTML tags from text for clean display"""
        if not isinstance(text, str):
            return str(text)

        # Remove common HTML tags
        clean_text = re.sub(r"<[^>]+>", "", text)
        return clean_text.strip()

    def _collect_card_fields(
        self, card: Dict[str, Any]
    ) -> List[Tuple[str, str, str, str]]:
        """Gather ordered card fields excluding hidden ones."""
        entries: List[Tuple[str, str, str, str]] = []
        seen: set[str] = set()

        def add_field(field_key: str, raw_value: Any):
            normalized_key = self.FIELD_ALIASES.get(field_key, field_key)

            if (
                normalized_key in seen
                or normalized_key in self.HIDDEN_FIELDS
                or normalized_key == "bin"
            ):
                return

            if normalized_key in {"city", "state", "zip"}:
                return

            if not self._is_displayable(raw_value):
                return

            formatted_value = self._format_field_value(normalized_key, raw_value)
            if formatted_value == "":
                return

            label = self.FIELD_LABELS.get(
                normalized_key, normalized_key.replace("_", " ").title()
            )
            emoji = self.FIELD_EMOJIS.get(normalized_key, "•")

            entries.append((normalized_key, label, emoji, formatted_value))
            seen.add(normalized_key)

        for field_key in self.PRIMARY_FIELD_ORDER:
            if field_key == "location":
                location_value = self._format_location(card)
                if location_value and "location" not in seen:
                    label = self.FIELD_LABELS.get("location", "Location")
                    emoji = self.FIELD_EMOJIS.get("location", "📍")
                    entries.append(("location", label, emoji, location_value))
                    seen.add("location")
                continue
            value = self._get_card_value(card, field_key)
            if value is not None:
                add_field(field_key, value)

        for field_key in card.keys():
            add_field(field_key, card[field_key])

        return entries

    def _format_field_value(self, field_key: str, value: Any) -> str:
        """Format field values consistently for display."""
        if isinstance(value, bool):
            return "✅" if value else ""

        if isinstance(value, Number):
            if value == 0:
                return ""
            if field_key in ["price", "original_price"]:
                return self._as_code(f"${float(value):.2f}")
            if field_key == "discount_percent":
                return self._as_code(f"{int(value)}% off")
            if value == 1:
                return "✅"
            return self._as_code(f"{value}")

        if isinstance(value, str):
            stripped = value.strip()
            if not stripped:
                return ""
            if stripped.lower() in {"unavailable", "n/a", "none", "null", "0"}:
                return ""
            if stripped == "1":
                return "✅"
            if field_key in ["price", "original_price"]:
                try:
                    numeric = float(stripped)
                    return "" if numeric == 0 else self._as_code(f"${numeric:.2f}")
                except (TypeError, ValueError):
                    pass
            if field_key == "dob":
                if stripped.upper() in ["YES", "AVAILABLE"]:
                    return "✅"
                elif stripped.upper() == "NO":
                    return "❌"
            if field_key in ["discounted", "expiring_soon"]:
                if stripped.lower() in ["true", "yes", "1"]:
                    return "✅"
                elif stripped.lower() in ["false", "no", "0"]:
                    return "❌"
            return self._as_code(stripped)

        if field_key in ["price", "original_price"]:
            try:
                numeric = float(value)
                if numeric == 0:
                    return ""
                return self._as_code(f"${numeric:.2f}")
            except (TypeError, ValueError):
                pass

        if isinstance(value, (list, tuple, set)):
            formatted_items = [
                self._format_field_value(field_key, item) for item in value
            ]
            items = [item for item in formatted_items if item]
            return ", ".join(items)

        if isinstance(value, dict):
            formatted_pairs = []
            for key, val in value.items():
                formatted = self._format_field_value(key, val)
                if formatted:
                    formatted_pairs.append(f"{key}: {formatted}")
            return ", ".join(formatted_pairs)

        return self._as_code(str(value))

    def _is_displayable(self, value: Any) -> bool:
        """Determine whether a value should be shown."""
        if value is None:
            return False

        if isinstance(value, bool):
            return value is True

        if isinstance(value, Number):
            return value != 0

        if isinstance(value, str):
            stripped = value.strip()
            if not stripped:
                return False
            return stripped.lower() not in {"unavailable", "n/a", "none", "null", "0"}

        if isinstance(value, (list, tuple, set)):
            return any(self._is_displayable(item) for item in value)

        if isinstance(value, dict):
            return any(self._is_displayable(val) for val in value.values())

        return True

    def _is_truthy_flag(self, value: Any) -> bool:
        """Interpret common truthy flag representations from API responses."""
        if value is None:
            return False

        if isinstance(value, bool):
            return value

        if isinstance(value, (int, float)):
            return value == 1

        if isinstance(value, str):
            normalized = value.strip().lower()
            return normalized in {
                "1",
                "yes",
                "true",
                "y",
                "present",
                "included",
                "available",
                "✅",
                "✔",
            }

        return False

    def _format_location(self, card: Dict[str, Any]) -> str:
        """Combine city, state, country into a single location string, excluding email-format ZIP codes."""
        parts: List[str] = []

        city = card.get("city")
        state = card.get("state")
        country = card.get("country")
        postal_code = card.get("zip")

        if self._is_displayable(city):
            formatted = self._format_field_value("city", city)
            if formatted:
                parts.append(self._strip_tags(formatted))

        if self._is_displayable(state):
            formatted = self._format_field_value("state", state)
            if formatted:
                parts.append(self._strip_tags(formatted))

        # Only include ZIP if it's not an email address
        if self._is_displayable(postal_code):
            postal_str = str(postal_code).strip()
            # Check if ZIP contains @ symbol (email format)
            if "@" not in postal_str:
                formatted = self._format_field_value("zip", postal_code)
                if formatted:
                    parts.append(self._strip_tags(formatted))

        if self._is_displayable(country):
            formatted = self._format_field_value("country", country)
            if formatted:
                parts.append(self._strip_tags(formatted))

        return ", ".join(parts)

    def _get_card_value(self, card: Dict[str, Any], field_key: str) -> Any:
        """Retrieve a card value by field name with alias support and enhanced fallbacks."""
        # Direct field lookup
        if field_key in card:
            return card[field_key]

        # Check aliases
        for original_key, alias in self.FIELD_ALIASES.items():
            if alias == field_key and original_key in card:
                return card[original_key]

        # Enhanced fallbacks for common field variations
        field_variations = {
            'name': ['cardholder_name', 'f_name', 'holder', 'full_name', 'cardholder'],
            'cardholder': ['cardholder_name', 'name', 'f_name', 'holder', 'full_name'],
            'cardholder_name': ['name', 'cardholder', 'f_name', 'holder', 'full_name'],
            'f_name': ['cardholder_name', 'name', 'holder', 'full_name', 'cardholder'],
            'country': ['country_raw', 'location_country', 'country_code', 'location'],
            'price': ['current_price', 'amount', 'cost', 'original_price', 'price_raw'],
            'current_price': ['price', 'price_raw', 'amount', 'cost', 'original_price'],
            'original_price': ['base_price', 'price', 'price_raw'],
            'bank': ['issuer', 'bank_name', 'issuer_name'],
            'bin': ['bin_number', 'display_bin', 'card_number'],
            'expiry': ['exp', 'expiration', 'exp_date', 'expiry_date'],
            'exp': ['expiry', 'expiration', 'exp_date', 'expiry_date'],
            'address': ['billing_address', 'addr', 'address_line1'],
            'phone': ['phone_number', 'tel', 'telephone'],
            'state': ['state_name', 'region', 'province'],
            'city': ['city_name', 'locality'],
            'type': ['card_type', 'type_name'],
            'level': ['level_name', 'tier'],
            'brand': ['scheme', 'card_brand', 'brand_name'],
            'scheme': ['brand', 'card_scheme', 'network'],
        }
        
        if field_key in field_variations:
            for variation in field_variations[field_key]:
                if variation in card:
                    return card[variation]
        
        # Return the original lookup
        return card.get(field_key)

    def _get_field_group(self, field_key: str) -> str:
        """Determine logical grouping for UI layout."""
        if field_key in {"bank", "brand", "price"}:
            return "overview"
        if field_key in {"location", "address"}:
            return "location"
        if field_key in {"quality", "level", "type", "expiry", "refundable"}:
            return "details"
        return "extras"

    def _as_code(self, text: str) -> str:
        """Wrap text in a code tag unless already formatted or not needed."""
        if not text:
            return ""
        if "✅" in text or "<code>" in text:
            return text
        return f"<code>{text}</code>"

    def _create_price_bubble(self, price_value: str) -> List[str]:
        """Create a refined price highlight without heavy box styling."""
        formatted_price = self._as_code(price_value)
        highlight = f"💰 Price: {formatted_price}"
        return [highlight]

    def _create_enhanced_price_display(self, price_value: str, discount: float = 0) -> List[str]:
        """Create an enhanced price display with better visual appeal and categorization."""
        try:
            price_float = float(price_value)
            
            # Calculate final price if discount exists
            if discount > 0:
                final_price = self._calculate_discounted_price(price_float, discount)
                
                # Left-aligned with 3 spaces when there's a discount
                if final_price == 0:
                    return [f"   🎁 <b>FREE</b> ✨"]
                elif final_price < 1:
                    return [f"   💸 <s>${price_float:.2f}</s> → <b>${final_price:.2f}</b> <i>(Budget, {discount:.0f}% OFF)</i>"]
                elif final_price < 10:
                    return [f"   💰 <s>${price_float:.2f}</s> → <b>${final_price:.2f}</b> <i>(Standard, {discount:.0f}% OFF)</i>"]
                else:
                    return [f"   💎 <s>${price_float:.2f}</s> → <b>${final_price:.2f}</b> <i>(Premium, {discount:.0f}% OFF)</i>"]
            else:
                # No discount
                if price_float == 0:
                    return [f"🎁 <b>FREE</b> ✨"]
                elif price_float < 1:
                    formatted_price = f"${price_float:.2f}"
                    return [f"💸 <b>{formatted_price}</b> <i>(Budget)</i>"]
                elif price_float < 10:
                    formatted_price = f"${price_float:.2f}"
                    return [f"💰 <b>{formatted_price}</b> <i>(Standard)</i>"]
                else:
                    formatted_price = f"${price_float:.2f}"
                    return [f"💎 <b>{formatted_price}</b> <i>(Premium)</i>"]
        except (ValueError, TypeError):
            return [f"💰 <b>{str(price_value)}</b>"]

    def _format_inline_expiry(self, card: Dict[str, Any]) -> str:
        """Build inline expiry text combining month and year when available."""
        month = self._normalize_expiry_month(self._get_card_value(card, "expmonth"))
        year = self._normalize_expiry_year(self._get_card_value(card, "expyear"))
        raw_expiry = self._get_card_value(card, "expiry")

        if (not month or not year) and isinstance(raw_expiry, str):
            parsed_month, parsed_year = self._parse_expiry_string(raw_expiry)
            month = month or parsed_month
            year = year or parsed_year

        if month and year:
            month_tag = self._as_code(month)
            year_tag = self._as_code(year)
            return f"⏳ Exp {month_tag} / {year_tag}"

        if self._is_displayable(raw_expiry):
            formatted = self._format_field_value("expiry", raw_expiry)
            if formatted:
                return f"⏳ Exp {formatted}"

        return ""

    @staticmethod
    def _normalize_expiry_month(value: Any) -> str:
        """Normalize month values to two-digit strings."""
        if value is None:
            return ""

        if isinstance(value, Number):
            month_int = int(value)
        elif isinstance(value, str):
            digits = re.sub(r"\D", "", value)
            if not digits:
                return ""
            month_int = int(digits)
        else:
            return ""

        if not 1 <= month_int <= 12:
            return ""
        return f"{month_int:02d}"

    @staticmethod
    def _normalize_expiry_year(value: Any) -> str:
        """Normalize year values to either two or four digits."""
        if value is None:
            return ""

        if isinstance(value, Number):
            year_int = int(value)
            if year_int <= 0:
                return ""
            return f"{year_int:02d}" if year_int < 100 else str(year_int)

        if isinstance(value, str):
            digits = re.sub(r"\D", "", value)
            if not digits:
                return ""
            if len(digits) <= 2:
                return f"{int(digits):02d}"
            if len(digits) == 4:
                return digits
            if len(digits) > 4:
                return digits[-4:]

        return ""

    @staticmethod
    def _parse_expiry_string(value: str) -> Tuple[str, str]:
        """Extract month and year components from a mixed expiry string."""
        if not value:
            return "", ""

        match = re.search(r"(0?[1-9]|1[0-2])\D*([0-9]{2,4})", value)
        if not match:
            return "", ""

        month = f"{int(match.group(1)):02d}"
        year_digits = match.group(2)
        if len(year_digits) <= 2:
            year = f"{int(year_digits):02d}"
        elif len(year_digits) == 4:
            year = year_digits
        else:
            year = year_digits[-4:]

        return month, year

    @staticmethod
    def _group_inline(entries: List[str], items_per_line: int = 2) -> List[str]:
        """Group multiple detail items per line for a tighter layout."""
        if not entries:
            return []

        grouped: List[str] = []
        for index in range(0, len(entries), items_per_line):
            chunk = entries[index : index + items_per_line]
            grouped.append("   ".join(chunk))
        return grouped

    @staticmethod
    def _append_group_value(
        groups: Dict[str, List[str]], group: str, value: str
    ) -> None:
        """Append a value to a group if it is non-empty and not already present."""
        if not value:
            return

        if value not in groups.get(group, []):
            groups[group].append(value)


    def format_cards_with_filters(
        self,
        cards: List[Dict[str, Any]],
        active_filters: Dict[str, Any],
        page: int = 1,
        total_count: int = None,
    ) -> str:
        """
        Format cards with filter information and pagination

        Args:
            cards: List of card data
            active_filters: Currently active filters
            page: Current page number
            total_count: Total number of cards available

        Returns:
            str: Complete formatted display with filters and pagination
        """
        try:
            logger.info(f"📝 Formatting {len(cards)} cards in compact mode")
            
            # Enhanced header with better visual hierarchy (optimized)
            header_parts = []
            if page > 1:
                header_parts.append(f"🛒 <b>Cards Catalog</b> <i>(Page {page})</i>")
            else:
                header_parts.append("🛒 <b>Cards Catalog</b>")
            
            # Format cards efficiently
            card_blocks = self._format_cards_efficiently(cards)
            
            # Combine parts efficiently
            result_parts = header_parts + card_blocks
            
            # Add simple pagination
            if page > 1 or (total_count and total_count > len(cards)):
                if total_count:
                    result_parts.append(f"<i>Page {page} • Total: {total_count} cards</i>")
                else:
                    result_parts.append(f"<i>Page {page}</i>")
            
            # Add emoji legend (only on first page to avoid clutter)
            if page == 1:
                # Clean legend with balanced line lengths for mobile
                legend = (
                    "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
                    "<i>🏛️ Bank • 💳 Type • ⭐️ Level • 🇺🇸 Location\n"
                    "👤 Name • 📱 Phone • 📧 Email\n"
                    "🌐 IP • 🎂 DOB • ⏳ Expiring • ♻️ Refundable</i>"
                )
                result_parts.append(legend)
            
            final_text = "\n\n".join(result_parts)
            
            logger.info(f"📄 Final formatted text length: {len(final_text)} characters")
            
            return final_text
            
        except Exception as e:
            logger.error(f"❌ Critical error in format_cards_with_filters: {e}")
            # Emergency fallback - avoid complex traceback processing that could hang
            return f"🛒 <b>Cards Catalog</b>\n\n📊 <b>Found {len(cards)} cards</b>\n\n❌ Display error occurred. Please try again."
    
    def _format_cards_simple_fallback(self, cards: List[Dict[str, Any]]) -> str:
        """Simple fallback card formatting showing only available data."""
        if not cards:
            return "📭 <b>No Cards Available</b>\n\n<i>No cards found matching your criteria.</i>"
        
        result_parts = []
        
        for i, card in enumerate(cards[:6], 1):
            try:
                card_parts = [f"\n{i}."]
                
                # BIN - only if available
                bin_value = self._get_actual_bin(card)
                if bin_value:
                    card_parts.append(f"💳 <b>BIN:</b> {bin_value}")
                
                # Name - only if available and valid
                name_fields = ['cardholder_name', 'name', 'cardholder', 'f_name']
                for field in name_fields:
                    name = card.get(field)
                    if name and str(name).strip() and self._is_valid_cardholder_name(str(name).strip()):
                        card_parts.append(f"   👤 <b>Name:</b> {str(name).strip()}")
                        break
                
                # Country - with proper flag emoji
                country = card.get('country')
                if country and str(country).strip():
                    country_str = str(country).strip().upper()
                    flag = self._get_country_flag_emoji(country_str)
                    if flag and flag != "📍":
                        card_parts.append(f"   {flag} <b>Country:</b> {country_str}")
                    else:
                        card_parts.append(f"   🌍 <b>Country:</b> {country_str}")
                
                # Expiry - only if available
                expiry = self._get_actual_expiry(card)
                if expiry:
                    card_parts.append(f"   📅 <b>Expiry:</b> {expiry}")
                
                # Price - only if available
                price_fields = ['current_price', 'price', 'original_price']
                for field in price_fields:
                    price = card.get(field)
                    if price is not None:
                        try:
                            price_val = float(price)
                            if price_val == 0:
                                card_parts.append(f"                 🎁 <b>FREE</b>")
                            else:
                                card_parts.append(f"                 💰 <b>${price_val:.2f}</b>")
                            break
                        except (ValueError, TypeError):
                            continue
                
                # Only add card if it has meaningful content
                if len(card_parts) > 1:  # More than just the index
                    result_parts.append("\n".join(card_parts))
                    
                    if i < len(cards) and i < 6:
                        result_parts.append("   ─────────────────────")
                    
            except Exception as card_error:
                logger.error(f"Error formatting card {i}: {card_error}")
                continue  # Skip problematic cards instead of showing error
        
        return "\n".join(result_parts) if result_parts else "📭 <b>No Valid Cards Found</b>"

    def _format_active_filters(self, filters: Dict[str, Any]) -> str:
        """Format active filters for display"""
        if not filters:
            return ""

        filter_parts = []

        # Handle price range specially
        price_from = filters.get("priceFrom")
        price_to = filters.get("priceTo")

        if price_from is not None and price_to is not None:
            filter_parts.append(
                f"💰 Price: ${float(price_from):.2f} - ${float(price_to):.2f}"
            )
        elif price_from is not None:
            filter_parts.append(f"💰 Min Price: ${float(price_from):.2f}")
        elif price_to is not None:
            filter_parts.append(f"💰 Max Price: ${float(price_to):.2f}")

        # Handle other filters
        for key, value in filters.items():
            if key in ["priceFrom", "priceTo"] or not value:
                continue

            emoji = self.FIELD_EMOJIS.get(key, "🔍")
            display_key = key.replace("_", " ").title()

            if isinstance(value, bool):
                if value:
                    filter_parts.append(f"{emoji} {display_key}: Yes")
            else:
                filter_parts.append(f"{emoji} {display_key}: {value}")

        if not filter_parts:
            return ""

        return f"🔍 <b>Active Filters:</b>\n" + "\n".join(
            [f"• {part}" for part in filter_parts]
        )

    def create_enhanced_card_keyboard(
        self,
        cards: List[Dict[str, Any]],
        page: int = 1,
        total_count: int = None,
        has_filters: bool = False,
        callback_prefix: str = "catalog",
    ) -> InlineKeyboardMarkup:
        """
        Create an enhanced keyboard for card browsing with improved UX

        Args:
            cards: List of cards being displayed
            page: Current page number
            total_count: Total number of cards
            has_filters: Whether filters are active
            callback_prefix: Prefix for callback data

        Returns:
            InlineKeyboardMarkup: Enhanced keyboard with better organization
        """
        keyboard_rows: List[List[InlineKeyboardButton]] = []

        # Remove "Add All" and "Refresh" buttons as requested

        # Enhanced card buttons with comprehensive information
        if cards:
            card_buttons = []
            for i, card in enumerate(cards[:6], 1):  # Max 6 cards per page
                card_id = card.get("_id")
                button_text = self._create_enhanced_card_button_text(i, card)

                card_buttons.append(
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=f"{callback_prefix}:add_to_cart:{card_id}",
                    )
                )

            # Organize card buttons in rows of 2 for better mobile experience
            for i in range(0, len(card_buttons), 2):
                keyboard_rows.append(card_buttons[i : i + 2])

        # Optimized pagination system - compact and single-line
        nav_buttons = []
        max_page = (total_count + 5) // 6 if total_count else 1

        # Compact pagination with optimized button sizing
        if page > 1:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="⬅️", callback_data=f"{callback_prefix}:view_cards:{page-1}"
                )
            )

        # Compact page indicator in X/Y format
        nav_buttons.append(
            InlineKeyboardButton(text=f"{page}/{max_page}", callback_data="noop")
        )

        if page < max_page:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="➡️", callback_data=f"{callback_prefix}:view_cards:{page+1}"
                )
            )

        # Always add navigation buttons in a single row
        if nav_buttons:
            keyboard_rows.append(nav_buttons)

        # Simplified utility actions - removed filter buttons as requested
        utility_row = [
            InlineKeyboardButton(text="🔍 Search", callback_data="catalog:search"),
            InlineKeyboardButton(text="🛒 Cart", callback_data="local:cart:view"),
        ]
        keyboard_rows.append(utility_row)

        # Enhanced navigation back with context
        keyboard_rows.append(
            [InlineKeyboardButton(text="⬅️ Back to Browse", callback_data="menu:browse")]
        )

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    def create_responsive_card_keyboard(
        self,
        cards: List[Dict[str, Any]],
        page: int = 1,
        total_count: int = None,
        has_filters: bool = False,
        callback_prefix: str = "catalog",
        device_type: str = "mobile",
    ) -> InlineKeyboardMarkup:
        """
        Create a responsive keyboard optimized for different device types

        Args:
            cards: List of cards being displayed
            page: Current page number
            total_count: Total number of cards
            has_filters: Whether filters are active
            callback_prefix: Prefix for callback data
            device_type: "mobile", "tablet", or "desktop"

        Returns:
            InlineKeyboardMarkup: Responsive keyboard optimized for device
        """
        keyboard_rows: List[List[InlineKeyboardButton]] = []
        settings = self.responsive_settings.get(
            device_type, self.responsive_settings["mobile"]
        )
        buttons_per_row = settings["buttons_per_row"]

        # Remove quick actions as requested (Add All, Refresh buttons)

        # Enhanced responsive card buttons with comprehensive information
        if cards:
            card_buttons = []
            max_cards = 3 if device_type == "mobile" else 6

            for i, card in enumerate(cards[:max_cards], 1):
                card_id = card.get("_id")

                # Use enhanced button text for all device types
                if device_type == "mobile":
                    # Mobile: Use slightly more compact format
                    button_text = self._create_compact_card_button_text(i, card)
                else:
                    # Tablet/Desktop: Use full enhanced format
                    button_text = self._create_enhanced_card_button_text(i, card)

                card_buttons.append(
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=f"{callback_prefix}:add_to_cart:{card_id}",
                    )
                )

            # Organize card buttons responsively
            for i in range(0, len(card_buttons), buttons_per_row):
                keyboard_rows.append(card_buttons[i : i + buttons_per_row])

        # Responsive navigation
        self._add_responsive_navigation(
            keyboard_rows, page, total_count, callback_prefix, device_type
        )

        # Responsive action buttons
        self._add_responsive_actions(keyboard_rows, has_filters, device_type)

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    def _add_responsive_navigation(
        self,
        keyboard_rows: List[List[InlineKeyboardButton]],
        page: int,
        total_count: int,
        callback_prefix: str,
        device_type: str,
    ) -> None:
        """Add responsive navigation buttons"""
        nav_buttons = []
        max_page = (total_count + 4) // 5 if total_count else 1

        # Unified compact navigation for all device types
        if page > 1:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="⬅️", callback_data=f"{callback_prefix}:view_cards:{page-1}"
                )
            )

        nav_buttons.append(
            InlineKeyboardButton(text=f"{page}/{max_page}", callback_data="noop")
        )

        if page < max_page:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="➡️", callback_data=f"{callback_prefix}:view_cards:{page+1}"
                )
            )

        if nav_buttons:
            # Split navigation responsively
            if device_type == "mobile" or len(nav_buttons) <= 3:
                keyboard_rows.append(nav_buttons)
            else:
                # Split into multiple rows for better mobile experience
                keyboard_rows.append(nav_buttons[:3])
                if len(nav_buttons) > 3:
                    keyboard_rows.append(nav_buttons[3:])

    def _add_responsive_actions(
        self,
        keyboard_rows: List[List[InlineKeyboardButton]],
        has_filters: bool,
        device_type: str,
    ) -> None:
        """Add responsive action buttons - removed filter buttons as requested"""
        # Simplified actions for all device types - no filter buttons
        action_buttons = [
            InlineKeyboardButton(text="🔍 Search", callback_data="catalog:search"),
            InlineKeyboardButton(text="🛒 Cart", callback_data="local:cart:view"),
        ]

        keyboard_rows.append(action_buttons)

        # Back button
        keyboard_rows.append(
            [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:browse")]
        )

    def _format_cards_efficiently(self, cards: List[Dict[str, Any]]) -> List[str]:
        """Format cards using the enhanced display with centered price and timeout protection."""
        import time
        start_time = time.time()
        card_blocks = []
        
        # Limit cards to prevent hanging
        max_cards = min(len(cards), 6)
        
        for i, card in enumerate(cards[:max_cards], 1):
            # Timeout protection - if formatting takes more than 10 seconds total, stop
            if time.time() - start_time > 10:
                logger.warning(f"Card formatting timeout after {i-1} cards, stopping to prevent hang")
                break
                
            try:
                # Skip empty or invalid cards
                if not card or not isinstance(card, dict):
                    logger.debug(f"Skipping invalid card {i}: {type(card)}")
                    continue
                
                # Use the same formatting as individual card display with centered price
                card_info = self.format_compact_card(card, index=i, device_type="mobile")
                if card_info and card_info.strip():
                    card_blocks.append(card_info)
                else:
                    logger.debug(f"Empty result for card {i}, skipping")
            except Exception as e:
                logger.warning(f"Error formatting card {i}: {e}")
                # Add a minimal fallback card instead of skipping entirely
                try:
                    bin_val = card.get('bin', 'N/A')
                    fallback = f"<b>{i}.</b> ◆ BIN: <b>{bin_val}</b>\n❌ <i>Card formatting error</i>"
                    card_blocks.append(fallback)
                except:
                    logger.error(f"Critical error with card {i}, skipping completely")
                continue
        
        logger.debug(f"Formatted {len(card_blocks)} cards in {time.time() - start_time:.2f} seconds")
        return card_blocks
    
    def _build_card_info_fast(self, card: Dict[str, Any], index: int) -> str:
        """Build card information with optimized field extraction."""
        parts = [f"<b>{index}.</b> ◆ BIN: <b>{self._get_bin_fast(card)}</b> • EXP: <b>{self._get_expiry_fast(card)}</b>"]
        
        # Bank info with type and brand on same line
        bank_line = self._get_bank_type_brand_line(card)
        if bank_line:
            parts.append(bank_line)
        
        # Cardholder name and location info - only show if available
        name = self._get_name_fast(card)
        location_info = self._get_location_info_with_flag(card)
        
        # Build line with available info only - no N/A placeholders
        if name and location_info:
            # Both name and location available
            import html
            parts.append(f"👤 {html.escape(name)} • {location_info}")
        elif name:
            # Only name available
            import html
            parts.append(f"👤 {html.escape(name)}")
        elif location_info:
            # Only location available (already has country flag)
            parts.append(location_info)
        
        # Additional info (phone only, price handled separately)
        phone_info = self._get_phone_info_fast(card)
        if phone_info:
            parts.append(phone_info)
        
        # Add centered price at the end
        price_display = self._get_centered_price_fast(card)
        if price_display:
            parts.append(price_display)
        
        return "\n".join(parts)
    
    def _get_bin_fast(self, card: Dict[str, Any]) -> str:
        """Fast BIN extraction."""
        import html
        bin_val = card.get('bin') or card.get('card_number')
        if bin_val and str(bin_val).strip() and str(bin_val).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            return html.escape(str(bin_val).strip())
        return 'N/A'
    
    def _get_expiry_fast(self, card: Dict[str, Any]) -> str:
        """Fast expiry extraction."""
        import html
        exp_val = card.get('expiry') or card.get('exp')
        if exp_val and str(exp_val).strip() and str(exp_val).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            return html.escape(str(exp_val).strip())
        return 'N/A'
    
    def _get_bank_type_brand_line(self, card: Dict[str, Any]) -> str:
        """Get bank on one line, then brand, type, and level on next line."""
        bank = card.get('bank')
        type_val = card.get('type', '')
        brand = card.get('brand') or card.get('scheme', '')
        level = card.get('level', '')
        
        lines = []
        
        # Bank on its own line - only if not N/A or Unknown
        if bank and str(bank).strip() and str(bank).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            import html
            lines.append(f"🏛️ {html.escape(str(bank).strip().upper())}")
        
        # Brand, type, and level on next line - only show if not N/A or Unknown
        brand_type_level_parts = []
        if brand and str(brand).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            import html
            brand_type_level_parts.append(f"💳 {html.escape(brand)}")
        
        if type_val and str(type_val).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            import html
            brand_type_level_parts.append(f"💳 {html.escape(type_val).upper()}")
        
        if level and str(level).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
            import html
            brand_type_level_parts.append(f"⭐️ {html.escape(level).upper()}")
        
        if brand_type_level_parts:
            lines.append(" • ".join(brand_type_level_parts))
        
        return "\n".join(lines) if lines else ""
    
    def _get_card_type_fast(self, card: Dict[str, Any]) -> str:
        """Fast card type info extraction.""" 
        type_val = card.get('type', '')
        level = card.get('level', '')
        brand = card.get('brand') or card.get('scheme', '')
        
        # Format: Bank • Type • Brand (matching expected UI format)
        type_parts = []
        
        if type_val:
            type_parts.append(f"💳 {type_val}")
        
        if brand:
            type_parts.append(f"🏷️ {brand}")
        
        return " • ".join(type_parts) if type_parts else ""
    
    def _get_name_fast(self, card: Dict[str, Any]) -> str:
        """Fast name extraction with validation."""
        for field in ['cardholder_name', 'name', 'cardholder']:
            name = card.get(field)
            if name and str(name).strip():
                name_str = str(name).strip()
                if len(name_str) > 1 and name_str.upper() not in ['UNKNOWN', 'N/A']:
                    # Add Left-to-Right mark (U+200E) to force LTR display for special characters
                    return f"\u200E{name_str}"
        return ""
    
    def _get_location_info_with_flag(self, card: Dict[str, Any]) -> str:
        """Build complete address - supports both API v1 (city/state/zip) and API v3 (address string)."""
        import html
        
        country = card.get('country')
        
        # Check for direct address field (API v3 style - address as string)
        address_field = card.get('address')
        address_str = None
        
        # If address is a string (API v3), use it directly
        if address_field and isinstance(address_field, str) and str(address_field).strip() and str(address_field).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NONE']:
            address_str = str(address_field).strip()
        else:
            # Otherwise, build from city/state/zip (API v1 style)
            city = card.get('city')
            state = card.get('state') 
            zip_code = card.get('zip')
            
            address_parts = []
            
            # Add city if available
            if city and str(city).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
                address_parts.append(str(city).strip().title())
            
            # Add state if available
            if state and str(state).strip().upper() not in ['N/A', 'UNKNOWN', 'NONE']:
                address_parts.append(str(state).strip().upper())
            
            # Add zip if available
            if zip_code and str(zip_code).strip() not in ['N/A', 'UNKNOWN', 'NONE', '0']:
                address_parts.append(str(zip_code).strip())
            
            if address_parts:
                address_str = ", ".join(address_parts)
        
        # Build display with country flag
        if address_str and country and str(country).strip():
            country_str = str(country).strip()
            if ',' in country_str:
                country_str = country_str.split(',')[0].strip()
            flag = self._get_country_flag_emoji(country_str)
            return f"{flag} \u200E{html.escape(address_str)}"
        elif country and str(country).strip():
            # Only country available
            country_str = str(country).strip()
            if ',' in country_str:
                country_str = country_str.split(',')[0].strip()
            flag = self._get_country_flag_emoji(country_str)
            return f"{flag} \u200E{html.escape(country_str)}"
        
        return ""
    
    def _get_country_fast(self, card: Dict[str, Any]) -> str:
        """Fast country extraction with flag."""
        country = card.get('country')
        if country and str(country).strip():
            country_str = str(country).strip()
            flag = self._get_country_flag_emoji(country_str)
            return f"{flag} {country_str}"
        return "🌍 Unknown"
    
    def _get_phone_info_fast(self, card: Dict[str, Any]) -> str:
        """Fast phone/email/IP/DOB/expiring info extraction - supports both API v1 (flags) and API v3 (strings)."""
        included_items = []
        
        # === PHONE ===
        phone = card.get('phone')
        if phone and isinstance(phone, str) and str(phone).strip() and str(phone).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NO', '']:
            # Has actual phone value (API v3)
            phone_str = str(phone).strip()
            if len(phone_str) > 15:
                phone_str = phone_str[:12] + "..."
            import html
            included_items.append(f"📱 {html.escape(phone_str)}")
        elif phone == 1 or phone == '1' or phone is True:
            # Boolean flag (API v1)
            included_items.append("📱")
        
        # === EMAIL ===
        email = card.get('email')
        if email and isinstance(email, str) and str(email).strip() and str(email).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NO', '']:
            # Has actual email value (API v3)
            email_str = str(email).strip()
            if len(email_str) > 25:
                email_str = email_str[:22] + "..."
            import html
            included_items.append(f"📧 {html.escape(email_str)}")
        elif email == 1 or email == '1' or email is True:
            # Boolean flag (API v1)
            included_items.append("📧")
        
        # === IP ===
        ip = card.get('ip')
        if ip and isinstance(ip, str) and str(ip).strip() and str(ip).strip() not in ['0', '1', 'N/A', 'UNKNOWN', 'NO', '']:
            # Has actual IP value (API v3)
            import html
            included_items.append(f"🌐 {html.escape(str(ip).strip())}")
        elif ip == 1 or ip == '1' or ip is True:
            # Boolean flag (API v1)
            included_items.append("🌐")
        
        # === DOB ===
        dob = card.get('dob')
        if dob and str(dob).strip().upper() in ['YES', 'TRUE', 'AVAILABLE', '1']:
            included_items.append("🎂")
        
        # === EXPIRING SOON ===
        expiring_soon = card.get('expiring_soon')
        if expiring_soon is True or str(expiring_soon).upper() in ['TRUE', 'YES', '1']:
            included_items.append("⏰")
        
        # === REFUNDABLE ===
        refundable = card.get('refundable')
        if refundable is True or str(refundable).upper() in ['TRUE', 'YES', '1']:
            included_items.append("♻️")
        
        return " • ".join(included_items) if included_items else ""
    
    def _get_centered_price_fast(self, card: Dict[str, Any]) -> str:
        """Get centered price display with discount support (API v3 compatible)."""
        # Extract API v3 price fields
        original_price = card.get('original_price')
        current_price = card.get('current_price')
        discount_pct = card.get('discount_percentage', 0)
        
        try:
            orig = float(original_price) if original_price is not None else None
            curr = float(current_price) if current_price is not None else None
            disc = float(discount_pct) if discount_pct else 0
        except (ValueError, TypeError):
            orig = curr = None
            disc = 0
        
        # Track if this is a discounted price for padding logic
        has_discount = False
        
        # Case 1: Both original and current prices (API v3 format)
        if orig is not None and curr is not None and orig > curr:
            has_discount = True
            if disc <= 0:
                disc = ((orig - curr) / orig) * 100
            if curr == 0:
                price_display = "   🎁 <b>FREE</b>"
            else:
                price_display = f"   💰 <s>${orig:.2f}</s> → <b>${curr:.2f}</b> <i>({disc:.0f}% OFF)</i>"
        # Case 2: Fallback to single price
        else:
            for field in ['current_price', 'price', 'original_price']:
                price = card.get(field)
                if price is not None:
                    try:
                        price_val = float(price)
                        if price_val == 0:
                            price_display = "💰 <b>FREE</b>"
                        else:
                            price_display = f"💰 <b>${price_val:.2f}</b>"
                        break
                    except (ValueError, TypeError):
                        continue
            else:
                return ""
        
        # Add refundable status
        refundable = card.get('refundable')
        if refundable is not None and (refundable == 1 or refundable == '1' or str(refundable).lower() in ['true', 'yes']):
            price_display += " 🔄"
        
        # Create centered display
        # Use 4 spaces padding for discounted prices (longer text), 17 spaces for regular prices
        separator = "━━━━━━━━━━━━━━━━━━━━━━━"
        padding = "    " if has_discount else "                 "
        return f"<code>{separator}</code>\n{padding}{price_display}\n<code>{separator}</code>"
    
    def _get_additional_info_fast(self, card: Dict[str, Any]) -> str:
        """Fast additional info extraction."""
        info_parts = []
        
        # Phone indicator
        if card.get('phone') or card.get('phone_available'):
            info_parts.append("📱 Phone included")
        
        # Price
        price = self._get_price_fast(card)
        if price:
            info_parts.append(price)
        
        return "...".join(info_parts) if info_parts else ""
    
    def _get_price_fast(self, card: Dict[str, Any]) -> str:
        """Fast price extraction with discount support (API v3 compatible)."""
        # Extract API v3 price fields
        original_price = card.get('original_price')
        current_price = card.get('current_price')
        discount_pct = card.get('discount_percentage', 0)
        
        try:
            orig = float(original_price) if original_price is not None else None
            curr = float(current_price) if current_price is not None else None
            disc = float(discount_pct) if discount_pct else 0
        except (ValueError, TypeError):
            orig = curr = None
            disc = 0
        
        # Case 1: Both original and current prices (API v3 format)
        if orig is not None and curr is not None and orig > curr:
            if disc <= 0:
                disc = ((orig - curr) / orig) * 100
            if curr == 0:
                return "💰 <b>FREE</b>"
            elif curr < 10:
                return f"💰 <s>${orig:.2f}</s> → <b>${curr:.2f}</b> <i>({disc:.0f}% OFF)</i>"
            else:
                return f"💰 <s>${orig:.2f}</s> → <b>${curr:.2f}</b> <i>(Premium, {disc:.0f}% OFF)</i>"
        
        # Case 2: Fallback to single price
        for field in ['current_price', 'price', 'original_price']:
            price = card.get(field)
            if price is not None:
                try:
                    price_val = float(price)
                    if price_val == 0:
                        return "💰 <b>FREE</b>"
                    elif price_val < 10:
                        return f"💰 <b>${price_val:.2f}</b> <i>(Standard)</i>"
                    else:
                        return f"💰 <b>${price_val:.2f}</b> <i>(Premium)</i>"
                except (ValueError, TypeError):
                    continue
        return ""
    
    def _get_simple_flag(self, country: str) -> str:
        """Simple flag lookup for common countries."""
        flag_map = {
            'UNITED STATES': '🇺🇸', 'USA': '🇺🇸', 'US': '🇺🇸',
            'CANADA': '🇨🇦', 'CA': '🇨🇦',
            'UNITED KINGDOM': '🇬🇧', 'UK': '🇬🇧', 'GB': '🇬🇧',
            'GERMANY': '🇩🇪', 'DE': '🇩🇪',
            'FRANCE': '🇫🇷', 'FR': '🇫🇷',
            'ITALY': '🇮🇹', 'IT': '🇮🇹',
            'SPAIN': '🇪🇸', 'ES': '🇪🇸',
            'JAPAN': '🇯🇵', 'JP': '🇯🇵',
            'AUSTRALIA': '🇦🇺', 'AU': '🇦🇺',
            'BRAZIL': '🇧🇷', 'BR': '🇧🇷',
            'INDIA': '🇮🇳', 'IN': '🇮🇳',
            'CHINA': '🇨🇳', 'CN': '🇨🇳',
            'CZECH REPUBLIC': '🇨🇿', 'CZ': '🇨🇿',
            'ISRAEL': '🇮🇱', 'IL': '🇮🇱'
        }
        return flag_map.get(country.upper(), '🌍')


class ProductCollectionManager:
    """Manages collections of products with enhanced display capabilities"""

    def __init__(self):
        self.formatter = ProductDisplayFormatter()
        self.collection_cache: Dict[str, List[Dict[str, Any]]] = {}

    def organize_products_by_category(
        self, products: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Organize products by category for better display

        Args:
            products: List of product data

        Returns:
            Dict: Products organized by category
        """
        categories = {}

        for product in products:
            # Determine category based on product attributes
            category = self._determine_product_category(product)

            if category not in categories:
                categories[category] = []

            categories[category].append(product)

        return categories

    def _determine_product_category(self, product: Dict[str, Any]) -> str:
        """Determine the category for a product"""
        # Priority-based categorization
        if product.get("brand"):
            return f"{product['brand']} Cards"
        elif product.get("country"):
            return f"{product['country']} Cards"
        elif product.get("type"):
            return f"{product['type']} Cards"
        else:
            return "Other Cards"

    def create_category_summary(
        self, categories: Dict[str, List[Dict[str, Any]]]
    ) -> str:
        """
        Create a summary of product categories

        Args:
            categories: Products organized by category

        Returns:
            str: Formatted category summary
        """
        if not categories:
            return "📭 <b>No Products Available</b>"

        summary_parts = ["📊 <b>Product Categories</b>"]

        total_products = sum(len(products) for products in categories.values())
        summary_parts.append(f"Total Products: <b>{total_products:,}</b>")
        summary_parts.append("")

        # Sort categories by product count (descending)
        sorted_categories = sorted(
            categories.items(), key=lambda x: len(x[1]), reverse=True
        )

        for category, products in sorted_categories:
            count = len(products)
            # Calculate price range if available
            prices = [
                float(p.get("price", 0)) for p in products if p.get("price") is not None
            ]

            category_line = f"• <b>{category}</b>: {count:,} items"

            if prices:
                min_price = min(prices)
                max_price = max(prices)
                if min_price == max_price:
                    category_line += f" (${min_price:.2f})"
                else:
                    category_line += f" (${min_price:.2f} - ${max_price:.2f})"

            summary_parts.append(category_line)

        return "\n".join(summary_parts)


# Global instances for easy import
product_formatter = ProductDisplayFormatter()
collection_manager = ProductCollectionManager()
