"""
Data Display Utilities

Provides reusable functions for filtering, formatting, and displaying API response data
with proper handling of null/empty values and intelligent grouping.
"""

from __future__ import annotations

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from utils.central_logger import get_logger

logger = get_logger()


def is_valid_value(value: Any) -> bool:
    """
    Check if a value is valid for display (not null, empty, or blank).
    
    Args:
        value: The value to check
        
    Returns:
        True if value should be displayed, False otherwise
    """
    if value is None:
        return False
    if value == "":
        return False
    if isinstance(value, str):
        if value.strip() == "":
            return False
        if value.lower() in ["null", "none", "n/a", "na", "undefined"]:
            return False
    return True


def filter_dict(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter a dictionary to remove null/empty values.
    
    Args:
        data: Dictionary to filter
        
    Returns:
        Filtered dictionary with only valid values
    """
    return {k: v for k, v in data.items() if is_valid_value(v)}


def format_sensitive_data(value: Any, mask_type: str = "default") -> str:
    """
    Format sensitive data with appropriate masking.
    
    Args:
        value: The value to format
        mask_type: Type of masking to apply (card, cvv, email, phone, ssn, default)
        
    Returns:
        Formatted string with appropriate masking
    """
    if not is_valid_value(value):
        return "N/A"
    
    value_str = str(value)
    
    if mask_type == "card" and len(value_str) >= 8:
        # Show first 4 and last 4 digits
        return f"{value_str[:4]} **** **** {value_str[-4:]}"
    elif mask_type == "cvv":
        return "***"
    elif mask_type == "email" and "@" in value_str:
        local, domain = value_str.split("@", 1)
        if len(local) > 2:
            return f"{local[:2]}***@{domain}"
        return f"{local[0]}***@{domain}"
    elif mask_type == "phone" and len(value_str) >= 6:
        # Remove non-digits for masking
        digits = "".join(filter(str.isdigit, value_str))
        if len(digits) >= 6:
            return f"{digits[:3]}***{digits[-2:]}"
        return "***"
    elif mask_type == "ssn" and len(value_str) >= 4:
        return f"***-**-{value_str[-4:]}"
    elif mask_type == "default":
        # Generic masking - show first and last character
        if len(value_str) > 2:
            return f"{value_str[0]}***{value_str[-1]}"
        return "***"
    
    return "***"


def format_date(date_value: Any) -> str:
    """
    Format date value for display.
    
    Args:
        date_value: Date string or datetime object
        
    Returns:
        Formatted date string
    """
    if not is_valid_value(date_value):
        return "N/A"
    
    try:
        if isinstance(date_value, datetime):
            return date_value.strftime("%Y-%m-%d %H:%M UTC")
        
        date_str = str(date_value)
        if "T" in date_str:
            # ISO format
            dt = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
            return dt.strftime("%Y-%m-%d %H:%M UTC")
        
        return date_str
    except Exception as e:
        logger.warning(f"Error formatting date {date_value}: {e}")
        return str(date_value)


def format_currency(amount: Any, currency: str = "$") -> str:
    """
    Format currency value for display.
    
    Args:
        amount: Amount to format
        currency: Currency symbol
        
    Returns:
        Formatted currency string
    """
    if not is_valid_value(amount):
        return "N/A"
    
    try:
        amount_float = float(amount)
        return f"{currency}{amount_float:.2f}"
    except (ValueError, TypeError):
        return f"{str(amount)}"


def format_boolean(value: Any, true_text: str = "Yes", false_text: str = "No") -> str:
    """
    Format boolean value for display.
    
    Args:
        value: Boolean value
        true_text: Text to display for True
        false_text: Text to display for False
        
    Returns:
        Formatted boolean string
    """
    if not is_valid_value(value):
        return "N/A"
    
    if isinstance(value, bool):
        return true_text if value else false_text
    
    # Handle numeric boolean (0/1)
    if isinstance(value, (int, float)):
        return true_text if value else false_text
    
    # Handle string boolean
    value_str = str(value).lower()
    if value_str in ["true", "yes", "1", "y"]:
        return true_text
    elif value_str in ["false", "no", "0", "n"]:
        return false_text
    
    return str(value)


class DataSection:
    """Represents a section of data for display"""
    
    def __init__(self, title: str, icon: str = "📋"):
        self.title = title
        self.icon = icon
        self.fields: List[Tuple[str, str, str]] = []  # (label, value, emoji)
    
    def add_field(self, label: str, value: Any, emoji: str = "•", formatter: Optional[callable] = None):
        """Add a field to the section"""
        if not is_valid_value(value):
            return
        
        if formatter:
            formatted_value = formatter(value)
        else:
            formatted_value = str(value)
        
        self.fields.append((label, formatted_value, emoji))
    
    def has_fields(self) -> bool:
        """Check if section has any fields"""
        return len(self.fields) > 0
    
    def render(self) -> str:
        """Render the section as formatted text"""
        if not self.has_fields():
            return ""
        
        lines = [
            f"{self.icon} <b>{self.title}</b>",
            "─" * 25
        ]
        
        for label, value, emoji in self.fields:
            # Ensure proper HTML escaping and consistent left alignment
            # Escape HTML special characters in the value to prevent parsing issues
            import html
            escaped_value = html.escape(str(value))
            lines.append(f"{emoji} <b>{label}:</b> {escaped_value}")
        
        lines.append("")  # Empty line after section
        return "\n".join(lines)


def create_card_display(card_data: Dict[str, Any], mask_sensitive: bool = False) -> str:
    """
    Create a comprehensive card data display with intelligent grouping.

    Args:
        card_data: Card data dictionary from API
        mask_sensitive: If True, mask sensitive data (card number, CVV, etc.).
                       If False, show raw unmasked values (default now).

    Returns:
        Formatted card display string
    """
    sections = []

    # === CARD INFORMATION SECTION ===
    # Bank on one line
    bank = card_data.get("bank")
    if bank:
        sections.append(f"🏛️ <b>{bank}</b>")
    
    # Brand, type, and level on next line
    brand_type_level_parts = []
    brand = card_data.get("brand")
    if brand:
        brand_type_level_parts.append(f"💳 <b>{brand}</b>")
    
    type_val = card_data.get("type")
    if type_val:
        brand_type_level_parts.append(f"📇 <b>{type_val}</b>")
    
    level = card_data.get("level")
    if level:
        brand_type_level_parts.append(f"🏷️ <b>{level}</b>")
    
    if brand_type_level_parts:
        sections.append(" • ".join(brand_type_level_parts))
    
    # Other card information on separate lines
    card_info = DataSection("CARD INFORMATION", "💳")
    card_info.add_field("Base", card_data.get("base"), "📊")
    if card_info.has_fields():
        sections.append(card_info)

    # === SENSITIVE CARD DATA SECTION ===
    # Only show this section if we're displaying unmasked data (State 2 & 3)
    if not mask_sensitive:
        card_data_section = DataSection("CARD DATA (UNMASKED)", "🔒")
        # Show raw values without masking
        card_data_section.add_field("Card Number", card_data.get("cc"), "💳")
        card_data_section.add_field("Expiry", card_data.get("exp"), "📅")
        card_data_section.add_field("CVV", card_data.get("cvv"), "🔐")
        card_data_section.add_field("Exp Month", card_data.get("expmonth"), "📆")
        card_data_section.add_field("Exp Year", card_data.get("expyear"), "📆")
        sections.append(card_data_section)
    else:
        # State 1: Show masked preview only
        card_data_section = DataSection("CARD DATA (MASKED)", "🔒")
        card_data_section.add_field("Card Number", card_data.get("cc"), "💳",
                                    lambda v: format_sensitive_data(v, "card"))
        card_data_section.add_field("Expiry", card_data.get("exp"), "📅")
        card_data_section.add_field("CVV", card_data.get("cvv"), "🔐",
                                    lambda v: format_sensitive_data(v, "cvv"))
        sections.append(card_data_section)
    
    # === CARDHOLDER INFORMATION SECTION ===
    # Only show full cardholder info if unmasked (State 2 & 3)
    if not mask_sensitive:
        cardholder_info = DataSection("CARDHOLDER INFORMATION (UNMASKED)", "👤")
        # Add Left-to-Right mark (U+200E) to name fields
        cardholder_info.add_field("Full Name", card_data.get("name"), "👤", 
                                 lambda v: f"\u200E{str(v)}" if v else "")
        cardholder_info.add_field("First Name", card_data.get("firstname"), "👤",
                                 lambda v: f"\u200E{str(v)}" if v else "")
        cardholder_info.add_field("Last Name", card_data.get("lastname"), "👤",
                                 lambda v: f"\u200E{str(v)}" if v else "")
        cardholder_info.add_field("Email", card_data.get("email"), "📧")
        cardholder_info.add_field("Phone", card_data.get("phone"), "📞")
        cardholder_info.add_field("Date of Birth", card_data.get("dob"), "🎂")
        cardholder_info.add_field("SSN", card_data.get("ssn"), "🆔")
        cardholder_info.add_field("Driver's License", card_data.get("dl"), "🪪")
        cardholder_info.add_field("Mother's Maiden Name", card_data.get("mmn"), "👪")
        sections.append(cardholder_info)
    else:
        # State 1: Show masked cardholder info
        cardholder_info = DataSection("CARDHOLDER INFORMATION (MASKED)", "👤")
        # Add Left-to-Right mark (U+200E) to name fields
        cardholder_info.add_field("Full Name", card_data.get("name"), "👤",
                                 lambda v: f"\u200E{str(v)}" if v else "")
        cardholder_info.add_field("Email", card_data.get("email"), "📧",
                                 lambda v: format_sensitive_data(v, "email"))
        cardholder_info.add_field("Phone", card_data.get("phone"), "📞",
                                 lambda v: format_sensitive_data(v, "phone"))
        cardholder_info.add_field("SSN", card_data.get("ssn"), "🆔",
                                 lambda v: format_sensitive_data(v, "ssn"))
        sections.append(cardholder_info)
    
    # === LOCATION INFORMATION SECTION ===
    location_info = DataSection("LOCATION INFORMATION", "📍")
    location_info.add_field("Country", card_data.get("country"), "🌍")
    location_info.add_field("State", card_data.get("state"), "🗺️")
    location_info.add_field("City", card_data.get("city"), "🏙️")
    location_info.add_field("ZIP Code", card_data.get("zip"), "📮")
    # Add Left-to-Right mark (U+200E) to address field
    location_info.add_field("Address", card_data.get("address"), "🏠",
                           lambda v: f"\u200E{str(v)}" if v else "")
    location_info.add_field("IP Address", card_data.get("ip"), "🌐")
    sections.append(location_info)
    
    # === ORDER STATUS SECTION ===
    status_info = DataSection("ORDER STATUS", "📊")
    status_info.add_field("Status", card_data.get("status"), "📌")
    status_info.add_field("Price", card_data.get("price"), "💰", format_currency)
    status_info.add_field("Refundable", card_data.get("refundable"), "🔄", format_boolean)
    status_info.add_field("Can Check", card_data.get("canCheck"), "🔍", format_boolean)
    status_info.add_field("Is Viewed", card_data.get("isviewed"), "👁️", format_boolean)
    sections.append(status_info)
    
    # === TIMESTAMPS SECTION ===
    timestamps = DataSection("TIMESTAMPS", "🕐")
    timestamps.add_field("Created At", card_data.get("createdAt"), "📅", format_date)
    timestamps.add_field("Start Date", card_data.get("start_Date"), "🚀", format_date)
    timestamps.add_field("Viewed At", card_data.get("viewedAt"), "👁️", format_date)
    timestamps.add_field("Check Date", card_data.get("check_Date"), "🔍", format_date)
    timestamps.add_field("Checked At", card_data.get("checkedAt"), "✅", format_date)
    timestamps.add_field("Refund At", card_data.get("refundAt"), "🔄", format_date)
    sections.append(timestamps)
    
    # === TECHNICAL DETAILS SECTION ===
    technical = DataSection("TECHNICAL DETAILS", "⚙️")
    technical.add_field("Order ID", card_data.get("_id"), "🆔")
    technical.add_field("Product ID", card_data.get("product_id"), "📦")
    technical.add_field("User ID", card_data.get("user_id"), "👤")
    technical.add_field("Seller ID", card_data.get("seller_id"), "🏪")
    technical.add_field("User Agent", card_data.get("ua"), "🖥️")
    technical.add_field("Other Info", card_data.get("other"), "ℹ️")
    sections.append(technical)
    
    # Render all sections that have fields
    rendered_sections = [section.render() for section in sections if section.has_fields()]
    
    if not rendered_sections:
        return "❌ No data available to display"
    
    return "\n".join(rendered_sections)

