## API v1 Checkout Implementation - Complete Documentation

## Overview

The API v1 checkout functionality provides a complete end-to-end solution for processing cart purchases with database integration, including:

- ✅ Cart checkout via API
- ✅ Purchase record creation
- ✅ Wallet balance deduction
- ✅ Transaction logging
- ✅ Atomicity and error handling
- ✅ Rollback capabilities

## Architecture

```
┌──────────────────────────────────────────────────────────────┐
│                     CartService                              │
│                                                              │
│  checkout_cart(user_id, clear_cart_on_success)               │
│      │                                                       │
│      ├─> 1. Get cart items (view_cart)                       │
│      ├─> 2. Call API checkout endpoint                       │
│      └─> 3. Process database operations via                  │
│          CheckoutProcessorService                            │
└──────────────────────────────────────────────────────────────┘
                         │
                         ▼
┌──────────────────────────────────────────────────────────────┐
│             CheckoutProcessorService                         │
│                                                              │
│  process_checkout(user_id, cart_items, api_order_id)         │
│      │                                                       │
│      ├─> 1. Validate cart items                              │
│      ├─> 2. Calculate total price                            │
│      ├─> 3. Check wallet balance                             │
│      ├─> 4. Create purchase records                          │
│      ├─> 5. Deduct from wallet                               │
│      └─> 6. Log transaction                                  │
└──────────────────────────────────────────────────────────────┘
                         │
                         ▼
┌──────────────────────────────────────────────────────────────┐
│                  Database Collections                        │
│                                                              │
│  • purchases         - Purchase records                      │
│  • wallets           - User wallet balances                  │
│  • wallet_transactions - Transaction audit log               │
└──────────────────────────────────────────────────────────────┘
```

## Usage

### Basic Checkout

```python
from api_v1.services.cart_service import get_cart_service

# Initialize service
cart_service = get_cart_service()

# Process checkout
result = await cart_service.checkout_cart(
    user_id="user123",
    clear_cart_on_success=True
)

if result.success:
    print(f"Purchased {result.data['items_count']} items")
    print(f"Total: ${result.data['total_price']:.2f}")
    print(f"New balance: ${result.data['new_balance']:.2f}")
else:
    print(f"Checkout failed: {result.message}")
```

### With Error Handling

```python
from api_v1.services.cart_service import get_cart_service, CartServiceError
from api_v1.services.checkout_processor_service import CheckoutProcessorError

cart_service = get_cart_service()

try:
    result = await cart_service.checkout_cart(user_id="user123")
    
    if result.success:
        # Success - process result
        purchases = result.data['purchases']
        transaction_id = result.data['transaction_id']
        
    else:
        # Handle specific error codes
        if result.error_code == "EMPTY_CART":
            print("Cart is empty")
        elif result.error_code == "INSUFFICIENT_BALANCE":
            print("Insufficient balance")
        elif result.error_code == "API_CHECKOUT_FAILED":
            print("API checkout failed")
        else:
            print(f"Error: {result.message}")
            
except CheckoutProcessorError as e:
    print(f"Checkout processing error: {e.message}")
except CartServiceError as e:
    print(f"Cart service error: {e.message}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Checkout Flow

### Step-by-Step Process

1. **View Cart**
   - Retrieves current cart items before checkout
   - Validates cart is not empty

2. **Call API Checkout**
   - Makes GET request to `/api/cart/checkout`
   - Receives order confirmation from API
   - Extracts order ID if available

3. **Process Database Operations**
   - Creates purchase records for each item
   - Deducts total from user wallet
   - Logs transaction for audit

4. **Clear Cart** (optional)
   - Cart is cleared by API automatically
   - Local verification

### Database Operations

#### Purchase Records

Each cart item becomes a Purchase document:

```python
{
    "_id": ObjectId(),
    "user_id": "user123",
    "sku": "card_12345_1698076800",
    "price": 17.99,
    "currency": "USD",
    "status": "success",
    "api_version": "v1",
    "product_type": "card",
    "external_order_id": "order_789",
    "external_product_id": "12345",
    "metadata": {
        "brand": "VISA",
        "bin": "417903",
        "country": "US",
        "discount": 0,
        "original_price": 17.99
    },
    "order_items": [...],
    "created_at": datetime(),
    "updated_at": datetime()
}
```

#### Wallet Deduction

Wallet balance is updated atomically:

```python
# Before checkout
{
    "user_id": "user123",
    "balance": 100.00,
    "currency": "USD"
}

# After checkout (purchased $23.98 worth of items)
{
    "user_id": "user123",
    "balance": 76.02,
    "currency": "USD",
    "updated_at": datetime()
}
```

#### Transaction Log

Each checkout creates a transaction log:

```python
{
    "_id": ObjectId(),
    "user_id": "user123",
    "amount": 23.98,
    "old_balance": 100.00,
    "new_balance": 76.02,
    "transaction_type": "purchase",
    "reference": "order_789",
    "purchase_ids": ["purchase_id_1", "purchase_id_2"],
    "status": "completed",
    "created_at": datetime(),
    "metadata": {
        "items_count": 2,
        "api_version": "v1"
    }
}
```

## Response Format

### Success Response

```python
{
    "success": True,
    "message": "Checkout completed successfully! 2 items purchased",
    "data": {
        "purchases": [
            {
                "_id": "64abc123def...",
                "sku": "card_12345_1698076800",
                "price": 17.99,
                "product_type": "card",
                "external_product_id": "12345"
            }
        ],
        "total_price": 23.98,
        "new_balance": 76.02,
        "items_count": 2,
        "transaction_id": "64abc456def...",
        "api_order_id": "order_789",
        "api_response": [...]
    },
    "error_code": None,
    "timestamp": datetime()
}
```

### Error Response

```python
{
    "success": False,
    "message": "Insufficient balance: have $10.00, need $23.98",
    "data": None,
    "error_code": "INSUFFICIENT_BALANCE",
    "timestamp": datetime()
}
```

## Error Codes

| Error Code | Description | User Action |
|------------|-------------|-------------|
| `EMPTY_CART` | Cart has no items | Add items to cart |
| `INSUFFICIENT_BALANCE` | Wallet balance too low | Add funds to wallet |
| `WALLET_NOT_FOUND` | User wallet doesn't exist | Contact support |
| `API_CHECKOUT_FAILED` | External API checkout failed | Try again later |
| `CART_FETCH_FAILED` | Failed to retrieve cart | Check API connection |
| `NO_PURCHASES_CREATED` | No purchase records created | Contact support |
| `BALANCE_DEDUCTION_FAILED` | Wallet update failed | Contact support |
| `PROCESSING_ERROR` | General processing error | Contact support |

## Features

### Atomicity

The checkout processor ensures all operations succeed or fail together:

```python
async def _process_checkout_transaction(...):
    try:
        # All operations must succeed
        purchases = await self._create_purchase_records(...)
        new_balance = await self._deduct_wallet_balance(...)
        transaction_log = await self._log_wallet_transaction(...)
        
        return {"success": True, ...}
    except Exception:
        # If any operation fails, all are rolled back
        # (via database transaction or manual rollback)
        raise
```

### Rollback Capability

If checkout fails after some operations, rollback is available:

```python
# Rollback failed checkout
success = await checkout_processor.rollback_checkout(
    purchase_ids=["purchase1", "purchase2"],
    user_id="user123",
    refund_amount=23.98
)
```

This will:
1. Mark purchases as `FAILED`
2. Refund amount to wallet
3. Log refund transaction

### Price Calculation

Handles discounts automatically:

```python
# Item with 30% discount
base_price = 17.99
discount = 30
final_price = base_price * (1 - discount / 100)  # = 12.59
```

### Metadata Preservation

All cart item metadata is preserved in purchases:

- Brand, BIN, Country, State, City
- Exp date, Level, Bank
- Base, Discount, Original price
- Full order item data

## Integration with Bot

### Telegram Bot Handler

```python
from aiogram import Router, types
from api_v1.services.cart_service import get_cart_service

router = Router()
cart_service = get_cart_service()

@router.callback_query(lambda c: c.data == "checkout")
async def checkout_handler(callback: types.CallbackQuery, user_doc):
    user_id = str(user_doc.id)
    
    # Show processing message
    await callback.message.edit_text("🔄 Processing checkout...")
    
    try:
        result = await cart_service.checkout_cart(user_id=user_id)
        
        if result.success:
            success_text = (
                f"✅ Checkout Successful!\n\n"
                f"💳 Items purchased: {result.data['items_count']}\n"
                f"💵 Total paid: ${result.data['total_price']:.2f}\n"
                f"💰 New balance: ${result.data['new_balance']:.2f}\n"
                f"📝 Transaction: {result.data['transaction_id'][:8]}..."
            )
            await callback.message.edit_text(success_text)
        else:
            error_text = f"❌ Checkout Failed\n\n{result.message}"
            await callback.message.edit_text(error_text)
    
    except Exception as e:
        await callback.message.edit_text(f"❌ Error: {str(e)}")
```

## Performance

### Expected Response Times

- Cart retrieval: ~500-1000ms
- API checkout: ~1000-2000ms
- Database operations: ~100-300ms
- **Total checkout time: ~1.5-3.5 seconds**

### Optimization Tips

1. **Pre-validate cart** before showing checkout button
2. **Cache wallet balance** to avoid extra queries
3. **Use loading stages** UI for better UX
4. **Batch operations** when possible

## Testing

### Unit Test Example

```python
import pytest
from api_v1.services.cart_service import get_cart_service

@pytest.mark.asyncio
async def test_checkout_success():
    cart_service = get_cart_service()
    result = await cart_service.checkout_cart(user_id="test_user")
    
    assert result.success
    assert result.data['items_count'] > 0
    assert result.data['total_price'] > 0
    assert result.data['new_balance'] >= 0
    assert result.data['transaction_id']

@pytest.mark.asyncio
async def test_checkout_empty_cart():
    cart_service = get_cart_service()
    result = await cart_service.checkout_cart(user_id="empty_cart_user")
    
    assert not result.success
    assert result.error_code == "EMPTY_CART"
```

### Integration Test

```python
@pytest.mark.asyncio
async def test_complete_checkout_flow():
    cart_service = get_cart_service()
    user_id = "integration_test_user"
    
    # 1. View cart
    cart = await cart_service.view_cart(user_id=user_id)
    assert cart.success
    original_count = cart.data['item_count']
    
    # 2. Checkout
    checkout = await cart_service.checkout_cart(user_id=user_id)
    assert checkout.success
    
    # 3. Verify cart is empty
    cart_after = await cart_service.view_cart(user_id=user_id)
    assert cart_after.data['item_count'] == 0
    
    # 4. Verify purchases created
    assert len(checkout.data['purchases']) == original_count
```

## Monitoring

### Log Messages

The checkout process logs at various stages:

```
INFO: 🛒 Starting checkout for user user123
INFO: 📞 Calling API checkout endpoint
INFO: ✅ API checkout successful, processing database operations
INFO: 💾 Created 2 purchase records
INFO: 💳 Deducted $23.98 from wallet, new balance: $76.02
INFO: 📝 Logged transaction: 64abc456def...
INFO: 💾 Database operations completed successfully
INFO: 🗑️  Cart cleared (API-side)
INFO: ✅ Checkout completed successfully for user user123
```

### Error Logs

```
ERROR: ❌ Checkout processing failed: Insufficient balance
ERROR: ❌ API checkout request failed: Connection timeout
ERROR: ❌ Failed to create purchase for item 12345: Invalid price
```

## Troubleshooting

### Common Issues

#### 1. "Insufficient balance" but wallet has funds

**Cause:** Concurrent checkouts or wallet update delay

**Solution:**
- Implement optimistic locking (already included)
- Retry checkout
- Check for pending transactions

#### 2. Checkout succeeds but purchases not created

**Cause:** Database connection issue or validation failure

**Solution:**
- Check database logs
- Verify cart items have valid prices and product IDs
- Use rollback if needed

#### 3. Wallet deducted but API checkout failed

**Cause:** API timeout after wallet deduction

**Solution:**
- Automatic rollback should occur
- If not, manually refund via rollback_checkout()
- Check transaction logs

### Debug Mode

Enable detailed logging:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)

# Now all debug messages will be shown
result = await cart_service.checkout_cart(user_id="debug_user")
```

## Security Considerations

1. **Wallet Validation**
   - Balance check before deduction
   - Atomic operations prevent race conditions
   - Transaction logging for audit

2. **Purchase Integrity**
   - All metadata preserved
   - SKU generation prevents duplicates
   - External IDs tracked for reconciliation

3. **Error Handling**
   - Sensitive data not logged
   - Rollback on failure
   - Clear error messages for users

## Future Enhancements

Potential improvements:

- [ ] Support for payment methods other than wallet
- [ ] Partial checkout (checkout some items)
- [ ] Scheduled purchases
- [ ] Purchase history export
- [ ] Refund API integration
- [ ] Multi-currency support
- [ ] Discount code validation

## See Also

- [CART_ENDPOINTS.md](./CART_ENDPOINTS.md) - Cart and order endpoints
- [INTEGRATION_GUIDE.md](./INTEGRATION_GUIDE.md) - Integration examples
- [examples/checkout_example.py](./examples/checkout_example.py) - Working examples

## Support

For issues or questions:
- Check the logs for detailed error messages
- Review the examples in `examples/checkout_example.py`
- Contact the development team

