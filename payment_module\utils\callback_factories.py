"""
Callback factories for payment processing.

This module provides callback data classes for payment-related operations.
"""

from aiogram.filters.callback_data import CallbackData
from typing import Optional


class DepositCallback(CallbackData, prefix="deposit"):
    """Callback data for deposit operations"""
    action: str
    amount: Optional[float] = None
    invoice_id: Optional[str] = None


class PaymentVerificationCallback(CallbackData, prefix="payment"):
    """Callback data for payment verification operations"""
    action: str
    track_id: Optional[str] = None


class PaymentCallback(CallbackData, prefix="pay"):
    """Callback data for general payment operations"""
    action: str
    amount: Optional[float] = None
    invoice_id: Optional[str] = None


# Export all callback factories
__all__ = [
    "DepositCallback",
    "PaymentVerificationCallback", 
    "PaymentCallback",
]

