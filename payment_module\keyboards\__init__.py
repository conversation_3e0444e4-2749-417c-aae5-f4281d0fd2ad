"""
Telegram keyboards for payment processing

This module contains all keyboard functions for payment flows.
"""

# Import all keyboard functions
try:
    from .deposit_kb import (
        deposit_amount_keyboard,
        deposit_pay_keyboard,
        deposit_cancel_keyboard,
        custom_amount_cancel_keyboard,
        payment_verification_keyboard,
        payment_success_keyboard,
        payment_processing_keyboard,
    )
except ImportError as e:
    # Create placeholder functions
    def deposit_amount_keyboard(*args, **kwargs):
        raise NotImplementedError(f"deposit_kb not available: {e}")
    deposit_pay_keyboard = deposit_amount_keyboard
    deposit_cancel_keyboard = deposit_amount_keyboard
    custom_amount_cancel_keyboard = deposit_amount_keyboard
    payment_verification_keyboard = deposit_amount_keyboard
    payment_success_keyboard = deposit_amount_keyboard
    payment_processing_keyboard = deposit_amount_keyboard

# Export all functions
__all__ = [
    'deposit_amount_keyboard',
    'deposit_pay_keyboard',
    'deposit_cancel_keyboard',
    'custom_amount_cancel_keyboard',
    'payment_verification_keyboard',
    'payment_success_keyboard',
    'payment_processing_keyboard',
]
