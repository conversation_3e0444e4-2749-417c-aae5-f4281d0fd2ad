curl ^"https://ronaldo-club.to/api/cards/dumps/list?page=3^&^&limit=10^&base=^&bank=^&bin=^&country=^&state=^&city=^&brand=^&type=^&zip=^&priceFrom=0^&priceTo=25^&zipCheck=false^&track1=false^&track2=false^&pin=false^" ^
  -X ^"POST^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -H ^"content-length: 0^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg10_=**********; __ddg8_=CjNNhGtj1oLRoTBG^" ^
  -H ^"origin: https://ronaldo-club.to^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cards/dump?page=3^&limit=10^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/api/cart/^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg10_=1758375652; __ddg8_=lj7dsJJxleMDiz2n^" ^
  -H ^"origin: https://ronaldo-club.to^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cards/dump?page=3^&limit=10^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" ^
  --data-raw ^"^{^\^"id^\^":94226,^\^"product_table_name^\^":^\^"dumps^\^"^}^" &
curl ^"https://ronaldo-club.to/api/cart/^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg8_=0GOJC9URXUrwG8rk; __ddg10_=1758375718^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/api/cart/checkout^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg10_=1758376857; __ddg8_=urn6bvj7tUuVGV7v^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/api/user/getme^" ^
  -H ^"accept: */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg8_=KfMfxvzi1abC1VHp; __ddg10_=1758377034^" ^
  -H ^"if-none-match: W/^\^"181-p9NoI2rzKiD9Lj5ICfu2TaWftww^\^"^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/api/user/getme^" ^
  -H ^"accept: */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg8_=KfMfxvzi1abC1VHp; __ddg10_=1758377034^" ^
  -H ^"if-none-match: W/^\^"181-fOgWtZG0I/eP3blTbcFkZ/c1Q5A^\^"^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/store/cart^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/api/user/getme^" ^
  -H ^"accept: */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -H ^"content-type: application/json^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg8_=lnmvaKxlezVEPr6J; __ddg10_=1758377042^" ^
  -H ^"if-none-match: W/^\^"181-fOgWtZG0I/eP3blTbcFkZ/c1Q5A^\^"^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/orders/cards/hq^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/api/cards/hq/orders?page=1^&^&limit=10^" ^
  -H ^"accept: application/json, text/plain, */*^" ^
  -H ^"accept-language: en-US,en;q=0.6^" ^
  -b ^"__ddg9_=***************; __ddg1_=KLxMOdvoPXlAy48wvvYp; loginToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.iMkZPV98TwkDS1QL_F6cDP46Jqb-bOeZbqRKdDmRfUY; testcookie=1; __ddg10_=1758377042; __ddg8_=mVuDvegLQTJdIA9a^" ^
  -H ^"priority: u=1, i^" ^
  -H ^"referer: https://ronaldo-club.to/orders/cards/hq^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"sec-fetch-dest: empty^" ^
  -H ^"sec-fetch-mode: cors^" ^
  -H ^"sec-fetch-site: same-origin^" ^
  -H ^"sec-gpc: 1^" ^
  -H ^"user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" &
curl ^"https://ronaldo-club.to/_next/static/css/1c105322b5a325b7.css^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"Referer: https://ronaldo-club.to/orders/cards/hq^" ^
  -H ^"User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" &
curl ^"https://ronaldo-club.to/_next/static/css/a206e5ae95006453.css^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"Referer: https://ronaldo-club.to/orders/cards/hq^" ^
  -H ^"User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" &
curl ^"https://translate.googleapis.com/element/log?format=json^&hasfast=true^&authuser=0^" ^
  -H ^"sec-ch-ua-platform: ^\^"Android^\^"^" ^
  -H ^"Referer: https://ronaldo-club.to/^" ^
  -H ^"Content-Encoding: gzip^" ^
  -H ^"sec-ch-ua: ^\^"Chromium^\^";v=^\^"140^\^", ^\^"Not=A?Brand^\^";v=^\^"24^\^", ^\^"Brave^\^";v=^\^"140^\^"^" ^
  -H ^"sec-ch-ua-mobile: ?1^" ^
  -H ^"User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36^" ^
  -H ^"Content-Type: application/binary^" ^
  -H ^"X-Goog-AuthUser: 0^" ^
  --data-raw ^"^^^^