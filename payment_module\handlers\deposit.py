from aiogram import Router, F, Dispatcher
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    CallbackQuery,
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
from aiogram.filters import Command, CommandStart
from aiogram.filters.callback_data import CallbackData
from datetime import datetime
import logging
import asyncio
from typing import Optional, Dict, Any, Union

# Import the keyboards
from ..keyboards.deposit_kb import (
    deposit_amount_keyboard,
    deposit_pay_keyboard,
    deposit_cancel_keyboard,
    custom_amount_cancel_keyboard,
    payment_verification_keyboard,
    payment_success_keyboard,
    payment_processing_keyboard,
)

# Import the payment link function directly
from ..core.payment_link import create_payment_link

# Import utilities
from ..utils.payment_utils import safe_edit_message, clear_state_data, format_crypto_amount
from ..utils.template_helpers import format_text
from ..database.payment_operations import save_payment_details
from ..database.user_operations import get_user_balance, update_user_balance, add_transaction


# Create a structured callback data for payment handling
class PaymentCallback(CallbackData, prefix="pay"):
    action: str
    amount: float = None
    invoice_id: str = None


router = Router()
router.name = "deposit_router"


@router.message(Command("deposit"))
async def cmd_deposit(message: Message, state: FSMContext):
    """Handle /deposit command."""
    deposit_message = format_text(
        "payment", 
        "deposit_funds_message",
        default=(
            "💰 <b>━━ DEPOSIT FUNDS ━━</b>\n"
            "━━━━━━━━━━━━━━━━━━━━━━━\n"
            "Choose an amount to deposit:\n\n"
            "💳 <b>Available Options:</b>\n"
            "• Predefined amounts: $10, $20, $50, $100, $200, $500\n"
            "• Custom amount: Enter any amount between $10-$1000\n\n"
            "🔒 <b>Secure Payment Processing</b>\n"
            "• Cryptocurrency payments via OXA Pay\n"
            "• Instant balance updates\n"
            "• Secure transaction processing\n\n"
            "<i>Select an amount below to continue.</i>"
        )
    )

    await message.answer(
        deposit_message,
        reply_markup=deposit_amount_keyboard(),
        parse_mode="HTML",
    )


# Handler for the deposit funds button
@router.callback_query(F.data == "deposit_funds")
async def process_deposit_funds(callback_query: CallbackQuery, state: FSMContext):
    """Handle the deposit funds button click."""
    await callback_query.answer()

    deposit_text = (
        "💰 <b>━━ DEPOSIT FUNDS ━━</b>\n"
        "━━━━━━━━━━━━━━━━━━━━━━━\n"
        "Choose an amount to deposit:\n\n"
        "💳 <b>Available Options:</b>\n"
        "• Predefined amounts: $10, $20, $50, $100, $200, $500\n"
        "• Custom amount: Enter any amount between $10-$1000\n\n"
        "🔒 <b>Secure Payment Processing</b>\n"
        "• Cryptocurrency payments via OXA Pay\n"
        "• Instant balance updates\n"
        "• Secure transaction processing\n\n"
        "<i>Select an amount below to continue.</i>"
    )

    try:
        await callback_query.message.edit_text(
            deposit_text,
            reply_markup=deposit_amount_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        logging.error(f"Error editing message: {e}")
        # Fallback: send new message
        await callback_query.message.answer(
            deposit_text,
            reply_markup=deposit_amount_keyboard(),
            parse_mode="HTML",
        )


# Add handler for amount selection (for predefined amounts)
@router.callback_query(F.data.startswith("select_amount:"))
async def handle_amount_selection(callback_query: CallbackQuery, state: FSMContext):
    """Handle selection of a predefined amount."""
    await callback_query.answer("Amount selected")

    # Extract the selected amount from the callback data
    amount = float(callback_query.data.split(":")[1])

    # Store the selected amount in the state
    await state.update_data(deposit_amount=amount, amount=amount)

    confirm_deposit_message = (
        f"💰 <b>• CONFIRM DEPOSIT •</b>\n\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
        f"<b>PAYMENT DETAILS</b>\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"• <b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"• <b>Status:</b> Ready for payment\n\n"
        f"🚨 <b>IMPORTANT SECURITY NOTICE</b> 🚨\n"
        f"<b>Each payment requires a unique crypto address</b>\n"
        f"<b>Never reuse old payment links or addresses</b>\n\n"
        f"<i>Please confirm this amount to proceed with your deposit.</i>"
    )

    await callback_query.message.edit_text(
        confirm_deposit_message,
        reply_markup=deposit_pay_keyboard(amount),
        parse_mode="HTML",
    )


# Add a direct handler for pay_deposit button
@router.callback_query(F.data.startswith("pay_deposit:"))
async def process_payment(callback_query: CallbackQuery, state: FSMContext):
    """Process the payment request when user clicks Pay Now."""
    await callback_query.answer("Generating payment link...")

    # Extract amount from callback data
    amount = float(callback_query.data.split(":")[1])

    # Generate unique identifiers
    user_id = callback_query.from_user.id
    timestamp = int(datetime.now().timestamp())
    invoice_id = f"INV-{user_id}-{timestamp}"
    order_id = f"ORD-{user_id}-{timestamp}"

    # Store info in state
    await state.update_data(
        deposit_amount=amount, amount=amount, invoice_id=invoice_id, order_id=order_id
    )

    # First update the message to show processing state
    generating_payment_message = (
        "⏳ <b>• GENERATING PAYMENT •</b>\n\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
        f"<b>PROCESSING REQUEST</b>\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"• <b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"• <b>Status:</b> <i>Processing</i>\n\n"
        f"<i>Please wait while we securely generate your payment link.</i>"
    )

    await callback_query.message.edit_text(
        generating_payment_message,
        parse_mode="HTML",
    )

    try:
        # Create payment link asynchronously - include user_id for callback verification
        payment_data = await create_payment_link(
            amount=amount,
            order_id=order_id,
            user_id=user_id,
            description=f"Deposit for user {user_id}",
        )

        if payment_data.get("status") == "success":
            # Extract payment details
            payment_url = payment_data.get("payLink")
            track_id = payment_data.get("trackId")

            # Log payment creation
            logging.info(
                f"Created payment for user {user_id}: amount={amount}, track_id={track_id}"
            )

            # Store in state
            await state.update_data(payment_url=payment_url, track_id=track_id)

            # Create keyboard with payment link
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🌐 Pay Now",
                            url=payment_url,
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="✅ I've Completed Payment",
                            callback_data=f"verify_latest_payment",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="cancel_deposit"
                        )
                    ],
                ]
            )

            # Show payment details with crypto payment warning
            payment_instructions = (
                "💳 <b>• PAYMENT INSTRUCTIONS •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>TRANSACTION DETAILS</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"• <b>Amount:</b> <code>${amount:.2f}</code>\n"
                f"• <b>Order ID:</b> <code>{order_id}</code>\n"
                f"• <b>Tracking ID:</b> <code>{track_id}</code>\n\n"
                f"🚨 <b>CRITICAL WARNING - READ CAREFULLY</b> 🚨\n"
                f"<b>━━━━━━━━━━━━━━━━━━━━━</b>\n"
                f"• <b>NEVER reuse old cryptocurrency addresses</b>\n"
                f"• <b>ALWAYS generate a new payment link for each transaction</b>\n"
                f"• <b>Previous payment addresses become INVALID after use</b>\n"
                f"• <b>Using old addresses may result in LOST FUNDS</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"⚠️ <b>CRYPTOCURRENCY PAYMENT NOTICE</b>\n"
                f"• Bitcoin (BTC) payments require 2 network confirmations\n"
                f"• Other cryptocurrencies require at least 1 confirmation\n"
                f"• Balance updates may be delayed due to network activity\n\n"
                f"<i>Please complete your payment using the button below. Your balance will be updated after network confirmation.</i>"
            )

            await callback_query.message.edit_text(
                payment_instructions,
                reply_markup=keyboard,
                parse_mode="HTML",
            )

            # Also send a separate message with payment details that won't be edited
            payment_details = (
                "💳 <b>• PAYMENT DETAILS •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>TRANSACTION REFERENCE</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"• <b>Amount:</b> <code>${amount:.2f}</code>\n"
                f"• <b>Order ID:</b> <code>{order_id}</code>\n"
                f"• <b>Tracking ID:</b> <code>{track_id}</code>\n\n"
                f"<i>Please keep this information for your records.</i>"
            )

            await callback_query.message.answer(
                payment_details,
                parse_mode="HTML",
            )
        else:
            # Payment link creation failed
            error_msg = payment_data.get("message", "Unknown error")

            # Display error message to user
            payment_error = (
                "❌ <b>• PAYMENT ERROR •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>GENERATION FAILED</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"<b>Error:</b> {error_msg}\n\n"
                f"<i>We were unable to create your payment link. Please try again or select a different amount.</i>"
            )

            await callback_query.message.edit_text(
                payment_error,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔁 Try Again",
                                callback_data=f"select_amount:{amount}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="✖ Cancel", callback_data="cancel_deposit"
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )

    except Exception as e:
        logging.error(f"Error processing payment: {str(e)}", exc_info=True)

        # Display system error message
        system_error = (
            "⚠️ <b>• SYSTEM ERROR •</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>PROCESSING ISSUE</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<i>An error occurred while processing your payment request:</i>\n"
            f"{str(e)}\n\n"
            f"<i>Please try again later or contact support if this issue persists.</i>"
        )

        await callback_query.message.edit_text(
            system_error,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="Return to Deposit", callback_data="deposit_funds"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="Return to Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )


# Handler for custom amount selection
@router.callback_query(F.data == "custom_amount")
async def custom_amount_clicked(callback_query: CallbackQuery, state: FSMContext):
    """Handle the custom amount button click."""
    await callback_query.answer("Enter your preferred amount")

    try:
        # Get config values for min/max deposit
        min_deposit = 10.00
        max_deposit = 1000.00

        custom_deposit_message = (
            "💳 <b>━━ CUSTOM DEPOSIT ━━</b>\n"
            "━━━━━━━━━━━━━━━━━━━━━━━\n"
            "Please enter the amount you'd like to deposit:\n\n"
            f"• <b>Minimum:</b> ${min_deposit:.2f}\n"
            f"• <b>Maximum:</b> ${max_deposit:.2f}\n"
            "• Use numbers only (e.g., <code>100</code> or <code>49.99</code>)\n"
            "━━━━━━━━━━━━━━━━━━━━━━━\n"
            "<i>Type your desired amount or press 'Cancel' to return.</i>"
        )

        # Set state to waiting for amount
        await state.set_state("waiting_for_amount")

        await callback_query.message.edit_text(
            custom_deposit_message,
            reply_markup=custom_amount_cancel_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        logging.error(f"Error in custom_amount_clicked: {str(e)}")
        await callback_query.message.answer(
            f"Error: {str(e)}",
            parse_mode="HTML",
        )


# Handler for processing the custom amount
@router.message(lambda message: message.text and message.text.replace('.', '').replace('-', '').isdigit())
async def process_custom_amount(message: Message, state: FSMContext):
    """Process the custom amount entered by the user."""
    try:
        # Try to convert the input to a float
        amount = float(message.text.strip())

        # Validate the amount
        min_deposit = 10.00
        max_deposit = 1000.00

        if amount < min_deposit:
            amount_too_low = (
                "⚠️ <b>• AMOUNT TOO LOW •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>MINIMUM REQUIRED</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"The minimum deposit amount is <code>${min_deposit:.2f}</code>.\n\n"
                f"<i>Please enter a higher amount to continue.</i>"
            )

            await message.reply(
                amount_too_low,
                parse_mode="HTML",
            )
            return

        if amount > max_deposit:
            amount_too_high = (
                "⚠️ <b>• AMOUNT TOO HIGH •</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>MAXIMUM EXCEEDED</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"The maximum deposit amount is <code>${max_deposit:.2f}</code>.\n\n"
                f"<i>Please enter a lower amount to continue.</i>"
            )

            await message.reply(
                amount_too_high,
                parse_mode="HTML",
            )
            return

        # Store the amount in the state
        await state.update_data(deposit_amount=amount)

        # Use template for custom deposit confirmation
        custom_deposit_confirm = (
            "💰 <b>• CUSTOM DEPOSIT •</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>PAYMENT DETAILS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"• <b>Amount:</b> <code>${amount:.2f}</code>\n"
            f"• <b>Status:</b> Ready for payment\n\n"
            f"🚨 <b>IMPORTANT SECURITY NOTICE</b> 🚨\n"
            f"<b>Each payment requires a unique crypto address</b>\n"
            f"<b>Never reuse old payment links or addresses</b>\n\n"
            f"<i>Please confirm this amount to proceed with your deposit.</i>"
        )

        await message.reply(
            custom_deposit_confirm,
            reply_markup=deposit_pay_keyboard(amount),
            parse_mode="HTML",
        )

        # Clear the waiting_for_amount state
        await state.set_state(None)

    except ValueError:
        # Handle invalid input
        invalid_amount = (
            "❌ <b>• INVALID AMOUNT •</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>FORMAT ERROR</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"Please enter a valid number format:\n\n"
            f"• Correct examples: 50, 100.50\n"
            f"• Do not include currency symbols\n\n"
            f"<i>Try again or click 'Cancel' to exit.</i>"
        )

        await message.reply(
            invalid_amount,
            parse_mode="HTML",
        )


# Handler for cancel_deposit button
@router.callback_query(F.data == "cancel_deposit")
async def cancel_deposit(callback_query: CallbackQuery, state: FSMContext):
    """Cancel the deposit process."""
    await callback_query.answer("Deposit cancelled")

    # Clear any deposit-related state
    await state.clear()

    # Use template for deposit cancellation message
    deposit_cancelled = (
        "✅ <b>• DEPOSIT CANCELLED •</b>\n\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
        f"<b>PROCESS TERMINATED</b>\n"
        f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"<i>Your deposit process has been cancelled. No charges have been made to your account.</i>"
    )

    await callback_query.message.edit_text(
        deposit_cancelled,
        reply_markup=deposit_cancel_keyboard(),
        parse_mode="HTML",
    )


def register_deposit_handlers(dp: Dispatcher):
    """Register all deposit-related handlers."""
    try:
        dp.include_router(router)
        logging.info(f"Registered deposit handlers")
    except RuntimeError as e:
        logging.warning(f"Deposit router already attached, skipping registration: {e}")
    except Exception as e:
        logging.error(f"Failed to register deposit handlers: {e}")