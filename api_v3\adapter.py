"""
API v3 Adapter

Provides a unified interface for API v3 integration with the existing CardService.
Converts between standard filter formats and API v3 specific formats.
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional

from .services import (
    APIV3BrowseService,
    APIV3BrowseParams,
    APIV3BrowseResponse,
    get_api_v3_browse_service,
    APIV3CheckoutService,
)
from .config import get_api_v3_config_from_env

from utils.central_logger import get_logger

logger = get_logger()


class APIV3Adapter:
    """
    Adapter for API v3 integration with CardService.

    Provides a unified interface that converts between standard filter formats
    and API v3 specific formats, making API v3 compatible with existing handlers.
    """

    def __init__(
        self,
        browse_service: Optional[APIV3BrowseService] = None,
        account_id: Optional[str] = None,
    ):
        """
        Initialize the adapter.

        Args:
            browse_service: Optional browse service instance. If not provided,
                          will create one using get_api_v3_browse_service()
            account_id: Optional account ID for filter caching
        """
        self.browse_service = browse_service or get_api_v3_browse_service()
        self.account_id = account_id

        # Initialize checkout service with API v3 config
        try:
            config = get_api_v3_config_from_env()
            self.checkout_service = APIV3CheckoutService(
                base_url=config.base_url,
                username=config.username,
                password=config.password,
                use_socks_proxy=config.use_socks_proxy,
                socks_url=config.socks_url,
            )
        except Exception as e:
            logger.warning(f"Failed to initialize checkout service: {e}")
            self.checkout_service = None

        self.logger = get_logger()

    async def browse_cards(
        self,
        filters: Optional[Dict[str, Any]] = None,
        page: int = 1,
        limit: int = 50,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Browse cards using standard filter format.

        Args:
            filters: Standard filter dictionary (country, brand, type, etc.)
            page: Page number
            limit: Items per page
            user_id: User ID for logging

        Returns:
            Dictionary with success, data, and metadata in CardService format
        """
        try:
            self.logger.info(
                f"Browsing cards for user {user_id} with filters: {filters}"
            )

            # Convert standard filters to API v3 parameters
            params = self._convert_filters_to_params(filters or {}, page, limit)

            # Make request through browse service
            response = await self.browse_service.list_items(params, user_id)

            if response.success:
                # Return in CardService expected format
                return {
                    "success": True,
                    "data": response.data.get("data", []),
                    "totalCount": response.data.get("totalCount", 0),
                    "page": page,
                    "limit": limit,
                    "filters_applied": response.data.get("filters_applied", {}),
                }
            else:
                return {
                    "success": False,
                    "error": response.error or "Unknown error",
                    "data": [],
                }

        except Exception as e:
            self.logger.error(f"Error browsing cards: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "data": [],
            }

    async def get_filters(
        self,
        filter_name: Optional[str] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get available filter options.

        Args:
            filter_name: Specific filter name (not used in API v3)
            user_id: User ID for logging

        Returns:
            Dictionary with success and filters data
        """
        try:
            self.logger.info(f"Getting filters for user {user_id}")

            response = await self.browse_service.get_filters(
                user_id=user_id, account_id=self.account_id
            )

            if response.success:
                return {
                    "success": True,
                    "filters": response.data.get("filters", {}),
                }
            else:
                return {
                    "success": False,
                    "error": response.error or "Unknown error",
                    "filters": {},
                }

        except Exception as e:
            self.logger.error(f"Error getting filters: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "filters": {},
            }

    async def get_filter_options(
        self,
        filter_name: str,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get filter options for a specific filter.

        This method provides compatibility with CardService's filter system.
        """
        # For API v3, we get all filters at once
        all_filters = await self.get_filters(user_id=user_id)

        if not all_filters.get("success"):
            return all_filters

        filters_data = all_filters.get("filters", {})

        # Map filter names to API v3 filter keys
        filter_mapping = {
            "country": "countries",
            "countries": "countries",
            "continent": "continents",
            "continents": "continents",
            "brand": "brands",
            "brands": "brands",
            "scheme": "schemes",
            "schemes": "schemes",
            "type": "types",
            "types": "types",
            "level": "levels",
            "levels": "levels",
            "bank": "banks",
            "banks": "banks",
        }

        api_v3_key = filter_mapping.get(filter_name, filter_name)
        options = filters_data.get(api_v3_key, [])

        return {
            "success": True,
            "options": options,
            "filter_name": filter_name,
        }

    def _convert_filters_to_params(
        self,
        filters: Dict[str, Any],
        page: int = 1,
        limit: int = 50,
    ) -> APIV3BrowseParams:
        """
        Convert standard filter format to API v3 parameters.

        Args:
            filters: Standard filter dictionary
            page: Page number
            limit: Items per page

        Returns:
            APIV3BrowseParams instance
        """
        return APIV3BrowseParams.from_standard_filters(filters, page=page, limit=limit)

    async def close(self):
        """Close the adapter and underlying services."""
        if hasattr(self.browse_service, "close"):
            await self.browse_service.close()


def get_api_v3_adapter() -> APIV3Adapter:
    """
    Get a configured API v3 adapter instance.

    Returns:
        APIV3Adapter instance ready for use
    """
    return APIV3Adapter()
