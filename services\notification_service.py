"""
Notification service for sending user notifications and alerts
"""

from __future__ import annotations

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from aiogram import Bot
from aiogram.exceptions import TelegramBadRequest, TelegramForbiddenError

from database.connection import get_collection
from models import User, Transaction
from config.settings import get_settings
from utils.performance import monitor_performance

from utils.central_logger import get_logger

logger = get_logger()


class NotificationService:
    """Service for managing user notifications and alerts"""

    def __init__(self, bot: Optional[Bot] = None):
        self.bot = bot
        self.settings = get_settings()
        self.users_collection = get_collection("users")
        self.notifications_collection = get_collection("notifications")

    @monitor_performance("send_notification")
    async def send_notification(
        self,
        user_id: int,
        message: str,
        notification_type: str = "info",
        priority: str = "normal"
    ) -> bool:
        """
        Send notification to a user
        
        Args:
            user_id: Telegram user ID
            message: Notification message
            notification_type: Type of notification (info, warning, error, success)
            priority: Priority level (low, normal, high)
            
        Returns:
            True if notification was sent successfully
        """
        try:
            if not self.bot:
                logger.warning("Bot instance not available for notifications")
                return False

            # Add emoji based on notification type
            emoji_map = {
                "info": "ℹ️",
                "warning": "⚠️", 
                "error": "❌",
                "success": "✅",
                "alert": "🚨"
            }
            
            emoji = emoji_map.get(notification_type, "📢")
            formatted_message = f"{emoji} {message}"
            
            # Send notification
            await self.bot.send_message(user_id, formatted_message)
            
            # Log notification
            await self._log_notification(user_id, message, notification_type, priority, "sent")
            
            return True
            
        except (TelegramBadRequest, TelegramForbiddenError) as e:
            logger.warning(f"Failed to send notification to user {user_id}: {e}")
            await self._log_notification(user_id, message, notification_type, priority, "failed")
            return False
        except Exception as e:
            logger.error(f"Error sending notification to user {user_id}: {e}")
            return False

    async def send_transaction_alert(self, user_id: int, transaction: Transaction) -> bool:
        """
        Send transaction-specific alert to user
        
        Args:
            user_id: Telegram user ID
            transaction: Transaction object
            
        Returns:
            True if alert was sent successfully
        """
        try:
            # Format transaction alert message
            tx_type_emoji = {
                "ADD_FUNDS": "💰",
                "PURCHASE": "🛒",
                "REFUND": "↩️",
                "TRANSFER": "💸"
            }
            
            emoji = tx_type_emoji.get(transaction.type, "💳")
            
            message = f"""
{emoji} <b>Transaction Alert</b>

<b>Type:</b> {transaction.type.replace('_', ' ').title()}
<b>Amount:</b> ${transaction.amount:.2f} {transaction.currency}
<b>Status:</b> {transaction.status.title()}
<b>Time:</b> {transaction.created_at.strftime('%Y-%m-%d %H:%M:%S')}

<i>Transaction ID: {transaction.hash[:16]}...</i>
"""
            
            return await self.send_notification(
                user_id, 
                message, 
                notification_type="info",
                priority="normal"
            )
            
        except Exception as e:
            logger.error(f"Error sending transaction alert: {e}")
            return False

    async def send_wallet_alert(self, user_id: int, alert_type: str, details: Dict[str, Any]) -> bool:
        """
        Send wallet-related alerts (low balance, locked wallet, etc.)
        
        Args:
            user_id: Telegram user ID
            alert_type: Type of wallet alert
            details: Additional alert details
            
        Returns:
            True if alert was sent successfully
        """
        try:
            alert_messages = {
                "low_balance": f"⚠️ <b>Low Balance Alert</b>\n\nYour wallet balance is ${details.get('balance', 0):.2f}. Consider adding funds.",
                "wallet_locked": "🔒 <b>Wallet Locked</b>\n\nYour wallet has been temporarily locked. Contact support if needed.",
                "daily_limit": f"📊 <b>Daily Limit Alert</b>\n\nYou've reached {details.get('percentage', 0):.1f}% of your daily spending limit.",
                "suspicious_activity": "🚨 <b>Security Alert</b>\n\nUnusual activity detected on your account. Please review your recent transactions."
            }
            
            message = alert_messages.get(alert_type, f"📢 Wallet notification: {alert_type}")
            notification_type = "warning" if alert_type in ["low_balance", "daily_limit"] else "alert"
            
            return await self.send_notification(user_id, message, notification_type, "high")
            
        except Exception as e:
            logger.error(f"Error sending wallet alert: {e}")
            return False

    async def _log_notification(
        self,
        user_id: int,
        message: str,
        notification_type: str,
        priority: str,
        status: str
    ) -> None:
        """Log notification for tracking and analytics"""
        try:
            log_entry = {
                "user_id": user_id,
                "message": message[:200],  # Truncate for storage
                "type": notification_type,
                "priority": priority,
                "status": status,
                "created_at": datetime.utcnow()
            }
            
            await self.notifications_collection.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Failed to log notification: {e}")

    async def get_notification_history(
        self, 
        user_id: int, 
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get notification history for a user
        
        Args:
            user_id: Telegram user ID
            limit: Maximum number of notifications to return
            
        Returns:
            List of notification records
        """
        try:
            cursor = self.notifications_collection.find(
                {"user_id": user_id}
            ).sort("created_at", -1).limit(limit)
            
            return await cursor.to_list(length=limit)
            
        except Exception as e:
            logger.error(f"Error getting notification history for user {user_id}: {e}")
            return []

    async def cleanup_old_notifications(self, days_to_keep: int = 30) -> int:
        """
        Clean up old notification logs
        
        Args:
            days_to_keep: Number of days to keep notifications
            
        Returns:
            Number of notifications deleted
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            result = await self.notifications_collection.delete_many({
                "created_at": {"$lt": cutoff_date}
            })
            
            deleted_count = result.deleted_count
            logger.info(f"Cleaned up {deleted_count} old notifications")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up notifications: {e}")
            return 0

    async def send_bulk_notification(
        self,
        user_ids: List[int],
        message: str,
        notification_type: str = "info"
    ) -> Dict[str, int]:
        """
        Send notification to multiple users
        
        Args:
            user_ids: List of Telegram user IDs
            message: Notification message
            notification_type: Type of notification
            
        Returns:
            Dictionary with success/failure counts
        """
        results = {"sent": 0, "failed": 0}
        
        # Send notifications in batches to avoid rate limiting
        batch_size = 10
        for i in range(0, len(user_ids), batch_size):
            batch = user_ids[i:i + batch_size]
            
            tasks = [
                self.send_notification(user_id, message, notification_type)
                for user_id in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, bool) and result:
                    results["sent"] += 1
                else:
                    results["failed"] += 1
            
            # Small delay between batches
            await asyncio.sleep(0.1)
        
        logger.info(f"Bulk notification complete: {results['sent']} sent, {results['failed']} failed")
        return results
