# Unmask Button and Multi-Item Checkout Fix

## Issues Fixed

### 1. **RecursionError during Checkout Job Status Update**

**Problem**: When multiple items were added to the cart and checkout completed, a `RecursionError` was thrown when trying to update the job status in MongoDB:

```
RecursionError: maximum recursion depth exceeded while encoding an object to BSON
```

**Root Cause**: The `result_data` dictionary stored in checkout metadata included the full `checkout_response` object, which contained circular references that couldn't be BSON-encoded.

**Location**: `services/checkout_queue_service.py` line 938

**Fix**: 
- Removed `checkout_response` from `result_data` (it was already stored in purchase records)
- Added BSON cleaning for `extracted_cards` using `self._clean_for_bson()`

```python
# BEFORE:
result_data = {
    # ...
    "checkout_response": checkout_resp.data,  # ❌ Causes RecursionError
}

# AFTER:
result_data = {
    # ...
    "purchased_cards": self._clean_for_bson(extracted_cards),  # ✅ Cleaned for BSON
    # DON'T store full checkout_response here - it causes RecursionError
    # It's already stored in purchase records with proper cleaning
}
```

---

### 2. **Unmask Button Showing Masked Card Details**

**Problem**: When the "🔓 Unmask Card" button was clicked, the card details displayed still showed masked data (e.g., `440393**********` or `[PAN_REDACTED]`) instead of the full unmasked card number.

**Root Cause**: Multiple issues:
1. The `order_doc["is_unmasked"]` flag wasn't being set explicitly when unmask completed
2. The `raw_data` from unmask response wasn't being stored
3. **Critical Issue**: The unmask API returns multiple cards (e.g., 2 cards), where Card 0 has masked/redacted data and Card 1 has the actual full data. The code was always displaying Card 0 instead of finding the card with real unmasked data.
4. Insufficient logging made it difficult to trace where unmasked data was lost

**Location**: `handlers/orders_handlers.py` line 4951-5054

**Fix**:
1. **Match Card by _id**: Use the target card's `_id` to find the correct card in the API response (critical when response contains multiple cards)
2. **Created helper function** `_find_matching_card_by_id()` to consistently match cards across all operations (unmask, check, download)
3. **Smart Card Selection**: Scan all returned cards to find the SPECIFIC card we're trying to unmask by matching `_id`
4. **Reorder cards**: Move the matched target card to index 0 so it gets displayed first
5. **Explicitly set `is_unmasked` flag** in `order_doc` (not just in `update_data`)
6. **Store `raw_data`** from unmask response in both `order_doc` and database
7. **Download detection**: If the matched card doesn't have full data, set a flag indicating download operation is required
8. **Added comprehensive logging** to track:
   - What data the unmask API returns
   - Whether each card matches the target `_id`
   - Whether each card has full numbers or masked numbers
   - Whether CVV is present for each card
   - Which card is being moved to index 0
   - Cache updates
9. **Enhanced data flow tracking** in `_create_full_card_view` to log what data source is being used and what sensitive data is available

```python
# Key Changes:

# 1. Find the SPECIFIC card by matching _id (not just any card with full data!)
target_card, target_card_idx = self._find_matching_card_by_id(
    response.data["extracted_cards"], 
    actual_card_id  # The card we're trying to unmask
)

# 2. Move the matched target card to index 0
if target_card and target_card_idx > 0:
    cards = response.data["extracted_cards"]
    cards[0], cards[target_card_idx] = cards[target_card_idx], cards[0]
    logger.info(f"🔄 Moving target card from index {target_card_idx} to index 0")

# 3. Check if the target card has full unmasked data
if target_card:
    card_num = target_card.get("card_number", "")
    has_full_data = (
        card_num 
        and card_num != "[PAN_REDACTED]"
        and "*" not in card_num 
        and len(re.sub(r'[^\d]', '', card_num)) >= 13
    )
    if not has_full_data:
        target_card["requires_download"] = True  # Flag for download needed

# 4. Mark order as unmasked
order_doc["is_unmasked"] = True  # ✅ CRITICAL: Mark order as unmasked in order_doc
update_data["extracted_cards"] = response.data["extracted_cards"]

# 5. Store raw_data if available
if "raw_data" in response.data:
    order_doc["raw_data"] = response.data["raw_data"]  # ✅ Store in order_doc
    update_data["raw_data"] = response.data["raw_data"]  # ✅ Store in database
    logger.info(f"✅ Stored raw_data in order_doc")

# 6. Update cache with unmasked data
cache_key = f"{db_user.id}:{order_id}:{card_id or short_card_id}"
self._cache_order(cache_key, order_doc)
logger.info(f"✅ Updated cache with unmasked data for key: {cache_key}")
```

---

## Files Modified

### 1. `services/checkout_queue_service.py`
- **Line 935**: Added `self._clean_for_bson()` to clean extracted cards before storing
- **Line 938-939**: Removed `checkout_response` from `result_data` (prevents RecursionError)

### 2. `handlers/orders_handlers.py`
- **Line 4951-4956**: Added logging to track what unmask API returns
- **Line 4958**: Explicitly set `order_doc["is_unmasked"] = True`
- **Line 4967-4970**: Store `raw_data` in both `order_doc` and database
- **Line 4978**: Added logging for cache update
- **Line 2924-2928**: Added logging in `_create_full_card_view` for API v1 data
- **Line 2969-2975**: Added logging in `_create_full_card_view` for API v3 data

---

## Testing Recommendations

### Test Case 1: Multi-Item Checkout
1. Add 2-3 items to cart
2. Click "✅ Checkout Cart"
3. Wait for checkout to complete
4. ✅ **Expected**: No RecursionError in logs
5. ✅ **Expected**: All items are purchased successfully
6. ✅ **Expected**: Wallet balance is deducted correctly

### Test Case 2: Unmask Single Card
1. Complete a purchase
2. View the purchased card
3. Click "🔓 Unmask Card"
4. Wait for unmask to complete
5. ✅ **Expected**: Full card number displayed (not masked with `**`)
6. ✅ **Expected**: CVV is visible
7. ✅ **Expected**: Check logs for: `"Card {idx}: card_number=FULL, cvv=YES"`

### Test Case 3: Unmask Multi-Item Purchase
1. Purchase 2-3 items in one checkout
2. View first purchased card
3. Click "🔓 Unmask Card"
4. ✅ **Expected**: First card is unmasked with full details
5. Navigate to other purchased cards
6. Click "🔓 Unmask Card" on each
7. ✅ **Expected**: Each card unmasks correctly

---

## Debugging Guide

If unmask button still shows masked data, check these logs:

### 1. Check what unmask API returns:
```
🔍 Unmask API returned 2 cards
   Card 0: card_number=MASKED/EMPTY, cvv=NO, _id=dda9ebf7aefc
   Card 1: card_number=FULL, cvv=YES, _id=d2f36136a042
   ✅ Found card with FULL data at index 1
🔄 Moving card with full data from index 1 to index 0
```

- If `card_number=MASKED` for ALL cards, the API didn't return full data → Check API implementation
- If `card_number=EMPTY` for ALL cards, the cards might require download → Show download prompt
- If one card has `FULL` data, that card should be moved to index 0 ✅

### 2. Check what data is being used for display:
```
✅ Using pre-extracted card data from order: [...] - CC: FULL/MASKED/NONE, CVV: YES/NO
```

- If `CC: MASKED`, the extracted_cards don't have full number → Check unmask response processing
- If `CC: NONE`, no card data available → Check order_doc structure

### 3. Check cache update:
```
✅ Updated cache with unmasked data for key: {user_id}:{order_id}:{card_id}
```

- If this log is missing, cache isn't being updated → Check unmask flow

### 4. Check comprehensive display creation:
```
🔓 UNMASKING RESULT: cc=✅/❌ cvv=✅/❌ exp=✅/❌
📱 Card Number: 1234****5678
```

- If `cc=❌`, sensitive data wasn't extracted from order_doc → Check `_create_full_card_view` logic
- If displayed card number has `****`, check `_extract_all_card_fields` logic

---

## API Notes

### API v1 (Legacy)
- **View Order**: Returns full card details immediately
- **Unmask**: Not typically used (view_order is sufficient)
- **Data Location**: `order["extracted_cards"][0]` or `order["raw_data"]["data"]`

### API v3 (Current)
- **View Order**: Returns masked card details
- **Unmask**: Returns unmasked details (may require download for full data)
- **Download**: Returns completely unmasked details with all fields
- **Data Location**: `order["extracted_cards"][0]` with `card_number_status="unmasked"`

---

## Known Limitations

1. **API might not return full data on unmask**: Some APIs return a status change but require a separate "download" operation to get the actual full card number.

2. **Multiple cards in one order**: When viewing order details, ensure only the FIRST card is displayed to prevent duplication.

3. **Cache invalidation**: Unmasking a card updates the cache immediately, but if the user views the card from a different menu path (e.g., "My Orders" vs "Recent Cards"), cache key might differ.

---

## Summary

✅ **Fixed**: RecursionError during multi-item checkout job status update
✅ **Fixed**: Unmask button now properly stores and displays unmasked card data
✅ **Enhanced**: Comprehensive logging for debugging unmask flow
✅ **Improved**: Data flow tracking from API response → order_doc → display

The fixes ensure that:
- Multi-item checkouts complete without database errors
- Unmasked card data is properly stored in both order document and cache
- Full card details are displayed when unmask button is clicked
- Debugging is easier with detailed logs showing data flow

