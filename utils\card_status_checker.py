"""
Enhanced Card Status Checking System
Provides comprehensive card verification with detailed status reporting and consistent UI
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

from aiogram.types import CallbackQuery, InlineKeyboardMarkup
from utils.ui_components import create_message, MessageType
from utils.enhanced_keyboards import create_enhanced_keyboard, KeyboardStyle, ButtonPriority
from utils.timer_ui import TimerEmojis, LoadingAnimation
from utils.loading_animations import LoadingStages, CHECK_STAGES, get_animation_runner, AnimationConfig
from services.external_api_service import get_external_api_service
from utils.central_logger import get_logger
from utils.card_data_extractor import get_card_data_extractor
from utils.card_formatter import (
    CentralizedCardFormatter,
    CardFormattingOptions,
    CardDisplayMode,
    format_card_full,
)

logger = get_logger()


@dataclass
class CardStatusResult:
    """Comprehensive card status result"""
    is_live: bool
    status: str
    status_code: Optional[str] = None
    reason: Optional[str] = None
    response_details: Optional[Dict[str, Any]] = None
    check_timestamp: Optional[datetime] = None
    gateway_info: Optional[Dict[str, Any]] = None
    card_info: Optional[Dict[str, Any]] = None
    security_info: Optional[Dict[str, Any]] = None


class CardStatusChecker:
    """Enhanced card status verification system"""
    
    def __init__(self):
        self._check_cache: Dict[str, CardStatusResult] = {}
        self._cache_expiry: Dict[str, datetime] = {}
        self._cache_duration = 300  # 5 minutes cache
    
    async def check_card_status(
        self,
        order_id: str,
        card_id: str,
        force_refresh: bool = False
    ) -> CardStatusResult:
        """
        Perform comprehensive card status check with enhanced data extraction
        
        Args:
            order_id: Order identifier
            card_id: Card identifier
            force_refresh: Force fresh check, ignore cache
            
        Returns:
            Detailed status result with extracted card data
        """
        cache_key = f"{order_id}:{card_id}"
        
        # Check cache first (unless forced refresh)
        if not force_refresh and self._is_cache_valid(cache_key):
            logger.debug(f"Returning cached status for {cache_key}")
            return self._check_cache[cache_key]
        
        try:
            # Perform API check
            logger.info(f"Performing enhanced card status check for order {order_id}, card {card_id}")
            
            async with get_external_api_service() as api_service:
                response = await api_service.check_card_status(
                    order_id=order_id,
                    card_id=card_id,
                    force_api_v3=True
                )
            
            # Process response with enhanced extraction
            result = self._process_status_response(response, order_id, card_id)
            
            # Cache result
            self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Card status check failed for {order_id}:{card_id} - {e}")
            
            # Return error result
            return CardStatusResult(
                is_live=False,
                status="error",
                reason=f"Check failed: {str(e)}",
                check_timestamp=datetime.now(timezone.utc)
            )
    
    def _process_status_response(
        self,
        response,
        order_id: str,
        card_id: str
    ) -> CardStatusResult:
        """Process API response into structured result with detailed card extraction"""
        
        if not response.success or not response.data:
            return CardStatusResult(
                is_live=False,
                status="api_error",
                reason="API request failed",
                check_timestamp=datetime.now(timezone.utc)
            )
        
        data = response.data
        check_time = datetime.now(timezone.utc)
        
        # Extract detailed card information using card data extractor
        try:
            extractor = get_card_data_extractor()
            extracted_cards = extractor.extract_from_api_response(data)
            logger.info(f"Extracted {len(extracted_cards)} cards from check response")
            
            # Find the specific card we're checking
            target_card = None
            for card in extracted_cards:
                card_id_field = card.get("_id", card.get("id", ""))
                if card_id in card_id_field or card_id_field.startswith(card_id):
                    target_card = card
                    logger.info(f"Found target card in extracted data: {card_id}")
                    break
            
            if target_card:
                # Use extracted card data for detailed information
                extracted_info = self._process_extracted_card_data(target_card)
                
                # Determine status from extracted data
                status = extracted_info.get("status", data.get("status", "unknown")).lower()
                status_code = extracted_info.get("response_code") or data.get("response_code") or data.get("code")
                reason = extracted_info.get("reason") or data.get("reason") or data.get("message", "")
                
                # Enhanced live status determination with extracted data
                is_live = self._determine_live_status_enhanced(status, status_code, data, extracted_info)
                
                return CardStatusResult(
                    is_live=is_live,
                    status=status,
                    status_code=status_code,
                    reason=reason,
                    response_details=data,
                    check_timestamp=check_time,
                    gateway_info=extracted_info.get("gateway_info", {}),
                    card_info=extracted_info.get("card_info", {}),
                    security_info=extracted_info.get("security_info", {})
                )
            else:
                logger.warning(f"Target card {card_id} not found in extracted data, using basic processing")
                
        except Exception as e:
            logger.error(f"Error extracting card data: {e}, falling back to basic processing")
        
        # Fallback to basic processing if extraction fails
        # Extract core status information
        status = data.get("status", "unknown").lower()
        status_code = data.get("response_code") or data.get("code")
        reason = data.get("reason") or data.get("message", "")
        
        # Determine if card is live
        is_live = self._determine_live_status(status, status_code, data)
        
        # Extract detailed information using legacy methods
        card_info = self._extract_card_info(data)
        gateway_info = self._extract_gateway_info(data)
        security_info = self._extract_security_info(data)
        
        return CardStatusResult(
            is_live=is_live,
            status=status,
            status_code=status_code,
            reason=reason,
            response_details=data,
            check_timestamp=check_time,
            gateway_info=gateway_info,
            card_info=card_info,
            security_info=security_info
        )

    def _process_extracted_card_data(self, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process extracted card data into structured information"""
        
        # Extract card information
        card_info = {
            "number": card_data.get("number", "").replace("*", ""),
            "cvv": card_data.get("cvv", ""),
            "expiry": card_data.get("expiry", ""),
            "exp_month": card_data.get("exp_month", ""),
            "exp_year": card_data.get("exp_year", ""),
            "bank": card_data.get("bank", ""),
            "brand": card_data.get("brand", card_data.get("type", "")),
            "level": card_data.get("level", ""),
            "country": card_data.get("country", ""),
            "balance": card_data.get("balance", ""),
            "currency": card_data.get("currency", ""),
            "bin": card_data.get("bin", ""),
            "last_four": card_data.get("number", "")[-4:] if card_data.get("number") else ""
        }
        
        # Extract gateway/response information
        gateway_info = {
            "response_code": card_data.get("response_code", ""),
            "response_message": card_data.get("response_message", ""),
            "gateway": card_data.get("gateway", ""),
            "auth_code": card_data.get("auth_code", ""),
            "transaction_id": card_data.get("transaction_id", ""),
            "processor_response": card_data.get("processor_response", ""),
            "network_response": card_data.get("network_response", "")
        }
        
        # Extract security information
        security_info = {
            "cvv_check": card_data.get("cvv_check", ""),
            "avs_check": card_data.get("avs_check", ""),
            "fraud_check": card_data.get("fraud_check", ""),
            "risk_score": card_data.get("risk_score", "")
        }
        
        # Extract status information
        status_info = {
            "status": card_data.get("status", "").lower(),
            "reason": card_data.get("reason", card_data.get("message", "")),
            "response_code": card_data.get("response_code", "")
        }
        
        return {
            "card_info": card_info,
            "gateway_info": gateway_info,
            "security_info": security_info,
            **status_info
        }
    
    def _determine_live_status_enhanced(
        self,
        status: str,
        status_code: Optional[str],
        data: Dict[str, Any],
        extracted_info: Dict[str, Any]
    ) -> bool:
        """Enhanced live status determination using extracted card data"""
        
        # First, try basic determination
        if self._determine_live_status(status, status_code, data):
            return True
        
        # Enhanced checks using extracted card information
        card_info = extracted_info.get("card_info", {})
        gateway_info = extracted_info.get("gateway_info", {})
        
        # Check if we have valid card number (indicates successful extraction)
        card_number = card_info.get("number", "")
        if card_number and len(card_number.replace("*", "").replace(" ", "")) >= 12:
            logger.debug(f"Found valid card number in extracted data: {card_number[:6]}***")
            
            # If we have card details, check response codes more thoroughly
            response_code = gateway_info.get("response_code", status_code)
            if response_code:
                # More comprehensive response code checking
                live_response_codes = [
                    "00", "000", "approved", "success", "live", "valid", "active",
                    "0", "200", "ok", "accepted", "authorization_approved"
                ]
                
                if any(str(response_code).lower() == code for code in live_response_codes):
                    return True
        
        # Check balance information from extracted data
        balance = card_info.get("balance", "")
        if balance and balance != "":
            try:
                balance_val = float(str(balance).replace("$", "").replace(",", ""))
                if balance_val >= 0:
                    logger.debug(f"Found valid balance in extracted data: {balance}")
                    return True
            except (ValueError, TypeError):
                pass
        
        # Check CVV and other security fields
        cvv = card_info.get("cvv", "")
        if cvv and cvv.strip() and cvv.strip() != "***":
            logger.debug(f"Found CVV in extracted data, indicates live card")
            return True
        
        return False
    
    def _determine_live_status(
        self,
        status: str,
        status_code: Optional[str],
        data: Dict[str, Any]
    ) -> bool:
        """Determine if card is live based on response data"""
        
        # Common live indicators
        live_statuses = ["live", "active", "approved", "success", "valid", "working"]
        live_codes = ["00", "000", "approved", "success"]
        
        # Check status
        if any(live_status in status for live_status in live_statuses):
            return True
        
        # Check status code
        if status_code and str(status_code).lower() in live_codes:
            return True
        
        # Check response details
        if data.get("is_live") or data.get("is_active") or data.get("is_valid"):
            return True
        
        # Check balance indicators
        if "balance" in data and data["balance"] is not None:
            try:
                balance = float(data["balance"])
                return balance >= 0  # Valid balance indicates live card
            except (ValueError, TypeError):
                pass
        
        return False

    def _extract_card_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract card-specific information"""
        return {
            "bank": data.get("bank") or data.get("bank_name"),
            "brand": data.get("brand") or data.get("card_brand"),
            "type": data.get("type") or data.get("card_type"),
            "level": data.get("level") or data.get("card_level"),
            "country": data.get("country") or data.get("country_code"),
            "balance": data.get("balance") or data.get("available_balance"),
            "currency": data.get("currency") or data.get("balance_currency"),
            "bin": data.get("bin") or data.get("card_bin"),
            "last_four": data.get("last_four") or data.get("last4")
        }
    
    def _extract_gateway_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract gateway/processor information"""
        return {
            "gateway": data.get("gateway") or data.get("processor"),
            "response_code": data.get("response_code") or data.get("code"),
            "auth_code": data.get("auth_code") or data.get("authorization_code"),
            "transaction_id": data.get("transaction_id") or data.get("txn_id"),
            "processor_response": data.get("processor_response"),
            "network_response": data.get("network_response")
        }
    
    def _extract_security_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract security-related information"""
        return {
            "fraud_check": data.get("fraud_check") or data.get("fraud_status"),
            "cvv_check": data.get("cvv_check") or data.get("cvv_result"),
            "avs_check": data.get("avs_check") or data.get("avs_result"),
            "security_flags": data.get("security_flags", []),
            "risk_score": data.get("risk_score"),
            "verification_status": data.get("verification_status")
        }
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached result is still valid"""
        if cache_key not in self._check_cache:
            return False
        
        if cache_key not in self._cache_expiry:
            return False
        
        return datetime.now(timezone.utc) < self._cache_expiry[cache_key]
    
    def _cache_result(self, cache_key: str, result: CardStatusResult) -> None:
        """Cache status result"""
        self._check_cache[cache_key] = result
        self._cache_expiry[cache_key] = datetime.now(timezone.utc) + \
                                      timedelta(seconds=self._cache_duration)
    
    def clear_cache(self, cache_key: Optional[str] = None) -> None:
        """Clear cache entries"""
        if cache_key:
            self._check_cache.pop(cache_key, None)
            self._cache_expiry.pop(cache_key, None)
        else:
            self._check_cache.clear()
            self._cache_expiry.clear()


class StatusMessageFormatter:
    """Formats status check results into beautiful messages"""
    
    @staticmethod
    def create_status_message(result: CardStatusResult) -> str:
        """Create comprehensive status display message"""
        
        # Determine message type and main emoji
        if result.is_live:
            msg_type = MessageType.SUCCESS
            main_emoji = "✅"
            status_emoji = "🟢"
        else:
            msg_type = MessageType.WARNING
            main_emoji = "⚠️"
            status_emoji = "🔴"
        
        msg = create_message(msg_type)
        msg.set_title(f"Card Status Check Results", main_emoji)
        
        # Primary status section
        status_display = result.status.title()
        check_time = result.check_timestamp.strftime('%H:%M:%S UTC') if result.check_timestamp else "Unknown"
        
        primary_info = [
            f"<b>Status:</b> {status_emoji} {status_display}",
            f"<b>Live Status:</b> {'🟢 Active' if result.is_live else '🔴 Inactive'}",
            f"<b>Checked:</b> 🕐 {check_time}"
        ]
        
        if result.reason:
            primary_info.append(f"<b>Details:</b> {result.reason}")
        
        msg.add_section(
            "📊 Status Overview",
            "\n".join(primary_info),
            "🎯"
        )
        
        # Card information section with enhanced extracted data
        if result.card_info:
            card_details = StatusMessageFormatter._format_card_details(result.card_info)
            if card_details:
                msg.add_section("💳 Card Information", card_details, "🏦")
        
        # Gateway information section with enhanced extracted data
        if result.gateway_info:
            gateway_details = StatusMessageFormatter._format_gateway_details(result.gateway_info)
            if gateway_details:
                msg.add_section("🌐 Gateway Response", gateway_details, "⚙️")
        
        # Security information section
        if result.security_info:
            security_details = StatusMessageFormatter._format_security_details(result.security_info)
            if security_details:
                msg.add_section("🔒 Security Checks", security_details, "🛡️")
        
        # Recommendations section
        recommendations = StatusMessageFormatter._get_recommendations(result)
        if recommendations:
            msg.add_section("💡 Recommendations", "\n".join(recommendations), "🎯")
        
        return msg.build(add_watermark=False)

    @staticmethod
    def _format_card_details(card_info: Dict[str, Any]) -> str:
        """Format card information section with enhanced extracted data"""
        details = []
        
        # Card number (masked for security)
        if card_info.get("number"):
            number = card_info["number"]
            if len(number) >= 12:
                masked_number = f"{number[:4]} **** **** {number[-4:]}"
                details.append(f"💳 Number: {masked_number}")
            elif card_info.get("last_four"):
                details.append(f"💳 Last 4: ****{card_info['last_four']}")
        
        # Expiry information
        if card_info.get("expiry"):
            details.append(f"📅 Expiry: {card_info['expiry']}")
        elif card_info.get("exp_month") and card_info.get("exp_year"):
            details.append(f"📅 Expiry: {card_info['exp_month']}/{card_info['exp_year']}")
        
        # CVV (masked)
        if card_info.get("cvv") and card_info["cvv"].strip() not in ["", "***"]:
            cvv_masked = "***" if len(card_info["cvv"]) >= 3 else card_info["cvv"]
            details.append(f"🔢 CVV: {cvv_masked}")
        
        # Bank and brand information
        if card_info.get("bank"):
            details.append(f"🏦 Bank: {card_info['bank']}")
        if card_info.get("brand"):
            details.append(f"🎯 Brand: {card_info['brand']}")
        if card_info.get("level"):
            details.append(f"⭐ Level: {card_info['level']}")
        if card_info.get("country"):
            details.append(f"🌍 Country: {card_info['country']}")
        
        # Balance information
        if card_info.get("balance") is not None and card_info["balance"] != "":
            currency = card_info.get("currency", "USD")
            balance = card_info["balance"]
            details.append(f"💰 Balance: {balance} {currency}")
        
        # BIN information
        if card_info.get("bin"):
            details.append(f"🔍 BIN: {card_info['bin']}")
        
        return "\n".join(details) if details else ""
    
    @staticmethod
    def _format_gateway_details(gateway_info: Dict[str, Any]) -> str:
        """Format gateway information section with enhanced extracted data"""
        details = []
        
        if gateway_info.get("gateway"):
            details.append(f"🌐 Gateway: {gateway_info['gateway']}")
        if gateway_info.get("response_code"):
            details.append(f"📋 Response Code: {gateway_info['response_code']}")
        if gateway_info.get("response_message"):
            details.append(f"💬 Response: {gateway_info['response_message']}")
        if gateway_info.get("auth_code"):
            details.append(f"🔑 Auth Code: {gateway_info['auth_code']}")
        if gateway_info.get("transaction_id"):
            details.append(f"🆔 Transaction: {gateway_info['transaction_id']}")
        if gateway_info.get("processor_response"):
            details.append(f"⚙️ Processor: {gateway_info['processor_response']}")
        if gateway_info.get("network_response"):
            details.append(f"🌐 Network: {gateway_info['network_response']}")
        
        return "\n".join(details) if details else ""
    
    @staticmethod
    def _format_security_details(security_info: Dict[str, Any]) -> str:
        """Format security information section"""
        details = []
        
        if security_info.get("fraud_check"):
            details.append(f"🛡️ Fraud Check: {security_info['fraud_check']}")
        if security_info.get("cvv_check"):
            details.append(f"🔢 CVV Check: {security_info['cvv_check']}")
        if security_info.get("avs_check"):
            details.append(f"📍 AVS Check: {security_info['avs_check']}")
        if security_info.get("risk_score"):
            details.append(f"⚠️ Risk Score: {security_info['risk_score']}")
        
        return "\n".join(details) if details else ""
    
    @staticmethod
    def _get_recommendations(result: CardStatusResult) -> List[str]:
        """Generate personalized recommendations"""
        recommendations = []
        
        if result.is_live:
            recommendations.extend([
                "✅ Card is ready for transactions",
                "🔒 Keep card details secure and private",
                "💡 Test with small amounts first",
                "📊 Monitor usage and check status regularly"
            ])
        else:
            recommendations.extend([
                "❌ Card may not be suitable for transactions",
                "🔍 Check card details and try again",
                "💬 Contact support if issues persist",
                "🔄 Consider using a different card"
            ])
        
        return recommendations


class StatusCheckUI:
    """UI components for status checking"""
    
    @staticmethod
    def create_status_keyboard(
        card_id: str,
        order_id: str,
        result: CardStatusResult,
        allow_recheck: bool = True
    ) -> InlineKeyboardMarkup:
        """Create status display keyboard"""
        
        kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
        
        # Recheck button (if allowed)
        if allow_recheck:
            kb.add_button(
                "🔄 Recheck",
                f"orders:check:{order_id}:{card_id}",
                ButtonPriority.PRIMARY
            )
        
        # Navigation
        kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
        kb.add_navigation_row(back_text="🏠 Main", back_callback="menu:main")
        
        return kb.build()


# Singleton instances
_status_checker = None

def get_status_checker() -> CardStatusChecker:
    """Get singleton status checker instance"""
    global _status_checker
    if _status_checker is None:
        _status_checker = CardStatusChecker()
    return _status_checker


async def perform_animated_status_check(
    callback: CallbackQuery,
    order_id: str,
    card_id: str,
    show_loading: bool = True
) -> Tuple[CardStatusResult, str]:
    """
    Perform status check with loading animation
    
    Returns:
        Tuple of (status_result, formatted_message)
    """
    
    if show_loading:
        # Define the actual work to be done
        async def check_work():
            # Perform actual check with enhanced extraction
            checker = get_status_checker()
            result = await checker.check_card_status(order_id, card_id)
            return result
        
        # Run loading concurrently with actual work
        result = await LoadingStages.run_concurrent_loading(
            callback,
            CHECK_STAGES,
            check_work(),
            operation_name="Card Status Check"
        )
    else:
        # Perform check without loading animation
        checker = get_status_checker()
        result = await checker.check_card_status(order_id, card_id)
    
    # Format result message
    message = StatusMessageFormatter.create_status_message(result)
    
    return result, message