# Multi-Card Display Fix - Complete Flow Overhaul

## Problem

When multiple cards were purchased in a single checkout, the system had critical issues with displaying the correct card data:

### Issues Identified

1. **Always Using First Card**: Code used `extracted_cards[0]` throughout, ignoring which card the user clicked
2. **Wrong Card Displayed**: Viewing card B would show data from card A
3. **Unmask Shows Wrong Data**: Unmasking card B would display unmasked data for card A
4. **Check Checks Wrong Card**: Checking card B would check card A's status
5. **Download Wrong Data**: Downloading card B would download card A's data

### Root Cause

The codebase had multiple instances of `extracted_cards[0]` hardcoded, which only works for single-card checkouts. For multi-card checkouts with 2+ items:
- Purchase creates separate order records for each card
- Each order has a unique `card_id` (_id)
- But viewing logic always used first card in array instead of matching by `_id`

## Solution

### Part 1: Enhanced Card Matching Helper

**File**: `handlers/orders_handlers.py` (lines 627-711)

Created two centralized helper functions:

```python
def _find_matching_card_by_id(
    self, 
    cards: List[Dict[str, Any]], 
    target_card_id: str
) -> Tuple[Optional[Dict[str, Any]], int]:
    """
    Find a card matching the target_card_id in a list of cards.
    Tries multiple ID fields for matching: _id, product_id, external_product_id, id
    """
    # Try exact _id match first (most reliable)
    # Then try other ID fields (product_id, external_product_id, id)
    # If only one card, return it as fallback
    # Otherwise return None

def _get_card_from_order(
    self,
    order: Dict[str, Any],
    target_card_id: str
) -> Optional[Dict[str, Any]]:
    """
    Extract the correct card data from an order by matching the target_card_id.
    Handles both single-card and multi-card orders.
    """
    # Check extracted_cards first
    # Fall back to raw_data or api_response
    # Log warnings if card not found
```

### Part 2: Fixed View Details Handler

**File**: `handlers/orders_handlers.py` (`cb_view_card_details`)

**Changes**:
1. Line 3679: Use `_get_card_from_order()` to find target card for unmask/check status
2. Line 3836: Use `_get_card_from_order()` to verify actual unmask status
3. Line 3867: Use `_get_card_from_order()` to get target card status
4. Line 3927: Use `_get_card_from_order()` to check canCheck field
5. **Line 3856-3863**: 🔥 CRITICAL - Filter `extracted_cards` to only contain target card before display

```python
# 🔥 CRITICAL FIX: Filter extracted_cards to only contain the TARGET card
# This ensures multi-card orders display the correct card
if "extracted_cards" in order and order["extracted_cards"]:
    target_card = self._get_card_from_order(order, card_id)
    if target_card:
        # Replace extracted_cards with ONLY the target card
        order["extracted_cards"] = [target_card]
        logger.info(f"✅ Filtered extracted_cards to target card {card_id[:12]} for display")
```

### Part 3: Fixed View Purchased Card Handler

**File**: `handlers/orders_handlers.py` (`cb_view_purchased_card`)

**Changes**:
1. Line 2578: Use `_get_card_from_order()` for API v1 unmask check
2. Line 2592: Use `_get_card_from_order()` for API v3 unmask check
3. Line 2640: Use `_get_card_from_order()` to verify actual unmask status
4. **Line 2658-2667**: 🔥 CRITICAL - Filter `extracted_cards` to only contain target card before display

### Part 4: Previously Fixed (Earlier Session)

**Unmask Handler** (`cb_unmask_card`): Already uses `_find_matching_card_by_id` to match correct card
**Check Handler** (`cb_check_card`): Already uses `_find_matching_card_by_id` to get status from correct card
**Download Handler**: Inherits correct card from filtered `extracted_cards`

## Technical Details

### Card ID Matching Strategy

**CRITICAL DATABASE MAPPING:**
- Database stores: `external_product_id` = card's `_id` from API
- When matching: `target_card_id` (from DB) → `card._id` (in API response)
- Example: DB record has `external_product_id="abc123"` → matches card with `_id="abc123"`

**Matching Priority:**
1. **Primary**: Match by `_id` field (handles DB external_product_id → API card._id)
2. **Secondary**: Try `product_id`, `external_product_id`, `id`, `productId` fields
3. **Fallback**: If only one card exists, use it (single-card checkout)
4. **Logging**: Comprehensive logging at each step for debugging, including all available card IDs

### Data Flow

```
User clicks card → 
  Resolve card_id (gets external_product_id from DB) → 
    Fetch order from DB → 
      _get_card_from_order(order, card_id) → 
        Search extracted_cards for card._id == card_id → 
          Match found (DB external_product_id == API card._id) → 
            Filter extracted_cards = [matched_card] → 
              Display/unmask/check/download correct card ✅
```

**Key Insight:**
The `card_id` passed around is actually the `external_product_id` from the database, which corresponds to the card's `_id` field in the API response. This is why we match `target_card_id` against `card._id` in the extracted_cards array.

### Before vs After

| Scenario | Before | After |
|----------|--------|-------|
| View Card B in 3-card checkout | ❌ Shows Card A | ✅ Shows Card B |
| Unmask Card C | ❌ Unmasks Card A | ✅ Unmasks Card C |
| Check Card B | ❌ Checks Card A | ✅ Checks Card B |
| Download Card C | ❌ Downloads Card A | ✅ Downloads Card C |
| Single card checkout | ✅ Works (used [0]) | ✅ Still works (fallback) |

## Testing

### Test Scenario 1: Two Cards Purchased Together

1. Add 2 different cards to cart
2. Complete checkout
3. Go to Orders
4. Click on second card
5. **Expected**: Should display second card's details, not first card
6. Click "Unmask Card"
7. **Expected**: Should unmask the second card, not first card
8. Click "Check Status"
9. **Expected**: Should check the second card's status

### Test Scenario 2: Three Cards from Same Provider

1. Add 3 cards from same bank to cart
2. Complete checkout
3. View middle card (card #2)
4. **Expected**: Shows card #2 data (different BIN/expiry than #1 and #3)
5. Unmask and verify card number matches card #2

### Test Scenario 3: Single Card (Regression Test)

1. Purchase single card
2. View, unmask, check, download
3. **Expected**: Everything works as before (backward compatible)

## Files Modified

1. `handlers/orders_handlers.py` - Main fixes
   - Enhanced `_find_matching_card_by_id()` (line 627)
   - Created `_get_card_from_order()` (line 669)
   - Fixed `cb_view_card_details()` (lines 3679, 3836, 3867, 3927, 3856-3863)
   - Fixed `cb_view_purchased_card()` (lines 2578, 2592, 2640, 2658-2667)

## Logs to Watch

Look for these log messages to verify correct behavior:

```
✅ Found matching card at index 1 by _id for {card_id}
✅ Found target card in extracted_cards by ID matching
✅ Filtered extracted_cards to target card {card_id} for display
✅ [My Orders] Target card {card_id} has unmasked data in database
🎯 [Check] Found matching card with status='live', refund_status=''
✅ [Unmask] Moving target card from index 2 to index 0
```

## Impact

### Positive
- ✅ Multi-card checkouts now work correctly
- ✅ Each card displays its own correct data
- ✅ Unmask/check/download operations target correct card
- ✅ Backward compatible with single-card checkouts
- ✅ Centralized card matching logic

### Performance
- Minimal impact: O(n) search through extracted_cards (typically n=1-5)
- Adds logging for debugging
- No database query overhead

## Related Fixes

This fix complements previous fixes:
- **CHECK_BUTTON_FIX.md**: Hide check button for already-checked cards
- **UNMASK_AND_CHECKOUT_FIX.md**: _id matching for unmask operations
- Both now work correctly for multi-card checkouts

## Future Improvements

1. Consider caching card lookup results per request
2. Add automated tests for multi-card scenarios
3. Add admin tool to verify multi-card order consistency
4. Consider showing card index in UI (e.g., "Card 2 of 3")

---

**Status**: ✅ Complete
**Date**: 2025-10-26
**Risk**: Low (adds fallbacks, logs warnings)
**Testing**: Manual testing recommended with 2-3 card checkout

