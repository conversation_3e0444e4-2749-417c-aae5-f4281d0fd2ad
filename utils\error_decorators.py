"""
Error handling decorators for consistent error management across handlers

This module provides decorators to standardize error handling in callback and message handlers,
reducing code duplication and ensuring consistent user feedback and logging.
"""

from functools import wraps
from typing import Callable, Optional, TypeVar, Any
from aiogram.types import CallbackQuery, Message
from utils.central_logger import get_logger

logger = get_logger()

F = TypeVar('F', bound=Callable[..., Any])


def handle_callback_errors(
    error_message: str = "An error occurred",
    show_alert: bool = True,
    log_level: str = "error"
) -> Callable[[F], F]:
    """
    Decorator for handling callback query errors consistently
    
    Args:
        error_message: User-friendly error message to display
        show_alert: Whether to show the error as an alert (popup)
        log_level: Logging level ('error', 'warning', 'info')
        
    Returns:
        Decorated function with error handling
        
    Example:
        @handle_callback_errors("Failed to load wallet menu")
        async def cb_wallet_menu(self, callback: CallbackQuery):
            # ... handler logic ...
            await callback.answer()
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, callback: CallbackQuery, *args, **kwargs):
            try:
                return await func(self, callback, *args, **kwargs)
            except Exception as e:
                # Log with context
                log_func = getattr(logger, log_level, logger.error)
                log_func(
                    f"Error in {func.__name__}: {e}",
                    extra={
                        "handler": func.__name__,
                        "user_id": callback.from_user.id if callback.from_user else None,
                        "callback_data": callback.data,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    },
                    exc_info=log_level == "error"  # Include traceback for errors
                )
                
                # Send user feedback
                try:
                    await callback.answer(f"❌ {error_message}", show_alert=show_alert)
                except Exception as answer_error:
                    # Callback might already be answered or message might be too old
                    logger.debug(f"Could not answer callback: {answer_error}")
                    
        return wrapper
    return decorator


def handle_message_errors(
    error_message: str = "An error occurred while processing your request",
    log_level: str = "error"
) -> Callable[[F], F]:
    """
    Decorator for handling message handler errors consistently
    
    Args:
        error_message: User-friendly error message to display
        log_level: Logging level ('error', 'warning', 'info')
        
    Returns:
        Decorated function with error handling
        
    Example:
        @handle_message_errors("Failed to process your search")
        async def handle_search(self, message: Message):
            # ... handler logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, message: Message, *args, **kwargs):
            try:
                return await func(self, message, *args, **kwargs)
            except Exception as e:
                # Log with context
                log_func = getattr(logger, log_level, logger.error)
                log_func(
                    f"Error in {func.__name__}: {e}",
                    extra={
                        "handler": func.__name__,
                        "user_id": message.from_user.id if message.from_user else None,
                        "message_text": message.text[:100] if message.text else None,  # First 100 chars
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    },
                    exc_info=log_level == "error"
                )
                
                # Send user feedback
                try:
                    await message.answer(f"❌ {error_message}")
                except Exception as answer_error:
                    logger.debug(f"Could not send error message: {answer_error}")
                    
        return wrapper
    return decorator


def handle_errors(
    error_message: str = "An error occurred",
    show_alert: bool = True,
    log_level: str = "error"
) -> Callable[[F], F]:
    """
    Universal error handler that works with both callbacks and messages
    
    Args:
        error_message: User-friendly error message to display
        show_alert: Whether to show the error as an alert (for callbacks)
        log_level: Logging level ('error', 'warning', 'info')
        
    Returns:
        Decorated function with error handling
        
    Example:
        @handle_errors("Operation failed")
        async def some_handler(self, update: CallbackQuery | Message):
            # ... handler logic ...
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(self, update, *args, **kwargs):
            try:
                return await func(self, update, *args, **kwargs)
            except Exception as e:
                # Determine update type
                is_callback = isinstance(update, CallbackQuery)
                is_message = isinstance(update, Message)
                
                # Get user ID for logging
                user_id = None
                if is_callback and update.from_user:
                    user_id = update.from_user.id
                elif is_message and update.from_user:
                    user_id = update.from_user.id
                
                # Log with context
                log_func = getattr(logger, log_level, logger.error)
                log_func(
                    f"Error in {func.__name__}: {e}",
                    extra={
                        "handler": func.__name__,
                        "user_id": user_id,
                        "update_type": "callback" if is_callback else "message" if is_message else "unknown",
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    },
                    exc_info=log_level == "error"
                )
                
                # Send user feedback based on update type
                try:
                    if is_callback:
                        await update.answer(f"❌ {error_message}", show_alert=show_alert)
                    elif is_message:
                        await update.answer(f"❌ {error_message}")
                except Exception as feedback_error:
                    logger.debug(f"Could not send error feedback: {feedback_error}")
                    
        return wrapper
    return decorator


def suppress_errors(log_level: str = "warning") -> Callable[[F], F]:
    """
    Decorator to suppress errors and only log them (no user feedback)
    
    Useful for background tasks or non-critical operations where user
    shouldn't be notified of failures.
    
    Args:
        log_level: Logging level ('error', 'warning', 'info')
        
    Returns:
        Decorated function with error suppression
        
    Example:
        @suppress_errors("info")
        async def background_cleanup(self):
            # ... cleanup logic ...
            # Errors will be logged but not shown to user
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                log_func = getattr(logger, log_level, logger.warning)
                log_func(
                    f"Suppressed error in {func.__name__}: {e}",
                    extra={
                        "handler": func.__name__,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    }
                )
                return None  # Return None on error
                
        return wrapper
    return decorator

