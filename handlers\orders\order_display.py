"""
Order Display Module

This module contains all display and formatting methods for order and card data.
Handles UI formatting, card previews, and data masking for security.
"""

from __future__ import annotations

import html
import logging
import re
from typing import Dict, Any, List, Optional

from utils.data_display import create_card_display, is_valid_value, format_date
from utils.card_formatter import (
    CentralizedCardFormatter,
    CardFormattingOptions,
    CardDisplayMode,
    format_card_full,
    format_card_masked,
)

logger = logging.getLogger(__name__)


class OrderDisplayFormatter:
    """Handles formatting and display of order and card data for UI presentation"""
    
    def __init__(self):
        """Initialize the display formatter"""
        pass
    
    def create_masked_card_preview(self, order: Dict[str, Any]) -> str:
        """
        Create a masked preview of card data for State 1 (not yet viewed).
        
        🎨 Now delegates to centralized formatter for consistency.
        
        Args:
            order: Order data dictionary
            
        Returns:
            Formatted preview string
        """
        try:
            # Use centralized formatter in MASKED mode
            return format_card_masked(order)
            
        except Exception as e:
            logger.error(f"Error creating masked preview: {e}")
            return "💳 <b>Card Information</b>\n\n🔒 Card details will be revealed after viewing."
    
    def format_order_details(self, order: Dict[str, Any]) -> str:
        """
        Format order details with enhanced UI and complete field display.
        
        🎨 Now delegates to centralized formatter for consistency.
        
        Args:
            order: Order data dictionary
            
        Returns:
            Formatted order details string
        """
        try:
            # Use centralized formatter for consistency
            options = CardFormattingOptions(
                mode=CardDisplayMode.FULL,
                show_sensitive=True,
                show_order_info=True,
                show_timestamps=True,
                show_technical_details=True,
                show_location=True
            )
            return CentralizedCardFormatter.format_card_details(order, options)
            
        except Exception as e:
            logger.error(f"Error formatting order details: {e}")
            return f"❌ <b>Error loading card details:</b> {str(e)}\n\n⚠️ Please try again."
    
    def format_card_details(self, card_data: Dict[str, Any], mask_sensitive: bool = True) -> str:
        """
        Format card details for display with optional masking.
        
        Args:
            card_data: Card data dictionary
            mask_sensitive: Whether to mask sensitive information
            
        Returns:
            Formatted card details string
        """
        try:
            # Determine if this is API v3 data
            api_version = card_data.get("api_version", "v1")
            
            if api_version == "v3":
                # API v3 format with comprehensive data
                return self._format_api_v3_card_details(card_data, mask_sensitive)
            else:
                # API v1/v2 format
                return create_card_display(card_data, mask_sensitive=mask_sensitive)
                
        except Exception as e:
            logger.error(f"Error using card display utility: {e}, using simple fallback")
            # Simple fallback for any card format
            return self._format_simple_card_fallback(card_data, mask_sensitive)
    
    def _format_api_v3_card_details(self, card_data: Dict[str, Any], mask_sensitive: bool = True) -> str:
        """
        Format API v3 card details with comprehensive information.
        
        Args:
            card_data: API v3 card data
            mask_sensitive: Whether to mask sensitive information
            
        Returns:
            Formatted card details string
        """
        try:
            lines = []
            
            # Header
            lines.append("💳 <b>Card Details</b>")
            lines.append("")
            
            # Card Information Section
            card_section = []
            
            # Card number
            card_number = card_data.get("card_number", "")
            if card_number:
                if mask_sensitive:
                    masked_number = self._mask_card_number(card_number)
                    card_section.append(f"🔢 <b>Number:</b> <code>{masked_number}</code>")
                else:
                    card_section.append(f"🔢 <b>Number:</b> <code>{card_number}</code>")
            
            # Expiry date
            expiry = card_data.get("expiry_date", "")
            if expiry:
                card_section.append(f"📅 <b>Expiry:</b> <code>{expiry}</code>")
            
            # CVV
            cvv = card_data.get("cvv", "")
            if cvv:
                if mask_sensitive:
                    masked_cvv = "***"
                    card_section.append(f"🔐 <b>CVV:</b> <code>{masked_cvv}</code>")
                else:
                    card_section.append(f"🔐 <b>CVV:</b> <code>{cvv}</code>")
            
            if card_section:
                lines.extend(card_section)
                lines.append("")
            
            # Bank Information Section - bank on one line, brand/type/level on next line
            bank = card_data.get("bank", "")
            if bank and bank != "Unknown Bank":
                lines.append(f"🏛️ <b>{bank}</b>")
                lines.append("")
            
            # Brand, type, and level on next line
            brand_type_level_parts = []
            brand = card_data.get("brand", "")
            if brand:
                brand_type_level_parts.append(f"💳 <b>{brand}</b>")
            
            type_val = card_data.get("type", "")
            if type_val:
                brand_type_level_parts.append(f"📇 <b>{type_val}</b>")
            
            level = card_data.get("level", "")
            if level:
                brand_type_level_parts.append(f"🏷️ <b>{level}</b>")
            
            if brand_type_level_parts:
                lines.append(" • ".join(brand_type_level_parts))
                lines.append("")
            
            # Country on separate line if available
            country = card_data.get("country", "")
            if country and country != "Unknown":
                lines.append(f"🌍 <b>Country:</b> {country}")
                lines.append("")
            
            # Contact Information Section
            contact_section = []
            
            cardholder_name = card_data.get("cardholder_name", "")
            if cardholder_name:
                if mask_sensitive:
                    masked_name = self._mask_name(cardholder_name)
                    contact_section.append(f"👤 <b>Name:</b> {html.escape(masked_name)}")
                else:
                    contact_section.append(f"👤 <b>Name:</b> {html.escape(cardholder_name)}")
            
            email = card_data.get("email", "")
            if email:
                if mask_sensitive:
                    masked_email = self._mask_email(email)
                    contact_section.append(f"📧 <b>Email:</b> {html.escape(masked_email)}")
                else:
                    contact_section.append(f"📧 <b>Email:</b> {html.escape(email)}")
            
            phone = card_data.get("phone", "")
            if phone:
                if mask_sensitive:
                    masked_phone = self._mask_phone(phone)
                    contact_section.append(f"📱 <b>Phone:</b> {html.escape(masked_phone)}")
                else:
                    contact_section.append(f"📱 <b>Phone:</b> {html.escape(phone)}")
            
            address = card_data.get("address", "")
            if address:
                if mask_sensitive:
                    masked_address = self._mask_address(address)
                    contact_section.append(f"🏠 <b>Address:</b> {html.escape(masked_address)}")
                else:
                    contact_section.append(f"🏠 <b>Address:</b> {address}")
            
            if contact_section:
                lines.extend(contact_section)
                lines.append("")
            
            # Additional Information
            additional_section = []
            
            status = card_data.get("status", "")
            if status:
                additional_section.append(f"🔸 <b>Status:</b> {status}")
            
            price = card_data.get("price")
            if price is not None:
                try:
                    price_float = float(price)
                    additional_section.append(f"💰 <b>Price:</b> ${price_float:.2f}")
                except (ValueError, TypeError):
                    pass
            
            features = card_data.get("features", [])
            if features and isinstance(features, list):
                features_str = ", ".join(features)
                additional_section.append(f"✨ <b>Features:</b> {features_str}")
            
            if additional_section:
                lines.extend(additional_section)
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"Error formatting API v3 card details: {e}")
            # Fallback to simple format
            return f"💳 Card Information\n\nStatus: {card_data.get('status', 'Unknown')}\n\nFull details available."

    def _format_simple_card_fallback(self, card_data: Dict[str, Any], mask_sensitive: bool = True) -> str:
        """
        Simple fallback formatter for card data.

        Args:
            card_data: Card data dictionary
            mask_sensitive: Whether to mask sensitive information

        Returns:
            Simple formatted card details string
        """
        try:
            lines = []
            status = card_data.get("status", "Unknown")
            lines.append(f"💳 <b>Card Information</b>")
            lines.append(f"🔸 <b>Status:</b> {status}")

            # Add basic fields if available
            for field, label, icon in [
                ("bank", "Bank", "🏦"),
                ("brand", "Brand", "💳"),
                ("country", "Country", "🌍"),
                ("price", "Price", "💰"),
            ]:
                value = card_data.get(field)
                if value and is_valid_value(value):
                    if field == "price":
                        try:
                            price_float = float(value)
                            lines.append(f"{icon} <b>{label}:</b> ${price_float:.2f}")
                        except (ValueError, TypeError):
                            pass
                    else:
                        lines.append(f"{icon} <b>{label}:</b> {value}")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error in simple card fallback: {e}")
            return "💳 Card Information\n\nError displaying details"

    def _mask_card_number(self, card_number: str) -> str:
        """Mask card number for security"""
        if not card_number:
            return ""

        # Remove spaces and special characters
        clean_number = re.sub(r'[^\d]', '', card_number)

        if len(clean_number) >= 8:
            # Show first 4 and last 4 digits
            return f"{clean_number[:4]} **** **** {clean_number[-4:]}"
        else:
            # For shorter numbers, mask middle
            return f"{clean_number[:2]}****{clean_number[-2:]}"

    def _mask_email(self, email: str) -> str:
        """Partially mask email for privacy"""
        if not email or "@" not in email:
            return email

        try:
            local, domain = email.split("@", 1)
            if len(local) <= 2:
                masked_local = local
            else:
                masked_local = local[0] + "*" * (len(local) - 2) + local[-1]

            return f"{masked_local}@{domain}"
        except Exception:
            return email

    def _mask_phone(self, phone: str) -> str:
        """Mask phone number for privacy"""
        if not phone:
            return ""

        # Extract digits only
        digits = re.sub(r'[^\d]', '', phone)

        if len(digits) >= 10:
            # Show area code and last 2 digits
            return f"({digits[:3]}) ***-**{digits[-2:]}"
        else:
            # For shorter numbers, mask middle
            return f"{digits[:2]}****{digits[-2:]}" if len(digits) >= 4 else phone

    def _mask_name(self, name: str) -> str:
        """Mask cardholder name for privacy"""
        if not name:
            return ""

        parts = name.strip().split()
        if len(parts) == 1:
            # Single name - show first and last character
            if len(parts[0]) <= 2:
                return f"\u200E{parts[0]}"
            return f"\u200E{parts[0][0]}***{parts[0][-1]}"
        else:
            # Multiple names - show first name and mask last name
            first_name = parts[0]
            last_name = parts[-1]
            if len(last_name) <= 2:
                masked_last = last_name
            else:
                masked_last = f"{last_name[0]}***{last_name[-1]}"

            return f"\u200E{first_name} {masked_last}"

    def _mask_address(self, address: str) -> str:
        """Mask address for privacy"""
        if not address:
            return ""

        # Show first part and mask the rest
        parts = address.split(",")
        if len(parts) > 1:
            return f"\u200E{parts[0]}, ***"
        else:
            # For single line address, show first few words
            words = address.split()
            if len(words) > 2:
                return f"\u200E{words[0]} {words[1]} ***"
            else:
                return f"\u200E{address}"

    def format_card_details_compact(self, card_data: Dict[str, Any]) -> str:
        """
        Format card details in a compact format for inline display.

        Args:
            card_data: Card data dictionary

        Returns:
            Compact formatted string
        """
        try:
            lines = []

            # Essential information only
            status = card_data.get("status", "Unknown")
            if status != "Unknown":
                lines.append(f"Status: {status}")

            bank = card_data.get("bank", "")
            if bank and bank != "Unknown Bank":
                lines.append(f"Bank: {bank}")

            brand = card_data.get("brand", "")
            level = card_data.get("level", "")
            if brand or level:
                type_info = " ".join(filter(None, [brand, level]))
                lines.append(f"Type: {type_info}")

            country = card_data.get("country", "")
            if country and country != "Unknown":
                lines.append(f"Country: {country}")

            price = card_data.get("price")
            if price is not None:
                try:
                    price_float = float(price)
                    lines.append(f"Price: ${price_float:.2f}")
                except (ValueError, TypeError):
                    pass

            return "\n".join(lines) if lines else "No details available"

        except Exception as e:
            logger.error(f"Error formatting compact card details: {e}")
            return "Error formatting details"

    def format_complete_card_message(self, card_info: Dict[str, Any], card_id: str, external_order_id: str) -> str:
        """
        Format complete card message with all available information.

        Args:
            card_info: Complete card information
            card_id: Card ID
            external_order_id: External order ID

        Returns:
            Complete formatted message
        """
        try:
            lines = []

            # Header with card identification
            lines.append("💳 <b>Complete Card Details</b>")
            lines.append("")
            lines.append(f"🆔 <b>Card ID:</b> <code>{card_id}</code>")
            lines.append(f"📋 <b>Order ID:</b> <code>{external_order_id}</code>")
            lines.append("")

            # Use the main formatting method
            card_details = self.format_card_details(card_info, mask_sensitive=False)
            lines.append(card_details)

            # Add timestamp
            lines.append("")
            lines.append(f"🕒 <i>Retrieved: {format_date('now')}</i>")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error formatting complete card message: {e}")
            return f"❌ <b>Error formatting card details:</b> {str(e)}"
