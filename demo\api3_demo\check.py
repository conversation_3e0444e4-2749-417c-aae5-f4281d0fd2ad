"""
Check flow (separate script):
- Reuse authenticated session to GET /orders/{order_id}/check?cc_id={cc_id}
- Follow server redirect back to /orders/{order_id}
- Save a structured JSON with both responses in check_response.json
"""

import argparse
import json
from urllib.parse import urljoin

import requests

from login import logger, REFERER, log_request, log_response
from session_manager import get_authenticated_session, save_session_cookies
from add_to_cart import _response_to_jsonable, follow_redirect_if_any


def _order_detail_url(order_id: str) -> str:
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, f"orders/{order_id}")


def _order_check_url(order_id: str) -> str:
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, f"orders/{order_id}/check")


def get_check(
    session: requests.Session, order_id: str, cc_id: str, referer: str
) -> requests.Response:
    url = _order_check_url(order_id)
    logger.info("GET check for order %s | cc_id=%s", order_id, cc_id)
    r = session.get(
        url,
        params={"cc_id": cc_id},
        headers={
            "Referer": referer,
        },
        allow_redirects=False,
        timeout=60,
    )
    log_request(r)
    log_response(r)
    return r


def save_check_result(
    get_resp: requests.Response,
    final_resp: requests.Response,
    out_path: str = "check_response.json",
) -> str:
    data = _response_to_jsonable(final_resp)
    
    # Print JSON response to console
    print("\n" + "=" * 60)
    print("CHECK RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")
    
    # Save to file
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return out_path


def main(order_id: str, cc_id: str) -> None:
    session = get_authenticated_session(logger)

    # Build the referer as the order details page
    referer = _order_detail_url(order_id)

    # 1) Fire the check GET with cc_id
    get_resp = get_check(session, order_id, cc_id, referer)
    # 2) Follow any redirect back to the order details
    final_resp = follow_redirect_if_any(session, get_resp)

    out = save_check_result(get_resp, final_resp)
    logger.info("Saved check response JSON to: %s", out)

    # Persist cookies for reuse
    save_session_cookies(session, logger=logger)


if __name__ == "__main__":
    p = argparse.ArgumentParser(description="Check an item on an order")
    p.add_argument("--order-id", required=True, help="Order id (e.g., 4W7vR5i9)")
    p.add_argument("--cc-id", required=True, help="Card/item id to check")
    args = p.parse_args()
    main(order_id=args.order_id, cc_id=args.cc_id)
