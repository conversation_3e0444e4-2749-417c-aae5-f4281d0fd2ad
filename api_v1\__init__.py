"""
API v1 - Unified API Management System

This module provides the consolidated API v1 system with unified
configuration, utility functions, and monitoring.

Note: The HTTP client (UnifiedHTTPClient) has been removed as it was deprecated.
Use shared_api.http.client.ConfigurableHTTPClient instead.
"""

from .services.api_config import (
    get_api_config_service,
    get_unified_api_config_service,
    UnifiedAPIConfigurationService,
)
from .utils.authentication import AuthenticationHelper
from .utils.encryption import EncryptionService
from .utils.error_handling import (
    get_error_handler,
    UnifiedErrorHandler,
    ErrorCategory,
)
from .core.base import BaseService, BaseResponse
from .core.constants import (
    DEFAULT_REQUEST_TIMEOUT,
    DEFAULT_MAX_RETRIES,
    DEFAULT_CACHE_TTL,
)

try:
    from .monitoring import service_status_router
except ImportError:
    service_status_router = None

from .examples import create_api_v1_configuration, API_V1_CONFIG_EXAMPLE

__version__ = "1.0.0"

__all__ = [
    # Services
    "get_api_config_service",
    "get_unified_api_config_service",
    "UnifiedAPIConfigurationService",
    # Utilities
    "AuthenticationHelper",
    "EncryptionService",
    "get_error_handler",
    "UnifiedErrorHandler",
    "ErrorCategory",
    # Core
    "BaseService",
    "BaseResponse",
    # Constants
    "DEFAULT_REQUEST_TIMEOUT",
    "DEFAULT_MAX_RETRIES",
    "DEFAULT_CACHE_TTL",
    # Monitoring (optional)
    "service_status_router",
    # Examples
    "create_api_v1_configuration",
    "API_V1_CONFIG_EXAMPLE",
]
