"""
FSM states for payment processing

This module contains all state definitions for payment flows.
"""

# Import all state classes
try:
    from .payment_states import (
        DepositStates,
        PaymentVerificationStates,
        PaymentStates,
    )
except ImportError as e:
    # Create placeholder classes
    class DepositStates:
        """Placeholder for DepositStates"""
        pass
    
    class PaymentVerificationStates:
        """Placeholder for PaymentVerificationStates"""
        pass
    
    class PaymentStates:
        """Placeholder for PaymentStates"""
        pass

# Export all classes
__all__ = [
    'DepositStates',
    'PaymentVerificationStates',
    'PaymentStates',
]
