"""
Unified Logging Utilities

Consolidates logging functionality from across the codebase
into reusable utilities with consistent formatting and context.
"""

import json
import logging
import time
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from utils.central_logger import get_logger

logger = get_logger()

from .authentication import AuthenticationHelper


class LogLevel(str, Enum):
    """Log levels for API logging"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class LogContext:
    """Context information for API logging"""
    request_id: str
    user_id: Optional[str] = None
    operation: Optional[str] = None
    api_name: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class APILogger:
    """
    Unified API logger for consistent logging across all services.
    
    Consolidates logging patterns from:
    - utils/api_logging.py
    - external_api_service.py
    - Various service files
    """

    def __init__(self, logger_name: str, log_level: LogLevel = LogLevel.INFO):
        self.logger_name = logger_name
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(getattr(logging, log_level.value))
        # Ensure the logger actually outputs to console even if global setup wasn't called
        if not self.logger.handlers:
            sh = logging.StreamHandler()
            sh.setLevel(getattr(logging, log_level.value))
            fmt = logging.Formatter("%(asctime)s [%(levelname)s] %(name)s: %(message)s")
            sh.setFormatter(fmt)
            self.logger.addHandler(sh)
            # Avoid double-propagation to root if handlers added later
            self.logger.propagate = False
        self._auth_helper = AuthenticationHelper()

    def create_context(
        self,
        user_id: Optional[str] = None,
        operation: Optional[str] = None,
        api_name: Optional[str] = None,
    ) -> LogContext:
        """Create a logging context for request tracking"""
        request_id = f"{int(time.time() * 1000)}"  # Simple timestamp-based ID
        return LogContext(
            request_id=request_id,
            user_id=user_id,
            operation=operation,
            api_name=api_name,
        )

    def log_request(
        self,
        context: LogContext,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        query_params: Optional[Dict[str, Any]] = None,
        body: Optional[Any] = None,
        timeout: Optional[float] = None,
        retry_count: int = 0,
    ):
        """Suppress request logging to keep output response-only."""
        return

    def log_response(
        self,
        context: LogContext,
        status_code: int,
        headers: Optional[Dict[str, str]] = None,
        body: Optional[Any] = None,
        response_time_ms: Optional[int] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ):
        """Log only the response body (no headers or metadata)."""
        safe_body = self._mask_sensitive_body(body)
        text = (
            json.dumps(safe_body, ensure_ascii=False)
            if isinstance(safe_body, dict)
            else (safe_body or "")
        )
        max_len = 4000
        if isinstance(text, str) and len(text) > max_len:
            text = text[:max_len] + "..."
        prefix = f"[{context.operation or 'http'}] "
        line = prefix + (text if isinstance(text, str) else str(text))
        if success:
            self.logger.info(line)
        else:
            self.logger.warning(line)

    def log_authentication_context(
        self,
        context: LogContext,
        auth_method: str,
        token_valid: bool,
        user_permissions: Optional[list] = None,
        rate_limit_info: Optional[Dict[str, Any]] = None,
    ):
        """Log authentication context"""
        log_data = {
            "event_type": "authentication_context",
            "context": asdict(context),
            "auth": {
                "method": auth_method,
                "token_valid": token_valid,
                "user_permissions": user_permissions or [],
                "rate_limit_info": rate_limit_info or {},
            },
        }

        self.logger.debug("Authentication context", extra={"log_data": log_data})

    def log_error(
        self,
        context: LogContext,
        error: Exception,
        operation: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
    ):
        """Log error with context"""
        log_data = {
            "event_type": "error",
            "context": asdict(context),
            "error": {
                "type": type(error).__name__,
                "message": str(error),
                "operation": operation,
                "additional_data": additional_data or {},
            },
        }

        self.logger.error(
            f"Error in {operation or 'unknown operation'}: {str(error)}",
            extra={"log_data": log_data},
            exc_info=True,
        )

    def log_performance(
        self,
        context: LogContext,
        operation: str,
        duration_ms: int,
        success: bool = True,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Log performance metrics"""
        log_data = {
            "event_type": "performance",
            "context": asdict(context),
            "performance": {
                "operation": operation,
                "duration_ms": duration_ms,
                "success": success,
                "metadata": metadata or {},
            },
        }

        self.logger.info(
            f"Performance: {operation} completed in {duration_ms}ms",
            extra={"log_data": log_data},
        )

    def log_security_event(
        self,
        context: LogContext,
        event_type: str,
        severity: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        """Log security-related events"""
        log_data = {
            "event_type": "security_event",
            "context": asdict(context),
            "security": {
                "event_type": event_type,
                "severity": severity,
                "details": details or {},
            },
        }

        log_level = getattr(logging, severity.upper(), logging.WARNING)
        self.logger.log(
            log_level,
            f"Security Event: {event_type}",
            extra={"log_data": log_data},
        )

    def _mask_sensitive_body(self, body: Optional[Any]) -> Optional[Any]:
        """Mask sensitive data in request/response body"""
        if not body:
            return body

        if isinstance(body, dict):
            return self._auth_helper.mask_sensitive_data(body)
        elif isinstance(body, str):
            try:
                parsed = json.loads(body)
                if isinstance(parsed, dict):
                    masked = self._auth_helper.mask_sensitive_data(parsed)
                    return json.dumps(masked)
            except json.JSONDecodeError:
                pass

        return body


# Global logger instances
_logger_instances: Dict[str, APILogger] = {}


def get_api_logger(name: str, log_level: LogLevel = LogLevel.INFO) -> APILogger:
    """Get or create an API logger instance"""
    if name not in _logger_instances:
        _logger_instances[name] = APILogger(name, log_level)
    return _logger_instances[name]


def setup_logging(log_level: str = "INFO", log_format: Optional[str] = None):
    """Setup logging configuration for the entire API v1 system"""
    if log_format is None:
        log_format = (
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("api_v1.log"),
        ],
    )

    # Set specific log levels for noisy libraries
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
