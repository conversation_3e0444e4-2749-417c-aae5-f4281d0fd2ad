"""
Purchase confirmation and processing handlers
"""

from __future__ import annotations

import asyncio
from typing import Optional

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from middleware import attach_common_middlewares
from services.user_service import UserService
from services.cart_service import CartService
from utils.texts import DEMO_WATERMARK
from utils.ui_manager import ui_manager
from utils.ui_components import create_message, MessageType
from utils.enhanced_keyboards import (
    create_enhanced_keyboard,
    KeyboardStyle,
    ButtonPriority,
)
from utils.loading_animations import LoadingStages, PURCHASE_STAGES
from config.settings import get_settings

from utils.central_logger import get_logger

logger = get_logger()


class PurchaseHandlers:
    """Handlers for purchase confirmation and processing"""

    def __init__(self):
        self.user_service = UserService()
        self.cart_service = CartService()

    async def cb_purchase_confirm(self, callback: CallbackQuery) -> None:
        """Handle purchase confirmation - Queue checkout for processing"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            user_id = str(db_user.id)
            telegram_user_id = user.id

            # Get cart contents for validation
            cart_contents = await self.cart_service.get_cart_contents(user_id)

            if cart_contents.get("is_empty", True):
                empty_msg = create_message(MessageType.INFO)
                empty_msg.set_title("Cart Empty", "🛒")
                empty_msg.add_content("Your cart is empty. Add some items first!")
                empty_kb = create_enhanced_keyboard().set_style(
                    KeyboardStyle.COMPACT, 2
                )
                empty_kb.add_button(
                    "🔎 Browse Cards", "menu:browse", ButtonPriority.SECONDARY
                )
                empty_kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    empty_msg.build(add_watermark=False),
                    empty_kb.build(),
                )
                await callback.answer()
                return

            # Show loading stages concurrently with purchase processing
            success, message, job_id = await LoadingStages.run_concurrent_loading(
                callback,
                PURCHASE_STAGES,
                self.cart_service.queue_checkout(user_id, telegram_user_id),
                operation_name="Purchase Processing"
            )

            if success:
                # Enhanced queue confirmation with better messaging
                confirm = create_message(MessageType.SUCCESS)
                confirm.set_title("🎉 Order Successfully Queued!", "⏳")
                confirm.add_content(message)
                confirm.add_list_section(
                    "📋 What happens next",
                    [
                        "🚀 Your order enters the secure processing queue",
                        "📱 You'll receive real-time progress notifications",
                        "💳 Payment will be captured when processing begins",
                        "⚡ You can cancel anytime while queued",
                    ],
                    "🔔",
                )

                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                if job_id:
                    kb.add_button(
                        "❌ Cancel Order",
                        f"purchase:cancel:{job_id}",
                        ButtonPriority.DANGER,
                    )
                    kb.add_button(
                        "📊 Queue Status",
                        f"purchase:status:{job_id}",
                        ButtonPriority.SECONDARY,
                    )
                kb.add_button(
                    "📜 Order History", "menu:history", ButtonPriority.SECONDARY
                )
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    confirm.build(add_watermark=False),
                    kb.build(),
                )
                await callback.answer("✅ Order queued!")

                # Start background polling to keep status fresh while queued/processing
                if job_id:
                    try:
                        chat_id = callback.message.chat.id
                        message_id = callback.message.message_id
                        asyncio.create_task(
                            self._poll_status_updates(chat_id, message_id, job_id)
                        )
                    except Exception:
                        pass

            else:
                # Error message
                err = create_message(MessageType.ERROR)
                err.set_title("Cannot Queue Order", "❌")
                err.add_content(message)
                err.add_section(
                    "Try this",
                    "Check your wallet balance and cart contents, then try again.",
                    "💡",
                )

                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button(
                    "💼 Check Wallet", "menu:wallet", ButtonPriority.SECONDARY
                )
                kb.add_button(
                    "🛒 View Cart", "local:cart:view", ButtonPriority.SECONDARY
                )
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    err.build(add_watermark=False),
                    kb.build(),
                )
                await callback.answer("❌ Queue failed", show_alert=True)

        except Exception as e:
            logger.error(f"Error in purchase confirmation: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_purchase_cancel(self, callback: CallbackQuery) -> None:
        """Handle purchase cancellation"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract job ID from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid cancel request", show_alert=True)
                return

            job_id = parts[2]

            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Import checkout queue service
            from services.checkout_queue_service import CheckoutQueueService

            queue_service = CheckoutQueueService()
            success, message = await queue_service.cancel_job(job_id)

            if success:
                msg = create_message(MessageType.SUCCESS)
                msg.set_title("Order Cancelled", "✅")
                msg.add_content(message)
                msg.add_section(
                    "What's next?",
                    "You can browse cards and create a new order anytime.",
                    "💡",
                )

                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button(
                    "🔎 Browse Cards", "menu:browse", ButtonPriority.SECONDARY
                )
                kb.add_button(
                    "🛒 View Cart", "local:cart:view", ButtonPriority.SECONDARY
                )
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    msg.build(add_watermark=False),
                    kb.build(),
                )
                await callback.answer("✅ Order cancelled")
            else:
                msg = create_message(MessageType.ERROR)
                msg.set_title("Cannot Cancel Order", "❌")
                msg.add_content(message)
                msg.add_section(
                    "Try this",
                    "Check the order status or contact support if you need help.",
                    "💡",
                )

                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button(
                    "📊 Check Status",
                    f"purchase:status:{job_id}",
                    ButtonPriority.SECONDARY,
                )
                kb.add_button(
                    "📜 Order History", "menu:history", ButtonPriority.SECONDARY
                )
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )

                await ui_manager.edit_message_safely(
                    callback,
                    msg.build(add_watermark=False),
                    kb.build(),
                )
                await callback.answer("❌ Cannot cancel", show_alert=True)

        except Exception as e:
            logger.error(f"Error cancelling purchase: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_purchase_status(self, callback: CallbackQuery) -> None:
        """Handle purchase status check"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Extract job ID from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid status request", show_alert=True)
                return

            job_id = parts[2]

            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Import checkout queue service
            from services.checkout_queue_service import CheckoutQueueService

            queue_service = CheckoutQueueService()
            job = await queue_service.get_job_status(job_id)

            if not job:
                msg = create_message(MessageType.ERROR)
                msg.set_title("Job Not Found", "❌")
                msg.add_content(f"Job #{job_id[:8]} was not found or has expired.")
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )
                await ui_manager.edit_message_safely(
                    callback, msg.build(add_watermark=False), kb.build()
                )
                await callback.answer()
                return

            # Build and render the status view
            text, markup = await self._build_status_view(queue_service, job, job_id)
            await ui_manager.edit_message_safely(
                callback,
                text,
                markup,
            )

            # Start/continue polling if queued or processing
            state = job.status.value
            if state in ("queued", "processing"):
                try:
                    chat_id = callback.message.chat.id
                    message_id = callback.message.message_id
                    asyncio.create_task(
                        self._poll_status_updates(chat_id, message_id, job_id)
                    )
                except Exception:
                    pass

            await callback.answer()

        except Exception as e:
            logger.error(f"Error checking purchase status: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    # --- Helper utilities for status updates ---

    async def _build_status_view(self, queue_service, job, job_id: str):
        """Compose status message text and keyboard for a job."""
        try:
            emoji_map = {
                "queued": "⏳",
                "processing": "🔄",
                "completed": "✅",
                "failed": "❌",
                "cancelled": "❌",
            }

            state = job.status.value
            emoji = emoji_map.get(state, "❓")
            status_name = state.title()

            # Enhanced details per state with better formatting
            if state == "queued":
                queue_position = await queue_service._get_queue_position(job_id)
                per_job_seconds = 30
                estimated_wait = max(0, queue_position * per_job_seconds)
                details = [
                    f"📍 <b>Queue Position:</b> #{queue_position}",
                    f"⏱️ <b>Estimated Wait:</b> {estimated_wait // 60}m {estimated_wait % 60}s",
                    "💡 You can cancel while the order is queued",
                ]
            elif state == "processing":
                details = [
                    "⚡ <b>Processing your order securely...</b>",
                    "🔒 <b>Payment verification:</b> <code>In Progress</code>",
                    "📦 <b>Digital cards:</b> <code>Being Prepared</code>",
                    "🔔 <b>Notification:</b> <i>You'll receive updates when complete</i>",
                ]
            elif state == "completed":
                details = [
                    "🎉 <b>Order completed successfully!</b>",
                    "✅ <b>Digital cards:</b> <code>Ready for Use</code>",
                    "💳 <b>Access:</b> <i>View and manage your cards anytime</i>",
                    "📋 <b>Receipt:</b> <i>Access order details and history</i>",
                ]
            elif state == "failed":
                details = [
                    f"❌ <b>Order failed:</b> {job.last_error or 'Unknown error'}",
                    "🔄 You can try again from your cart",
                    "💬 Contact support if the issue persists",
                ]
            elif state == "cancelled":
                details = ["🚫 Order was cancelled by user"]
            else:
                details = ["❓ Status unknown - please refresh"]

            msg = create_message(MessageType.INFO)
            msg.set_title(f"Order Status: {status_name}", emoji)
            msg.add_list_section("Status Details", details, "📊")

            kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
            kb.add_button(
                "🔄 Refresh", f"purchase:status:{job_id}", ButtonPriority.SECONDARY
            )

            if state == "queued":
                kb.add_button(
                    "❌ Cancel Order",
                    f"purchase:cancel:{job_id}",
                    ButtonPriority.DANGER,
                )
            elif state == "completed":
                # Prefer direct single-card view when we know the purchased card id
                job_metadata = getattr(job, "metadata", {})
                purchased_card_ids = job_metadata.get("purchased_card_ids", [])
                external_order_id = job_metadata.get("external_order_id")
                transaction_id = job_metadata.get("transaction_id")
                api_version = job_metadata.get("api_version", "v1")  # Get API version from metadata

                if purchased_card_ids:
                    first_card_id = purchased_card_ids[0]
                    
                    # For API v1, use view_details to show same view as My Orders
                    # For other APIs, use view_card for direct view
                    if api_version == 'v1':
                        kb.add_button(
                            "🃏 View Card",
                            f"orders:view_details:{first_card_id}",
                            ButtonPriority.PRIMARY,
                        )
                        logger.info(f"✅ [Purchase Complete] Using view_details for API v1 card: {first_card_id}")
                    else:
                        # Use full card ID to ensure proper lookup for other APIs
                        kb.add_button(
                            "🃏 View Card",
                            f"orders:view_card:{first_card_id}",
                            ButtonPriority.PRIMARY,
                        )
                        logger.info(f"✅ [Purchase Complete] Using view_card for API {api_version} card: {first_card_id}")
                else:
                    # Fallback to recent cards list if we don't have the card id in metadata
                    lookup_id = transaction_id if transaction_id else job_id
                    kb.add_button(
                        "🃏 View Cards",
                        f"orders:view_recent_cards:{lookup_id}",
                        ButtonPriority.PRIMARY,
                    )

                # Add direct unmask button when we know order+card
                if external_order_id and purchased_card_ids:
                    first_card_id = purchased_card_ids[0]
                    kb.add_button(
                        "🔓 Unmask Cards",
                        f"orders:unmask:{external_order_id}:{first_card_id}",
                        ButtonPriority.PRIMARY,
                    )
                
                kb.add_button(
                    "🧾 View Receipt",
                    f"orders:receipt:{job_id}",
                    ButtonPriority.SECONDARY,
                )

            kb.add_button("📜 Orders", "menu:orders", ButtonPriority.SECONDARY)
            kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)

            return msg.build(add_watermark=False), kb.build()
        except Exception:
            # Fallback minimal UI
            return (
                "❌ Error building status view",
                InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="menu:main"
                            )
                        ]
                    ]
                ),
            )

    async def _poll_status_updates(self, chat_id: int, message_id: int, job_id: str):
        """Periodically refresh the status message while the job is queued/processing."""
        try:
            settings = get_settings()
            from aiogram import Bot
            from services.checkout_queue_service import CheckoutQueueService

            bot = Bot(token=settings.BOT_TOKEN)
            queue_service = CheckoutQueueService()

            # Poll up to ~90 seconds, every 10s
            for _ in range(9):
                try:
                    job = await queue_service.get_job_status(job_id)
                    if not job:
                        break

                    text, markup = await self._build_status_view(
                        queue_service, job, job_id
                    )
                    await bot.edit_message_text(
                        chat_id=chat_id,
                        message_id=message_id,
                        text=text,
                        reply_markup=markup,
                        parse_mode="HTML",
                    )

                    state = job.status.value
                    if state not in ("queued", "processing"):
                        break
                except Exception:
                    # Ignore transient errors and keep polling
                    pass

                await asyncio.sleep(10)

            await bot.session.close()
        except Exception:
            # Silent failure to avoid impacting user experience
            pass


def get_purchase_router() -> Router:
    """Create and return purchase router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = PurchaseHandlers()

    # Purchase callback handlers
    router.callback_query.register(
        handlers.cb_purchase_confirm, F.data.startswith("purchase:confirm")
    )
    router.callback_query.register(
        handlers.cb_purchase_cancel, F.data.startswith("purchase:cancel")
    )
    router.callback_query.register(
        handlers.cb_purchase_status, F.data.startswith("purchase:status:")
    )

    logger.debug("Purchase handlers registered")
    return router
