"""
API v1 Checkout Example

Demonstrates the complete checkout workflow including:
- Viewing cart
- Processing checkout
- Database operations (purchases, wallet)
- Error handling
"""

import asyncio
from api_v1.services.cart_service import get_cart_service


async def example_complete_checkout_flow():
    """Complete checkout workflow example"""
    print("\n" + "=" * 60)
    print("API v1 Complete Checkout Flow Example")
    print("=" * 60)
    
    cart_service = get_cart_service()
    user_id = "test_user_123"  # Replace with actual user ID
    
    try:
        # Step 1: View cart before checkout
        print("\n📋 Step 1: Viewing cart...")
        cart_result = await cart_service.view_cart(user_id=user_id)
        
        if cart_result.success:
            print(f"✓ Cart retrieved")
            print(f"  Items: {cart_result.data['item_count']}")
            print(f"  Total: ${cart_result.data['total_price']:.2f}")
            
            # Show cart items
            for i, item in enumerate(cart_result.data['items'], 1):
                print(f"  {i}. {item.get('brand')} {item.get('bin')} - ${item.get('price')}")
        else:
            print(f"✗ Failed to view cart: {cart_result.message}")
            return
        
        # Check if cart is empty
        if cart_result.data['item_count'] == 0:
            print("\n⚠️  Cart is empty, nothing to checkout")
            return
        
        # Step 2: Process checkout
        print("\n💳 Step 2: Processing checkout...")
        checkout_result = await cart_service.checkout_cart(
            user_id=user_id,
            clear_cart_on_success=True
        )
        
        if checkout_result.success:
            print(f"✓ Checkout completed successfully!")
            print(f"\n📦 Checkout Summary:")
            print(f"  Items purchased: {checkout_result.data['items_count']}")
            print(f"  Total paid: ${checkout_result.data['total_price']:.2f}")
            print(f"  New balance: ${checkout_result.data['new_balance']:.2f}")
            print(f"  Transaction ID: {checkout_result.data['transaction_id']}")
            
            if checkout_result.data.get('api_order_id'):
                print(f"  API Order ID: {checkout_result.data['api_order_id']}")
            
            print(f"\n🎫 Purchases created:")
            for i, purchase in enumerate(checkout_result.data['purchases'], 1):
                print(f"  {i}. SKU: {purchase['sku']}")
                print(f"     Product ID: {purchase['external_product_id']}")
                print(f"     Price: ${purchase['price']:.2f}")
                print(f"     Type: {purchase['product_type']}")
        else:
            print(f"✗ Checkout failed: {checkout_result.message}")
            print(f"  Error code: {checkout_result.error_code}")
            
            # Show additional error details if available
            if checkout_result.data:
                print(f"  Details: {checkout_result.data}")
        
        # Step 3: Verify cart is empty after checkout
        if checkout_result.success:
            print("\n🔍 Step 3: Verifying cart is empty...")
            verify_result = await cart_service.view_cart(user_id=user_id)
            
            if verify_result.success:
                if verify_result.data['item_count'] == 0:
                    print("✓ Cart successfully cleared")
                else:
                    print(f"⚠️  Cart still has {verify_result.data['item_count']} items")
        
    except Exception as e:
        print(f"\n❌ Error during checkout: {e}")
        import traceback
        traceback.print_exc()


async def example_checkout_with_error_handling():
    """Example with comprehensive error handling"""
    print("\n" + "=" * 60)
    print("Checkout with Error Handling Example")
    print("=" * 60)
    
    cart_service = get_cart_service()
    user_id = "test_user_123"
    
    try:
        print("\n💳 Attempting checkout...")
        result = await cart_service.checkout_cart(user_id=user_id)
        
        if result.success:
            print("✅ SUCCESS!")
            print(f"   {result.message}")
            print(f"   Purchased: {result.data['items_count']} items")
            print(f"   Total: ${result.data['total_price']:.2f}")
        else:
            # Handle different error types
            error_code = result.error_code
            
            if error_code == "EMPTY_CART":
                print("⚠️  Your cart is empty. Add items before checkout.")
            elif error_code == "INSUFFICIENT_BALANCE":
                print("💰 Insufficient balance.")
                print(f"   {result.message}")
            elif error_code == "API_CHECKOUT_FAILED":
                print("🌐 API checkout failed.")
                print(f"   {result.message}")
            elif error_code == "WALLET_NOT_FOUND":
                print("💳 Wallet not found. Please contact support.")
            else:
                print(f"❌ Checkout failed: {result.message}")
                print(f"   Error code: {error_code}")
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def example_view_purchased_items():
    """View items after purchase"""
    print("\n" + "=" * 60)
    print("View Purchased Items Example")
    print("=" * 60)
    
    cart_service = get_cart_service()
    user_id = "test_user_123"
    
    try:
        # List orders to see purchases
        print("\n📦 Fetching your orders...")
        orders_result = await cart_service.list_orders(
            category="hq",
            page=1,
            limit=5,
            user_id=user_id
        )
        
        if orders_result.success:
            orders = orders_result.data['orders']
            print(f"✓ Found {orders_result.data['total_count']} total orders")
            
            if orders:
                print(f"\n📋 Recent Orders:")
                for i, order in enumerate(orders, 1):
                    print(f"\n{i}. Order #{order['_id']}")
                    print(f"   Status: {order['status']}")
                    print(f"   Price: ${order['price']}")
                    print(f"   Card: {order.get('brand')} {order.get('bin')}")
                    print(f"   Country: {order.get('country')}")
                    print(f"   Created: {order.get('createdAt')}")
                    
                    # View first order details
                    if i == 1:
                        print(f"\n   📄 Viewing details...")
                        detail_result = await cart_service.view_order(
                            order_id=order['_id'],
                            category="hq",
                            user_id=user_id
                        )
                        
                        if detail_result.success:
                            order_detail = detail_result.data['order']
                            print(f"   CC: {order_detail.get('cc', 'N/A')}")
                            print(f"   Exp: {order_detail.get('exp', 'N/A')}")
                            print(f"   CVV: {order_detail.get('cvv', 'N/A')}")
            else:
                print("📦 No orders yet")
        else:
            print(f"✗ Failed to fetch orders: {orders_result.message}")
    
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_checkout_simulation():
    """Simulate a realistic checkout scenario"""
    print("\n" + "=" * 60)
    print("Realistic Checkout Simulation")
    print("=" * 60)
    
    cart_service = get_cart_service()
    user_id = "test_user_456"  # Simulated user
    
    print("\n📝 Simulation Steps:")
    print("1. Check cart contents")
    print("2. Validate balance")
    print("3. Process checkout")
    print("4. Verify purchase")
    
    try:
        # Step 1: Check cart
        print("\n1️⃣  Checking cart...")
        cart = await cart_service.view_cart(user_id=user_id)
        
        if not cart.success or cart.data['item_count'] == 0:
            print("   ⚠️  Cart is empty - simulation cannot proceed")
            print("   Tip: Add items to cart first using the add_to_cart endpoint")
            return
        
        print(f"   ✓ Cart has {cart.data['item_count']} items")
        print(f"   ✓ Total: ${cart.data['total_price']:.2f}")
        
        # Step 2: Simulate balance check (this happens in checkout_processor)
        print("\n2️⃣  Validating balance...")
        print("   ✓ Balance check will be performed during checkout")
        
        # Step 3: Process checkout
        print("\n3️⃣  Processing checkout...")
        result = await cart_service.checkout_cart(user_id=user_id)
        
        if result.success:
            print("   ✅ Checkout successful!")
            
            # Step 4: Verify
            print("\n4️⃣  Verifying purchase...")
            print(f"   ✓ {result.data['items_count']} items purchased")
            print(f"   ✓ Transaction ID: {result.data['transaction_id']}")
            print(f"   ✓ New balance: ${result.data['new_balance']:.2f}")
            
            print("\n🎉 Simulation completed successfully!")
        else:
            print(f"   ❌ Checkout failed: {result.message}")
    
    except Exception as e:
        print(f"\n❌ Simulation error: {e}")


async def main():
    """Run all examples"""
    print("=" * 60)
    print("API v1 Checkout Examples")
    print("=" * 60)
    
    try:
        # Run examples
        await example_complete_checkout_flow()
        await example_checkout_with_error_handling()
        await example_view_purchased_items()
        await example_checkout_simulation()
        
        print("\n" + "=" * 60)
        print("All examples completed!")
        print("=" * 60)
    
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

