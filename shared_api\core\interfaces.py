"""
Shared API Interfaces and Protocols

Defines the contracts that API clients and configurations must implement
to work with the shared API system.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Protocol, runtime_checkable
from datetime import datetime

from .constants import HTT<PERSON>ethod, AuthenticationType


@runtime_checkable
class APIConfigProtocol(Protocol):
    """Protocol defining the interface for API configurations"""
    
    name: str
    base_url: str
    endpoints: Dict[str, Any]
    authentication: Dict[str, Any]
    default_headers: Dict[str, str]
    timeout: int
    max_retries: int
    
    def get_endpoint_url(self, endpoint_name: str) -> str:
        """Get the full URL for a specific endpoint"""
        ...
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for requests"""
        ...
    
    def validate(self) -> bool:
        """Validate the configuration"""
        ...


@runtime_checkable
class APIClientProtocol(Protocol):
    """Protocol defining the interface for API clients"""
    
    config: APIConfigProtocol
    
    async def request(
        self,
        endpoint: str,
        method: HTTPMethod = HTTPMethod.GET,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Make an API request"""
        ...
    
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a GET request"""
        ...
    
    async def post(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a POST request"""
        ...
    
    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a PUT request"""
        ...
    
    async def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a DELETE request"""
        ...
    
    async def health_check(self) -> bool:
        """Check if the API is healthy"""
        ...
    
    async def close(self) -> None:
        """Close the client and clean up resources"""
        ...


class BaseAPIConfiguration(ABC):
    """Abstract base class for API configurations"""
    
    def __init__(
        self,
        name: str,
        base_url: str,
        authentication: Optional[Dict[str, Any]] = None,
        default_headers: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        max_retries: int = 3,
        **kwargs
    ):
        self.name = name
        self.base_url = base_url.rstrip('/')
        self.authentication = authentication or {}
        self.default_headers = default_headers or {}
        self.timeout = timeout
        self.max_retries = max_retries
        self.created_at = datetime.utcnow()
        
        # Store additional configuration
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @abstractmethod
    def get_endpoint_url(self, endpoint_name: str) -> str:
        """Get the full URL for a specific endpoint"""
        pass
    
    @abstractmethod
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for requests"""
        pass
    
    @abstractmethod
    def validate(self) -> bool:
        """Validate the configuration"""
        pass
    
    def get_request_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get combined headers for requests"""
        headers = self.default_headers.copy()
        auth_headers = self.get_auth_headers()
        headers.update(auth_headers)
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers


class BaseAPIClient(ABC):
    """Abstract base class for API clients"""
    
    def __init__(self, config: APIConfigProtocol):
        self.config = config
        self._session = None
        self._closed = False
    
    @abstractmethod
    async def _make_request(
        self,
        method: HTTPMethod,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Make the actual HTTP request"""
        pass
    
    async def request(
        self,
        endpoint: str,
        method: HTTPMethod = HTTPMethod.GET,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Make an API request to a specific endpoint"""
        if self._closed:
            raise RuntimeError("Client has been closed")
        
        url = self.config.get_endpoint_url(endpoint)
        request_headers = self.config.get_request_headers(headers)
        
        return await self._make_request(
            method=method,
            url=url,
            headers=request_headers,
            params=params,
            data=data,
            **kwargs
        )
    
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a GET request"""
        return await self.request(endpoint, HTTPMethod.GET, **kwargs)
    
    async def post(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a POST request"""
        return await self.request(endpoint, HTTPMethod.POST, **kwargs)
    
    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a PUT request"""
        return await self.request(endpoint, HTTPMethod.PUT, **kwargs)
    
    async def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a DELETE request"""
        return await self.request(endpoint, HTTPMethod.DELETE, **kwargs)
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the API is healthy"""
        pass
    
    async def close(self) -> None:
        """Close the client and clean up resources"""
        self._closed = True
        if self._session:
            await self._session.close()
            self._session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
