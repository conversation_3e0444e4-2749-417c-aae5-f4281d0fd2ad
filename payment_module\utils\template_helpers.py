"""
Template helpers for payment processing.

This module provides template management and text formatting utilities.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging
import re

logger = logging.getLogger(__name__)

# Global template cache
_template_cache = {}
_template_dir = Path(__file__).parent.parent / "templates"


def format_text(template_file: str, key: str, default: str = "", **kwargs) -> str:
    """
    Format text from template file with variables.
    
    Args:
        template_file: Name of template file (e.g., "payment.json")
        key: Key in the template file
        default: Default text if key not found
        **kwargs: Variables to substitute in the text
        
    Returns:
        str: Formatted text
    """
    try:
        # Load template file
        template_data = _load_template(template_file)
        
        # Get text from template
        text = template_data.get(key, default)
        
        if not text:
            return default
            
        # Format with variables
        try:
            return text.format(**kwargs)
        except (KeyError, ValueError) as e:
            logger.warning(f"Error formatting text for {template_file}.{key}: {e}")
            return text
            
    except Exception as e:
        logger.error(f"Error loading template {template_file}: {e}")
        return default


def _load_template(template_file: str) -> Dict[str, Any]:
    """Load template file from cache or disk."""
    global _template_cache
    
    if template_file in _template_cache:
        return _template_cache[template_file]
    
    try:
        template_path = _template_dir / template_file
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
                _template_cache[template_file] = template_data
                return template_data
        else:
            logger.warning(f"Template file not found: {template_path}")
            return {}
            
    except Exception as e:
        logger.error(f"Error loading template {template_file}: {e}")
        return {}


def update_text(template_file: str, key: str, value: str) -> bool:
    """
    Update text in template file.
    
    Args:
        template_file: Name of template file
        key: Key to update
        value: New value
        
    Returns:
        bool: True if successful
    """
    try:
        template_path = _template_dir / template_file
        
        # Load existing data
        template_data = {}
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
        
        # Update the key
        template_data[key] = value
        
        # Save back to file
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2, ensure_ascii=False)
        
        # Update cache
        global _template_cache
        _template_cache[template_file] = template_data
        
        return True
        
    except Exception as e:
        logger.error(f"Error updating template {template_file}.{key}: {e}")
        return False


def clear_template_cache():
    """Clear template cache."""
    global _template_cache
    _template_cache.clear()
    logger.info("Template cache cleared")


def get_template_keys(template_file: str) -> list:
    """
    Get all keys from a template file.
    
    Args:
        template_file: Name of template file
        
    Returns:
        list: List of keys
    """
    try:
        template_data = _load_template(template_file)
        return list(template_data.keys())
    except Exception as e:
        logger.error(f"Error getting template keys for {template_file}: {e}")
        return []


def create_default_templates():
    """Create default template files if they don't exist."""
    try:
        _template_dir.mkdir(exist_ok=True)
        
        # Default payment templates
        payment_template = {
            "deposit_funds_message": (
                "💰 <b>━━ DEPOSIT FUNDS ━━</b>\n"
                "━━━━━━━━━━━━━━━━━━━━━━━\n"
                "Choose an amount to deposit:\n\n"
                "💳 <b>Available Options:</b>\n"
                "• Predefined amounts: $10, $20, $50, $100, $200, $500\n"
                "• Custom amount: Enter any amount between $10-$1000\n\n"
                "🔒 <b>Secure Payment Processing</b>\n"
                "• Cryptocurrency payments via OXA Pay\n"
                "• Instant balance updates\n"
                "• Secure transaction processing\n\n"
                "<i>Select an amount below to continue.</i>"
            ),
            "confirm_deposit": (
                "💰 <b>• CONFIRM DEPOSIT •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>PAYMENT DETAILS</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "• <b>Amount:</b> <code>${amount}</code>\n"
                "• <b>Status:</b> Ready for payment\n\n"
                "🚨 <b>IMPORTANT SECURITY NOTICE</b> 🚨\n"
                "<b>Each payment requires a unique crypto address</b>\n"
                "<b>Never reuse old payment links or addresses</b>\n\n"
                "<i>Please confirm this amount to proceed with your deposit.</i>"
            ),
            "generating_payment": (
                "⏳ <b>• GENERATING PAYMENT •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>PROCESSING REQUEST</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "• <b>Amount:</b> <code>${amount}</code>\n"
                "• <b>Status:</b> <i>Processing</i>\n\n"
                "<i>Please wait while we securely generate your payment link.</i>"
            ),
            "payment_instructions": (
                "💳 <b>• PAYMENT INSTRUCTIONS •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>TRANSACTION DETAILS</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "• <b>Amount:</b> <code>${amount}</code>\n"
                "• <b>Order ID:</b> <code>{order_id}</code>\n"
                "• <b>Tracking ID:</b> <code>{track_id}</code>\n\n"
                "🚨 <b>CRITICAL WARNING - READ CAREFULLY</b> 🚨\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━</b>\n"
                "• <b>NEVER reuse old cryptocurrency addresses</b>\n"
                "• <b>ALWAYS generate a new payment link for each transaction</b>\n"
                "• <b>Previous payment addresses become INVALID after use</b>\n"
                "• <b>Using old addresses may result in LOST FUNDS</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━━━━</b>\n\n"
                "⚠️ <b>CRYPTOCURRENCY PAYMENT NOTICE</b>\n"
                "• Bitcoin (BTC) payments require 2 network confirmations\n"
                "• Other cryptocurrencies require at least 1 confirmation\n"
                "• Balance updates may be delayed due to network activity\n\n"
                "<i>Please complete your payment using the button below. Your balance will be updated after network confirmation.</i>"
            ),
            "payment_details": (
                "💳 <b>• PAYMENT DETAILS •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>TRANSACTION REFERENCE</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "• <b>Amount:</b> <code>${amount}</code>\n"
                "• <b>Order ID:</b> <code>{order_id}</code>\n"
                "• <b>Tracking ID:</b> <code>{track_id}</code>\n\n"
                "<i>Please keep this information for your records.</i>"
            ),
            "deposit_cancelled": (
                "✅ <b>• DEPOSIT CANCELLED •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>PROCESS TERMINATED</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>Your deposit process has been cancelled. No charges have been made to your account.</i>"
            ),
            "custom_deposit_message": (
                "💳 <b>━━ CUSTOM DEPOSIT ━━</b>\n"
                "━━━━━━━━━━━━━━━━━━━━━━━\n"
                "Please enter the amount you'd like to deposit:\n\n"
                "• <b>Minimum:</b> ${min_deposit}\n"
                "• <b>Maximum:</b> ${max_deposit}\n"
                "• Use numbers only (e.g., <code>100</code> or <code>49.99</code>)\n"
                "━━━━━━━━━━━━━━━━━━━━━━━\n"
                "<i>Type your desired amount or press 'Cancel' to return.</i>"
            ),
            "custom_deposit_confirm": (
                "💰 <b>• CUSTOM DEPOSIT •</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>PAYMENT DETAILS</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "• <b>Amount:</b> <code>${amount}</code>\n"
                "• <b>Status:</b> Ready for payment\n\n"
                "🚨 <b>IMPORTANT SECURITY NOTICE</b> 🚨\n"
                "<b>Each payment requires a unique crypto address</b>\n"
                "<b>Never reuse old payment links or addresses</b>\n\n"
                "<i>Please confirm this amount to proceed with your deposit.</i>"
            )
        }
        
        # Save payment template
        payment_path = _template_dir / "payment.json"
        with open(payment_path, 'w', encoding='utf-8') as f:
            json.dump(payment_template, f, indent=2, ensure_ascii=False)
        
        logger.info("Created default payment templates")
        return True
        
    except Exception as e:
        logger.error(f"Error creating default templates: {e}")
        return False

