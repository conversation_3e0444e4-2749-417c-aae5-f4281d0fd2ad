"""
Virtual Shopping Cart for API v3

This module implements a virtual shopping cart that stores items locally
before they are submitted to the actual API cart during checkout.

Features:
- Local storage of cart items
- Quantity management
- Price calculations
- Data validation
- Checkout verification
"""

from __future__ import annotations

import json
import time
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Any
from decimal import Decimal

from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class VirtualCartItem:
    """Represents an item in the virtual cart."""
    
    item_id: str
    bin: str
    name: str
    country: str
    price: float
    brand: str
    type: str
    level: str
    quality: str
    expiry: str
    quantity: int = 1
    added_at: float = None
    
    def __post_init__(self):
        if self.added_at is None:
            self.added_at = time.time()
    
    @property
    def total_price(self) -> float:
        """Calculate total price for this item (price * quantity)."""
        return self.price * self.quantity
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VirtualCartItem':
        """Create from dictionary."""
        return cls(**data)
    
    @classmethod
    def from_api_item(cls, api_item: Dict[str, Any], quantity: int = 1) -> 'VirtualCartItem':
        """Create from API v3 item response."""
        return cls(
            item_id=api_item.get("_id", ""),
            bin=api_item.get("bin", ""),
            name=api_item.get("name", ""),
            country=api_item.get("country", ""),
            price=float(api_item.get("price", 0)),
            brand=api_item.get("brand", ""),
            type=api_item.get("type", ""),
            level=api_item.get("level", ""),
            quality=api_item.get("quality", ""),
            expiry=api_item.get("expiry", ""),
            quantity=quantity,
        )


@dataclass
class VirtualCartSummary:
    """Summary of virtual cart contents."""
    
    total_items: int
    unique_items: int
    total_price: float
    items_by_brand: Dict[str, int]
    items_by_country: Dict[str, int]
    average_price: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class VirtualCart:
    """
    Virtual shopping cart that stores items locally before API submission.
    
    This cart maintains items in memory and optionally persists to disk.
    During checkout, items are transferred to the actual API cart.
    """
    
    def __init__(self, user_id: str, storage_dir: Optional[Path] = None):
        """
        Initialize virtual cart.
        
        Args:
            user_id: User identifier for this cart
            storage_dir: Optional directory for persistent storage
        """
        self.user_id = user_id
        self.items: Dict[str, VirtualCartItem] = {}
        self.created_at = time.time()
        self.modified_at = time.time()
        
        # Setup storage
        if storage_dir:
            self.storage_dir = Path(storage_dir)
            self.storage_dir.mkdir(parents=True, exist_ok=True)
            self.storage_file = self.storage_dir / f"virtual_cart_{user_id}.json"
            self._load_from_storage()
        else:
            self.storage_file = None
        
        self.logger = get_logger()
    
    def add_item(self, api_item: Dict[str, Any], quantity: int = 1) -> bool:
        """
        Add an item to the virtual cart.
        
        Args:
            api_item: Item data from API v3 browse response
            quantity: Quantity to add
            
        Returns:
            True if item was added successfully
        """
        try:
            item_id = api_item.get("_id")
            if not item_id:
                self.logger.error("Cannot add item without ID")
                return False
            
            if item_id in self.items:
                # Update quantity for existing item
                self.items[item_id].quantity += quantity
                self.logger.info(f"Updated quantity for item {item_id}: {self.items[item_id].quantity}")
            else:
                # Add new item
                cart_item = VirtualCartItem.from_api_item(api_item, quantity)
                self.items[item_id] = cart_item
                self.logger.info(f"Added new item to cart: {item_id} (qty: {quantity})")
            
            self._update_modified_time()
            self._save_to_storage()
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding item to cart: {e}")
            return False
    
    def remove_item(self, item_id: str, quantity: Optional[int] = None) -> bool:
        """
        Remove an item from the virtual cart.
        
        Args:
            item_id: ID of item to remove
            quantity: Quantity to remove (None = remove all)
            
        Returns:
            True if item was removed successfully
        """
        try:
            if item_id not in self.items:
                self.logger.warning(f"Item {item_id} not found in cart")
                return False
            
            if quantity is None:
                # Remove entire item
                del self.items[item_id]
                self.logger.info(f"Removed item {item_id} from cart")
            else:
                # Reduce quantity
                self.items[item_id].quantity -= quantity
                if self.items[item_id].quantity <= 0:
                    del self.items[item_id]
                    self.logger.info(f"Removed item {item_id} from cart (quantity reached 0)")
                else:
                    self.logger.info(f"Reduced quantity for item {item_id}: {self.items[item_id].quantity}")
            
            self._update_modified_time()
            self._save_to_storage()
            return True
            
        except Exception as e:
            self.logger.error(f"Error removing item from cart: {e}")
            return False
    
    def clear(self) -> None:
        """Clear all items from the virtual cart."""
        self.items.clear()
        self._update_modified_time()
        self._save_to_storage()
        self.logger.info("Cleared virtual cart")
    
    def get_items(self) -> List[VirtualCartItem]:
        """Get all items in the cart."""
        return list(self.items.values())
    
    def get_item_count(self) -> int:
        """Get total number of items (including quantities)."""
        return sum(item.quantity for item in self.items.values())
    
    def get_unique_item_count(self) -> int:
        """Get number of unique items."""
        return len(self.items)
    
    def get_total_price(self) -> float:
        """Calculate total price of all items in cart."""
        return sum(item.total_price for item in self.items.values())
    
    def get_summary(self) -> VirtualCartSummary:
        """Get comprehensive cart summary."""
        items = self.get_items()
        
        # Count by brand
        brands = {}
        countries = {}
        total_price = 0
        total_items = 0
        
        for item in items:
            brands[item.brand] = brands.get(item.brand, 0) + item.quantity
            countries[item.country] = countries.get(item.country, 0) + item.quantity
            total_price += item.total_price
            total_items += item.quantity
        
        average_price = total_price / total_items if total_items > 0 else 0
        
        return VirtualCartSummary(
            total_items=total_items,
            unique_items=len(items),
            total_price=total_price,
            items_by_brand=brands,
            items_by_country=countries,
            average_price=average_price,
        )
    
    def get_cart_data_for_api(self) -> List[str]:
        """
        Get cart item IDs formatted for API submission.
        
        Returns:
            List of item IDs to submit to API cart
        """
        cart_ids = []
        for item in self.items.values():
            # Add item ID multiple times for quantity
            cart_ids.extend([item.item_id] * item.quantity)
        return cart_ids
    
    def _update_modified_time(self) -> None:
        """Update the modified timestamp."""
        self.modified_at = time.time()
    
    def _save_to_storage(self) -> None:
        """Save cart to persistent storage."""
        if not self.storage_file:
            return
        
        try:
            cart_data = {
                "user_id": self.user_id,
                "created_at": self.created_at,
                "modified_at": self.modified_at,
                "items": {item_id: item.to_dict() for item_id, item in self.items.items()}
            }
            
            with open(self.storage_file, 'w') as f:
                json.dump(cart_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving cart to storage: {e}")
    
    def _load_from_storage(self) -> None:
        """Load cart from persistent storage."""
        if not self.storage_file or not self.storage_file.exists():
            return
        
        try:
            with open(self.storage_file, 'r') as f:
                cart_data = json.load(f)
            
            self.created_at = cart_data.get("created_at", time.time())
            self.modified_at = cart_data.get("modified_at", time.time())
            
            # Load items
            items_data = cart_data.get("items", {})
            for item_id, item_data in items_data.items():
                self.items[item_id] = VirtualCartItem.from_dict(item_data)
            
            self.logger.info(f"Loaded {len(self.items)} items from storage")
            
        except Exception as e:
            self.logger.error(f"Error loading cart from storage: {e}")


class VirtualCartManager:
    """
    Manager for virtual carts across multiple users.
    
    Handles cart creation, retrieval, and cleanup.
    """
    
    def __init__(self, storage_dir: Optional[Path] = None):
        """
        Initialize cart manager.
        
        Args:
            storage_dir: Directory for cart storage
        """
        self.storage_dir = storage_dir
        self.carts: Dict[str, VirtualCart] = {}
        self.logger = get_logger()
    
    def get_cart(self, user_id: str) -> VirtualCart:
        """
        Get or create a virtual cart for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            VirtualCart instance for the user
        """
        if user_id not in self.carts:
            self.carts[user_id] = VirtualCart(user_id, self.storage_dir)
            self.logger.info(f"Created virtual cart for user {user_id}")
        
        return self.carts[user_id]
    
    def clear_cart(self, user_id: str) -> None:
        """Clear a user's virtual cart."""
        if user_id in self.carts:
            self.carts[user_id].clear()
    
    def remove_cart(self, user_id: str) -> None:
        """Remove a user's virtual cart entirely."""
        if user_id in self.carts:
            del self.carts[user_id]
            self.logger.info(f"Removed virtual cart for user {user_id}")


# Global cart manager instance
_cart_manager = VirtualCartManager(storage_dir=Path("storage/virtual_carts"))


def get_virtual_cart(user_id: str) -> VirtualCart:
    """
    Get virtual cart for a user.
    
    Args:
        user_id: User identifier
        
    Returns:
        VirtualCart instance
    """
    return _cart_manager.get_cart(user_id)
