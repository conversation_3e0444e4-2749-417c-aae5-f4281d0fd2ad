"""
Dynamic Country Flag Manager
Loads countries from filter.json and applies smart flag detection
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Set
from functools import lru_cache

from utils.central_logger import get_logger

logger = get_logger()


class DynamicCountryFlagManager:
    """Smart flag detection system using dynamic country data from filter.json"""
    
    def __init__(self):
        self._countries_cache: Optional[List[str]] = None
        self._filter_data_cache: Optional[Dict] = None
        
    @lru_cache(maxsize=1)
    def _load_filter_data(self) -> Dict:
        """Load filter data from filter_response.json"""
        try:
            filter_file = Path(__file__).parent.parent / "data" / "filters" / "filter_response.json"
            if not filter_file.exists():
                logger.warning(f"Filter file not found: {filter_file}")
                return {}
                
            with open(filter_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            logger.debug(f"Loaded filter data with {len(data)} filter categories")
            return data
            
        except Exception as e:
            logger.error(f"Error loading filter data: {e}")
            return {}
    
    @lru_cache(maxsize=1) 
    def get_available_countries(self) -> List[str]:
        """Get list of countries available in filter data"""
        if self._countries_cache is not None:
            return self._countries_cache
            
        try:
            filter_data = self._load_filter_data()
            countries = []
            
            # Find country filter section
            for filter_section in filter_data:
                if filter_section.get("name") == "country[]":
                    options = filter_section.get("options", [])
                    for option in options:
                        country_name = option.get("label", "").strip()
                        if country_name:
                            countries.append(country_name)
                    break
            
            # Remove duplicates and sort
            countries = sorted(list(set(countries)))
            logger.debug(f"Found {len(countries)} countries in filter data")
            
            self._countries_cache = countries
            return countries
            
        except Exception as e:
            logger.error(f"Error extracting countries from filter data: {e}")
            return []
    
    def _smart_country_detection(self, country_input: str) -> Optional[str]:
        """Smart detection of country using various matching strategies"""
        if not country_input or not isinstance(country_input, str):
            return None
            
        # Normalize input
        normalized_input = country_input.strip().upper()
        available_countries = self.get_available_countries()
        
        # Strategy 1: Exact match
        for country in available_countries:
            if country.upper() == normalized_input:
                return country
                
        # Strategy 2: Partial match (input contained in country name)
        for country in available_countries:
            if normalized_input in country.upper():
                return country
                
        # Strategy 3: Country name contained in input
        for country in available_countries:
            if country.upper() in normalized_input:
                return country
                
        # Strategy 4: Handle common variations
        variations = {
            "USA": "UNITED STATES",
            "US": "UNITED STATES", 
            "UK": "UNITED KINGDOM",
            "UAE": "UNITED ARAB EMIRATES",
            "SOUTH KOREA": "KOREA, REPUBLIC OF",
            "KOREA": "KOREA, REPUBLIC OF",
            "VIETNAM": "VIET NAM",
            "LAOS": "LAO PEOPLE'S DEMOCRATIC REPUBLIC",
            "MACEDONIA": "MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF",
            "BOLIVIA": "BOLIVIA, PLURINATIONAL STATE OF",
            "VENEZUELA": "VENEZUELA, BOLIVARIAN REPUBLIC OF",
            "TANZANIA": "TANZANIA, UNITED REPUBLIC OF",
            "MOLDOVA": "MOLDOVA, REPUBLIC OF",
            "CONGO": "CONGO, THE DEMOCRATIC REPUBLIC OF THE",
            "PALESTINE": "PALESTINE, STATE OF",
            "CAPE VERDE": "CABO VERDE",
            "ESWATINI": "SWAZILAND",
            "TAIWAN": "TAIWAN, PROVINCE OF CHINA",
            "BURMA": "MYANMAR",
            "TIMOR-LESTE": "TIMOR-LESTE",
            "CZECH REPUBLIC": "CZECH REPUBLIC",
            "CZECHIA": "CZECH REPUBLIC"
        }
        
        variation_match = variations.get(normalized_input)
        if variation_match:
            for country in available_countries:
                if country.upper() == variation_match:
                    return country
        
        # Strategy 5: Remove common prefixes/suffixes and retry
        cleaned_input = re.sub(r'\b(REPUBLIC OF|STATE OF|KINGDOM OF|FEDERATION|DEMOCRATIC|PEOPLE\'S|ISLAMIC)\b', 
                              '', normalized_input).strip()
        if cleaned_input != normalized_input:
            return self._smart_country_detection(cleaned_input)
        
        return None
    
    def get_country_flag(self, country_input: str) -> str:
        """Get flag for country using smart detection + centralized flag system"""
        from utils.country_flags import get_country_flag
        
        # First try direct lookup with centralized system
        flag = get_country_flag(country_input)
        if flag != "🌍":
            return flag
        
        # Try smart detection with available countries
        detected_country = self._smart_country_detection(country_input)
        if detected_country:
            # Use detected country name with centralized flag system
            flag = get_country_flag(detected_country)
            if flag != "🌍":
                return flag
        
        # Fallback
        return "🌍"
    
    def get_country_with_flag(self, country_input: str) -> str:
        """Get country name prefixed with flag"""
        if not country_input or not isinstance(country_input, str):
            return "🌍 Unknown"
            
        flag = self.get_country_flag(country_input)
        country_name = country_input.strip()
        
        # Don't duplicate flag if already present
        if flag != "🌍" and not country_name.startswith(flag):
            return f"{flag} {country_name}"
            
        return country_name
    
    def get_all_countries_with_flags(self) -> List[Dict[str, str]]:
        """Get all available countries with their flags"""
        countries_with_flags = []
        available_countries = self.get_available_countries()
        
        for country in available_countries:
            flag = self.get_country_flag(country)
            countries_with_flags.append({
                "name": country,
                "flag": flag,
                "display": f"{flag} {country}" if flag != "🌍" else country
            })
        
        return countries_with_flags
    
    def get_statistics(self) -> Dict[str, int]:
        """Get statistics about flag coverage"""
        available_countries = self.get_available_countries()
        countries_with_flags = 0
        countries_without_flags = 0
        
        for country in available_countries:
            flag = self.get_country_flag(country)
            if flag != "🌍":
                countries_with_flags += 1
            else:
                countries_without_flags += 1
        
        return {
            "total_countries": len(available_countries),
            "countries_with_flags": countries_with_flags,
            "countries_without_flags": countries_without_flags,
            "flag_coverage_percentage": round((countries_with_flags / len(available_countries)) * 100, 1) if available_countries else 0
        }


# Global instance for easy import
dynamic_flag_manager = DynamicCountryFlagManager()

# Convenience functions
def get_dynamic_country_flag(country_input: str) -> str:
    """Get country flag using dynamic detection"""
    return dynamic_flag_manager.get_country_flag(country_input)

def get_dynamic_country_with_flag(country_input: str) -> str:
    """Get country name with flag prefix using dynamic detection"""
    return dynamic_flag_manager.get_country_with_flag(country_input)

def get_available_countries_with_flags() -> List[Dict[str, str]]:
    """Get all available countries from filter data with flags"""
    return dynamic_flag_manager.get_all_countries_with_flags()

def get_dynamic_flag_statistics() -> Dict[str, int]:
    """Get flag coverage statistics"""
    return dynamic_flag_manager.get_statistics()