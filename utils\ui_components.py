"""
UI Enhancement Utilities and Reusable Components
Provides consistent professional appearance across the bot interface
"""

from __future__ import annotations

from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from enum import Enum

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from utils.texts import DEMO_WATERMARK

from utils.central_logger import get_logger

logger = get_logger()


class MessageType(Enum):
    """Message type classifications for consistent styling"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    LOADING = "loading"
    CONFIRMATION = "confirmation"


class UITheme:
    """UI theme configuration for consistent styling"""

    # Emojis for different message types
    TYPE_EMOJIS = {
        MessageType.INFO: "ℹ️",
        MessageType.SUCCESS: "✅",
        MessageType.WARNING: "⚠️",
        MessageType.ERROR: "❌",
        MessageType.LOADING: "⏳",
        MessageType.CONFIRMATION: "❓"
    }

    # Visual separators
    SEPARATORS = {
        "section": "─" * 40,
        "item": "—" * 25,
        "highlight": "═" * 30,
        "light": "·" * 20
    }

    # Enhanced status indicators with more visual variety
    STATUS_INDICATORS = {
        "active": "🟢",
        "inactive": "🔴",
        "pending": "🟡",
        "error": "❌",
        "maintenance": "🔧",
        "success": "✅",
        "warning": "⚠️",
        "loading": "⏳",
        "processing": "🔄",
        "completed": "✅",
        "failed": "❌",
        "cancelled": "🚫",
        "available": "✅",
        "unavailable": "❌",
        "verified": "🔐",
        "premium": "💎",
        "free": "🎁",
        "new": "🆕",
        "popular": "🔥",
        "recommended": "⭐"
    }

    # Enhanced action button styles with better visual hierarchy
    ACTION_BUTTONS = {
        "primary": {"emoji": "🔥", "style": "bold", "priority": 1},
        "secondary": {"emoji": "", "style": "normal", "priority": 2},
        "success": {"emoji": "✅", "style": "bold", "priority": 1},
        "danger": {"emoji": "⚠️", "style": "bold", "priority": 1},
        "info": {"emoji": "ℹ️", "style": "italic", "priority": 3},
        "premium": {"emoji": "💎", "style": "bold", "priority": 1},
        "quick": {"emoji": "⚡", "style": "normal", "priority": 2},
        "utility": {"emoji": "🔧", "style": "normal", "priority": 3}
    }

    # Loading animation frames for better user feedback
    LOADING_FRAMES = [
        "⏳ Loading",
        "⌛ Loading.",
        "⏳ Loading..",
        "⌛ Loading...",
        "⏳ Loading....",
        "⌛ Loading....."
    ]

    # Progress indicators
    PROGRESS_BARS = {
        "dots": ["⚪", "🔵", "🟢"],
        "circles": ["⚪", "🟡", "🟢"],
        "squares": ["⬜", "🟨", "🟩"],
        "diamonds": ["◇", "◈", "◆"]
    }


class MessageBuilder:
    """Professional message builder with consistent formatting"""

    def __init__(self, message_type: MessageType = MessageType.INFO):
        self.message_type = message_type
        self.parts: List[str] = []
        self.sections: Dict[str, str] = {}
        self.footer_parts: List[str] = []

    def set_title(self, title: str, custom_emoji: str = None) -> 'MessageBuilder':
        """Set the message title with appropriate emoji"""
        emoji = custom_emoji or UITheme.TYPE_EMOJIS[self.message_type]
        self.parts = [f"{emoji} <b>{title}</b>"]
        return self

    def add_content(self, content: str) -> 'MessageBuilder':
        """Add main content to the message"""
        if content:
            self.parts.append(content)
        return self

    def add_section(self, title: str, content: str, emoji: str = None) -> 'MessageBuilder':
        """Add a formatted section to the message"""
        section_title = f"{emoji} <b>{title}:</b>" if emoji else f"<b>{title}:</b>"
        self.sections[section_title] = content
        return self

    def add_list_section(
        self,
        title: str,
        items: List[str],
        emoji: str = None,
        bullet: str = "•"
    ) -> 'MessageBuilder':
        """Add a list section to the message"""
        if not items:
            return self

        formatted_items = [f"{bullet} {item}" for item in items]
        content = "\n".join(formatted_items)
        return self.add_section(title, content, emoji)

    def add_key_value_section(
        self,
        title: str,
        data: Dict[str, Any],
        emoji: str = None
    ) -> 'MessageBuilder':
        """Add a key-value section to the message"""
        if not data:
            return self

        formatted_items = []
        for key, value in data.items():
            formatted_key = key.replace("_", " ").title()
            formatted_items.append(f"• <b>{formatted_key}:</b> {value}")

        content = "\n".join(formatted_items)
        return self.add_section(title, content, emoji)

    def add_separator(self, separator_type: str = "section") -> 'MessageBuilder':
        """Add a visual separator"""
        separator = UITheme.SEPARATORS.get(separator_type, UITheme.SEPARATORS["section"])
        self.parts.append(separator)
        return self

    def add_footer(self, text: str, italic: bool = True) -> 'MessageBuilder':
        """Add footer text"""
        if italic:
            self.footer_parts.append(f"<i>{text}</i>")
        else:
            self.footer_parts.append(text)
        return self

    def add_timestamp(self, dt: datetime = None) -> 'MessageBuilder':
        """Add a timestamp to the footer"""
        if dt is None:
            dt = datetime.now()

        timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
        return self.add_footer(f"🕒 {timestamp}")

    def build(self, add_watermark: bool = True) -> str:
        """Build the final message"""
        message_parts = self.parts.copy()

        # Add sections
        for section_title, section_content in self.sections.items():
            message_parts.append(f"{section_title}\n{section_content}")

        # Add footer
        if self.footer_parts:
            message_parts.extend(self.footer_parts)

        # Add watermark
        if add_watermark:
            message_parts.append(DEMO_WATERMARK)

        return "\n\n".join(message_parts)


class StatusIndicator:
    """Enhanced status indicator utilities for consistent status display"""

    @staticmethod
    def format_api_status(status: str, name: str = None) -> str:
        """Format API status with appropriate emoji and text"""
        emoji = UITheme.STATUS_INDICATORS.get(status.lower(), "❓")
        status_text = status.title()

        if name:
            return f"{emoji} <b>{name}</b> ({status_text})"
        else:
            return f"{emoji} {status_text}"

    @staticmethod
    def format_health_status(is_healthy: bool, details: str = None) -> str:
        """Format health status indicator"""
        if is_healthy:
            base = f"{UITheme.STATUS_INDICATORS['success']} <b>Healthy</b>"
        else:
            base = f"{UITheme.STATUS_INDICATORS['error']} <b>Unhealthy</b>"

        if details:
            return f"{base} - {details}"
        return base

    @staticmethod
    def format_card_availability(available: bool, stock_level: str = None) -> str:
        """Format card availability status with stock level"""
        if available:
            if stock_level == "high":
                return f"{UITheme.STATUS_INDICATORS['success']} <b>In Stock</b> 🔥"
            elif stock_level == "medium":
                return f"{UITheme.STATUS_INDICATORS['warning']} <b>Limited Stock</b>"
            elif stock_level == "low":
                return f"{UITheme.STATUS_INDICATORS['warning']} <b>Few Left</b> ⚡"
            else:
                return f"{UITheme.STATUS_INDICATORS['success']} <b>Available</b>"
        else:
            return f"{UITheme.STATUS_INDICATORS['error']} <b>Out of Stock</b>"

    @staticmethod
    def format_price_trend(trend: str, percentage: float = None) -> str:
        """Format price trend indicator"""
        if trend == "up":
            emoji = "📈"
            text = "Rising"
            color = "🔴"
        elif trend == "down":
            emoji = "📉"
            text = "Falling"
            color = "🟢"
        else:
            emoji = "➡️"
            text = "Stable"
            color = "🟡"

        if percentage is not None:
            return f"{emoji} <b>{text}</b> {color} ({percentage:+.1f}%)"
        else:
            return f"{emoji} <b>{text}</b> {color}"

    @staticmethod
    def format_quality_score(score: float, max_score: float = 10.0) -> str:
        """Format quality score with visual indicators"""
        percentage = (score / max_score) * 100

        if percentage >= 90:
            return f"💎 <b>Excellent</b> ({score:.1f}/{max_score})"
        elif percentage >= 80:
            return f"⭐ <b>Very Good</b> ({score:.1f}/{max_score})"
        elif percentage >= 70:
            return f"✅ <b>Good</b> ({score:.1f}/{max_score})"
        elif percentage >= 60:
            return f"🟡 <b>Fair</b> ({score:.1f}/{max_score})"
        else:
            return f"🔴 <b>Poor</b> ({score:.1f}/{max_score})"

    @staticmethod
    def format_verification_level(level: str, verified_fields: List[str] = None) -> str:
        """Format verification level with field details"""
        verified_fields = verified_fields or []

        if level == "full":
            base = f"🔐✨ <b>Fully Verified</b>"
        elif level == "partial":
            base = f"🔐 <b>Partially Verified</b>"
        elif level == "basic":
            base = f"🔒 <b>Basic Verification</b>"
        else:
            base = f"❓ <b>Unverified</b>"

        if verified_fields:
            field_count = len(verified_fields)
            if field_count <= 3:
                fields_text = ", ".join(verified_fields)
            else:
                fields_text = f"{', '.join(verified_fields[:2])}, +{field_count-2} more"
            return f"{base}\n<i>Fields: {fields_text}</i>"

        return base

    @staticmethod
    def format_progress_bar(current: int, total: int, width: int = 10) -> str:
        """Create a visual progress bar"""
        if total == 0:
            return "[" + "░" * width + "] 0%"

        filled = int((current / total) * width)
        bar = "█" * filled + "░" * (width - filled)
        percentage = (current / total) * 100

        return f"[{bar}] {percentage:.1f}%"


class DataFormatter:
    """Enhanced data formatting utilities for consistent display"""

    @staticmethod
    def format_currency(amount: float, currency: str = "USD") -> str:
        """Format currency amounts consistently with enhanced styling"""
        if currency == "USD":
            if amount == 0:
                return "🎁 <b>FREE</b>"
            elif amount < 1:
                return f"💸 <b>${amount:.2f}</b>"
            elif amount < 10:
                return f"💰 <b>${amount:.2f}</b>"
            else:
                return f"💎 <b>${amount:.2f}</b>"
        else:
            return f"💰 <b>{amount:.2f} {currency}</b>"

    @staticmethod
    def format_large_number(number: int) -> str:
        """Format large numbers with appropriate suffixes and styling"""
        if number >= 1_000_000:
            return f"<b>{number / 1_000_000:.1f}M</b>"
        elif number >= 1_000:
            return f"<b>{number / 1_000:.1f}K</b>"
        else:
            return f"<b>{number:,}</b>"

    @staticmethod
    def format_percentage(value: float, show_sign: bool = True) -> str:
        """Format percentage with visual indicators"""
        if show_sign:
            if value > 0:
                return f"📈 <b>+{value:.1f}%</b>"
            elif value < 0:
                return f"📉 <b>{value:.1f}%</b>"
            else:
                return f"➡️ <b>{value:.1f}%</b>"
        else:
            return f"<b>{value:.1f}%</b>"

    @staticmethod
    def format_time_ago(seconds: int) -> str:
        """Format time elapsed in human-readable format"""
        if seconds < 60:
            return f"<i>{seconds}s ago</i>"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"<i>{minutes}m ago</i>"
        elif seconds < 86400:
            hours = seconds // 3600
            return f"<i>{hours}h ago</i>"
        else:
            days = seconds // 86400
            return f"<i>{days}d ago</i>"

    @staticmethod
    def format_analytics_summary(data: Dict[str, Any]) -> str:
        """Format analytics data into a readable summary"""
        summary_parts = []

        if "total_cards" in data:
            total = DataFormatter.format_large_number(data["total_cards"])
            summary_parts.append(f"📊 <b>Total Cards:</b> {total}")

        if "avg_price" in data:
            avg_price = DataFormatter.format_currency(data["avg_price"])
            summary_parts.append(f"💰 <b>Avg Price:</b> {avg_price}")

        if "top_country" in data:
            from utils.dynamic_country_flags import get_dynamic_country_with_flag
            country = data["top_country"]
            country_with_flag = get_dynamic_country_with_flag(country)
            summary_parts.append(f"🌍 <b>Top Country:</b> {country_with_flag}")

        if "success_rate" in data:
            rate = DataFormatter.format_percentage(data["success_rate"], False)
            summary_parts.append(f"✅ <b>Success Rate:</b> {rate}")

        return "\n".join(summary_parts)

    @staticmethod
    def format_duration(seconds: int) -> str:
        """Format duration in human-readable format"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{minutes}m {remaining_seconds}s"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            return f"{hours}h {remaining_minutes}m"

    @staticmethod
    def format_file_size(bytes_count: int) -> str:
        """Format file size in human-readable format"""
        if bytes_count < 1024:
            return f"{bytes_count} B"
        elif bytes_count < 1024 * 1024:
            return f"{bytes_count / 1024:.1f} KB"
        elif bytes_count < 1024 * 1024 * 1024:
            return f"{bytes_count / (1024 * 1024):.1f} MB"
        else:
            return f"{bytes_count / (1024 * 1024 * 1024):.1f} GB"

    @staticmethod
    def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
        """Truncate text with ellipsis if too long"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix

    @staticmethod
    def format_card_info(data: Dict[str, Any]) -> str:
        """Format card information with proper handling of different statuses"""
        formatted_parts = []
        
        # Handle card number display based on status
        card_number = data.get("card_number")
        card_status = data.get("card_number_status", "")
        
        if card_number:
            formatted_parts.append(f"💳 {card_number}")
        elif card_status == "requires_download":
            formatted_parts.append(f"💳 📥 Available via download")
        elif data.get("display_note"):
            formatted_parts.append(f"💳 {data.get('display_note')}")
        
        # Bank on one line
        if data.get("bank"):
            formatted_parts.append(f"🏛️ <b>{data['bank']}</b>")
        
        # Brand, type, and level on next line
        brand_type_level_parts = []
        if data.get("brand"):
            brand_type_level_parts.append(f"💳 <b>{data['brand']}</b>")
        if data.get("type"):
            brand_type_level_parts.append(f"📇 <b>{data['type']}</b>")
        if data.get("level"):
            brand_type_level_parts.append(f"🏷️ <b>{data['level']}</b>")
        
        if brand_type_level_parts:
            formatted_parts.append(" • ".join(brand_type_level_parts))
        
        # Country on separate line if available
        if data.get("country"):
            formatted_parts.append(f"🌍 {data['country']}")
        
        return "\n".join(formatted_parts) if formatted_parts else "ℹ️ Card information not available"


class TableFormatter:
    """Table formatting utilities for structured data display"""

    @staticmethod
    def create_simple_table(
        headers: List[str],
        rows: List[List[str]],
        max_width: int = 30
    ) -> str:
        """Create a simple text table"""
        if not headers or not rows:
            return "No data available"

        # Calculate column widths
        col_widths = []
        for i, header in enumerate(headers):
            max_width_col = len(header)
            for row in rows:
                if i < len(row):
                    max_width_col = max(max_width_col, len(str(row[i])))
            col_widths.append(min(max_width_col, max_width))

        # Format header
        header_parts = []
        for i, header in enumerate(headers):
            header_parts.append(header.ljust(col_widths[i]))

        table_lines = [" | ".join(header_parts)]
        table_lines.append("-" * len(table_lines[0]))

        # Format rows
        for row in rows:
            row_parts = []
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    cell_str = str(cell)
                    if len(cell_str) > col_widths[i]:
                        cell_str = cell_str[:col_widths[i]-3] + "..."
                    row_parts.append(cell_str.ljust(col_widths[i]))
            table_lines.append(" | ".join(row_parts))

        return f"<pre>{chr(10).join(table_lines)}</pre>"

    @staticmethod
    def create_key_value_table(data: Dict[str, Any]) -> str:
        """Create a key-value table"""
        if not data:
            return "No data available"

        lines = []
        max_key_length = max(len(str(key)) for key in data.keys())

        for key, value in data.items():
            key_str = str(key).ljust(max_key_length)
            lines.append(f"{key_str} : {value}")

        return f"<pre>{chr(10).join(lines)}</pre>"


class NotificationBuilder:
    """Builder for creating notification messages"""

    def __init__(self):
        self.notifications: List[Dict[str, Any]] = []

    def add_notification(
        self,
        message: str,
        notification_type: MessageType = MessageType.INFO,
        action_text: str = None,
        action_callback: str = None
    ) -> 'NotificationBuilder':
        """Add a notification"""
        notification = {
            "message": message,
            "type": notification_type,
            "action_text": action_text,
            "action_callback": action_callback,
            "timestamp": datetime.now()
        }
        self.notifications.append(notification)
        return self

    def build_summary(self, max_notifications: int = 5) -> str:
        """Build a summary of notifications"""
        if not self.notifications:
            return "📭 <b>No Notifications</b>"

        builder = MessageBuilder(MessageType.INFO)
        builder.set_title("Notifications")

        # Show recent notifications
        recent = self.notifications[-max_notifications:]

        for notification in recent:
            emoji = UITheme.TYPE_EMOJIS[notification["type"]]
            timestamp = notification["timestamp"].strftime("%H:%M")
            message = f"{emoji} {notification['message']} <i>({timestamp})</i>"
            builder.add_content(message)

        if len(self.notifications) > max_notifications:
            builder.add_footer(f"... and {len(self.notifications) - max_notifications} more")

        return builder.build()

    def clear(self) -> 'NotificationBuilder':
        """Clear all notifications"""
        self.notifications = []
        return self


class ErrorHandler:
    """Enhanced error handling with user-friendly messages"""

    ERROR_MESSAGES = {
        "network": "🌐 Network connection issue. Please check your internet and try again.",
        "timeout": "⏱️ Request timed out. The service might be busy, please try again.",
        "not_found": "🔍 The requested item was not found. It may have been removed or is temporarily unavailable.",
        "permission": "🔒 You don't have permission to perform this action.",
        "validation": "📝 Invalid input provided. Please check your data and try again.",
        "service": "🔧 Service temporarily unavailable. Our team has been notified.",
        "rate_limit": "⏳ Too many requests. Please wait a moment before trying again.",
        "maintenance": "🚧 System is under maintenance. Please try again later."
    }

    @staticmethod
    def format_error_message(
        error_type: str,
        details: str = None,
        suggestion: str = None
    ) -> str:
        """Format a user-friendly error message"""
        builder = MessageBuilder(MessageType.ERROR)

        # Get base error message
        base_message = ErrorHandler.ERROR_MESSAGES.get(
            error_type,
            "❌ An unexpected error occurred."
        )

        builder.set_title("Error Occurred")
        builder.add_content(base_message)

        if details:
            builder.add_section("Details", details, "📋")

        if suggestion:
            builder.add_section("Suggestion", suggestion, "💡")

        builder.add_footer("If the problem persists, please contact support.")

        return builder.build()

    @staticmethod
    def create_retry_keyboard(
        retry_callback: str,
        cancel_callback: str = "menu:main"
    ) -> InlineKeyboardMarkup:
        """Create a keyboard with retry and cancel options"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔄 Retry", callback_data=retry_callback),
                InlineKeyboardButton(text="❌ Cancel", callback_data=cancel_callback)
            ]
        ])


# Convenience functions for easy import
def create_message(message_type: MessageType = MessageType.INFO) -> MessageBuilder:
    """Create a new message builder"""
    return MessageBuilder(message_type)


def create_success_message(title: str, content: str = None) -> str:
    """Quick success message"""
    builder = MessageBuilder(MessageType.SUCCESS).set_title(title)
    if content:
        builder.add_content(content)
    return builder.build()


def create_error_message(title: str, content: str = None) -> str:
    """Quick error message"""
    builder = MessageBuilder(MessageType.ERROR).set_title(title)
    if content:
        builder.add_content(content)
    return builder.build()


def create_info_message(title: str, content: str = None) -> str:
    """Quick info message"""
    builder = MessageBuilder(MessageType.INFO).set_title(title)
    if content:
        builder.add_content(content)
    return builder.build()


# Global instances for easy import
notification_center = NotificationBuilder()
data_formatter = DataFormatter()
status_indicator = StatusIndicator()
table_formatter = TableFormatter()