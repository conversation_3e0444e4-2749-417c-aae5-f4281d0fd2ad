"""Decorators for error handling, validation, and user authorization"""

from __future__ import annotations

import asyncio
import functools
from typing import Any, Callable, List, Optional, Union, TypeVar

from aiogram.types import CallbackQuery, Message, User as TgUser
from aiogram.exceptions import TelegramBadRequest, TelegramRetryAfter

from config.settings import get_settings
from utils.security import (
    check_rate_limit_security,
    validate_admin_session,
)
from utils.central_logger import get_logger
from utils.validation import ValidationError

logger = get_logger()

F = TypeVar("F", bound=Callable[..., Any])


def handle_errors(
    error_message: str = "❌ An error occurred",
    log_error: bool = True,
    show_alert: bool = False,
) -> Callable[[F], F]:
    """
    Decorator for handling errors in bot handlers
    
    Args:
        error_message: Message to show user on error
        log_error: Whether to log the error
        show_alert: Whether to show error as alert
        
    Returns:
        Decorated function with error handling
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ValidationError as e:
                if log_error:
                    logger.warning(f"Validation error in {func.__name__}: {e}")
                
                # Try to respond to user
                await _send_error_response(
                    args, f"❌ {str(e)}", show_alert=True
                )
                
            except asyncio.CancelledError:
                # Don't handle cancellation errors
                raise
                
            except Exception as e:
                if log_error:
                    logger.error(f"Error in {func.__name__}: {e}", exc_info=True)
                
                # Handle Telegram callback timeout specifically
                if "query is too old" in str(e) or "response timeout expired" in str(e):
                    logger.warning(f"Callback query timed out in {func.__name__} - this is normal for long operations")
                    try:
                        # Try to answer the callback to prevent further timeout errors
                        await _send_error_response(args, "⏳ Operation completed", show_alert=False)
                    except Exception:
                        pass
                    return
                
                # Try to respond to user
                await _send_error_response(
                    args, error_message, show_alert=show_alert
                )
        
        return wrapper
    return decorator


def error_handler(
    _func: F | None = None,
    *,
    error_message: str = "❌ An error occurred",
    log_error: bool = True,
    show_alert: bool = False,
) -> Union[Callable[[F], F], F]:
    """Backward-compatible alias for handle_errors.

    Supports both usages:
      - @error_handler
      - @error_handler(error_message="...", show_alert=True)
    """
    decorator = handle_errors(
        error_message=error_message, log_error=log_error, show_alert=show_alert
    )
    if _func is None:
        return decorator
    return decorator(_func)  # type: ignore[return-value]


def admin_required(func: F) -> F:
    """
    Ensure handler is only accessible by admins.

    Works for both class methods (expects `self._is_admin(user_id)`) and
    standalone functions by checking `settings.admin_ids`.
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        update: Union[CallbackQuery, Message, None] = None
        self_obj: Any | None = None

        for arg in args:
            if isinstance(arg, (CallbackQuery, Message)):
                update = arg
            elif self_obj is None:
                self_obj = arg

        if update is None:
            for val in kwargs.values():
                if isinstance(val, (CallbackQuery, Message)):
                    update = val
                    break

        if update is None:
            # If no update object found, proceed without check
            return await func(*args, **kwargs)

        user = update.from_user if hasattr(update, 'from_user') else None
        if not user:
            await _send_error_response([update], "❌ User not found", show_alert=True)
            return

        is_admin = False
        try:
            if self_obj is not None and hasattr(self_obj, '_is_admin'):
                is_admin = bool(self_obj._is_admin(user.id))
            else:
                from config.settings import get_settings
                settings = get_settings()
                admin_ids = getattr(settings, 'admin_ids', [])
                # If no admins configured, allow access (development-friendly default)
                if not admin_ids:
                    logger.warning("No ADMIN_USER_IDS configured; allowing admin access by default")
                    is_admin = True
                else:
                    is_admin = user.id in admin_ids
        except Exception:
            is_admin = False

        if not is_admin:
            await _send_error_response([update], "❌ Unauthorized", show_alert=True)
            return

        return await func(*args, **kwargs)

    return wrapper


def rate_limit(calls: int = 5, period: int = 60) -> Callable[[F], F]:
    """
    Decorator for rate limiting handler calls per user
    
    Args:
        calls: Number of calls allowed
        period: Time period in seconds
        
    Returns:
        Decorated function with rate limiting
    """
    user_calls = {}
    
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract user ID from args
            user_id = None
            for arg in args:
                if hasattr(arg, 'from_user') and arg.from_user:
                    user_id = arg.from_user.id
                    break
            
            if user_id:
                import time
                current_time = time.time()
                
                # Clean old entries
                user_calls[user_id] = [
                    call_time for call_time in user_calls.get(user_id, [])
                    if current_time - call_time < period
                ]
                
                # Check rate limit
                if len(user_calls[user_id]) >= calls:
                    await _send_error_response(
                        args, "❌ Too many requests. Please wait.", show_alert=True
                    )
                    return
                
                # Record this call
                user_calls[user_id].append(current_time)
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def validate_input(**validators) -> Callable[[F], F]:
    """
    Decorator for validating input parameters
    
    Args:
        **validators: Mapping of parameter names to validation functions
        
    Returns:
        Decorated function with input validation
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Validate kwargs
            for param_name, validator in validators.items():
                if param_name in kwargs:
                    try:
                        kwargs[param_name] = validator(kwargs[param_name])
                    except ValidationError as e:
                        await _send_error_response(
                            args, f"❌ Invalid {param_name}: {e}", show_alert=True
                        )
                        return
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


async def _send_error_response(
    args: tuple, 
    message: str, 
    show_alert: bool = False
) -> None:
    """
    Helper function to send error response to user
    
    Args:
        args: Handler arguments
        message: Error message
        show_alert: Whether to show as alert
    """
    try:
        # Find the update object (CallbackQuery or Message)
        update = None
        for arg in args:
            if isinstance(arg, (CallbackQuery, Message)):
                update = arg
                break
        
        if not update:
            return
        
        if isinstance(update, CallbackQuery):
            try:
                result = update.answer(message, show_alert=show_alert)
                if asyncio.iscoroutine(result):
                    await result
            except TypeError:
                # Fallback for non-awaitable mocks
                try:
                    update.answer(message, show_alert=show_alert)
                except Exception:
                    pass
        elif isinstance(update, Message):
            try:
                result = update.reply(message)
                if asyncio.iscoroutine(result):
                    await result
            except TypeError:
                try:
                    update.reply(message)
                except Exception:
                    pass
            
    except Exception as e:
        logger.error(f"Failed to send error response: {e}")


def log_handler_call(func: F) -> F:
    """
    Decorator to log handler calls for debugging
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function with logging
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract user info for logging
        user_info = "unknown"
        for arg in args:
            if hasattr(arg, 'from_user') and arg.from_user:
                user = arg.from_user
                user_info = f"{user.id}:{user.username or user.first_name}"
                break
        
        logger.debug(f"Handler {func.__name__} called by user {user_info}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Handler {func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.debug(f"Handler {func.__name__} failed: {e}")
            raise
    
    return wrapper


def retry_on_failure(max_retries: int = 3, delay: float = 1.0) -> Callable[[F], F]:
    """
    Decorator to retry function calls on failure
    
    Args:
        max_retries: Maximum number of retries
        delay: Delay between retries in seconds
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except asyncio.CancelledError:
                    # Don't retry cancellation
                    raise
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {delay}s..."
                        )
                        await asyncio.sleep(delay)
                    else:
                        logger.error(
                            f"All {max_retries + 1} attempts failed for {func.__name__}"
                        )
            
            # Re-raise the last exception if all retries failed
            raise last_exception
        
        return wrapper
    return decorator
