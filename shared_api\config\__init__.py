"""
Configuration management for shared API clients

This module provides configuration classes and utilities for defining
and managing API client configurations in a flexible, reusable way.
"""

from .api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from .client_factory import APIClientFactory
from .registry import APIRegistry, register_api_v2

__all__ = [
    "APIConfiguration",
    "EndpointConfiguration", 
    "AuthenticationConfiguration",
    "TimeoutConfiguration",
    "RetryConfiguration",
    "APIClientFactory",
    "APIRegistry",
    "register_api_v2",
]
