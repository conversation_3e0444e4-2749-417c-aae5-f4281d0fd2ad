"""
API Health Monitoring Service

This service monitors the health and availability of external API services,
particularly API v3, to provide early detection of service issues.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from utils.central_logger import get_logger

logger = get_logger()

from utils.api_logging import logger


class ServiceStatus(Enum):
    """Service status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNAVAILABLE = "unavailable"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check"""
    service_name: str
    status: ServiceStatus
    response_time_ms: float
    error_message: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class ServiceHealthMetrics:
    """Health metrics for a service"""
    service_name: str
    current_status: ServiceStatus
    last_check: datetime
    success_rate_24h: float
    avg_response_time_ms: float
    consecutive_failures: int
    last_error: Optional[str] = None


class APIHealthMonitor:
    """
    Monitors the health of external API services
    """
    
    def __init__(self):
        self.health_history: Dict[str, List[HealthCheckResult]] = {}
        self.current_metrics: Dict[str, ServiceHealthMetrics] = {}
        self.check_intervals = {
            "api_v3": 60,  # Check every 60 seconds
        }
        self.max_history_hours = 24
        
    def _is_service_unavailable_error(self, error_msg: str) -> bool:
        """
        Check if an error message indicates service unavailability.
        
        Args:
            error_msg: Error message to check
            
        Returns:
            bool: True if error indicates unavailability
        """
        unavailable_indicators = [
            "502 Bad Gateway",
            "503 Service Unavailable", 
            "504 Gateway Timeout",
            "Connection refused",
            "Connection timeout",
            "Network is unreachable",
            "Temporary failure in name resolution",
            "Bad Gateway",
            "Service Unavailable",
            "Gateway Timeout"
        ]
        
        error_lower = error_msg.lower()
        return any(indicator.lower() in error_lower for indicator in unavailable_indicators)
    
    async def check_api_v3_health(self, external_api_service) -> HealthCheckResult:
        """
        Check the health of API v3 service.
        
        Args:
            external_api_service: External API service instance
            
        Returns:
            HealthCheckResult: Result of the health check
        """
        start_time = time.time()
        
        try:
            # Try a simple cart view request as health check
            response = await external_api_service.view_cart()
            
            response_time_ms = (time.time() - start_time) * 1000
            
            if response.success:
                return HealthCheckResult(
                    service_name="api_v3",
                    status=ServiceStatus.HEALTHY,
                    response_time_ms=response_time_ms
                )
            else:
                error_msg = response.error or "Unknown error"
                
                if self._is_service_unavailable_error(error_msg):
                    status = ServiceStatus.UNAVAILABLE
                else:
                    status = ServiceStatus.DEGRADED
                
                return HealthCheckResult(
                    service_name="api_v3",
                    status=status,
                    response_time_ms=response_time_ms,
                    error_message=error_msg
                )
                
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            error_msg = str(e)
            
            if self._is_service_unavailable_error(error_msg):
                status = ServiceStatus.UNAVAILABLE
            else:
                status = ServiceStatus.DEGRADED
            
            return HealthCheckResult(
                service_name="api_v3",
                status=status,
                response_time_ms=response_time_ms,
                error_message=error_msg
            )
    
    def record_health_check(self, result: HealthCheckResult):
        """
        Record a health check result.
        
        Args:
            result: Health check result to record
        """
        service_name = result.service_name
        
        # Initialize history if needed
        if service_name not in self.health_history:
            self.health_history[service_name] = []
        
        # Add result to history
        self.health_history[service_name].append(result)
        
        # Clean up old history (keep only last 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=self.max_history_hours)
        self.health_history[service_name] = [
            r for r in self.health_history[service_name] 
            if r.timestamp > cutoff_time
        ]
        
        # Update current metrics
        self._update_metrics(service_name)
        
        # Log health status
        self._log_health_status(result)
    
    def _update_metrics(self, service_name: str):
        """
        Update metrics for a service based on recent history.
        
        Args:
            service_name: Name of the service
        """
        history = self.health_history.get(service_name, [])
        if not history:
            return
        
        latest_result = history[-1]
        
        # Calculate success rate (last 24 hours)
        successful_checks = sum(1 for r in history if r.status == ServiceStatus.HEALTHY)
        success_rate = (successful_checks / len(history)) * 100 if history else 0
        
        # Calculate average response time
        response_times = [r.response_time_ms for r in history if r.response_time_ms is not None]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # Count consecutive failures
        consecutive_failures = 0
        for result in reversed(history):
            if result.status != ServiceStatus.HEALTHY:
                consecutive_failures += 1
            else:
                break
        
        # Update metrics
        self.current_metrics[service_name] = ServiceHealthMetrics(
            service_name=service_name,
            current_status=latest_result.status,
            last_check=latest_result.timestamp,
            success_rate_24h=success_rate,
            avg_response_time_ms=avg_response_time,
            consecutive_failures=consecutive_failures,
            last_error=latest_result.error_message
        )
    
    def _log_health_status(self, result: HealthCheckResult):
        """
        Log health status based on result.
        
        Args:
            result: Health check result
        """
        service_name = result.service_name.upper()
        response_time = f"{result.response_time_ms:.1f}ms"
        
        if result.status == ServiceStatus.HEALTHY:
            logger.info(f"🟢 {service_name} service is healthy (response: {response_time})")
        elif result.status == ServiceStatus.DEGRADED:
            logger.warning(f"🟡 {service_name} service is degraded (response: {response_time}): {result.error_message}")
        elif result.status == ServiceStatus.UNAVAILABLE:
            logger.error(f"🔴 {service_name} service is unavailable (response: {response_time}): {result.error_message}")
        else:
            logger.warning(f"⚪ {service_name} service status unknown (response: {response_time})")
    
    def get_service_health(self, service_name: str) -> Optional[ServiceHealthMetrics]:
        """
        Get current health metrics for a service.
        
        Args:
            service_name: Name of the service
            
        Returns:
            ServiceHealthMetrics or None if no data available
        """
        return self.current_metrics.get(service_name)
    
    def is_service_healthy(self, service_name: str) -> bool:
        """
        Check if a service is currently healthy.
        
        Args:
            service_name: Name of the service
            
        Returns:
            bool: True if service is healthy, False otherwise
        """
        metrics = self.get_service_health(service_name)
        if not metrics:
            return False  # Unknown status, assume unhealthy
        
        return metrics.current_status == ServiceStatus.HEALTHY
    
    def get_health_summary(self) -> Dict[str, Dict]:
        """
        Get a summary of all service health metrics.
        
        Returns:
            Dict containing health summary for all services
        """
        summary = {}
        
        for service_name, metrics in self.current_metrics.items():
            summary[service_name] = {
                "status": metrics.current_status.value,
                "last_check": metrics.last_check.isoformat(),
                "success_rate_24h": f"{metrics.success_rate_24h:.1f}%",
                "avg_response_time_ms": f"{metrics.avg_response_time_ms:.1f}ms",
                "consecutive_failures": metrics.consecutive_failures,
                "last_error": metrics.last_error
            }
        
        return summary


# Global health monitor instance
health_monitor = APIHealthMonitor()
