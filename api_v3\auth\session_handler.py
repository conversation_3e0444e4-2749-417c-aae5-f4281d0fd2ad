"""
API v3 Session Handler

Session-based authentication handler for API v3.
Provides a wrapper around the proven demo login functionality.
"""

from __future__ import annotations

import asyncio
from typing import Optional

import requests

from .session_manager import SessionManager, get_authenticated_session

from utils.central_logger import get_logger

logger = get_logger()


class APIV3SessionHandler:
    """
    Session handler for API v3 authentication.

    Wraps the proven demo/api3_demo/login.py implementation with async interface.
    """

    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        use_socks_proxy: bool = False,
        socks_url: str = "socks5h://127.0.0.1:9150",
    ):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.use_socks_proxy = use_socks_proxy or ".onion" in base_url
        self.socks_url = socks_url

        # Initialize session manager
        self.session_manager = SessionManager(
            base_url=base_url,
            username=username,
            password=password,
            use_socks_proxy=self.use_socks_proxy,
            socks_url=socks_url,
        )

        self._session: Optional[requests.Session] = None
        self._authenticated = False

    async def login(self) -> bool:
        """Perform login (async wrapper)"""
        try:
            # Run login in executor to handle sync requests
            loop = asyncio.get_event_loop()
            self._session = await loop.run_in_executor(
                None, self.session_manager.get_session
            )
            self._authenticated = True
            logger.info("✅ Login successful - session authenticated")
            return True
        except Exception as e:
            logger.error(f"❌ Login failed: {e}")
            self._authenticated = False
            return False

    async def ensure_session(self) -> requests.Session:
        """
        Ensure we have a valid authenticated session.
        This method was missing and causing AttributeError.
        """
        if not self._authenticated or self._session is None:
            success = await self.login()
            if not success:
                raise Exception("Failed to authenticate session")

        # Validate session periodically
        loop = asyncio.get_event_loop()
        is_valid = await loop.run_in_executor(
            None, self.session_manager.validate_session
        )

        if not is_valid:
            logger.warning("Session invalid, refreshing...")
            self._session = await loop.run_in_executor(
                None, self.session_manager.refresh_session
            )
            self._authenticated = True

        return self._session

    def is_authenticated(self) -> bool:
        """Check if currently authenticated"""
        return self._authenticated and self._session is not None

    def get_session(self) -> Optional[requests.Session]:
        """Get current session (sync)"""
        return self._session

    async def validate_session(self, force_check: bool = False) -> bool:
        """Validate current session (async wrapper)"""
        if self._session is None:
            return False

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, self.session_manager.validate_session, force_check
        )

    async def refresh_session(self) -> requests.Session:
        """Force refresh the session (async wrapper)"""
        loop = asyncio.get_event_loop()
        self._session = await loop.run_in_executor(
            None, self.session_manager.refresh_session
        )
        self._authenticated = True
        return self._session

    async def close(self):
        """
        Close the session handler.
        Note: Does not close the underlying session since it's shared globally
        via the session manager cache. This prevents disrupting other services.
        """
        # Don't close the session - it's managed by the global session cache
        # Closing it here would break other services using the same session
        self._session = None
        self._authenticated = False
        # session_manager.close() is a no-op for the same reason


# Legacy alias for backward compatibility
SessionHandler = APIV3SessionHandler
