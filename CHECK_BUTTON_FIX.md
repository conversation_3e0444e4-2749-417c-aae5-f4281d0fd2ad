# Check Button Fix - Hide Check Button for Already Checked Cards

## Problem

When a card is checked and has a valid completed status (like "Refunded", "Declined", "NonRefundable"):
1. The recheck button was showing after check completion
2. The check button was showing even when viewing an already-checked card
This allowed users to check the same card multiple times unnecessarily.

### Logs Showing the Issue

```
"status": "Refunded",
"refund_status": "refunded",
"refund_reason": "DECLINEDCVV : AUTH-FAILED",
⚠️ [Check Result] Unknown status '' - enabling recheck button
```

## Root Cause

The same issue as unmask/check - **matching by wrong card**:

1. Check API returns **2 cards** in response
2. Card 0: `status = ""` (empty)
3. Card 1: `status = "Refunded"` ✅ (the target card)
4. Code was using `extracted_cards[0]` (first card) instead of matching by `_id`
5. Read empty status → treated as "unknown" → enabled recheck button

## Fix

**Files Modified**: `handlers/orders_handlers.py`

### Part 1: Hide Recheck Button After Check Completes (lines 5636-5678)

This fix ensures the recheck button doesn't appear after a check is completed.

### Part 2: Hide Check Button When Viewing Already-Checked Cards (lines 2663-2668, 2696-2710)

This fix ensures the check button doesn't appear when first viewing a card that's already been checked.

### Changes Made

1. **Match card by `_id`** instead of using first card
2. **Read status from matched card** (not first card)
3. **Check both fields**: `status` and `refund_status`
4. **Hide recheck button** when status is completed

### Code Change

```python
# BEFORE:
card_status = extracted_cards[0].get("status", "").lower()  # ❌ Wrong card!

# AFTER:
# Find the specific card that was checked by matching _id
checked_card, _ = self._find_matching_card_by_id(extracted_cards, card_id)

if checked_card:
    # Get status from the matched card (not just first card!)
    card_status = checked_card.get("status", "").lower()
    refund_status = checked_card.get("refund_status", "").lower()
    
    # Use refund_status if status is empty
    if not card_status and refund_status:
        card_status = refund_status
```

### Status Logic

**COMPLETED (No Recheck Button):**
- ✅ `refunded`
- ✅ `declined`
- ✅ `nonrefundable`
- ✅ `dead`
- ✅ `invalid`
- ✅ `live`
- ✅ `active`
- ✅ `valid`
- ✅ `approved`

**ALLOW RECHECK (Show Recheck Button):**
- ⏳ `checking`
- ⏳ `pending`
- ⏳ `started`
- ⏳ `processing`
- ⏳ Empty or unknown status

## Testing

### Test 1: Check Button After Checking
1. Purchase a card
2. Click "🔍 Check Status"
3. Wait for check to complete with status (e.g., "Refunded")
4. **Expected**: Recheck button should NOT appear ✅

### Test 2: Check Button When Viewing Already-Checked Card
1. View a card that was previously checked (status: "Refunded")
2. **Expected**: Check button should NOT appear at all ✅
3. Only see: Unmask, Download, Back buttons

### Test 3: Check Button for Unchecked Card
1. View a newly purchased card (never checked)
2. **Expected**: Check button SHOULD appear with 30s timer ✅

## Logs After Fix

```
🎯 [Check] Found matching card with status='refunded', refund_status='refunded'
✅ [Check Result] Status is 'refunded' (DEAD/REFUNDED) - check completed, NO RECHECK BUTTON
```

## Benefits

- ✅ Prevents unnecessary rechecking of completed cards
- ✅ Cleaner UI (no confusing recheck button after completion)
- ✅ Matches correct card by `_id` (consistent with unmask fix)
- ✅ Checks both `status` and `refund_status` fields
- ✅ Clear logging for debugging

## Related Fixes

This fix uses the same `_find_matching_card_by_id()` helper function created for the unmask fix, ensuring consistent card matching across all operations:
- ✅ Unmask
- ✅ Check
- ✅ Download

