"""
API v3 Package

This package contains API v3 functionality based on the demo API implementation.
It includes:

- auth: Session-based authentication with form login
- config: API configuration management
- http: HTTP client with session handling
- services: Browse, cart, and workflow services
- models: Data models for cards and responses
- utils: Utility functions for filters, SOCKS, and Tor detection

This API uses form-based authentication and provides browse/filter functionality
with table-based data structure.
"""

__version__ = "3.0.0"
__author__ = "Bot v3 Team"

# Core API components
from .auth import LoginSession, SessionManager, get_authenticated_session
from .config import APIV3Config, create_api_v3_configuration, get_api_v3_config_from_env
from .http import APIV3HTTPClient
from .adapter import APIV3Adapter, get_api_v3_adapter

# Services
from .services import (
    APIV3BrowseService,
    APIV3BrowseParams,
    APIV3BrowseResponse,
    get_api_v3_browse_service,
    APIV3CartService,
    APIV3CheckoutService,
    APIV3OrderService,
    APIV3VirtualCartWorkflow,
)

# Models
from .models import APIV3Card, APIV3CardList

__all__ = [
    # Auth
    "LoginSession",
    "SessionManager",
    "get_authenticated_session",
    # Config
    "APIV3Config",
    "create_api_v3_configuration",
    "get_api_v3_config_from_env",
    # HTTP
    "APIV3HTTPClient",
    # Adapter (main entry point)
    "APIV3Adapter",
    "get_api_v3_adapter",
    # Services
    "APIV3BrowseService",
    "APIV3BrowseParams",
    "APIV3BrowseResponse",
    "get_api_v3_browse_service",
    "APIV3CartService",
    "APIV3CheckoutService",
    "APIV3OrderService",
    "APIV3VirtualCartWorkflow",
    # Models
    "APIV3Card",
    "APIV3CardList",
]
