"""
API Client Factory

Provides a factory pattern for creating API clients with different configurations,
making it easy to instantiate clients for different APIs or environments.
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, Type, Union

from ..core.interfaces import APIClientProtocol, APIConfigProtocol
from ..core.exceptions import ConfigurationError
from ..http.client import ConfigurableHTTPClient
from .api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from ..core.constants import HTTPMethod, AuthenticationType

from utils.central_logger import get_logger

logger = get_logger()


class APIClientFactory:
    """
    Factory for creating API clients with different configurations
    
    Supports:
    - Creating clients from configuration objects
    - Loading configurations from files (JSON, YAML)
    - Creating clients from dictionaries
    - Registering custom client types
    """
    
    def __init__(self):
        self._client_types: Dict[str, Type[APIClientProtocol]] = {
            "default": ConfigurableHTTPClient,
            "http": ConfigurableHTTPClient,
        }
        self._configurations: Dict[str, APIConfiguration] = {}
    
    def register_client_type(self, name: str, client_class: Type[APIClientProtocol]) -> None:
        """Register a custom client type"""
        self._client_types[name] = client_class
        logger.info(f"Registered client type: {name}")
    
    def register_configuration(self, config: APIConfiguration) -> None:
        """Register a configuration for later use"""
        self._configurations[config.name] = config
        logger.info(f"Registered configuration: {config.name}")
    
    def create_client(
        self,
        config: Union[APIConfiguration, str, Dict[str, Any]],
        client_type: str = "default"
    ) -> APIClientProtocol:
        """
        Create an API client from configuration
        
        Args:
            config: Configuration object, name of registered config, or config dict
            client_type: Type of client to create
            
        Returns:
            Configured API client instance
        """
        if client_type not in self._client_types:
            raise ConfigurationError(f"Unknown client type: {client_type}")
        
        # Resolve configuration
        if isinstance(config, str):
            if config not in self._configurations:
                raise ConfigurationError(f"Configuration '{config}' not found")
            api_config = self._configurations[config]
        elif isinstance(config, dict):
            api_config = self.create_configuration_from_dict(config)
        elif isinstance(config, APIConfiguration):
            api_config = config
        else:
            raise ConfigurationError(f"Invalid configuration type: {type(config)}")
        
        # Create client
        client_class = self._client_types[client_type]
        client = client_class(api_config)
        
        logger.info(f"Created {client_type} client for API: {api_config.name}")
        return client
    
    def create_configuration_from_dict(self, config_dict: Dict[str, Any]) -> APIConfiguration:
        """Create API configuration from dictionary"""
        try:
            return APIConfiguration.from_dict(config_dict)
        except Exception as e:
            raise ConfigurationError(f"Failed to create configuration from dict: {e}")
    
    def load_configuration_from_file(self, file_path: Union[str, Path]) -> APIConfiguration:
        """Load API configuration from file"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise ConfigurationError(f"Configuration file not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    config_dict = json.load(f)
                elif file_path.suffix.lower() in ['.yml', '.yaml']:
                    try:
                        import yaml
                        config_dict = yaml.safe_load(f)
                    except ImportError:
                        raise ConfigurationError("PyYAML is required to load YAML configuration files")
                else:
                    raise ConfigurationError(f"Unsupported configuration file format: {file_path.suffix}")
            
            config = self.create_configuration_from_dict(config_dict)
            logger.info(f"Loaded configuration from {file_path}: {config.name}")
            return config
            
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration from {file_path}: {e}")
    
    def create_client_from_file(
        self,
        file_path: Union[str, Path],
        client_type: str = "default"
    ) -> APIClientProtocol:
        """Create API client from configuration file"""
        config = self.load_configuration_from_file(file_path)
        return self.create_client(config, client_type)
    
    def list_registered_configurations(self) -> Dict[str, str]:
        """List all registered configurations"""
        return {name: config.description or "No description" for name, config in self._configurations.items()}
    
    def list_client_types(self) -> Dict[str, str]:
        """List all registered client types"""
        return {name: cls.__name__ for name, cls in self._client_types.items()}
    
    def create_api_v1_configuration(
        self,
        base_url: str = "https://ronaldo-club.to/api",
        login_token: Optional[str] = None,
        session_cookies: Optional[Dict[str, str]] = None
    ) -> APIConfiguration:
        """
        Create configuration for API v1 (existing implementation)
        
        This method creates a configuration that matches the existing API v1
        implementation to ensure backward compatibility.
        """
        # Define endpoints based on existing API v1 structure
        endpoints = {
            "list_items": EndpointConfiguration(
                name="list_items",
                path="/cards/hq/list",
                method=HTTPMethod.POST,
                description="List available items with filtering"
            ),
            "cart_view": EndpointConfiguration(
                name="cart_view",
                path="/cart/",
                method=HTTPMethod.GET,
                description="View cart contents"
            ),
            "cart_add": EndpointConfiguration(
                name="cart_add",
                path="/cart/",
                method=HTTPMethod.POST,
                description="Add item to cart"
            ),
            "cart_remove": EndpointConfiguration(
                name="cart_remove",
                path="/cart/",
                method=HTTPMethod.DELETE,
                description="Remove item from cart"
            ),
            "user_info": EndpointConfiguration(
                name="user_info",
                path="/user/getme",
                method=HTTPMethod.GET,
                description="Get user information"
            ),
            "checkout": EndpointConfiguration(
                name="checkout",
                path="/checkout/",
                method=HTTPMethod.POST,
                description="Process checkout"
            ),
            "check_order": EndpointConfiguration(
                name="check_order",
                path="/cards/hq/check",
                method=HTTPMethod.POST,
                description="Check order status"
            ),
        }
        
        # Set up authentication
        auth = AuthenticationConfiguration(
            type=AuthenticationType.BEARER_TOKEN if login_token else AuthenticationType.NONE,
            bearer_token=login_token,
        )
        
        # Default headers matching existing implementation
        default_headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.6",
            "origin": base_url.replace("/api", ""),
            "referer": base_url.replace("/api", "/store/cards/hq"),
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
        }
        
        # Add session cookies if provided
        if session_cookies:
            # Note: In a real implementation, you'd handle cookies differently
            # This is just for configuration compatibility
            default_headers.update({
                f"cookie-{k}": v for k, v in session_cookies.items()
            })
        
        config = APIConfiguration(
            name="api_v1",
            base_url=base_url,
            endpoints=endpoints,
            authentication=auth,
            default_headers=default_headers,
            timeout=TimeoutConfiguration(connect=10, read=30, total=60),
            retry=RetryConfiguration(max_attempts=3, delay=1.0, backoff_factor=1.0),
            description="API v1 - External Cart API (Ronaldo Club)",
            version="1.0",
            environment="production",
        )

        return config

    def create_api_v2_configuration(
        self,
        *,
        base_url: str = "https://ronaldo-club.to/api/cards/vhq/",
        login_token: Optional[str] = None,
        session_cookies: Optional[Dict[str, str]] = None,
        environment: str = "production",
    ) -> APIConfiguration:
        """Create configuration for API v2/BASE 2 integration."""

        from api_v2.config.api_config import create_api_v2_configuration

        return create_api_v2_configuration(
            base_url=base_url,
            login_token=login_token,
            session_cookies=session_cookies,
            environment=environment,
        )
    
    def create_example_configuration(self) -> APIConfiguration:
        """Create an example configuration for demonstration purposes"""
        endpoints = {
            "get_users": EndpointConfiguration(
                name="get_users",
                path="/users",
                method=HTTPMethod.GET,
                description="Get list of users"
            ),
            "create_user": EndpointConfiguration(
                name="create_user",
                path="/users",
                method=HTTPMethod.POST,
                description="Create a new user"
            ),
            "get_user": EndpointConfiguration(
                name="get_user",
                path="/users/{id}",
                method=HTTPMethod.GET,
                description="Get user by ID"
            ),
        }
        
        auth = AuthenticationConfiguration(
            type=AuthenticationType.API_KEY,
            api_key="your-api-key-here",
            api_key_header="X-API-Key",
        )
        
        config = APIConfiguration(
            name="example_api",
            base_url="https://api.example.com/v1",
            endpoints=endpoints,
            authentication=auth,
            description="Example API configuration",
            version="1.0",
            environment="development",
        )
        
        return config


# Global factory instance
api_client_factory = APIClientFactory()
