"""
🎨 Centralized Card Formatter - Single Source of Truth
=====================================================

This module is the SINGLE SOURCE OF TRUTH for all card detail formatting across the application.
ALL card display functionality should use this formatter to ensure consistency.

Usage Areas:
- View Card Details (orders handlers)
- Check Card Status  
- Post-Checkout Display
- Order History
- Download Card Data
- Any other card display scenarios

The formatter ensures consistent:
- UI styling and layout
- Emoji usage
- Field ordering
- Sensitive data masking
- Section organization
"""

from __future__ import annotations

import html
import re
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from utils.central_logger import get_logger

logger = get_logger()


class CardDisplayMode(Enum):
    """Display mode for card formatting"""
    FULL = "full"  # Complete card details with all fields
    COMPACT = "compact"  # Shortened version for inline display
    MASKED = "masked"  # Preview with sensitive data masked
    UNMASKED = "unmasked"  # Full details with sensitive data visible


@dataclass
class CardFormattingOptions:
    """Options for card formatting"""
    mode: CardDisplayMode = CardDisplayMode.FULL
    show_sensitive: bool = True
    show_order_info: bool = True
    show_timestamps: bool = True
    show_technical_details: bool = True
    show_location: bool = True
    compact: bool = False
    # Header customization
    title_override: Optional[str] = None
    include_watermark: bool = False


class CentralizedCardFormatter:
    """
    🎯 THE SINGLE SOURCE OF TRUTH FOR CARD FORMATTING
    
    This class handles ALL card display scenarios with consistent styling.
    """
    
    # =====================================================================
    # SECTION 1: MAIN PUBLIC API
    # =====================================================================
    
    @staticmethod
    def format_card_details(
        card_data: Dict[str, Any],
        options: Optional[CardFormattingOptions] = None
    ) -> str:
        """
        🎨 Main entry point for card formatting
        
        This is the PRIMARY METHOD to format card details.
        Use this for all card display needs.
        
        Args:
            card_data: Card data dictionary from API
            options: Formatting options (defaults to FULL mode with sensitive data)
            
        Returns:
            Formatted card details string with HTML formatting
            
        Example:
            >>> formatter = CentralizedCardFormatter()
            >>> formatted = formatter.format_card_details(card_data)
            >>> await bot.send_message(chat_id, formatted, parse_mode="HTML")
        """
        if options is None:
            options = CardFormattingOptions()
        
        try:
            if options.mode == CardDisplayMode.MASKED:
                return CentralizedCardFormatter._format_masked_preview(card_data, options)
            elif options.mode == CardDisplayMode.COMPACT:
                return CentralizedCardFormatter._format_compact_view(card_data, options)
            else:
                # FULL or UNMASKED mode
                return CentralizedCardFormatter._format_complete_view(card_data, options)
                
        except Exception as e:
            logger.error(f"Error formatting card details: {e}", exc_info=True)
            return CentralizedCardFormatter._format_error_fallback(card_data)
    
    # =====================================================================
    # SECTION 2: FORMATTING MODES
    # =====================================================================
    
    @staticmethod
    def _format_complete_view(card_data: Dict[str, Any], options: CardFormattingOptions) -> str:
        """
        📋 Format complete card view with all details
        
        This is the primary format matching the user's desired UI.
        """
        sections = []
        
        # ═══════════════════════════════════════════════════════════
        # HEADER
        # ═══════════════════════════════════════════════════════════
        if options.title_override:
            sections.append(f"💳 <b>{options.title_override}</b> 📋")
        else:
            sections.append("💳 <b>Card Details</b> 📋")
        
        sections.append("━━━━━━━━━━━━━━━━━━━━━━")
        sections.append("")
        
        # ═══════════════════════════════════════════════════════════
        # PAYMENT CREDENTIALS SECTION
        # ═══════════════════════════════════════════════════════════
        payment_section = CentralizedCardFormatter._build_payment_credentials_section(
            card_data, options.show_sensitive
        )
        if payment_section:
            sections.append("🔓 <b>Payment Credentials</b>")
            sections.append("┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈")
            sections.append(payment_section)
            sections.append("")
        
        # ═══════════════════════════════════════════════════════════
        # CARD INFORMATION SECTION
        # ═══════════════════════════════════════════════════════════
        card_info_section = CentralizedCardFormatter._build_card_information_section(card_data)
        if card_info_section:
            sections.append("💎 <b>Card Information</b>")
            sections.append("┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈")
            sections.append(card_info_section)
            sections.append("")
        
        # ═══════════════════════════════════════════════════════════
        # CARDHOLDER DETAILS SECTION
        # ═══════════════════════════════════════════════════════════
        if not options.compact:
            cardholder_section = CentralizedCardFormatter._build_cardholder_details_section(
                card_data, options.show_sensitive
            )
            if cardholder_section:
                sections.append("👥 <b>Cardholder Details</b>")
                sections.append("┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈")
                sections.append(cardholder_section)
                sections.append("")
        
        # ═══════════════════════════════════════════════════════════
        # CHECK STATUS SECTION (AT THE END)
        # ═══════════════════════════════════════════════════════════
        if not options.compact:
            check_section = CentralizedCardFormatter._build_check_status_section(card_data)
            if check_section:
                sections.append("🔍 <b>Card Check Status</b>")
                sections.append("┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈")
                sections.append(check_section)
                sections.append("")
        
        # ═══════════════════════════════════════════════════════════
        # FOOTER
        # ═══════════════════════════════════════════════════════════
        sections.append("━━━━━━━━━━━━━━━━━━━━━━")
        sections.append("🔒 <b>Keep secure and private</b>")
        
        return "\n".join(sections)
    
    @staticmethod
    def _format_masked_preview(card_data: Dict[str, Any], options: CardFormattingOptions) -> str:
        """
        🔒 Format masked preview (before unmask)
        """
        sections = []
        
        sections.append("💳 <b>Card Preview</b> 📋")
        sections.append("━━━━━━━━━━━━━━━━━━━━━━")
        sections.append("")
        
        # Card information (non-sensitive)
        card_info_section = CentralizedCardFormatter._build_card_information_section(card_data)
        if card_info_section:
            sections.append("💎 <b>Card Information</b>")
            sections.append("┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈")
            sections.append(card_info_section)
            sections.append("")
        
        sections.append("━━━━━━━━━━━━━━━━━━━━━━")
        sections.append("🔒 <b>Unmask to view full details</b>")
        
        return "\n".join(sections)
    
    @staticmethod
    def _format_compact_view(card_data: Dict[str, Any], options: CardFormattingOptions) -> str:
        """
        📦 Format compact view for inline/quick display
        """
        lines = []
        
        # Essential info only
        bank = card_data.get("bank", "")
        brand = card_data.get("brand", "")
        level = card_data.get("level", "")
        country = card_data.get("country", "")
        price = card_data.get("price")
        status = card_data.get("status", "")
        
        if bank:
            lines.append(f"🏦 {bank}")
        
        card_type = []
        if brand:
            card_type.append(brand)
        if level:
            card_type.append(level)
        if card_type:
            lines.append(f"🃏 {' • '.join(card_type)}")
        
        if country:
            lines.append(f"🇷🇸 {country}")
        
        if price is not None:
            try:
                lines.append(f"💰 ${float(price):.2f}")
            except (ValueError, TypeError):
                pass
        
        if status:
            lines.append(f"📋 {status}")
        
        return "\n".join(lines) if lines else "No card details available"
    
    # =====================================================================
    # SECTION 3: SECTION BUILDERS
    # =====================================================================
    
    @staticmethod
    def _build_payment_credentials_section(card_data: Dict[str, Any], show_sensitive: bool) -> str:
        """
        🔐 Build payment credentials section
        
        Format:
        💳 Card: ****************
        📅 Expiry: 10/25
        🔐 CVV: 334
        """
        lines = []
        
        # Card Number
        card_number = CentralizedCardFormatter._extract_card_number(card_data)
        if card_number:
            if show_sensitive:
                # Show full number
                clean_number = CentralizedCardFormatter._clean_card_number(card_number)
                lines.append(f"💳 <b>Card:</b> <code>{clean_number}</code>")
            else:
                # Mask the number
                masked = CentralizedCardFormatter._mask_card_number(card_number)
                lines.append(f"💳 <b>Card:</b> <code>{masked}</code>")
        else:
            # Check if requires download
            if card_data.get("unmasked_but_requires_download") or card_data.get("requires_download"):
                lines.append(f"💳 <b>Card:</b> 📥 <i>Download required</i>")
            else:
                lines.append(f"💳 <b>Card:</b> <i>Not available</i>")
        
        # Expiry Date
        expiry = CentralizedCardFormatter._extract_expiry(card_data)
        if expiry:
            lines.append(f"📅 <b>Expiry:</b> <code>{expiry}</code>")
        else:
            lines.append(f"📅 <b>Expiry:</b> <i>Not available</i>")
        
        # CVV
        cvv = CentralizedCardFormatter._extract_cvv(card_data)
        if cvv:
            if show_sensitive:
                lines.append(f"🔐 <b>CVV:</b> <code>{cvv}</code>")
            else:
                lines.append(f"🔐 <b>CVV:</b> <code>***</code>")
        else:
            lines.append(f"🔐 <b>CVV:</b> <i>Not available</i>")
        
        return "\n".join(lines)
    
    @staticmethod
    def _build_card_information_section(card_data: Dict[str, Any]) -> str:
        """
        💎 Build card information section
        
        Format:
        🏦 Bank: KOMERCIJALNA BANKA AD BEOGRAD CJSC
        🃏 Type: DEBIT
        ⭐ Level: PREPAID
        🇷🇸 Country: RS
        🔢 BIN: 431374
        """
        lines = []
        
        # Bank
        bank = card_data.get("bank", "")
        if bank and bank not in ["Unknown Bank", "Unknown", "N/A"]:
            lines.append(f"🏦 <b>Bank:</b> {html.escape(str(bank))}")
        
        # Type (DEBIT/CREDIT)
        card_type = card_data.get("type", "") or card_data.get("card_type", "")
        if card_type and str(card_type) not in ["Unknown", "N/A", ""]:
            lines.append(f"🃏 <b>Type:</b> {html.escape(str(card_type).upper())}")
        
        # Level (PREPAID, CLASSIC, etc.)
        level = card_data.get("level", "")
        if level and str(level) not in ["Unknown", "N/A", ""]:
            lines.append(f"⭐ <b>Level:</b> {html.escape(str(level).upper())}")
        
        # Country
        country = card_data.get("country", "")
        if country and str(country) not in ["Unknown", "N/A", ""]:
            # Add country flag emoji if available
            country_str = str(country).upper()
            lines.append(f"🇷🇸 <b>Country:</b> {country_str}")
        
        # BIN
        bin_info = card_data.get("bin", "") or card_data.get("bin_info", "")
        if bin_info and str(bin_info) not in ["Unknown", "N/A", ""]:
            lines.append(f"🔢 <b>BIN:</b> {html.escape(str(bin_info))}")
        
        return "\n".join(lines)
    
    @staticmethod
    def _build_cardholder_details_section(card_data: Dict[str, Any], show_sensitive: bool) -> str:
        """
        👥 Build cardholder details section
        
        Format:
        👤 Name: Nevena Popovic
        📧 Email: <EMAIL>
        📞 Phone: 0663997391
        📍 Address: 130 Obilicev Venac
        🏙️ Location: Vojvodina, Subotica, 24953
        """
        lines = []
        
        # Name
        name = card_data.get("cardholder_name", "") or card_data.get("name", "") or card_data.get("holder", "")
        if name and str(name) not in ["Unknown", "N/A", ""]:
            if show_sensitive:
                # Use zero-width joiner to preserve RTL display
                lines.append(f"👤 <b>Name:</b> \u200E{html.escape(str(name))}")
            else:
                masked_name = CentralizedCardFormatter._mask_name(str(name))
                lines.append(f"👤 <b>Name:</b> {html.escape(masked_name)}")
        
        # Email
        email = card_data.get("email", "")
        if email and str(email) not in ["Unknown", "N/A", "", "0", "1"]:
            if show_sensitive:
                lines.append(f"📧 <b>Email:</b> {html.escape(str(email))}")
            else:
                masked_email = CentralizedCardFormatter._mask_email(str(email))
                lines.append(f"📧 <b>Email:</b> {html.escape(masked_email)}")
        
        # Phone
        phone = card_data.get("phone", "")
        if phone and str(phone) not in ["Unknown", "N/A", "", "0", "1"]:
            if show_sensitive:
                lines.append(f"📞 <b>Phone:</b> {html.escape(str(phone))}")
            else:
                masked_phone = CentralizedCardFormatter._mask_phone(str(phone))
                lines.append(f"📞 <b>Phone:</b> {html.escape(masked_phone)}")
        
        # Address
        address = card_data.get("address", "")
        if address and str(address) not in ["Unknown", "N/A", "", "0", "1"]:
            if show_sensitive:
                # Use zero-width joiner for proper display
                lines.append(f"📍 <b>Address:</b> \u200E{html.escape(str(address))}")
            else:
                masked_address = CentralizedCardFormatter._mask_address(str(address))
                lines.append(f"📍 <b>Address:</b> {html.escape(masked_address)}")
        
        # Location (City, State, ZIP)
        location_parts = []
        state = card_data.get("state", "")
        city = card_data.get("city", "")
        zip_code = card_data.get("zip", "") or card_data.get("zip_code", "")
        
        if state and str(state) not in ["Unknown", "N/A", ""]:
            location_parts.append(str(state))
        if city and str(city) not in ["Unknown", "N/A", ""]:
            location_parts.append(str(city))
        if zip_code and str(zip_code) not in ["Unknown", "N/A", ""]:
            location_parts.append(str(zip_code))
        
        if location_parts:
            location_str = ", ".join(location_parts)
            lines.append(f"🏙️ <b>Location:</b> {html.escape(location_str)}")
        
        return "\n".join(lines)
    
    @staticmethod
    def _build_order_information_section(card_data: Dict[str, Any]) -> str:
        """
        📦 Build order information section
        
        Format:
        📋 Status: Nonrefundable
        💰 Price: $8.99
        """
        lines = []
        
        # Status
        status = card_data.get("status", "")
        if status and str(status) not in ["Unknown", "N/A", ""]:
            status_str = str(status).title()
            status_emoji = CentralizedCardFormatter._get_status_emoji(status_str)
            lines.append(f"📋 <b>Status:</b> {status_emoji} {status_str}")
        
        # Price
        price = card_data.get("price")
        if price is not None:
            try:
                price_float = float(price)
                lines.append(f"💰 <b>Price:</b> ${price_float:.2f}")
            except (ValueError, TypeError):
                pass
        
        # Order ID (if available)
        order_id = card_data.get("order_id", "") or card_data.get("_id", "")
        if order_id and str(order_id) not in ["Unknown", "N/A", ""]:
            # Truncate long IDs
            order_id_str = str(order_id)
            if len(order_id_str) > 20:
                order_id_str = order_id_str[:17] + "..."
            lines.append(f"🆔 <b>Order ID:</b> <code>{order_id_str}</code>")
        
        return "\n".join(lines)
    
    @staticmethod
    def _build_check_status_section(card_data: Dict[str, Any]) -> str:
        """
        🔍 Build card check status section
        
        Format:
        🟢 Status: LIVE
        ✅ Verified: 2025-01-15 14:30:22 UTC
        🎯 Response: Approved
        💳 Gateway: Stripe
        🔢 Auth Code: 123456
        """
        lines = []
        
        # Check if card has been checked
        is_checked = card_data.get("is_checked") or card_data.get("checked")
        check_status = card_data.get("check_status", "")
        card_status = card_data.get("card_status", "")
        
        # Also check the main 'status' field which might contain check results
        main_status = card_data.get("status", "")
        
        # Log for debugging
        from utils.central_logger import get_logger
        logger = get_logger()
        logger.info(f"🔍 [Check Status Section] Building section for card...")
        logger.info(f"  - is_checked: {is_checked}")
        logger.info(f"  - check_status: '{check_status}'")
        logger.info(f"  - card_status: '{card_status}'")
        logger.info(f"  - main_status: '{main_status}'")
        
        # Determine if card is live
        is_live = False
        status_display = ""
        
        # Priority 1: Use check_status if available
        if check_status:
            status_lower = str(check_status).lower()
            is_live = status_lower in ["live", "approved", "valid", "active", "nonrefundable"]
            status_display = str(check_status).upper()
        # Priority 2: Use card_status if available
        elif card_status:
            status_lower = str(card_status).lower()
            is_live = status_lower in ["live", "approved", "valid", "active", "nonrefundable"]
            status_display = str(card_status).upper()
        # Priority 3: Use main status field if it looks like a check result
        # NOTE: For API v1, statuses like "Refunded", "NonRefundable" ARE check results
        # We don't require is_checked flag for these obvious check result statuses
        elif main_status:
            status_lower = str(main_status).lower()
            logger.info(f"  - Checking if main_status '{status_lower}' is a check result...")
            # Check if status is a check result (not just "active" or "pending")
            if status_lower in ["nonrefundable", "refunded", "declined", "approved", "valid", "invalid"]:
                is_live = status_lower in ["nonrefundable", "approved", "valid", "active"]
                status_display = str(main_status).upper()
                logger.info(f"  ✅ YES! Using main_status as check result: {status_display}, is_live={is_live}")
                # Mark as checked since the status proves it was checked
                is_checked = True
            else:
                logger.info(f"  ❌ NO - '{status_lower}' is not a recognized check result status")
        
        # Show status if available
        if status_display:
            # Status with emoji
            if is_live:
                lines.append(f"🟢 <b>Status:</b> <b>{status_display}</b>")
            else:
                lines.append(f"🔴 <b>Status:</b> <b>{status_display}</b>")
        elif is_checked:
            # Card was checked but no clear status - show generic checked message
            lines.append(f"✓ <b>Card has been checked</b>")
            logger.debug("[Check Status Section] Card marked as checked but no status display available")
        
        # Check timestamp - support both camelCase (API v1) and snake_case (internal)
        checked_at = (
            card_data.get("checked_at") 
            or card_data.get("checkedAt") 
            or card_data.get("check_Date") 
            or card_data.get("checkDate")
        )
        if checked_at:
            try:
                if isinstance(checked_at, str):
                    # Parse ISO format or other formats
                    from datetime import datetime
                    try:
                        dt = datetime.fromisoformat(checked_at.replace('Z', '+00:00'))
                        checked_str = dt.strftime("%Y-%m-%d %H:%M:%S UTC")
                    except:
                        checked_str = str(checked_at)
                else:
                    checked_str = str(checked_at)
                lines.append(f"✅ <b>Verified:</b> {checked_str}")
            except:
                pass
        
        # Response code/message
        response = card_data.get("response") or card_data.get("response_code") or card_data.get("responseCode")
        if response and str(response) not in ["Unknown", "N/A", ""]:
            lines.append(f"🎯 <b>Response:</b> {html.escape(str(response))}")
        
        # Message from gateway
        message = card_data.get("message") or card_data.get("response_message")
        if message and str(message) not in ["Unknown", "N/A", ""]:
            # Truncate long messages
            message_str = str(message)
            if len(message_str) > 50:
                message_str = message_str[:47] + "..."
            lines.append(f"💬 <b>Message:</b> {html.escape(message_str)}")
        # If no response/message but we have a check result status, show default message
        elif status_display and not response and not message:
            status_lower = str(main_status).lower() if main_status else ""
            if status_lower == "nonrefundable":
                lines.append(f"💬 <b>Message:</b> ✅ Card is working! No refund issued - ready to use")
            elif status_lower == "refunded":
                lines.append(f"💬 <b>Message:</b> 💰 Card was declined - Full refund issued automatically to your balance")
        
        # Gateway
        gateway = card_data.get("gateway") or card_data.get("processor")
        if gateway and str(gateway) not in ["Unknown", "N/A", ""]:
            lines.append(f"💳 <b>Gateway:</b> {html.escape(str(gateway))}")
        
        # Auth code
        auth_code = card_data.get("auth_code") or card_data.get("authCode") or card_data.get("authorization_code")
        if auth_code and str(auth_code) not in ["Unknown", "N/A", "", "0"]:
            lines.append(f"🔢 <b>Auth Code:</b> <code>{html.escape(str(auth_code))}</code>")
        
        result = "\n".join(lines)
        logger.info(f"🔍 [Check Status Section] Generated {len(lines)} lines, result length: {len(result)}")
        if result:
            logger.info(f"  ✅ Section will be displayed!")
        else:
            logger.warning(f"  ⚠️ Section is EMPTY - will NOT be displayed!")
        
        return result
    
    # =====================================================================
    # SECTION 4: DATA EXTRACTION HELPERS
    # =====================================================================
    
    @staticmethod
    def _extract_card_number(card_data: Dict[str, Any]) -> Optional[str]:
        """Extract card number from various possible fields"""
        possible_fields = [
            "card_number",
            "full_card_number", 
            "cardNumber",
            "number",
            "pan",
            "cc",
            "ccnum"
        ]
        
        for field in possible_fields:
            value = card_data.get(field)
            if value and str(value) not in ["[PAN_REDACTED]", "N/A", "Unknown", "", "0"]:
                return str(value)
        
        return None
    
    @staticmethod
    def _extract_expiry(card_data: Dict[str, Any]) -> Optional[str]:
        """Extract expiry date from various possible fields"""
        possible_fields = [
            "expiry_date",
            "expiry",
            "exp",
            "expiryDate",
            "expiration"
        ]
        
        for field in possible_fields:
            value = card_data.get(field)
            if value and str(value) not in ["N/A", "Unknown", "", "0"]:
                return str(value)
        
        return None
    
    @staticmethod
    def _extract_cvv(card_data: Dict[str, Any]) -> Optional[str]:
        """Extract CVV from various possible fields"""
        possible_fields = [
            "cvv",
            "cvv2",
            "cvc",
            "csc",
            "securityCode"
        ]
        
        for field in possible_fields:
            value = card_data.get(field)
            if value and str(value) not in ["N/A", "Unknown", "", "0"]:
                return str(value)
        
        return None
    
    # =====================================================================
    # SECTION 5: MASKING UTILITIES
    # =====================================================================
    
    @staticmethod
    def _mask_card_number(card_number: str) -> str:
        """Mask card number for privacy"""
        if not card_number:
            return "****"
        
        # Remove spaces and special characters
        clean = re.sub(r'[^\d]', '', str(card_number))
        
        if len(clean) >= 8:
            # Show first 4 and last 4
            return f"{clean[:4]}********{clean[-4:]}"
        elif len(clean) >= 4:
            return f"{clean[:2]}****{clean[-2:]}"
        else:
            return "****"
    
    @staticmethod
    def _clean_card_number(card_number: str) -> str:
        """Clean card number (remove extra spaces but keep readable format)"""
        clean = re.sub(r'[^\d]', '', str(card_number))
        # Format as: XXXX XXXX XXXX XXXX or return as-is if not standard length
        if len(clean) == 16:
            return f"{clean[:4]} {clean[4:8]} {clean[8:12]} {clean[12:]}"
        elif len(clean) == 15:
            return f"{clean[:4]} {clean[4:10]} {clean[10:]}"
        else:
            return clean
    
    @staticmethod
    def _mask_email(email: str) -> str:
        """Mask email for privacy"""
        if not email or "@" not in email:
            return email
        
        try:
            local, domain = email.split("@", 1)
            if len(local) <= 2:
                masked_local = local[0] + "*"
            else:
                masked_local = local[0] + "*" * (len(local) - 2) + local[-1]
            return f"{masked_local}@{domain}"
        except Exception:
            return email
    
    @staticmethod
    def _mask_phone(phone: str) -> str:
        """Mask phone number for privacy"""
        if not phone:
            return ""
        
        digits = re.sub(r'[^\d]', '', str(phone))
        
        if len(digits) >= 10:
            return f"({digits[:3]}) ***-**{digits[-2:]}"
        elif len(digits) >= 4:
            return f"{digits[:2]}****{digits[-2:]}"
        else:
            return "****"
    
    @staticmethod
    def _mask_name(name: str) -> str:
        """Mask cardholder name for privacy"""
        if not name:
            return ""
        
        parts = str(name).strip().split()
        if len(parts) == 1:
            if len(parts[0]) <= 2:
                return parts[0]
            return f"{parts[0][0]}***{parts[0][-1]}"
        else:
            first_name = parts[0]
            last_name = parts[-1]
            if len(last_name) <= 2:
                masked_last = last_name
            else:
                masked_last = f"{last_name[0]}***{last_name[-1]}"
            return f"{first_name} {masked_last}"
    
    @staticmethod
    def _mask_address(address: str) -> str:
        """Mask address for privacy"""
        if not address:
            return ""
        
        parts = str(address).split(",")
        if len(parts) > 1:
            return f"{parts[0]}, ***"
        else:
            words = address.split()
            if len(words) > 2:
                return f"{words[0]} {words[1]} ***"
            else:
                return address[:10] + "***" if len(address) > 10 else address
    
    # =====================================================================
    # SECTION 6: UTILITY HELPERS
    # =====================================================================
    
    @staticmethod
    def _get_status_emoji(status: str) -> str:
        """Get emoji for card status"""
        status_lower = str(status).lower()
        
        if status_lower in ["live", "active", "valid", "approved"]:
            return "✅"
        elif status_lower in ["refunded", "dead", "invalid", "declined", "nonrefundable"]:
            return "❌"
        elif status_lower in ["pending", "checking", "processing", "started"]:
            return "⏳"
        else:
            return "📋"
    
    @staticmethod
    def _format_error_fallback(card_data: Dict[str, Any]) -> str:
        """Simple fallback format if main formatting fails"""
        return (
            "💳 <b>Card Details</b>\n"
            "━━━━━━━━━━━━━━━━━━━━━━\n"
            "\n"
            "❌ Unable to format card details properly.\n"
            f"Status: {card_data.get('status', 'Unknown')}\n"
            "\n"
            "Please contact support if this issue persists."
        )


# =====================================================================
# CONVENIENCE FUNCTIONS FOR EASY IMPORT
# =====================================================================

def format_card_full(card_data: Dict[str, Any], show_sensitive: bool = True) -> str:
    """
    Quick format card with full details
    
    Usage:
        from utils.card_formatter import format_card_full
        formatted = format_card_full(card_data, show_sensitive=True)
    """
    options = CardFormattingOptions(
        mode=CardDisplayMode.FULL,
        show_sensitive=show_sensitive
    )
    return CentralizedCardFormatter.format_card_details(card_data, options)


def format_card_masked(card_data: Dict[str, Any]) -> str:
    """
    Quick format card with masked/preview mode
    
    Usage:
        from utils.card_formatter import format_card_masked
        formatted = format_card_masked(card_data)
    """
    options = CardFormattingOptions(
        mode=CardDisplayMode.MASKED,
        show_sensitive=False
    )
    return CentralizedCardFormatter.format_card_details(card_data, options)


def format_card_compact(card_data: Dict[str, Any]) -> str:
    """
    Quick format card in compact mode
    
    Usage:
        from utils.card_formatter import format_card_compact
        formatted = format_card_compact(card_data)
    """
    options = CardFormattingOptions(
        mode=CardDisplayMode.COMPACT,
        compact=True
    )
    return CentralizedCardFormatter.format_card_details(card_data, options)

