#!/usr/bin/env python3
"""
Simplified login module - maintains core functionality with reduced complexity
"""
import os
import sys
import logging
from typing import Optional, Tuple, List, Dict, Any
from urllib.parse import urljoin, unquote

import requests
from bs4 import BeautifulSoup
from dotenv import load_dotenv


def setup_logger() -> logging.Logger:
    """Setup basic logging"""
    logger = logging.getLogger("login")
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            "%(asctime)s | %(levelname)s | %(message)s", "%H:%M:%S"
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.setLevel(
        getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper(), logging.INFO)
    )
    return logger


# Load environment and setup logging
load_dotenv(override=True)
log = setup_logger()

# Export logger for other modules
logger = log

# Configuration
BASE_URL = os.getenv("BASE_URL", "").strip()
if not BASE_URL:
    log.error("BASE_URL is required in environment variables")
    sys.exit(1)

if not BASE_URL.startswith(("http://", "https://")):
    BASE_URL = "https://" + BASE_URL

REFERER = BASE_URL.rstrip("/") + "/"
LOGIN_URL = urljoin(REFERER, "login")
USERNAME = os.getenv("USERNAME", "").strip()
PASSWORD = os.getenv("PASSWORD", "").strip()

# Proxy settings
USE_PROXY = (
    os.getenv("USE_SOCKS_PROXY", "").lower() in {"1", "true", "yes"}
    or ".onion" in BASE_URL
)
SOCKS_URL = os.getenv("SOCKS_URL", "socks5h://127.0.0.1:9150")


# Utility functions


# Utility functions
def log_request(response: requests.Response) -> None:
    """Log request details"""
    log.info(f"Request: {response.request.method} {response.request.url}")


def log_response(response: requests.Response) -> None:
    """Log response details"""
    log.info(f"Response: {response.status_code} ({len(response.text)} chars)")


def refresh_xsrf_headers_from_cookies(session: requests.Session) -> None:
    """Refresh XSRF headers from cookies"""
    xsrf_token = None
    for cookie in session.cookies:
        if cookie.name == "XSRF-TOKEN":
            xsrf_token = cookie.value
            break

    if xsrf_token:
        try:
            header_value = unquote(xsrf_token)
        except Exception:
            header_value = xsrf_token
        session.headers.update(
            {
                "X-XSRF-TOKEN": header_value,
                "X-CSRF-TOKEN": header_value,
            }
        )


def cookies_to_serializable(cookies) -> List[Dict[str, Any]]:
    """Convert cookies to serializable format"""
    cookie_list = []
    for cookie in cookies:
        cookie_data = {
            "name": cookie.name,
            "value": cookie.value,
            "domain": cookie.domain,
            "path": cookie.path,
            "secure": cookie.secure,
            "expires": cookie.expires,
        }
        cookie_list.append(cookie_data)
    return cookie_list


def load_cookies_into_jar(cookie_jar, cookie_list: List[Dict[str, Any]]) -> int:
    """Load cookies from list into cookie jar"""
    count = 0
    for cookie_data in cookie_list:
        try:
            cookie_jar.set(
                name=cookie_data["name"],
                value=cookie_data["value"],
                domain=cookie_data.get("domain"),
                path=cookie_data.get("path", "/"),
            )
            count += 1
        except Exception as e:
            log.warning(f"Failed to load cookie: {e}")
    return count


def prune_cookie_duplicates(cookie_jar, cookie_name: str, base_url: str = None) -> None:
    """Remove duplicate cookies, keeping only the most recent"""
    cookies_to_remove = []
    seen_cookies = {}

    for cookie in cookie_jar:
        if cookie.name == cookie_name:
            key = f"{cookie.domain}{cookie.path}"
            if key in seen_cookies:
                cookies_to_remove.append(seen_cookies[key])
            seen_cookies[key] = cookie

    for cookie in cookies_to_remove:
        try:
            cookie_jar.clear(domain=cookie.domain, path=cookie.path, name=cookie.name)
        except Exception:
            pass


class LoginSession:
    """Simplified login session handler"""

    def __init__(self):
        self.session = self._create_session()

    def _create_session(self) -> requests.Session:
        """Create HTTP session with proper headers"""
        session = requests.Session()
        session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive",
                "Referer": REFERER,
            }
        )

        if USE_PROXY:
            log.info("Using SOCKS proxy: %s", SOCKS_URL)
            session.proxies.update({"http": SOCKS_URL, "https": SOCKS_URL})

        return session

    def _get_cookie_value(self, name: str) -> Optional[str]:
        """Get cookie value safely"""
        try:
            return self.session.cookies.get(name)
        except Exception:
            return None

    def _extract_csrf_token(self, html: str) -> Optional[str]:
        """Extract CSRF token from HTML"""
        soup = BeautifulSoup(html, "html.parser")

        # Check input fields
        for name in ("_token", "csrf_token", "csrf", "token"):
            token_input = soup.find("input", {"name": name})
            if token_input and token_input.get("value"):
                return token_input.get("value").strip()

        # Check meta tags
        meta_token = soup.find("meta", {"name": "csrf-token"})
        if meta_token and meta_token.get("content"):
            return meta_token.get("content").strip()

        return None

    def _build_login_form(self, html: str) -> Optional[Dict[str, str]]:
        """Build login form data from HTML"""
        soup = BeautifulSoup(html, "html.parser")

        # Find form with password field
        login_form = None
        for form in soup.find_all("form"):
            if form.find("input", {"type": "password"}):
                login_form = form
                break

        if not login_form:
            return None

        form_data = {}

        # Process all inputs
        for inp in login_form.find_all("input"):
            name = inp.get("name")
            if not name:
                continue

            input_type = inp.get("type", "text").lower()
            value = inp.get("value", "")

            if input_type in ("hidden", "text", "email"):
                form_data[name] = value
            elif input_type == "password":
                form_data[name] = PASSWORD

        # Find and set username field
        username_field = self._find_username_field(login_form)
        if username_field:
            form_data[username_field] = USERNAME

        return form_data if form_data else None

    def _find_username_field(self, form) -> Optional[str]:
        """Find username field in login form"""
        for inp in form.find_all("input"):
            name = inp.get("name", "").lower()
            input_type = inp.get("type", "").lower()
            if input_type in ("text", "email") and any(
                keyword in name for keyword in ("username", "email", "login", "user")
            ):
                return inp.get("name")
        return None

    def fetch_login_page(self) -> Tuple[Optional[str], Optional[str]]:
        """Fetch login page and extract tokens"""
        log.info("Fetching login page: %s", LOGIN_URL)

        try:
            response = self.session.get(LOGIN_URL, timeout=30)
            response.raise_for_status()

            # Extract CSRF token
            csrf_token = self._extract_csrf_token(response.text)

            # Update XSRF headers from cookies
            refresh_xsrf_headers_from_cookies(self.session)

            log.info("Login page fetched successfully")
            return csrf_token, response.text

        except requests.RequestException as e:
            log.error("Failed to fetch login page: %s", e)
            return None, None

    def perform_login(self, csrf_token: Optional[str], login_html: str) -> bool:
        """Perform login with credentials"""
        if not USERNAME or not PASSWORD:
            log.error("Username and password are required")
            return False

        # Build form data
        form_data = self._build_login_form(login_html)
        if not form_data:
            # Fallback form data
            form_data = {
                "_token": csrf_token or "",
                "username": USERNAME,
                "password": PASSWORD,
            }

        log.info("Attempting login...")

        try:
            response = self.session.post(
                LOGIN_URL, data=form_data, timeout=30, allow_redirects=False
            )

            # Check if login was successful
            if response.is_redirect:
                # Follow redirect
                location = response.headers.get("Location")
                if location:
                    redirect_url = urljoin(LOGIN_URL, location)
                    response = self.session.get(redirect_url, timeout=30)

            # Check for successful login indicators
            session_cookie = self._get_cookie_value("bbm_session")
            if session_cookie:
                log.info("Login request completed - session cookie received")
                return True
            elif response.is_redirect:
                log.info("Login request completed - redirect received")
                return True
            else:
                log.warning("Login may have failed - no clear success indicators")
                return True  # Let verify_login_success() do the final check

        except requests.RequestException as e:
            log.error("Login failed: %s", e)
            return False

    def verify_login_success(self) -> bool:
        """Verify if login was successful by checking session or accessing a protected page"""
        # First check if we have a session cookie
        session_cookie = self._get_cookie_value("bbm_session")
        if not session_cookie:
            log.warning("No session cookie found")
            return False

        # Try to access a protected page to verify login
        try:
            shop_url = urljoin(REFERER, "shop")
            response = self.session.get(shop_url, timeout=15)

            # Check if we're redirected to login page (indicating failed login)
            if "login" in response.url.lower() or response.status_code == 401:
                log.error("Redirected to login page - authentication failed")
                return False

            response.raise_for_status()
            log.info("Login verification successful - can access protected pages")
            return True

        except requests.RequestException as e:
            log.error("Failed to verify login: %s", e)
            return False


def main():
    """Main login flow - fast login verification without JSON saving"""
    log.info("=== Starting Fast Login Verification ===")
    log.info("BASE_URL: %s", BASE_URL)
    log.info("Using proxy: %s", USE_PROXY)

    try:
        # Create login session
        login_session = LoginSession()

        # Fetch login page and extract tokens
        csrf_token, login_html = login_session.fetch_login_page()
        if not login_html:
            log.error("Could not fetch login page")
            return False

        # Perform login
        if not login_session.perform_login(csrf_token, login_html):
            log.error("Login failed")
            return False

        # Verify login success
        if login_session.verify_login_success():
            log.info("✓ Login process completed successfully")
            return True
        else:
            log.error("✗ Login verification failed")
            return False

    except requests.exceptions.ConnectTimeout:
        log.error("Connection timeout - check network or proxy settings")
        return False
    except requests.exceptions.ProxyError:
        log.error("Proxy error - ensure Tor is running if using .onion sites")
        return False
    except Exception as e:
        log.error("Unexpected error: %s", e)
        return False


# Compatibility functions for session_manager and other modules
def make_session() -> requests.Session:
    """Create a session - wrapper for LoginSession._create_session"""
    login_session = LoginSession()
    return login_session.session


def fetch_login_and_extract() -> (
    Tuple[requests.Session, Optional[str], Optional[str], Dict]
):
    """Fetch login page and return session, csrf, and other data"""
    login_session = LoginSession()
    csrf_token, login_html = login_session.fetch_login_page()
    return login_session.session, csrf_token, login_html, {}


def perform_login(session: requests.Session, csrf_token: str) -> bool:
    """Perform login using existing session"""
    try:
        response = session.get(LOGIN_URL, timeout=30)
        login_html = response.text
    except Exception:
        return False

    # Create a temporary LoginSession with the existing session
    login_session = LoginSession()
    login_session.session = session
    return login_session.perform_login(csrf_token, login_html)


# Legacy function names for backward compatibility
_cookies_to_serializable = cookies_to_serializable
_load_cookies_into_jar = load_cookies_into_jar


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
