"""
API v1 Configuration Defaults

Externalized configuration defaults for API v1 services.
This separates hardcoded values from business logic.
"""

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class APIDefaults:
    """Default values for API configurations"""

    # Timeouts
    DEFAULT_TIMEOUT: int = 30
    DEFAULT_CONNECT_TIMEOUT: int = 10
    DEFAULT_READ_TIMEOUT: int = 20

    # Retries
    DEFAULT_MAX_RETRIES: int = 3
    DEFAULT_RETRY_DELAYS: List[int] = None

    # Cache
    DEFAULT_CACHE_TTL: int = 300  # 5 minutes

    # Health monitoring
    DEFAULT_HEALTH_CHECK_INTERVAL: int = 300  # 5 minutes

    def __post_init__(self):
        if self.DEFAULT_RETRY_DELAYS is None:
            self.DEFAULT_RETRY_DELAYS = [1, 2, 4]


# API v1 BASE1 (BIN cards) default configuration
# ⚠️ VERIFIED with real API captures from demo/RC folder (Oct 23, 2025)
API_V1_BASE1_DEFAULTS = {
    "name": "api_v1_base1",
    "display_name": "API v1 - BIN Cards",
    "description": "BIN card lookup and purchase API",
    "base_url": "https://ronaldo-club.to/api",  # Default base URL
    "endpoints": {
        "list": {
            "path": "/cards/hq/list",  # POST with query params, empty body
            "method": "POST",  # ⚠️ POST not GET!
            "timeout": 30,
        },
        "add_to_cart": {
            "path": "/cart/",  # POST with JSON body {"id": xxx, "product_table_name": "Cards"}
            "method": "POST",
            "timeout": 30,
        },
        "view_cart": {
            "path": "/cart/",  # GET request
            "method": "GET",
            "timeout": 30,
        },
        "delete_from_cart": {
            "path": "/cart/{id}",  # DELETE /cart/{cart_item_id}
            "method": "DELETE",  # ⚠️ DELETE not POST!
            "timeout": 30,
        },
        "checkout": {
            "path": "/cart/checkout",  # GET request (not POST!)
            "method": "GET",  # ⚠️ GET not POST!
            "timeout": 60,
        },
        "user_info": {
            "path": "/user/getme",  # GET /user/getme
            "method": "GET",
            "timeout": 30,
        },
        "filters": {
            "path": "/cards/hq/filters",  # GET request for available filters
            "method": "GET",
            "timeout": 30,
        },
    },
    "authentication": {
        "type": "cookie",  # Uses cookie-based auth (loginToken JWT)
        "cookie_name": "loginToken",
        "token_header": "Authorization",  # Fallback for other auth types
        "token_prefix": "Bearer",
    },
    "rate_limiting": {
        "requests_per_minute": 60,
        "burst_size": 10,
    },
}

# API v1 CART default configuration
API_V1_CART_DEFAULTS = {
    "name": "api_v1_cart",
    "display_name": "API v1 - Cart Operations",
    "description": "Dedicated cart management API",
    "base_url": "",  # Must be configured
    "endpoints": {
        "view_cart": {
            "path": "/cart",
            "method": "GET",
            "timeout": 30,
        },
        "add": {
            "path": "/cart/add",
            "method": "POST",
            "timeout": 30,
        },
        "remove": {
            "path": "/cart/remove",
            "method": "POST",
            "timeout": 30,
        },
        "clear": {
            "path": "/cart/clear",
            "method": "POST",
            "timeout": 30,
        },
    },
    "authentication": {
        "type": "session_cookie",
        "cookie_name": "session_id",
    },
}

# API v2 BASE2 default configuration
API_V2_BASE2_DEFAULTS = {
    "name": "api_v2_base2",
    "display_name": "API v2 - Enhanced Operations",
    "description": "API v2 with improved features",
    "base_url": "",  # Must be configured
    "endpoints": {
        "list": {
            "path": "/v2/list",
            "method": "GET",
            "timeout": 30,
        },
        "purchase": {
            "path": "/v2/purchase",
            "method": "POST",
            "timeout": 60,
        },
    },
    "authentication": {
        "type": "api_key",
        "key_header": "X-API-Key",
    },
}

# API v1 DUMPS default configuration
API_V1_DUMPS_DEFAULTS = {
    "name": "api_v1_dumps",
    "display_name": "API v1 - Dumps",
    "description": "Data dumps API",
    "base_url": "",  # Must be configured
    "endpoints": {
        "list": {
            "path": "/dumps/list",
            "method": "GET",
            "timeout": 45,
        },
        "download": {
            "path": "/dumps/download",
            "method": "GET",
            "timeout": 120,
        },
    },
    "authentication": {
        "type": "bearer_token",
    },
}

# Registry of all default configurations (lowercase keys for consistency)
API_DEFAULTS_REGISTRY: Dict[str, Dict[str, Any]] = {
    "api_v1_base1": API_V1_BASE1_DEFAULTS,
    "api_v1_cart": API_V1_CART_DEFAULTS,
    "api_v2_base2": API_V2_BASE2_DEFAULTS,
    "api_v1_dumps": API_V1_DUMPS_DEFAULTS,
}

# Alias mapping for case-insensitive and uppercase lookups
API_NAME_ALIASES: Dict[str, str] = {
    "API_V1_BASE1": "api_v1_base1",
    "API_V1_CART": "api_v1_cart",
    "API_V2_BASE2": "api_v2_base2",
    "API_V1_DUMPS": "api_v1_dumps",
}


def get_default_config(api_name: str) -> Dict[str, Any]:
    """
    Get default configuration for an API

    Args:
        api_name: Name of the API (case-insensitive)
                 Examples: "api_v1_base1", "API_V1_BASE1", "api_v1_cart"

    Returns:
        Default configuration dictionary

    Raises:
        ValueError: If API name not found
    """
    # Normalize to lowercase
    normalized_name = api_name.lower()

    # Try direct lookup first
    config = API_DEFAULTS_REGISTRY.get(normalized_name)

    # Try alias mapping if direct lookup fails
    if not config and api_name in API_NAME_ALIASES:
        normalized_name = API_NAME_ALIASES[api_name]
        config = API_DEFAULTS_REGISTRY.get(normalized_name)

    if not config:
        raise ValueError(f"No default configuration found for API: {api_name}")
    return config.copy()


def list_available_apis() -> List[str]:
    """Get list of APIs with default configurations"""
    return list(API_DEFAULTS_REGISTRY.keys())


def get_defaults_instance() -> APIDefaults:
    """Get singleton instance of API defaults"""
    return APIDefaults()
