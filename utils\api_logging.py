"""
Comprehensive API Request/Response Logging System

This module provides detailed logging for all API interactions with:
- Request/response logging with correlation IDs
- Security-aware data masking
- Performance metrics
- Error context and debugging information
- Structured logging for searchability
"""

from __future__ import annotations

import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from dataclasses import dataclass, field, asdict
from enum import Enum
import re

from utils.central_logger import get_logger

logger = get_logger()


class LogLevel(Enum):
    """API logging levels"""
    TRACE = "TRACE"      # Most detailed - all data
    DEBUG = "DEBUG"      # Detailed debugging info
    INFO = "INFO"        # General information
    WARNING = "WARNING"  # Warning conditions
    ERROR = "ERROR"      # Error conditions
    CRITICAL = "CRITICAL" # Critical failures


@dataclass
class APIRequestContext:
    """Context information for API requests"""
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    operation: Optional[str] = None
    service_name: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging"""
        return {
            "correlation_id": self.correlation_id,
            "user_id": self.user_id,
            "operation": self.operation,
            "service_name": self.service_name,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class APIRequestLog:
    """Structured API request log entry"""
    context: APIRequestContext
    method: str
    url: str
    headers: Dict[str, str]
    query_params: Optional[Dict[str, Any]] = None
    body: Optional[Union[str, Dict[str, Any]]] = None
    timeout: Optional[float] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging"""
        return {
            "type": "api_request",
            "context": self.context.to_dict(),
            "method": self.method,
            "url": self.url,
            "headers": self.headers,
            "query_params": self.query_params,
            "body": self.body,
            "timeout": self.timeout,
            "retry_count": self.retry_count
        }


@dataclass
class APIResponseLog:
    """Structured API response log entry"""
    context: APIRequestContext
    status_code: int
    status_message: str
    headers: Dict[str, str]
    body: Optional[Union[str, Dict[str, Any]]] = None
    response_time_ms: Optional[float] = None
    response_size_bytes: Optional[int] = None
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging"""
        return {
            "type": "api_response",
            "context": self.context.to_dict(),
            "status_code": self.status_code,
            "status_message": self.status_message,
            "headers": self.headers,
            "body": self.body,
            "response_time_ms": self.response_time_ms,
            "response_size_bytes": self.response_size_bytes,
            "error_type": self.error_type,
            "error_message": self.error_message
        }


class DataMasker:
    """Utility class for masking sensitive data in logs"""
    
    # Patterns for sensitive data
    SENSITIVE_HEADERS = {
        'authorization', 'cookie', 'x-auth-token', 'x-api-key', 
        'authentication', 'proxy-authorization', 'www-authenticate'
    }
    
    SENSITIVE_PARAMS = {
        'password', 'token', 'key', 'secret', 'auth', 'login_token',
        'session_id', 'csrf_token', 'api_key'
    }
    
    # Regex patterns for sensitive data
    TOKEN_PATTERNS = [
        re.compile(r'Bearer\s+([A-Za-z0-9\-_]+\.){2}[A-Za-z0-9\-_]+'),  # JWT tokens
        re.compile(r'[A-Za-z0-9]{32,}'),  # Long alphanumeric strings (likely tokens)
    ]
    
    @classmethod
    def mask_headers(cls, headers: Dict[str, str]) -> Dict[str, str]:
        """Mask sensitive headers"""
        masked = {}
        for key, value in headers.items():
            if key.lower() in cls.SENSITIVE_HEADERS:
                masked[key] = cls._mask_value(value)
            else:
                masked[key] = value
        return masked
    
    @classmethod
    def mask_params(cls, params: Dict[str, Any]) -> Dict[str, Any]:
        """Mask sensitive parameters"""
        if not params:
            return params
            
        masked = {}
        for key, value in params.items():
            if key.lower() in cls.SENSITIVE_PARAMS:
                masked[key] = cls._mask_value(str(value))
            else:
                masked[key] = value
        return masked
    
    @classmethod
    def mask_body(cls, body: Union[str, Dict[str, Any]]) -> Union[str, Dict[str, Any]]:
        """Mask sensitive data in request/response body"""
        if isinstance(body, dict):
            return cls.mask_params(body)
        elif isinstance(body, str):
            return cls._mask_string_content(body)
        return body
    
    @classmethod
    def _mask_value(cls, value: str) -> str:
        """Mask a sensitive value"""
        if not value or len(value) < 8:
            return "***"
        return f"{value[:4]}...{value[-4:]}"
    
    @classmethod
    def _mask_string_content(cls, content: str) -> str:
        """Mask sensitive patterns in string content"""
        masked = content
        for pattern in cls.TOKEN_PATTERNS:
            masked = pattern.sub(lambda m: cls._mask_value(m.group(0)), masked)
        return masked


class APILogger:
    """Comprehensive API request/response logger"""
    
    def __init__(self, service_name: str, log_level: LogLevel = LogLevel.INFO):
        self.service_name = service_name
        self.log_level = log_level
        self.logger = logging.getLogger(f"api.{service_name}")
        
        # Performance tracking
        self._request_times: Dict[str, float] = {}
    
    def create_context(
        self, 
        user_id: Optional[str] = None, 
        operation: Optional[str] = None
    ) -> APIRequestContext:
        """Create a new request context with correlation ID"""
        return APIRequestContext(
            user_id=user_id,
            operation=operation,
            service_name=self.service_name
        )
    
    def log_request(
        self,
        context: APIRequestContext,
        method: str,
        url: str,
        headers: Dict[str, str],
        query_params: Optional[Dict[str, Any]] = None,
        body: Optional[Union[str, Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
        retry_count: int = 0
    ) -> None:
        """Record request start and log request details."""
        # Track start time for response timing
        self._request_times[context.correlation_id] = time.time()
        
        # Format request info
        request_info = f"[{context.operation or 'api'}] {method} {url}"
        if query_params:
            params_str = "&".join([f"{k}={v}" for k, v in query_params.items()])
            request_info += f"?{params_str}"
        
        # Log to main API logger (for file)
        self.logger.info(f"REQUEST: {request_info}")
        
        # Skip console logging to avoid duplicates with API client output
        
        # Also log request body if present
        if body:
            masked_body = DataMasker.mask_body(body)
            if isinstance(masked_body, dict):
                try:
                    body_str = json.dumps(masked_body, ensure_ascii=False)  # Show full body
                except Exception:
                    body_str = str(masked_body)
            else:
                body_str = str(masked_body)
            self.logger.info(f"REQUEST BODY: {body_str}")
            # Skip console logging to avoid duplicates with API client output
    
    def log_response(
        self,
        context: APIRequestContext,
        status_code: int,
        status_message: str,
        headers: Dict[str, str],
        body: Optional[Union[str, Dict[str, Any]]] = None,
        error_type: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> None:
        """Log API response data with full details"""

        # Calculate response time
        start_time = self._request_times.pop(context.correlation_id, None)
        response_time_ms = (time.time() - start_time) * 1000 if start_time else None

        # Format response for logging
        operation = context.operation or 'api'
        response_time_str = f" ({response_time_ms:.1f}ms)" if response_time_ms else ""
        
        # Log basic response info
        response_info = f"[{operation}] {status_code} {status_message}{response_time_str}"
        
        # Get console logger
        console_logger = logging.getLogger("api.console")
        
        # Choose log level based on status code (file logging only)
        if status_code >= 500:
            self.logger.error(f"RESPONSE: {response_info}")
        elif status_code >= 400:
            self.logger.warning(f"RESPONSE: {response_info}")
        else:
            self.logger.info(f"RESPONSE: {response_info}")
        
        # Skip console logging to avoid duplicates with API client output
        
        # Log response body if present (masked and truncated)
        if body:
            def _format_response_body(obj: Optional[Union[str, Dict[str, Any]]]) -> str:
                if obj is None:
                    return "<EMPTY>"
                if isinstance(obj, dict):
                    try:
                        return json.dumps(obj, ensure_ascii=False, indent=None)
                    except Exception:
                        return str(obj)
                return str(obj)
            
            # Show response body in file logs only (console handled by API clients)
            body_text = _format_response_body(body)
            
            # Truncate large response bodies to prevent log flooding
            if len(body_text) > 500:
                body_text = body_text[:500] + f"... (truncated, {len(body_text)} chars total)"
            
            # Log response body at same level as response (file only)
            if status_code >= 500:
                self.logger.error(f"RESPONSE BODY: {body_text}")
                # Skip console output to avoid duplicates with API client output
            elif status_code >= 400:
                self.logger.warning(f"RESPONSE BODY: {body_text}")
                # Skip console output to avoid duplicates with API client output
            else:
                self.logger.info(f"RESPONSE BODY: {body_text}")
                # Skip console output to avoid duplicates with API client output
    
    def log_authentication_context(
        self,
        context: APIRequestContext,
        auth_method: str,
        token_valid: bool,
        user_permissions: Optional[List[str]] = None,
        rate_limit_info: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log authentication and authorization context"""
        
        auth_context = {
            "correlation_id": context.correlation_id,
            "auth_method": auth_method,
            "token_valid": token_valid,
            "user_permissions": user_permissions or [],
            "rate_limit_info": rate_limit_info or {}
        }
        
        # Keep auth context minimal; avoid noisy logs
        if not token_valid:
            self.logger.error(
                f"auth_failed {context.operation or 'api'} [{context.correlation_id}]"
            )
    
    def log_403_error_context(
        self,
        context: APIRequestContext,
        endpoint: str,
        auth_method: str,
        token_status: str,
        user_role: Optional[str] = None,
        required_permissions: Optional[List[str]] = None,
        rate_limit_headers: Optional[Dict[str, str]] = None
    ) -> None:
        """Log detailed context for 403 Forbidden errors"""
        
        error_context = {
            "correlation_id": context.correlation_id,
            "error_type": "403_forbidden",
            "endpoint": endpoint,
            "auth_method": auth_method,
            "token_status": token_status,
            "user_role": user_role,
            "required_permissions": required_permissions or [],
            "rate_limit_headers": rate_limit_headers or {},
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        self.logger.error(f"403 Forbidden Error Context: {json.dumps(error_context, indent=2)}")


# Global API logger instances
_api_loggers: Dict[str, APILogger] = {}


def get_api_logger(service_name: str, log_level: LogLevel = LogLevel.INFO) -> APILogger:
    """Get or create an API logger for a service"""
    if service_name not in _api_loggers:
        _api_loggers[service_name] = APILogger(service_name, log_level)
    return _api_loggers[service_name]
