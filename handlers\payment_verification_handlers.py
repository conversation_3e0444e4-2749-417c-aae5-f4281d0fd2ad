"""
Payment Verification Handlers
Handles payment verification and completion
"""

from __future__ import annotations

from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

from services.payment_service import get_payment_service
from services.user_service import UserService
from utils.keyboards import wallet_menu_keyboard
from utils.texts import DEMO_WATERMARK

from utils.central_logger import get_logger

logger = get_logger()


class PaymentVerificationHandlers:
    """Handlers for payment verification and completion"""

    def __init__(self):
        self.payment_service = get_payment_service()
        self.user_service = UserService()

    async def cb_verify_payment(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle payment verification"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user's latest payment
            success, payments, error_msg = await self.payment_service.get_user_payment_history(user.id)
            
            if not success or not payments:
                await callback.answer("❌ No recent payments found", show_alert=True)
                return

            # Get the latest payment
            latest_payment = payments[0]
            track_id = latest_payment.get('track_id')
            
            if not track_id:
                await callback.answer("❌ Invalid payment data", show_alert=True)
                return

            # Verify payment
            success, verification_data, error_msg = await self.payment_service.verify_payment(track_id)
            
            if not success:
                await callback.answer(f"❌ Payment verification failed: {error_msg}", show_alert=True)
                return

            # Check if payment is completed (handle various successful statuses)
            payment_status = verification_data.get('status', '').lower()
            if payment_status not in ['completed', 'confirmed', 'success', 'paid', 'manual_accept']:
                await callback.answer("⏳ Payment is still processing. Please wait a moment.", show_alert=True)
                return

            # Get payment amount
            amount = verification_data.get('amount', 0)
            
            # Check for underpayment/overpayment using new features
            status_result, details = await self.payment_service.check_payment_amounts(
                amount, amount, track_id
            )

            if status_result == "underpayment":
                # Handle underpayment
                await self.payment_service.handle_underpayment(
                    callback.bot, user.id, track_id, amount, amount
                )
                await callback.answer("⚠️ Underpayment detected - please complete the remaining amount")
                return
            elif status_result == "overpayment":
                # Handle overpayment
                await self.payment_service.handle_overpayment(
                    callback.bot, user.id, track_id, amount, amount
                )
                await callback.answer("💰 Overpayment detected - excess amount credited")
                return

            # Update user balance
            balance_success, balance_error = await self.payment_service.update_user_balance(
                user_id=user.id,
                amount=amount,
                transaction_type="deposit"
            )

            if not balance_success:
                await callback.answer(f"❌ Failed to update balance: {balance_error}", show_alert=True)
                return

            # Handle payment completion
            await self.payment_service.handle_payment_completion(
                callback.bot, user.id, track_id, amount, amount
            )

            # Show success message
            success_text = (
                f"🎉 <b>Payment Successful!</b>\n\n"
                f"💰 <b>Amount:</b> ${amount:.2f}\n"
                f"🆔 <b>Track ID:</b> <code>{track_id}</code>\n"
                f"✅ <b>Status:</b> Completed\n\n"
                f"💳 <b>Your funds have been added to your wallet!</b>\n"
                f"You can now use them for purchases.\n\n"
                f"🔒 <b>Transaction Details:</b>\n"
                f"• Payment verified and processed\n"
                f"• Balance updated successfully\n"
                f"• Transaction recorded\n"
                f"• Enhanced security applied"
            )

            # Get payment keyboards
            keyboards = self.payment_service.get_payment_keyboards()
            success_keyboard = keyboards.get('payment_success')
            
            if success_keyboard:
                await callback.message.edit_text(
                    success_text,
                    reply_markup=success_keyboard(),
                    parse_mode="HTML"
                )
            else:
                await callback.message.edit_text(
                    success_text,
                    reply_markup=wallet_menu_keyboard(),
                    parse_mode="HTML"
                )

            await callback.answer("✅ Payment verified and funds added!")

        except Exception as e:
            logger.error(f"Error in payment verification: {e}")
            await callback.answer("❌ Error verifying payment", show_alert=True)

    async def cb_check_payment_status(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle payment status check"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            # Get user's latest payment
            success, payments, error_msg = await self.payment_service.get_user_payment_history(user.id)
            
            if not success or not payments:
                await callback.answer("❌ No recent payments found", show_alert=True)
                return

            # Get the latest payment
            latest_payment = payments[0]
            track_id = latest_payment.get('track_id')
            status = latest_payment.get('status', 'unknown')
            
            if not track_id:
                await callback.answer("❌ Invalid payment data", show_alert=True)
                return

            # Show status
            status_text = (
                f"📊 <b>Payment Status</b>\n\n"
                f"🆔 Track ID: <code>{track_id}</code>\n"
                f"📅 Status: <b>{status.upper()}</b>\n"
                f"💰 Amount: <b>${latest_payment.get('amount', 0):.2f}</b>\n\n"
            )

            if status == 'completed':
                status_text += (
                    f"✅ <b>Payment Completed</b>\n"
                    f"Your funds have been added to your wallet!"
                )
            elif status == 'pending':
                status_text += (
                    f"⏳ <b>Payment Pending</b>\n"
                    f"Please complete the payment in your crypto wallet."
                )
            elif status == 'failed':
                status_text += (
                    f"❌ <b>Payment Failed</b>\n"
                    f"Please try again or contact support."
                )
            else:
                status_text += (
                    f"🔄 <b>Payment Processing</b>\n"
                    f"Please wait for the payment to be processed."
                )

            await callback.message.edit_text(
                status_text,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer("Payment status updated")

        except Exception as e:
            logger.error(f"Error checking payment status: {e}")
            await callback.answer("❌ Error checking payment status", show_alert=True)

    async def cb_return_to_wallet(self, callback: CallbackQuery, state: FSMContext) -> None:
        """Handle return to wallet menu"""
        try:
            await state.clear()
            
            await callback.message.edit_text(
                "💰 <b>Wallet Menu</b>\n\n"
                "Choose an option below:" + DEMO_WATERMARK,
                reply_markup=wallet_menu_keyboard(),
                parse_mode="HTML"
            )
            await callback.answer("Returned to wallet menu")

        except Exception as e:
            logger.error(f"Error returning to wallet: {e}")
            await callback.answer("❌ Error returning to wallet", show_alert=True)


def get_payment_verification_router() -> Router:
    """Get payment verification router"""
    router = Router()
    handlers = PaymentVerificationHandlers()
    
    # Callbacks
    router.callback_query.register(
        handlers.cb_verify_payment, 
        F.data == "verify_latest_payment"
    )
    router.callback_query.register(
        handlers.cb_check_payment_status, 
        F.data == "check_payment_status"
    )
    router.callback_query.register(
        handlers.cb_return_to_wallet, 
        F.data == "return_to_wallet"
    )
    
    return router
