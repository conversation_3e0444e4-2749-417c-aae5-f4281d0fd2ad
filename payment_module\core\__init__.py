"""
Core payment functionality

This module contains the core payment processing functions:
- Payment link generation
- Payment verification
- Currency conversion
- Flask callback server
- Payment configuration
"""

# Import all core functions for easy access
from .payment_link import create_payment_link, create_payment_link_sync
from .currency_converter import (
    convert_currency,
    process_payment_with_conversion,
    format_conversion_display,
    get_usdt_equivalent_amount,
    is_usd_stablecoin,
    fetch_currency_prices,
)
from .oxa_verify import check_oxapay_payment, verify_payment
from .payment_config import (
    OXA_PAY_API_KEY,
    get_callback_url,
    get_external_callback_url,
    get_health_url,
    get_host_ip,
    generate_hmac,
)

# Export all functions
__all__ = [
    # Payment link functions
    'create_payment_link',
    'create_payment_link_sync',
    
    # Currency conversion functions
    'convert_currency',
    'process_payment_with_conversion',
    'format_conversion_display',
    'get_usdt_equivalent_amount',
    'is_usd_stablecoin',
    'fetch_currency_prices',
    
    # Payment verification functions
    'check_oxapay_payment',
    'verify_payment',
    
    # Configuration functions
    'OXA_PAY_API_KEY',
    'get_callback_url',
    'get_external_callback_url',
    'get_health_url',
    'get_host_ip',
    'generate_hmac',
]
