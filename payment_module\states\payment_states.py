"""
State management for payment processing.

This module provides FSM states for payment-related operations.
"""

from aiogram.fsm.state import StatesGroup, State


class DepositStates(StatesGroup):
    """States for deposit operations"""
    waiting_for_amount = State()
    waiting_for_confirmation = State()
    waiting_for_payment = State()
    waiting_for_verification = State()


class PaymentVerificationStates(StatesGroup):
    """States for payment verification operations"""
    waiting_for_track_id = State()
    verifying_payment = State()


class PaymentStates(StatesGroup):
    """General payment states"""
    processing_payment = State()
    waiting_for_callback = State()
    payment_completed = State()

