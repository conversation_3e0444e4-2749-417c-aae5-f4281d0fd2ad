"""
Additional Database Operations for Payment Processing

This module provides additional database operations that are essential for
complete payment processing, using the main bot's database connection directly.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


def _get_users_collection():
    """Get the users collection from the main bot's database or create a new connection."""
    try:
        from database.connection import get_collection, is_database_connected
        if is_database_connected():
            return get_collection("users")
        else:
            logger.warning("Main bot database not connected, creating new connection for payment module")
            # Create a new database connection for the payment module
            from motor.motor_asyncio import AsyncIOMotorClient
            from config.settings import get_settings
            
            settings = get_settings()
            client = AsyncIOMotorClient(settings.MONGODB_URL)
            db = client[settings.DATABASE_NAME]
            return db.users
    except Exception as e:
        logger.error(f"Failed to get users collection: {e}")
        raise RuntimeError(f"Payment module requires database connection: {e}")


def _get_transactions_collection():
    """Get the transactions collection from the main bot's database or create a new connection."""
    try:
        from database.connection import get_collection, is_database_connected
        if is_database_connected():
            return get_collection("transactions")
        else:
            logger.warning("Main bot database not connected, creating new connection for payment module")
            # Create a new database connection for the payment module
            from motor.motor_asyncio import AsyncIOMotorClient
            from config.settings import get_settings
            
            settings = get_settings()
            client = AsyncIOMotorClient(settings.MONGODB_URL)
            db = client[settings.DATABASE_NAME]
            return db.transactions
    except Exception as e:
        logger.error(f"Failed to get transactions collection: {e}")
        raise RuntimeError(f"Payment module requires database connection: {e}")


async def get_or_create_user(
    user_id: int, name: Optional[str] = None, username: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """Gets user data, creates if not exists. Updates name/username/last_seen."""
    try:
        # Use main bot's database connection directly
        users_collection = _get_users_collection()
        now = datetime.now()
        update_fields = {"last_seen": now}
        if name:
            update_fields["name"] = name
        if username:
            update_fields["username"] = username

        user = await users_collection.find_one_and_update(
            {"telegram_id": user_id},
            {
                "$set": update_fields,
                "$setOnInsert": {"telegram_id": user_id, "balance": 0.0, "created_at": now},
            },
            upsert=True,
            return_document=True,
        )
        return user
    except Exception as e:
        logger.error(f"Error in get_or_create_user: {e}")
        return None


async def get_user(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets user data by user_id."""
    try:
        # Use main bot's database connection directly
        users_collection = _get_users_collection()
        return await users_collection.find_one({"telegram_id": user_id})
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        return None


async def get_user_balance(user_id: int) -> float:
    """Gets a user's balance, returns 0.0 if user not found or error."""
    user = await get_user(user_id)
    return user.get("balance", 0.0) if user else 0.0


async def update_user_balance(user_id: int, new_balance: float) -> bool:
    """Updates a user's balance. Returns True on success. Creates user if not exists."""
    try:
        # Use main bot's database connection directly
        users_collection = _get_users_collection()
        
        # First check if the user exists
        user = await users_collection.find_one({"telegram_id": user_id})

        if user:
            # User exists, update balance
            result = await users_collection.update_one(
                {"telegram_id": user_id}, {"$set": {"balance": float(new_balance)}}
            )
            success = result.modified_count > 0 or result.matched_count > 0
        else:
            # User doesn't exist, create with the specified balance
            now = datetime.now()
            user_data = {
                "telegram_id": user_id,
                "balance": float(new_balance),
                "created_at": now,
                "last_seen": now,
            }
            result = await users_collection.insert_one(user_data)
            success = result.acknowledged

            if success:
                logger.info(
                    f"Created new user {user_id} with initial balance {new_balance}"
                )

        return success
    except Exception as e:
        logger.error(f"Error updating user balance: {e}")
        return False


async def add_transaction(
    user_id: int, transaction_type: str, amount: float, **kwargs
) -> Optional[Dict[str, Any]]:
    """Adds a transaction record. Returns the transaction dict on success, None on error."""
    try:
        # Use main bot's database connection directly
        transactions_collection = _get_transactions_collection()
        
        # Generate unique hash to avoid duplicate key errors
        import hashlib
        import time
        
        timestamp = time.time()
        random_salt = hashlib.sha256(f"{timestamp}:{user_id}:{amount}".encode()).hexdigest()[:8]
        hash_value = hashlib.sha256(f"{transaction_type}:{user_id}:{amount}:{timestamp}:{random_salt}".encode()).hexdigest()
        
        transaction = {
            "user_id": user_id,
            "type": transaction_type,
            "amount": float(amount),
            "timestamp": datetime.now(),
            "hash": hash_value,
            **kwargs,
        }
        if "items" in transaction and transaction["items"] is None:
            transaction["items"] = []

        result = await transactions_collection.insert_one(transaction)
        if result.acknowledged:
            transaction["_id"] = result.inserted_id
            return transaction
        else:
            logger.error(f"Transaction insert not acknowledged for user {user_id}")
            return None
    except Exception as e:
        logger.error(f"Error adding transaction: {e}")
        return None


def get_user_transactions(
    user_id: int, limit: int = 10, transaction_type: Optional[str] = None
) -> List[Dict[str, Any]]:
    """Gets user transactions with optional filtering."""
    try:
        users_collection = _get_users_collection()
        transactions_collection = _get_transactions_collection()
        
        if isinstance(transactions_collection, dict):
            # In-memory fallback
            user_transactions = []
            for transaction in transactions_collection.values():
                if transaction.get("user_id") == user_id:
                    if not transaction_type or transaction.get("type") == transaction_type:
                        user_transactions.append(transaction)
            return sorted(user_transactions, key=lambda x: x.get("timestamp", datetime.min), reverse=True)[:limit]
        else:
            # MongoDB
            query = {"user_id": user_id}
            if transaction_type:
                query["type"] = transaction_type
            
            transactions = list(transactions_collection.find(query).sort("timestamp", -1).limit(limit))
            return transactions
    except Exception as e:
        logger.error(f"Error getting user transactions: {e}")
        return []


# Async versions for compatibility
async def get_user_balance_async(user_id: int) -> float:
    """Gets a user's balance (async), returns 0.0 if user not found or error."""
    return get_user_balance(user_id)


async def update_user_balance_async(user_id: int, new_balance: float) -> bool:
    """Updates a user's balance (async). Returns True on success."""
    return update_user_balance(user_id, new_balance)


async def add_transaction_async(
    user_id: int, transaction_type: str, amount: float, **kwargs
) -> Optional[Dict[str, Any]]:
    """Adds a transaction record (async). Returns the transaction dict on success."""
    return add_transaction(user_id, transaction_type, amount, **kwargs)

