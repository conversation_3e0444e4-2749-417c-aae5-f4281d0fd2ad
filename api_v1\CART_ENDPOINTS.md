# API v1 Cart and Orders Endpoints

This document describes the cart and order management endpoints implemented in the API v1 cart service.

## Table of Contents

- [Overview](#overview)
- [Service Initialization](#service-initialization)
- [Endpoints](#endpoints)
  - [View Cart](#view-cart)
  - [Checkout Cart](#checkout-cart)
  - [List Orders](#list-orders)
  - [View Order](#view-order)
  - [Check Order](#check-order)
  - [Download Order](#download-order)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [Examples](#examples)

## Overview

The cart service provides a comprehensive interface for managing shopping carts and orders. It supports:

- Viewing cart contents with total pricing
- Listing user orders with pagination
- Viewing detailed order information
- Marking orders as checked (non-refundable)
- Downloading order data in pipe-delimited format

All operations are authenticated using the configured API credentials and support automatic retry with exponential backoff.

## Service Initialization

```python
from api_v1.services.cart_service import get_cart_service

# Get service instance with default config
cart_service = get_cart_service(config_name="api_v1_base")

# Or use custom config
cart_service = get_cart_service(config_name="custom_api_config")
```

## Endpoints

### View Cart

Get all items currently in the user's shopping cart.

**Method:** `view_cart(user_id: Optional[str] = None)`

**HTTP Request:** `GET /api/cart/`

**Parameters:**
- `user_id` (optional): User ID for tracking purposes

**Returns:** `BaseResponse` with cart data:
```python
{
    "success": True,
    "message": "Cart retrieved successfully",
    "data": {
        "items": [
            {
                "_id": 420518,
                "product_id": 2007269,
                "brand": "VISA",
                "bin": "417903",
                "city": "Okemos",
                "state": "MI",
                "country": "US",
                "price": "17.99",
                "exp": "01/28",
                # ... more fields
            }
        ],
        "total_price": 23.98,
        "item_count": 2
    }
}
```

**Example:**
```python
result = await cart_service.view_cart(user_id="12345")
if result.success:
    print(f"Cart has {result.data['item_count']} items")
    print(f"Total: ${result.data['total_price']}")
```

### Checkout Cart

Process cart checkout, creating purchases and deducting from wallet balance.

**Method:** `checkout_cart(user_id: str, clear_cart_on_success: bool = True)`

**HTTP Request:** `GET /api/cart/checkout`

**Parameters:**
- `user_id` (required): User ID for database operations
- `clear_cart_on_success` (default: True): Whether to clear cart after checkout

**Database Operations:**
1. Creates purchase records for each cart item
2. Deducts total amount from user wallet
3. Logs transaction for audit trail
4. Clears cart (optional)

**Returns:** `BaseResponse` with checkout results:
```python
{
    "success": True,
    "message": "Checkout completed successfully! 2 items purchased",
    "data": {
        "purchases": [
            {
                "_id": "64abc123def...",
                "sku": "card_12345_1698076800",
                "price": 17.99,
                "product_type": "card",
                "external_product_id": "12345"
            }
        ],
        "total_price": 23.98,
        "new_balance": 76.02,
        "items_count": 2,
        "transaction_id": "64abc456def...",
        "api_order_id": "order_789"
    }
}
```

**Example:**
```python
result = await cart_service.checkout_cart(user_id="12345")
if result.success:
    print(f"Purchased {result.data['items_count']} items")
    print(f"Total: ${result.data['total_price']:.2f}")
    print(f"New balance: ${result.data['new_balance']:.2f}")
    print(f"Transaction: {result.data['transaction_id']}")
    
    # Access individual purchases
    for purchase in result.data['purchases']:
        print(f"SKU: {purchase['sku']} - ${purchase['price']}")
else:
    if result.error_code == "INSUFFICIENT_BALANCE":
        print("Not enough balance to complete checkout")
    elif result.error_code == "EMPTY_CART":
        print("Cart is empty")
    else:
        print(f"Checkout failed: {result.message}")
```

**See also:** [CHECKOUT_DOCUMENTATION.md](./CHECKOUT_DOCUMENTATION.md) for complete checkout documentation.

### List Orders

Get a paginated list of user's orders.

**Method:** `list_orders(category: str = "hq", page: int = 1, limit: int = 10, user_id: Optional[str] = None)`

**HTTP Request:** `GET /api/cards/{category}/orders?page=1&limit=10`

**Parameters:**
- `category` (default: "hq"): Product category
- `page` (default: 1): Page number
- `limit` (default: 10): Items per page
- `user_id` (optional): User ID for tracking

**Returns:** `BaseResponse` with orders list:
```python
{
    "success": True,
    "message": "Orders retrieved successfully",
    "data": {
        "orders": [
            {
                "_id": 327837,
                "user_id": 197870,
                "status": "Started",
                "price": "1.99",
                "brand": "VISA",
                "bin": "423223",
                "country": "US",
                "createdAt": "2025-10-07T07:26:23.000Z",
                # ... more fields
            }
        ],
        "total_count": 35,
        "page": 1,
        "limit": 10,
        "total_pages": 4
    }
}
```

**Example:**
```python
result = await cart_service.list_orders(category="hq", page=1, limit=20)
if result.success:
    print(f"Found {result.data['total_count']} total orders")
    for order in result.data['orders']:
        print(f"Order #{order['_id']}: {order['status']}")
```

### View Order

Get detailed information about a specific order.

**Method:** `view_order(order_id: int, category: str = "hq", user_id: Optional[str] = None)`

**HTTP Request:** `POST /api/cards/{category}/order/view`

**Request Body:** `{"id": order_id}`

**Parameters:**
- `order_id` (required): Order ID to view
- `category` (default: "hq"): Product category
- `user_id` (optional): User ID for tracking

**Returns:** `BaseResponse` with order details:
```python
{
    "success": True,
    "message": "Order details retrieved successfully",
    "data": {
        "order": {
            "_id": 347387,
            "status": "Started",
            "price": "1.39",
            "cc": "****************",
            "exp": "10/25",
            "cvv": "720",
            "name": "Tupac Solorzano",
            "address": "214 Cherokee Avenue East",
            "city": "Big Stone Gap",
            "state": "VA",
            "zip": "24219",
            "country": "US",
            "bank": "PATHWARD, NATIONAL ASSOCIATION",
            "brand": "MASTERCARD",
            "type": "DEBIT",
            "canCheck": 0,
            # ... more fields
        }
    }
}
```

**Example:**
```python
result = await cart_service.view_order(order_id=347387, category="hq")
if result.success:
    order = result.data['order']
    print(f"Order #{order['_id']}: {order['status']}")
    print(f"Card: {order['cc']}")
```

### Check Order

Mark an order as checked, making it non-refundable.

**Method:** `check_order(order_id: int, category: str = "hq", user_id: Optional[str] = None)`

**HTTP Request:** `POST /api/cards/{category}/check`

**Request Body:** `{"id": order_id}`

**Parameters:**
- `order_id` (required): Order ID to check
- `category` (default: "hq"): Product category
- `user_id` (optional): User ID for tracking

**Returns:** `BaseResponse` with updated order data:
```python
{
    "success": True,
    "message": "Order marked as checked successfully",
    "data": {
        "order": {
            "_id": 347387,
            "status": "NonRefundable",
            "checkedAt": "2025-10-23T20:26:01.000Z",
            # ... more fields
        }
    }
}
```

**Example:**
```python
result = await cart_service.check_order(order_id=347387, category="hq")
if result.success:
    order = result.data['order']
    print(f"Order checked at: {order['checkedAt']}")
    print(f"New status: {order['status']}")
```

### Download Order

Download order data in pipe-delimited text format.

**Method:** `download_order(order_id: int, category: str = "hq", user_id: Optional[str] = None)`

**HTTP Request:** `POST /api/cards/{category}/download/single`

**Request Body:** `{"_id": order_id}`

**Parameters:**
- `order_id` (required): Order ID to download
- `category` (default: "hq"): Product category
- `user_id` (optional): User ID for tracking

**Returns:** `BaseResponse` with text data:
```python
{
    "success": True,
    "message": "Order downloaded successfully",
    "data": {
        "raw_text": "_id|bin|base|level|cc|exp|...\n1896680|544768|...",
        "headers": "_id|bin|base|level|cc|exp|expmonth|expyear|cvv|...",
        "data": "1896680|544768|25SEP_80VR_PHONE_EMAIL_IP4|PREPAID|...",
        "format": "pipe-delimited"
    }
}
```

**Example:**
```python
result = await cart_service.download_order(order_id=347387, category="hq")
if result.success:
    # Save to file
    with open(f"order_{order_id}.txt", "w") as f:
        f.write(result.data['raw_text'])
    print("Order data saved to file")
```

## Response Format

All methods return a `BaseResponse` object with the following structure:

```python
@dataclass
class BaseResponse:
    success: bool              # Operation success status
    message: Optional[str]     # Human-readable message
    data: Optional[Dict]       # Response data
    error_code: Optional[str]  # Error code if failed
    timestamp: datetime        # Response timestamp
```

## Error Handling

The service uses custom exceptions and provides detailed error information:

```python
from api_v1.services.cart_service import CartServiceError

try:
    result = await cart_service.view_cart()
    if not result.success:
        print(f"Error: {result.message} (code: {result.error_code})")
except CartServiceError as e:
    print(f"Cart service error: {e.message}")
    print(f"Error code: {e.error_code}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

### Common Error Codes

- `CONFIG_NOT_FOUND`: API configuration not found
- `CART_FETCH_ERROR`: Failed to fetch cart
- `ORDERS_FETCH_ERROR`: Failed to fetch orders
- `ORDER_VIEW_ERROR`: Failed to view order
- `ORDER_CHECK_ERROR`: Failed to check order
- `ORDER_DOWNLOAD_ERROR`: Failed to download order
- `INVALID_RESPONSE`: API returned invalid response format
- `DOWNLOAD_FAILED`: Download request failed

## Examples

See [cart_service_example.py](./examples/cart_service_example.py) for complete usage examples.

### Basic Usage

```python
import asyncio
from api_v1.services.cart_service import get_cart_service

async def main():
    # Initialize service
    cart_service = get_cart_service()
    
    # View cart
    cart = await cart_service.view_cart()
    print(f"Cart total: ${cart.data['total_price']}")
    
    # List orders
    orders = await cart_service.list_orders(page=1, limit=10)
    print(f"Total orders: {orders.data['total_count']}")
    
    # View specific order
    if orders.data['orders']:
        order_id = orders.data['orders'][0]['_id']
        order = await cart_service.view_order(order_id)
        print(f"Order details: {order.data['order']}")

asyncio.run(main())
```

### With Error Handling

```python
async def safe_view_cart(user_id: str):
    cart_service = get_cart_service()
    
    try:
        result = await cart_service.view_cart(user_id=user_id)
        
        if result.success:
            return result.data
        else:
            print(f"Cart fetch failed: {result.message}")
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return None
```

### Pagination Example

```python
async def fetch_all_orders(category: str = "hq"):
    cart_service = get_cart_service()
    all_orders = []
    page = 1
    
    while True:
        result = await cart_service.list_orders(
            category=category,
            page=page,
            limit=50
        )
        
        if not result.success:
            break
            
        orders = result.data['orders']
        if not orders:
            break
            
        all_orders.extend(orders)
        
        if page >= result.data['total_pages']:
            break
            
        page += 1
    
    return all_orders
```

## Notes

- All endpoints require proper authentication via the API configuration
- The service automatically retries failed requests up to 3 times with exponential backoff
- Response data structures may vary based on the API version and configuration
- The `check_order` operation is irreversible and marks orders as non-refundable
- Downloaded order data is in pipe-delimited format suitable for bulk processing

