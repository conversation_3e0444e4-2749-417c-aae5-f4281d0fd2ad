#!/bin/bash
# Setup Tor for API v3 .onion connectivity

set -e

echo "============================================================"
echo "Tor Setup Script for API v3"
echo "============================================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if running as root for installation
if [ "$EUID" -ne 0 ] && ! command -v tor &> /dev/null; then
    echo -e "${YELLOW}Note: Tor not found. You may need sudo for installation.${NC}"
fi

# Step 1: Check if Tor is installed
echo "Step 1: Checking Tor installation..."
if command -v tor &> /dev/null; then
    TOR_PATH=$(which tor)
    echo -e "${GREEN}✓ Tor is installed at: $TOR_PATH${NC}"
else
    echo -e "${YELLOW}✗ Tor is not installed${NC}"
    echo ""
    echo "Installing Tor..."
    
    # Detect OS and install
    if [ -f /etc/debian_version ]; then
        echo "Detected Debian/Ubuntu system"
        sudo apt update
        sudo apt install -y tor
    elif [ -f /etc/redhat-release ]; then
        echo "Detected RedHat/Fedora system"
        sudo dnf install -y tor || sudo yum install -y tor
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "Detected macOS"
        if command -v brew &> /dev/null; then
            brew install tor
        else
            echo -e "${RED}Error: Homebrew not found. Install from https://brew.sh${NC}"
            exit 1
        fi
    else
        echo -e "${RED}Error: Unsupported OS. Please install Tor manually.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ Tor installed successfully${NC}"
fi

echo ""

# Step 2: Start Tor service
echo "Step 2: Starting Tor service..."

if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if brew services list | grep -q "tor.*started"; then
        echo -e "${GREEN}✓ Tor service is already running${NC}"
    else
        echo "Starting Tor service..."
        brew services start tor
        sleep 3
        echo -e "${GREEN}✓ Tor service started${NC}"
    fi
else
    # Linux with systemd
    if systemctl is-active --quiet tor; then
        echo -e "${GREEN}✓ Tor service is already running${NC}"
    else
        echo "Starting Tor service..."
        sudo systemctl start tor
        sudo systemctl enable tor
        sleep 3
        
        if systemctl is-active --quiet tor; then
            echo -e "${GREEN}✓ Tor service started and enabled${NC}"
        else
            echo -e "${RED}✗ Failed to start Tor service${NC}"
            echo "Checking status..."
            sudo systemctl status tor --no-pager
            exit 1
        fi
    fi
fi

echo ""

# Step 3: Check Tor connectivity
echo "Step 3: Checking Tor connectivity..."

check_port() {
    local port=$1
    local name=$2
    
    if timeout 2 bash -c "echo > /dev/tcp/127.0.0.1/$port" 2>/dev/null; then
        echo -e "${GREEN}✓ $name (port $port): OPEN${NC}"
        echo "  SOCKS URL: socks5h://127.0.0.1:$port"
        return 0
    else
        echo -e "${RED}✗ $name (port $port): CLOSED${NC}"
        return 1
    fi
}

TOR_FOUND=false

if check_port 9050 "System Tor"; then
    TOR_PORT=9050
    TOR_FOUND=true
fi

if check_port 9150 "Tor Browser"; then
    TOR_PORT=9150
    TOR_FOUND=true
fi

echo ""

if [ "$TOR_FOUND" = false ]; then
    echo -e "${RED}✗ No Tor SOCKS proxy found on ports 9050 or 9150${NC}"
    echo ""
    echo "Troubleshooting:"
    echo "1. Check if Tor is running: sudo systemctl status tor"
    echo "2. Check Tor logs: sudo journalctl -u tor -n 50"
    echo "3. Verify Tor config: cat /etc/tor/torrc | grep -v '^#'"
    exit 1
fi

# Step 4: Update .env configuration
echo "Step 4: Updating .env configuration..."

ENV_FILE=".env"

if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}Warning: .env file not found${NC}"
    echo "Creating .env file..."
    touch "$ENV_FILE"
fi

# Determine which port to use (prefer 9050 for system Tor)
if timeout 2 bash -c "echo > /dev/tcp/127.0.0.1/9050" 2>/dev/null; then
    SOCKS_PORT=9050
    SOCKS_NAME="System Tor"
elif timeout 2 bash -c "echo > /dev/tcp/127.0.0.1/9150" 2>/dev/null; then
    SOCKS_PORT=9150
    SOCKS_NAME="Tor Browser"
else
    echo -e "${RED}Error: No Tor port accessible${NC}"
    exit 1
fi

SOCKS_URL="socks5h://127.0.0.1:$SOCKS_PORT"

# Update or add SOCKS_URL in .env
if grep -q "^SOCKS_URL=" "$ENV_FILE"; then
    # Update existing
    sed -i.bak "s|^SOCKS_URL=.*|SOCKS_URL=$SOCKS_URL|" "$ENV_FILE"
    echo -e "${GREEN}✓ Updated SOCKS_URL in .env${NC}"
else
    # Add new
    echo "" >> "$ENV_FILE"
    echo "# Tor SOCKS proxy (auto-configured by setup_tor.sh)" >> "$ENV_FILE"
    echo "SOCKS_URL=$SOCKS_URL" >> "$ENV_FILE"
    echo -e "${GREEN}✓ Added SOCKS_URL to .env${NC}"
fi

echo "  SOCKS_URL=$SOCKS_URL ($SOCKS_NAME)"

echo ""

# Step 5: Test Tor connection
echo "Step 5: Testing Tor connection..."

if command -v curl &> /dev/null; then
    echo "Testing connection to Tor check service..."
    if curl --socks5-hostname 127.0.0.1:$SOCKS_PORT -s https://check.torproject.org/api/ip | grep -q '"IsTor":true'; then
        echo -e "${GREEN}✓ Tor connection verified - you are connected through Tor!${NC}"
    else
        echo -e "${YELLOW}⚠ Could not verify Tor connection${NC}"
        echo "  This might be normal if the check service is unavailable"
    fi
else
    echo -e "${YELLOW}⚠ curl not found, skipping Tor verification${NC}"
fi

echo ""
echo "============================================================"
echo -e "${GREEN}✓ Tor setup complete!${NC}"
echo "============================================================"
echo ""
echo "Configuration:"
echo "  - Tor service: Running on port $SOCKS_PORT"
echo "  - SOCKS URL: $SOCKS_URL"
echo "  - .env file: Updated"
echo ""
echo "Next steps:"
echo "  1. Restart your bot: python3 run.py"
echo "  2. Test API v3: python3 tests/test_api_v3_integration.py"
echo ""
echo "To check Tor status later:"
echo "  - Service: sudo systemctl status tor"
echo "  - Connectivity: python3 scripts/check_tor.py"
echo ""

