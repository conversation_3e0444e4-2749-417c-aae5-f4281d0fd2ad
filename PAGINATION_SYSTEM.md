# Unified Pagination System

## Overview
This document describes the unified pagination system implemented in the bot. All browsing operations (browse all, filters, search) now use a single, consistent pagination logic.

## Key Features

### 1. **Smart Caching**
- API fetches 100 cards per call
- UI displays 6 cards per page
- 1 API call provides 16+ UI pages
- Automatic background prefetching

### 2. **Single Source of Truth**
All pagination logic is centralized in `handlers/catalog_handlers.py`:

```python
# Constants
CARDS_PER_UI_PAGE = 6      # Cards shown per UI page
CARDS_PER_API_PAGE = 100   # Cards fetched per API call

# Core Methods
_render_cards_page()        # Main entry point for all browsing
_get_cached_page()          # Retrieves cards from cache
_update_search_cache()      # Stores API results
_should_prefetch_next_batch()  # Determines when to fetch more
_prefetch_next_batch()      # Background fetch of next batch
```

### 3. **Unified Flow**
All browsing operations use the same flow:
```
User Action → _render_cards_page() → Check Cache → Render or Fetch → Display
```

Operations using this system:
- ✅ Browse All Cards
- ✅ Browse with Filters
- ✅ Search Results
- ✅ Category Browsing
- ✅ Pagination (Next/Previous)

## How It Works

### Initial Load (Page 1)
```
User clicks "Browse" 
  ↓
_render_cards_page(page=1) called
  ↓
Cache empty → Fetch API page 1 (100 cards)
  ↓
Cache updated with 100 cards
  ↓
Display cards 1-6 (UI page 1)
```

### Subsequent Pages (Cache Hit)
```
User clicks "Next" (page 2)
  ↓
_render_cards_page(page=2) called
  ↓
Cache hit → Cards 7-12 available
  ↓
Display instantly (no API call)
```

### Smart Prefetching
```
User on page 14 (near end of cached data)
  ↓
_should_prefetch_next_batch() returns True
  ↓
Background task fetches API page 2 (next 100 cards)
  ↓
Cache now has 200 cards = 33 UI pages
  ↓
User continues browsing smoothly
```

## Benefits

### Performance
- **First load**: 1 API call for 100 cards
- **Pages 1-16**: Instant rendering (from cache)
- **Page 17+**: Seamless, already prefetched

### Consistency
- Same logic for all browsing modes
- Predictable behavior
- Easy to maintain and extend

### User Experience
- Fast page transitions
- No repeated API calls
- Background prefetching is invisible
- Loading animations only on cache miss

## Cache Structure

```python
user_search_cache[user_id] = {
    'filters_hash': str,           # Detect filter changes
    'cards': List[Dict],           # All cached cards
    'fetched_api_pages': Set[int], # Which API pages are loaded
    'page_size': 6,                # UI page size
    'api_page_size': 100,          # API page size
    'total_count': int,            # Total available from API
    'last_fetch_time': float,      # Cache timestamp
    'has_more_data': bool,         # More data available
    'api_version': str,            # API version
    'product_type': str            # Product type
}
```

## Configuration

To adjust pagination settings, modify the constants in `handlers/catalog_handlers.py`:

```python
CARDS_PER_UI_PAGE = 6   # Change to show more/fewer cards per page
CARDS_PER_API_PAGE = 100  # Match your API's page size
```

## Code Examples

### Using the System
All handlers automatically use the unified system:

```python
# Browse all
await self._render_cards_page(callback, page=1)

# Browse with filters
self.user_applied_filters[user_id] = filters
await self._render_cards_page(callback, page=1)

# Pagination
await self._render_cards_page(callback, page=page_number)
```

### Checking Cache Status
```python
cached_cards, total_count, cache_hit = self._get_cached_page(user_id, page)
if cache_hit:
    # Instant render
    pass
else:
    # Fetch from API
    pass
```

## Troubleshooting

### Issue: Cards not updating after filter change
**Solution**: Cache is automatically invalidated when filters change via `_invalidate_search_cache()`

### Issue: Slow pagination
**Check**: 
1. Is prefetching working? (Check logs for "Prefetching")
2. Is cache being reused? (Check logs for "Cache hit")
3. Network issues? (Check API response times)

### Issue: Duplicate API calls
**Check**: Ensure all browsing operations use `_render_cards_page()` and not custom pagination logic

## Performance Metrics

Expected behavior:
- **First page load**: 1-4 seconds (API call + render)
- **Cached pages**: < 100ms (instant)
- **Prefetch**: Background, no user wait time
- **Cache lifespan**: Until filter change or session end

## Future Enhancements

Potential improvements:
- [ ] Persistent cache (database/Redis)
- [ ] Configurable prefetch threshold
- [ ] Multi-level cache (L1: memory, L2: disk)
- [ ] Cache compression for large datasets
- [ ] Analytics on cache hit rates

## Testing

To test the pagination system:

1. **Test Cache Hit**:
   ```
   Browse → Page 1 → Page 2 → Page 1
   Expected: Instant navigation, no API calls after initial load
   ```

2. **Test Prefetch**:
   ```
   Browse → Navigate to page 15
   Check logs for: "Should prefetch" and "Prefetched X cards"
   ```

3. **Test Filter Changes**:
   ```
   Browse → Apply filters → Check page 1
   Expected: New API call, cache invalidated
   ```

4. **Test API Page Boundary**:
   ```
   Browse → Navigate to page 17 (crosses 100-card boundary)
   Expected: Seamless, second batch already prefetched
   ```

## Related Files

- `handlers/catalog_handlers.py` - Main implementation
- `handlers/search_handlers.py` - Search UI (delegates to catalog)
- `utils/product_display.py` - Card formatting
- `services/card_service.py` - API calls

## Version History

- **v3.0** (2025-10-25): Unified pagination system
  - Removed 1000-card limit
  - Implemented smart prefetching
  - Created single source of truth
  - Improved cache management

---

**Last Updated**: October 25, 2025  
**Status**: ✅ Production Ready

