# Log Management PowerShell Script for Demo Wallet Bot v3
# Provides convenient commands for viewing and managing log files

# Colors for output
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Red = [System.ConsoleColor]::Red
$Cyan = [System.ConsoleColor]::Cyan
$White = [System.ConsoleColor]::White

function Write-ColorOutput {
    param(
        [string]$Message,
        [System.ConsoleColor]$ForegroundColor = $White
    )
    $currentColor = [Console]::ForegroundColor
    [Console]::ForegroundColor = $ForegroundColor
    Write-Output $Message
    [Console]::ForegroundColor = $currentColor
}

function Show-LogStatus {
    Write-ColorOutput "=== Demo Wallet Bot v3 - Log Status ===" $Cyan
    Write-ColorOutput ""
    
    $logsPath = "logs"
    if (-not (Test-Path $logsPath)) {
        Write-ColorOutput "❌ Logs directory not found. Run the bot first to initialize logging." $Red
        return
    }
    
    $logFiles = @(
        "application.log",
        "errors.log",
        "debug.log", 
        "api-requests.log",
        "api-responses.log",
        "http-client.log",
        "telegram-bot.log",
        "transactions.log",
        "user-actions.log",
        "database.log",
        "performance.log",
        "security.log",
        "background-services.log",
        "health-monitor.log",
        "admin-actions.log"
    )
    
    foreach ($logFile in $logFiles) {
        $filePath = Join-Path $logsPath $logFile
        if (Test-Path $filePath) {
            $size = (Get-Item $filePath).Length
            $sizeKB = [math]::Round($size / 1KB, 2)
            $lastWrite = (Get-Item $filePath).LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
            Write-ColorOutput "✅ $logFile - ${sizeKB} KB - Last: $lastWrite" $Green
        } else {
            Write-ColorOutput "❌ $logFile - Not found" $Red
        }
    }
    Write-ColorOutput ""
}

function Show-RecentErrors {
    param([int]$Lines = 20)
    
    Write-ColorOutput "=== Recent Errors (Last $Lines lines) ===" $Red
    Write-ColorOutput ""
    
    $errorLogPath = "logs\errors.log"
    if (Test-Path $errorLogPath) {
        Get-Content $errorLogPath -Tail $Lines | ForEach-Object {
            Write-ColorOutput $_ $Red
        }
    } else {
        Write-ColorOutput "No error log found." $Yellow
    }
    Write-ColorOutput ""
}

function Show-RecentActivity {
    param([int]$Lines = 30)
    
    Write-ColorOutput "=== Recent Application Activity (Last $Lines lines) ===" $Cyan
    Write-ColorOutput ""
    
    $appLogPath = "logs\application.log"
    if (Test-Path $appLogPath) {
        Get-Content $appLogPath -Tail $Lines | ForEach-Object {
            if ($_ -match "ERROR|CRITICAL") {
                Write-ColorOutput $_ $Red
            } elseif ($_ -match "WARNING") {
                Write-ColorOutput $_ $Yellow
            } else {
                Write-ColorOutput $_ $White
            }
        }
    } else {
        Write-ColorOutput "No application log found." $Yellow
    }
    Write-ColorOutput ""
}

function Watch-Logs {
    param(
        [string]$LogType = "application",
        [int]$Lines = 10
    )
    
    $logPath = "logs\$LogType.log"
    if (-not (Test-Path $logPath)) {
        Write-ColorOutput "❌ Log file not found: $logPath" $Red
        return
    }
    
    Write-ColorOutput "=== Watching $LogType.log (Press Ctrl+C to stop) ===" $Cyan
    Write-ColorOutput "Last $Lines lines:" $Yellow
    Write-ColorOutput ""
    
    Get-Content $logPath -Tail $Lines -Wait | ForEach-Object {
        $timestamp = Get-Date -Format "HH:mm:ss"
        if ($_ -match "ERROR|CRITICAL") {
            Write-ColorOutput "[$timestamp] $_" $Red
        } elseif ($_ -match "WARNING") {
            Write-ColorOutput "[$timestamp] $_" $Yellow
        } elseif ($_ -match "INFO") {
            Write-ColorOutput "[$timestamp] $_" $Green
        } else {
            Write-ColorOutput "[$timestamp] $_" $White
        }
    }
}

function Show-APIActivity {
    param([int]$Lines = 20)
    
    Write-ColorOutput "=== Recent API Activity ===" $Cyan
    Write-ColorOutput ""
    
    # Show API requests
    $requestLogPath = "logs\api-requests.log"
    if (Test-Path $requestLogPath) {
        Write-ColorOutput "--- API Requests (Last $Lines) ---" $Yellow
        Get-Content $requestLogPath -Tail $Lines | ForEach-Object {
            Write-ColorOutput $_ $White
        }
        Write-ColorOutput ""
    }
    
    # Show API responses
    $responseLogPath = "logs\api-responses.log"
    if (Test-Path $responseLogPath) {
        Write-ColorOutput "--- API Responses (Last $Lines) ---" $Yellow
        Get-Content $responseLogPath -Tail $Lines | ForEach-Object {
            if ($_ -match '"status_code":\s*[45]\d\d') {
                Write-ColorOutput $_ $Red
            } else {
                Write-ColorOutput $_ $Green
            }
        }
    }
    Write-ColorOutput ""
}

function Show-UserActivity {
    param([int]$Lines = 25)
    
    Write-ColorOutput "=== Recent User Activity ===" $Cyan
    Write-ColorOutput ""
    
    $userLogPath = "logs\user-actions.log"
    if (Test-Path $userLogPath) {
        Get-Content $userLogPath -Tail $Lines | ForEach-Object {
            Write-ColorOutput $_ $Green
        }
    } else {
        Write-ColorOutput "No user activity log found." $Yellow
    }
    Write-ColorOutput ""
}

function Show-SecurityEvents {
    param([int]$Lines = 15)
    
    Write-ColorOutput "=== Recent Security Events ===" $Red
    Write-ColorOutput ""
    
    $securityLogPath = "logs\security.log"
    if (Test-Path $securityLogPath) {
        Get-Content $securityLogPath -Tail $Lines | ForEach-Object {
            Write-ColorOutput $_ $Red
        }
    } else {
        Write-ColorOutput "No security events found." $Green
    }
    Write-ColorOutput ""
}

function Clear-OldLogs {
    param([int]$DaysOld = 7)
    
    Write-ColorOutput "=== Clearing logs older than $DaysOld days ===" $Yellow
    
    $logsPath = "logs"
    if (-not (Test-Path $logsPath)) {
        Write-ColorOutput "❌ Logs directory not found." $Red
        return
    }
    
    $cutoffDate = (Get-Date).AddDays(-$DaysOld)
    $logFiles = Get-ChildItem $logsPath -Filter "*.log.*" | Where-Object { $_.LastWriteTime -lt $cutoffDate }
    
    if ($logFiles.Count -eq 0) {
        Write-ColorOutput "✅ No old log files to clean up." $Green
        return
    }
    
    foreach ($file in $logFiles) {
        try {
            Remove-Item $file.FullName -Force
            Write-ColorOutput "🗑️ Removed: $($file.Name)" $Yellow
        } catch {
            Write-ColorOutput "❌ Failed to remove: $($file.Name) - $($_.Exception.Message)" $Red
        }
    }
    
    Write-ColorOutput "✅ Log cleanup completed." $Green
}

function Test-LoggingSystem {
    Write-ColorOutput "=== Testing Logging System ===" $Cyan
    Write-ColorOutput ""
    
    # Check if Python is available
    try {
        $pythonVersion = python --version 2>&1
        Write-ColorOutput "Python detected: $pythonVersion" $Green
    } catch {
        Write-ColorOutput "❌ Python not found. Please install Python to run the logging test." $Red
        return
    }
    
    # Run the logging test script
    Write-ColorOutput "Running logging system test..." $Yellow
    try {
        python test_logging_system.py
        Write-ColorOutput "✅ Logging system test completed successfully!" $Green
    } catch {
        Write-ColorOutput "❌ Logging system test failed: $($_.Exception.Message)" $Red
    }
}

function Show-LogAnalytics {
    Write-ColorOutput "=== Log Analytics Summary ===" $Cyan
    Write-ColorOutput ""
    
    $logsPath = "logs"
    if (-not (Test-Path $logsPath)) {
        Write-ColorOutput "❌ Logs directory not found." $Red
        return
    }
    
    # Analyze error frequency
    $errorLogPath = "logs\errors.log"
    if (Test-Path $errorLogPath) {
        $errorCount = (Get-Content $errorLogPath | Measure-Object).Count
        Write-ColorOutput "🔴 Total Error Events: $errorCount" $Red
    }
    
    # Analyze API activity
    $apiRequestLogPath = "logs\api-requests.log"
    if (Test-Path $apiRequestLogPath) {
        $apiCount = (Get-Content $apiRequestLogPath | Measure-Object).Count
        Write-ColorOutput "🌐 Total API Requests: $apiCount" $Cyan
    }
    
    # Analyze user activity
    $userLogPath = "logs\user-actions.log"
    if (Test-Path $userLogPath) {
        $userActionCount = (Get-Content $userLogPath | Measure-Object).Count
        Write-ColorOutput "👤 Total User Actions: $userActionCount" $Green
    }
    
    # Analyze security events
    $securityLogPath = "logs\security.log"
    if (Test-Path $securityLogPath) {
        $securityCount = (Get-Content $securityLogPath | Measure-Object).Count
        if ($securityCount -gt 0) {
            Write-ColorOutput "🛡️ Security Events: $securityCount" $Red
        } else {
            Write-ColorOutput "🛡️ No Security Events" $Green
        }
    }
    
    Write-ColorOutput ""
}

function Show-Help {
    Write-ColorOutput "=== Demo Wallet Bot v3 - Log Management Commands ===" $Cyan
    Write-ColorOutput ""
    Write-ColorOutput "Available Commands:" $White
    Write-ColorOutput "  Show-LogStatus          - Display status of all log files" $Green
    Write-ColorOutput "  Show-RecentErrors       - Show recent error messages" $Green
    Write-ColorOutput "  Show-RecentActivity     - Show recent application activity" $Green
    Write-ColorOutput "  Show-APIActivity        - Show recent API requests/responses" $Green
    Write-ColorOutput "  Show-UserActivity       - Show recent user actions" $Green
    Write-ColorOutput "  Show-SecurityEvents     - Show security events" $Green
    Write-ColorOutput "  Show-LogAnalytics       - Show log analytics summary" $Green
    Write-ColorOutput "  Watch-Logs [type]       - Watch live log updates (default: application)" $Green
    Write-ColorOutput "  Clear-OldLogs [days]    - Remove logs older than X days (default: 7)" $Green
    Write-ColorOutput "  Test-LoggingSystem      - Run comprehensive logging system test" $Green
    Write-ColorOutput "  Show-Help               - Show this help message" $Green
    Write-ColorOutput ""
    Write-ColorOutput "Examples:" $Yellow
    Write-ColorOutput "  Show-RecentErrors 50                    # Show last 50 error lines" $Yellow
    Write-ColorOutput "  Watch-Logs api-requests                 # Watch API request log" $Yellow
    Write-ColorOutput "  Clear-OldLogs 14                        # Clear logs older than 14 days" $Yellow
    Write-ColorOutput ""
    Write-ColorOutput "Available log types for Watch-Logs:" $White
    Write-ColorOutput "  application, errors, debug, api-requests, api-responses, telegram-bot," $White
    Write-ColorOutput "  transactions, user-actions, database, performance, security," $White
    Write-ColorOutput "  background-services, health-monitor, admin-actions" $White
    Write-ColorOutput ""
}

# Show help by default
Write-ColorOutput "Demo Wallet Bot v3 - Log Management Utility" $Cyan
Write-ColorOutput "Type 'Show-Help' for available commands" $Yellow
Write-ColorOutput ""