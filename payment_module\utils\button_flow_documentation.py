"""
Comprehensive Button Flow Documentation and Implementation

This module provides detailed documentation and implementation of all payment-related
button flows, including user interactions, callback handling, and state management.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.fsm.context import FSMContext

logger = logging.getLogger(__name__)


class PaymentButtonFlows:
    """
    Comprehensive documentation and implementation of payment button flows.
    """
    
    def __init__(self):
        self.flows = {
            "deposit_flow": self._document_deposit_flow(),
            "verification_flow": self._document_verification_flow(),
            "underpayment_flow": self._document_underpayment_flow(),
            "overpayment_flow": self._document_overpayment_flow(),
            "admin_flow": self._document_admin_flow(),
            "user_management_flow": self._document_user_management_flow()
        }
    
    def _document_deposit_flow(self) -> Dict[str, Any]:
        """
        Document the complete deposit flow with all button interactions.
        """
        return {
            "name": "Deposit Flow",
            "description": "Complete user deposit process from amount selection to payment completion",
            "steps": [
                {
                    "step": 1,
                    "name": "Initial Deposit Command",
                    "trigger": "/deposit command",
                    "buttons": [
                        {
                            "text": "💰 Deposit Funds",
                            "callback_data": "deposit_funds",
                            "action": "Start deposit process"
                        }
                    ],
                    "state": "DepositStates.waiting_for_amount",
                    "next_step": "Amount Selection"
                },
                {
                    "step": 2,
                    "name": "Amount Selection",
                    "trigger": "User clicks deposit button",
                    "buttons": [
                        {"text": "$10", "callback_data": "select_amount:10", "action": "Select $10 amount"},
                        {"text": "$20", "callback_data": "select_amount:20", "action": "Select $20 amount"},
                        {"text": "$50", "callback_data": "select_amount:50", "action": "Select $50 amount"},
                        {"text": "$100", "callback_data": "select_amount:100", "action": "Select $100 amount"},
                        {"text": "$200", "callback_data": "select_amount:200", "action": "Select $200 amount"},
                        {"text": "$500", "callback_data": "select_amount:500", "action": "Select $500 amount"},
                        {"text": "💵 Custom Amount", "callback_data": "custom_amount", "action": "Enter custom amount"},
                        {"text": "❌ Cancel", "callback_data": "cancel_deposit", "action": "Cancel deposit process"}
                    ],
                    "state": "DepositStates.waiting_for_confirmation",
                    "next_step": "Payment Confirmation"
                },
                {
                    "step": 3,
                    "name": "Custom Amount Input",
                    "trigger": "User clicks custom amount",
                    "input_required": "Numeric amount",
                    "validation": "Must be positive number",
                    "buttons": [
                        {"text": "✅ Confirm", "callback_data": "confirm_custom_amount", "action": "Confirm custom amount"},
                        {"text": "❌ Cancel", "callback_data": "cancel_custom_amount", "action": "Cancel custom amount"}
                    ],
                    "state": "DepositStates.waiting_for_confirmation",
                    "next_step": "Payment Confirmation"
                },
                {
                    "step": 4,
                    "name": "Payment Confirmation",
                    "trigger": "Amount selected",
                    "buttons": [
                        {"text": "💳 Pay Now", "callback_data": "pay_deposit:{amount}", "action": "Generate payment link"},
                        {"text": "❌ Cancel", "callback_data": "cancel_deposit", "action": "Cancel deposit process"}
                    ],
                    "state": "DepositStates.waiting_for_payment",
                    "next_step": "Payment Processing"
                },
                {
                    "step": 5,
                    "name": "Payment Processing",
                    "trigger": "Payment link generated",
                    "buttons": [
                        {"text": "💳 Pay Now", "url": "{payment_url}", "action": "Open payment page"},
                        {"text": "✅ I've Completed Payment", "callback_data": "verify_latest_payment", "action": "Verify payment"},
                        {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}", "action": "Check payment status"},
                        {"text": "❌ Cancel", "callback_data": "cancel_deposit", "action": "Cancel deposit process"}
                    ],
                    "state": "DepositStates.waiting_for_verification",
                    "next_step": "Payment Verification"
                },
                {
                    "step": 6,
                    "name": "Payment Verification",
                    "trigger": "User clicks verify payment",
                    "buttons": [
                        {"text": "🔄 Try Again", "callback_data": "verify_latest_payment", "action": "Retry verification"},
                        {"text": "📊 View Balance", "callback_data": "view_balance", "action": "View user balance"},
                        {"text": "🏠 Main Menu", "callback_data": "main_menu", "action": "Return to main menu"}
                    ],
                    "state": "DepositStates.completed",
                    "next_step": "Flow Complete"
                }
            ],
            "error_handling": {
                "invalid_amount": "Show error message with retry option",
                "payment_failed": "Show failure message with retry option",
                "network_error": "Show network error with retry option",
                "timeout": "Show timeout message with retry option"
            },
            "success_completion": {
                "message": "Payment completed successfully",
                "buttons": [
                    {"text": "📊 View Balance", "callback_data": "view_balance"},
                    {"text": "💰 Make Another Deposit", "callback_data": "deposit_funds"},
                    {"text": "🏠 Main Menu", "callback_data": "main_menu"}
                ]
            }
        }
    
    def _document_verification_flow(self) -> Dict[str, Any]:
        """
        Document the payment verification flow with all button interactions.
        """
        return {
            "name": "Payment Verification Flow",
            "description": "Manual and automatic payment verification process",
            "steps": [
                {
                    "step": 1,
                    "name": "Verification Initiation",
                    "trigger": "/verify command or verify button",
                    "buttons": [
                        {"text": "🔍 Verify Payment", "callback_data": "verify_payment", "action": "Start verification"},
                        {"text": "📊 View All Payments", "callback_data": "view_payment_history", "action": "View payment history"},
                        {"text": "❌ Cancel", "callback_data": "cancel_verification", "action": "Cancel verification"}
                    ],
                    "state": "ManualVerificationStates.waiting_for_track_id",
                    "next_step": "Track ID Input"
                },
                {
                    "step": 2,
                    "name": "Track ID Input",
                    "trigger": "User clicks verify payment",
                    "input_required": "Payment track ID",
                    "validation": "Must be valid track ID format",
                    "buttons": [
                        {"text": "✅ Verify", "callback_data": "process_verification", "action": "Process verification"},
                        {"text": "❌ Cancel", "callback_data": "cancel_verification", "action": "Cancel verification"}
                    ],
                    "state": "ManualVerificationStates.verifying_payment",
                    "next_step": "Verification Processing"
                },
                {
                    "step": 3,
                    "name": "Verification Processing",
                    "trigger": "Track ID submitted",
                    "buttons": [
                        {"text": "⏳ Processing...", "callback_data": "processing", "action": "Show processing status"}
                    ],
                    "state": "ManualVerificationStates.verifying_payment",
                    "next_step": "Verification Result"
                },
                {
                    "step": 4,
                    "name": "Verification Result",
                    "trigger": "Verification completed",
                    "scenarios": {
                        "completed": {
                            "message": "Payment verified successfully",
                            "buttons": [
                                {"text": "📊 View Balance", "callback_data": "view_balance"},
                                {"text": "💰 Make Another Payment", "callback_data": "deposit_funds"},
                                {"text": "🏠 Main Menu", "callback_data": "main_menu"}
                            ]
                        },
                        "pending": {
                            "message": "Payment still pending",
                            "buttons": [
                                {"text": "🔄 Try Again", "callback_data": "retry_verification"},
                                {"text": "⏰ Check Later", "callback_data": "schedule_check"},
                                {"text": "❌ Cancel", "callback_data": "cancel_verification"}
                            ]
                        },
                        "failed": {
                            "message": "Payment verification failed",
                            "buttons": [
                                {"text": "🔄 Try Again", "callback_data": "retry_verification"},
                                {"text": "📞 Contact Support", "callback_data": "contact_support"},
                                {"text": "❌ Cancel", "callback_data": "cancel_verification"}
                            ]
                        },
                        "underpaid": {
                            "message": "Payment is underpaid",
                            "buttons": [
                                {"text": "💳 Complete Payment", "url": "{payment_url}"},
                                {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}"},
                                {"text": "❌ Cancel", "callback_data": "cancel_payment:{track_id}"}
                            ]
                        },
                        "overpaid": {
                            "message": "Payment is overpaid",
                            "buttons": [
                                {"text": "📊 View Balance", "callback_data": "view_balance"},
                                {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}"},
                                {"text": "📞 Contact Support", "callback_data": "contact_support"}
                            ]
                        }
                    }
                }
            ],
            "auto_verification": {
                "enabled": True,
                "interval": "30 seconds",
                "max_attempts": 10,
                "progressive_delay": "30s, 1m, 2m, 5m, 10m, 15m, 30m, 1h, 2h, 4h"
            }
        }
    
    def _document_underpayment_flow(self) -> Dict[str, Any]:
        """
        Document the underpayment handling flow with all button interactions.
        """
        return {
            "name": "Underpayment Flow",
            "description": "Handling of underpaid payments with completion options",
            "steps": [
                {
                    "step": 1,
                    "name": "Underpayment Detection",
                    "trigger": "Payment verification detects underpayment",
                    "detection_criteria": {
                        "threshold": "< 95% of required amount",
                        "slight_threshold": "95-99% (processed as completed)",
                        "severe_threshold": "< 95% (requires completion)"
                    },
                    "buttons": [
                        {"text": "⚠️ Underpayment Detected", "callback_data": "underpayment_detected", "action": "Show underpayment details"}
                    ],
                    "next_step": "Underpayment Notification"
                },
                {
                    "step": 2,
                    "name": "Underpayment Notification",
                    "trigger": "Underpayment detected",
                    "message_template": "underpayment_detected",
                    "buttons": [
                        {"text": "💳 Complete Payment", "url": "{payment_url}", "action": "Open payment page for remaining amount"},
                        {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}", "action": "Check current payment status"},
                        {"text": "❌ Cancel", "callback_data": "cancel_payment:{track_id}", "action": "Cancel underpaid payment"}
                    ],
                    "state": "ManualVerificationStates.handling_underpayment",
                    "next_step": "Payment Completion"
                },
                {
                    "step": 3,
                    "name": "Payment Completion",
                    "trigger": "User clicks complete payment",
                    "scenarios": {
                        "completion_success": {
                            "message": "Payment completed successfully",
                            "buttons": [
                                {"text": "📊 View Balance", "callback_data": "view_balance"},
                                {"text": "💰 Make Another Payment", "callback_data": "deposit_funds"},
                                {"text": "🏠 Main Menu", "callback_data": "main_menu"}
                            ]
                        },
                        "still_underpaid": {
                            "message": "Payment still underpaid",
                            "buttons": [
                                {"text": "💳 Complete Payment", "url": "{payment_url}"},
                                {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}"},
                                {"text": "📞 Contact Support", "callback_data": "contact_support"}
                            ]
                        },
                        "completion_failed": {
                            "message": "Payment completion failed",
                            "buttons": [
                                {"text": "🔄 Try Again", "callback_data": "retry_completion"},
                                {"text": "📞 Contact Support", "callback_data": "contact_support"},
                                {"text": "❌ Cancel", "callback_data": "cancel_payment:{track_id}"}
                            ]
                        }
                    }
                },
                {
                    "step": 4,
                    "name": "Underpayment Resolution",
                    "trigger": "Payment completed or cancelled",
                    "final_actions": [
                        "Update payment status",
                        "Update user balance",
                        "Record transaction",
                        "Trigger VIP check",
                        "Send completion notification"
                    ]
                }
            ],
            "admin_notifications": {
                "enabled": True,
                "threshold": "Multiple underpayments from same user",
                "actions": ["Log to admin channel", "Send alert to admin"]
            }
        }
    
    def _document_overpayment_flow(self) -> Dict[str, Any]:
        """
        Document the overpayment handling flow with all button interactions.
        """
        return {
            "name": "Overpayment Flow",
            "description": "Handling of overpaid payments with account crediting",
            "steps": [
                {
                    "step": 1,
                    "name": "Overpayment Detection",
                    "trigger": "Payment verification detects overpayment",
                    "detection_criteria": {
                        "normal_overpayment": "110-150% of required amount",
                        "significant_overpayment": "> 150% of required amount"
                    },
                    "buttons": [
                        {"text": "💰 Overpayment Detected", "callback_data": "overpayment_detected", "action": "Show overpayment details"}
                    ],
                    "next_step": "Overpayment Notification"
                },
                {
                    "step": 2,
                    "name": "Overpayment Notification",
                    "trigger": "Overpayment detected",
                    "scenarios": {
                        "normal_overpayment": {
                            "message_template": "overpayment_detected",
                            "buttons": [
                                {"text": "📊 View Balance", "callback_data": "view_balance", "action": "View updated balance"},
                                {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}", "action": "Check payment status"},
                                {"text": "💰 Make Another Payment", "callback_data": "deposit_funds", "action": "Make another payment"}
                            ]
                        },
                        "significant_overpayment": {
                            "message_template": "significant_overpayment_detected",
                            "buttons": [
                                {"text": "🔄 Check Status", "callback_data": "check_payment:{track_id}", "action": "Check payment status"},
                                {"text": "📞 Contact Support", "callback_data": "contact_support", "action": "Contact support for review"},
                                {"text": "📊 View Balance", "callback_data": "view_balance", "action": "View current balance"}
                            ]
                        }
                    },
                    "state": "ManualVerificationStates.handling_overpayment",
                    "next_step": "Overpayment Processing"
                },
                {
                    "step": 3,
                    "name": "Overpayment Processing",
                    "trigger": "Overpayment confirmed",
                    "actions": [
                        "Credit full amount to user account",
                        "Log overpayment details",
                        "Calculate bonus on required amount only",
                        "Record transaction with overpayment flag",
                        "Trigger VIP check",
                        "Send completion notification"
                    ],
                    "next_step": "Overpayment Resolution"
                },
                {
                    "step": 4,
                    "name": "Overpayment Resolution",
                    "trigger": "Overpayment processed",
                    "final_actions": [
                        "Update payment status to completed",
                        "Update user balance with full amount",
                        "Record transaction with overpayment details",
                        "Send success notification",
                        "Log for admin monitoring"
                    ]
                }
            ],
            "admin_notifications": {
                "enabled": True,
                "thresholds": {
                    "normal_overpayment": "Log for monitoring",
                    "significant_overpayment": "Send alert to admin"
                },
                "actions": ["Log to admin channel", "Send alert to admin", "Flag for manual review"]
            }
        }
    
    def _document_admin_flow(self) -> Dict[str, Any]:
        """
        Document the admin management flow with all button interactions.
        """
        return {
            "name": "Admin Management Flow",
            "description": "Admin controls for payment management and monitoring",
            "steps": [
                {
                    "step": 1,
                    "name": "Admin Dashboard",
                    "trigger": "/admin command",
                    "buttons": [
                        {"text": "📊 Payment Statistics", "callback_data": "admin_payment_stats", "action": "View payment statistics"},
                        {"text": "🔍 Search Payments", "callback_data": "admin_search_payments", "action": "Search payment records"},
                        {"text": "⚠️ Underpayments", "callback_data": "admin_underpayments", "action": "View underpaid payments"},
                        {"text": "💰 Overpayments", "callback_data": "admin_overpayments", "action": "View overpaid payments"},
                        {"text": "👥 User Management", "callback_data": "admin_user_management", "action": "Manage users"},
                        {"text": "⚙️ System Settings", "callback_data": "admin_system_settings", "action": "Configure system settings"}
                    ],
                    "next_step": "Admin Function Selection"
                },
                {
                    "step": 2,
                    "name": "Payment Statistics",
                    "trigger": "Admin clicks payment statistics",
                    "buttons": [
                        {"text": "📈 Daily Stats", "callback_data": "admin_daily_stats", "action": "View daily statistics"},
                        {"text": "📊 Weekly Stats", "callback_data": "admin_weekly_stats", "action": "View weekly statistics"},
                        {"text": "📅 Monthly Stats", "callback_data": "admin_monthly_stats", "action": "View monthly statistics"},
                        {"text": "🔄 Refresh", "callback_data": "admin_refresh_stats", "action": "Refresh statistics"},
                        {"text": "⬅️ Back", "callback_data": "admin_dashboard", "action": "Return to admin dashboard"}
                    ],
                    "next_step": "Statistics Display"
                },
                {
                    "step": 3,
                    "name": "Payment Search",
                    "trigger": "Admin clicks search payments",
                    "input_required": "Search query (track_id, user_id, amount)",
                    "buttons": [
                        {"text": "🔍 Search", "callback_data": "admin_execute_search", "action": "Execute search"},
                        {"text": "📋 Recent Payments", "callback_data": "admin_recent_payments", "action": "View recent payments"},
                        {"text": "❌ Failed Payments", "callback_data": "admin_failed_payments", "action": "View failed payments"},
                        {"text": "⬅️ Back", "callback_data": "admin_dashboard", "action": "Return to admin dashboard"}
                    ],
                    "next_step": "Search Results"
                },
                {
                    "step": 4,
                    "name": "Underpayment Management",
                    "trigger": "Admin clicks underpayments",
                    "buttons": [
                        {"text": "📋 All Underpayments", "callback_data": "admin_all_underpayments", "action": "View all underpayments"},
                        {"text": "⏰ Pending Resolution", "callback_data": "admin_pending_underpayments", "action": "View pending underpayments"},
                        {"text": "✅ Resolved", "callback_data": "admin_resolved_underpayments", "action": "View resolved underpayments"},
                        {"text": "⬅️ Back", "callback_data": "admin_dashboard", "action": "Return to admin dashboard"}
                    ],
                    "next_step": "Underpayment List"
                },
                {
                    "step": 5,
                    "name": "Overpayment Management",
                    "trigger": "Admin clicks overpayments",
                    "buttons": [
                        {"text": "📋 All Overpayments", "callback_data": "admin_all_overpayments", "action": "View all overpayments"},
                        {"text": "🚨 Significant Overpayments", "callback_data": "admin_significant_overpayments", "action": "View significant overpayments"},
                        {"text": "✅ Processed", "callback_data": "admin_processed_overpayments", "action": "View processed overpayments"},
                        {"text": "⬅️ Back", "callback_data": "admin_dashboard", "action": "Return to admin dashboard"}
                    ],
                    "next_step": "Overpayment List"
                }
            ],
            "admin_permissions": {
                "required": True,
                "user_ids": "Configured admin user IDs",
                "commands": ["/admin", "/stats", "/search", "/manage"]
            }
        }
    
    def _document_user_management_flow(self) -> Dict[str, Any]:
        """
        Document the user management flow with all button interactions.
        """
        return {
            "name": "User Management Flow",
            "description": "User account management and balance operations",
            "steps": [
                {
                    "step": 1,
                    "name": "User Dashboard",
                    "trigger": "/profile command or profile button",
                    "buttons": [
                        {"text": "📊 View Balance", "callback_data": "view_balance", "action": "View current balance"},
                        {"text": "📋 Transaction History", "callback_data": "transaction_history", "action": "View transaction history"},
                        {"text": "💰 Make Deposit", "callback_data": "deposit_funds", "action": "Start deposit process"},
                        {"text": "🔍 Verify Payment", "callback_data": "verify_payment", "action": "Verify payment manually"},
                        {"text": "👑 VIP Status", "callback_data": "vip_status", "action": "View VIP tier status"},
                        {"text": "⚙️ Settings", "callback_data": "user_settings", "action": "User settings"}
                    ],
                    "next_step": "User Function Selection"
                },
                {
                    "step": 2,
                    "name": "Balance View",
                    "trigger": "User clicks view balance",
                    "buttons": [
                        {"text": "💰 Make Deposit", "callback_data": "deposit_funds", "action": "Start deposit process"},
                        {"text": "📋 Transaction History", "callback_data": "transaction_history", "action": "View transaction history"},
                        {"text": "🔄 Refresh", "callback_data": "refresh_balance", "action": "Refresh balance"},
                        {"text": "⬅️ Back", "callback_data": "user_dashboard", "action": "Return to user dashboard"}
                    ],
                    "next_step": "Balance Display"
                },
                {
                    "step": 3,
                    "name": "Transaction History",
                    "trigger": "User clicks transaction history",
                    "buttons": [
                        {"text": "📅 Last 7 Days", "callback_data": "transactions_7days", "action": "View last 7 days"},
                        {"text": "📅 Last 30 Days", "callback_data": "transactions_30days", "action": "View last 30 days"},
                        {"text": "📅 All Time", "callback_data": "transactions_all", "action": "View all transactions"},
                        {"text": "🔍 Search", "callback_data": "search_transactions", "action": "Search transactions"},
                        {"text": "⬅️ Back", "callback_data": "user_dashboard", "action": "Return to user dashboard"}
                    ],
                    "next_step": "Transaction List"
                },
                {
                    "step": 4,
                    "name": "VIP Status",
                    "trigger": "User clicks VIP status",
                    "buttons": [
                        {"text": "📊 Current Tier", "callback_data": "vip_current_tier", "action": "View current VIP tier"},
                        {"text": "🎯 Next Tier", "callback_data": "vip_next_tier", "action": "View next VIP tier"},
                        {"text": "📈 Progress", "callback_data": "vip_progress", "action": "View VIP progress"},
                        {"text": "🎁 Benefits", "callback_data": "vip_benefits", "action": "View VIP benefits"},
                        {"text": "⬅️ Back", "callback_data": "user_dashboard", "action": "Return to user dashboard"}
                    ],
                    "next_step": "VIP Information"
                }
            ],
            "user_permissions": {
                "required": False,
                "access": "All authenticated users",
                "restrictions": "Users can only access their own data"
            }
        }
    
    def get_flow_documentation(self, flow_name: str) -> Optional[Dict[str, Any]]:
        """
        Get documentation for a specific flow.
        
        Args:
            flow_name: Name of the flow to get documentation for
            
        Returns:
            Flow documentation dictionary or None if not found
        """
        return self.flows.get(flow_name)
    
    def get_all_flows(self) -> Dict[str, Any]:
        """
        Get documentation for all flows.
        
        Returns:
            Dictionary containing all flow documentation
        """
        return self.flows
    
    def get_button_actions(self, callback_data: str) -> Optional[Dict[str, Any]]:
        """
        Get button action information for a specific callback data.
        
        Args:
            callback_data: Callback data to look up
            
        Returns:
            Button action information or None if not found
        """
        for flow_name, flow_data in self.flows.items():
            for step in flow_data.get("steps", []):
                for button in step.get("buttons", []):
                    if button.get("callback_data") == callback_data:
                        return {
                            "flow": flow_name,
                            "step": step.get("name"),
                            "action": button.get("action"),
                            "button_text": button.get("text")
                        }
        return None
    
    def validate_button_flow(self, callback_data: str, current_state: str) -> bool:
        """
        Validate if a button action is valid for the current state.
        
        Args:
            callback_data: Callback data to validate
            current_state: Current FSM state
            
        Returns:
            True if button action is valid for current state
        """
        button_info = self.get_button_actions(callback_data)
        if not button_info:
            return False
        
        # Add state validation logic here
        # This would check if the button action is valid for the current state
        return True


# Global instance for easy access
payment_button_flows = PaymentButtonFlows()


def get_button_flow_documentation(flow_name: str = None) -> Union[Dict[str, Any], Dict[str, Dict[str, Any]]]:
    """
    Get button flow documentation.
    
    Args:
        flow_name: Specific flow name (optional)
        
    Returns:
        Flow documentation
    """
    if flow_name:
        return payment_button_flows.get_flow_documentation(flow_name)
    else:
        return payment_button_flows.get_all_flows()


def get_button_action_info(callback_data: str) -> Optional[Dict[str, Any]]:
    """
    Get button action information.
    
    Args:
        callback_data: Callback data to look up
        
    Returns:
        Button action information
    """
    return payment_button_flows.get_button_actions(callback_data)


def validate_button_action(callback_data: str, current_state: str) -> bool:
    """
    Validate button action for current state.
    
    Args:
        callback_data: Callback data to validate
        current_state: Current FSM state
        
    Returns:
        True if action is valid
    """
    return payment_button_flows.validate_button_flow(callback_data, current_state)
