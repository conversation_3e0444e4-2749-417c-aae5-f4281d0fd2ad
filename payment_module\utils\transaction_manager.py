"""
Transaction Manager for Payment Processing

This module provides transaction management with rollback capabilities
for complex payment operations.
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from contextlib import contextmanager
from datetime import datetime

logger = logging.getLogger(__name__)


class TransactionError(Exception):
    """Exception raised for transaction errors."""
    pass


class TransactionManager:
    """
    Manages complex transactions with rollback capabilities.
    """
    
    def __init__(self, transaction_name: str):
        self.transaction_name = transaction_name
        self.steps = []
        self.executed_steps = []
        self.logger = logging.getLogger(__name__ + f".{transaction_name}")
        self._context_result = None
    
    def add_step(self, step_name: str, func: Callable, *args, **kwargs):
        """
        Add a step to the transaction.
        
        Args:
            step_name: Name of the step
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
        """
        self.steps.append({
            "name": step_name,
            "func": func,
            "args": args,
            "kwargs": kwargs,
            "result": None,
            "rollback_func": None
        })
    
    def add_step_with_rollback(self, step_name: str, func: Callable, rollback_func: Callable, *args, **kwargs):
        """
        Add a step with rollback capability.
        
        Args:
            step_name: Name of the step
            func: Function to execute
            rollback_func: Function to rollback this step
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
        """
        self.steps.append({
            "name": step_name,
            "func": func,
            "args": args,
            "kwargs": kwargs,
            "result": None,
            "rollback_func": rollback_func
        })
    
    def execute(self) -> Dict[str, Any]:
        """
        Execute all steps in the transaction.
        
        Returns:
            Dict containing execution results
        """
        try:
            self.logger.info(f"Starting transaction: {self.transaction_name}")
            
            for step in self.steps:
                try:
                    self.logger.debug(f"Executing step: {step['name']}")
                    
                    # Execute the step
                    result = step["func"](*step["args"], **step["kwargs"])
                    step["result"] = result
                    self.executed_steps.append(step)
                    
                    self.logger.debug(f"Step completed: {step['name']}")
                    
                except Exception as e:
                    self.logger.error(f"Step failed: {step['name']}, error: {e}")
                    
                    # Rollback executed steps
                    self._rollback()
                    
                    return {
                        "success": False,
                        "error": f"Step '{step['name']}' failed: {str(e)}",
                        "executed_steps": len(self.executed_steps),
                        "total_steps": len(self.steps)
                    }
            
            self.logger.info(f"Transaction completed successfully: {self.transaction_name}")
            return {
                "success": True,
                "executed_steps": len(self.executed_steps),
                "total_steps": len(self.steps),
                "results": [step["result"] for step in self.executed_steps]
            }
            
        except Exception as e:
            self.logger.error(f"Transaction failed: {self.transaction_name}, error: {e}")
            self._rollback()
            
            return {
                "success": False,
                "error": str(e),
                "executed_steps": len(self.executed_steps),
                "total_steps": len(self.steps)
            }
    
    def _rollback(self):
        """Rollback executed steps."""
        self.logger.info(f"Rolling back transaction: {self.transaction_name}")
        
        # Rollback in reverse order
        for step in reversed(self.executed_steps):
            if step["rollback_func"]:
                try:
                    self.logger.debug(f"Rolling back step: {step['name']}")
                    step["rollback_func"](step["result"])
                    self.logger.debug(f"Rollback completed: {step['name']}")
                except Exception as e:
                    self.logger.error(f"Rollback failed for step {step['name']}: {e}")
        
        self.executed_steps.clear()
    
    def get_context_result(self):
        """Get the context result."""
        return self._context_result


@contextmanager
def transaction_context(transaction_name: str):
    """
    Context manager for transaction execution.
    
    Args:
        transaction_name: Name of the transaction
    """
    transaction = TransactionManager(transaction_name)
    
    try:
        yield transaction
        result = transaction.execute()
        transaction._context_result = result
        
        if not result["success"]:
            raise TransactionError(result["error"])
            
    except Exception as e:
        logger.error(f"Transaction context error: {e}")
        raise


def create_payment_transaction(user_id: int, amount: float, track_id: str) -> TransactionManager:
    """
    Create a transaction for processing a payment.
    Includes balance updates, payment recording, and bonus calculation.
    
    Args:
        user_id: User ID
        amount: Payment amount
        track_id: Payment tracking ID
        
    Returns:
        TransactionManager: Configured transaction
    """
    transaction = TransactionManager(f"payment_user_{user_id}")
    
    # Step 1: Validate user exists
    def validate_user(user_id: int) -> Dict[str, Any]:
        # In a real implementation, you would check if user exists in database
        return {"user_id": user_id, "valid": True}
    
    transaction.add_step("validate_user", validate_user, user_id)
    
    # Step 2: Calculate bonus
    def calculate_bonus(amount: float, user_id: int) -> Dict[str, Any]:
        from .bonus_calculator import calculate_deposit_bonus
        return calculate_deposit_bonus(amount, user_id)
    
    transaction.add_step("calculate_bonus", calculate_bonus, amount, user_id)
    
    # Step 3: Update user balance
    def update_balance(user_id: int, amount: float, bonus_amount: float) -> Dict[str, Any]:
        # In a real implementation, you would update the user's balance in database
        total_amount = amount + bonus_amount
        return {"user_id": user_id, "amount_added": total_amount, "new_balance": total_amount}
    
    def rollback_balance(result: Dict[str, Any]):
        # Rollback balance update
        logger.info(f"Rolling back balance update for user {result.get('user_id')}")
    
    transaction.add_step_with_rollback(
        "update_balance", 
        update_balance, 
        rollback_balance,
        user_id, 
        amount, 
        0.0  # bonus_amount will be updated from previous step
    )
    
    # Step 4: Record transaction
    def record_transaction(user_id: int, amount: float, track_id: str) -> Dict[str, Any]:
        # In a real implementation, you would record the transaction in database
        return {
            "user_id": user_id,
            "amount": amount,
            "track_id": track_id,
            "timestamp": datetime.now(),
            "status": "completed"
        }
    
    transaction.add_step("record_transaction", record_transaction, user_id, amount, track_id)
    
    return transaction

