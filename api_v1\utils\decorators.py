"""
API v1 Decorators

Provides common decorators for API v1 components including
caching, retry logic, and performance monitoring.
"""

import functools
import logging
import time
import asyncio
from typing import Any, Callable, Optional
from utils.central_logger import get_logger

logger = get_logger()


def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator for async functions to retry on failure with exponential backoff
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Multiplier for delay on each retry
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise
                    
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff
            
            raise last_exception
        
        return wrapper
    return decorator


def cache_result(ttl_seconds: int = 300):
    """
    Simple cache decorator for functions
    
    Args:
        ttl_seconds: Time to live for cached results in seconds
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            current_time = time.time()
            
            # Check if we have a valid cached result
            if cache_key in cache:
                result, timestamp = cache[cache_key]
                if current_time - timestamp < ttl_seconds:
                    return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache[cache_key] = (result, current_time)
            
            # Clean up old cache entries
            expired_keys = [
                key for key, (_, timestamp) in cache.items()
                if current_time - timestamp >= ttl_seconds
            ]
            for key in expired_keys:
                del cache[key]
            
            return result
        
        return wrapper
    return decorator


def log_execution(level: int = 20):  # INFO level
    """
    Decorator to log function execution
    
    Args:
        level: Logging level to use
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger.log(level, f"Starting execution of {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.log(level, f"Completed {func.__name__} in {execution_time:.3f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Failed {func.__name__} after {execution_time:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator


def log_execution_async(level: int = 20):  # INFO level
    """
    Async version of log_execution decorator
    
    Args:
        level: Logging level to use
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            logger.log(level, f"Starting execution of {func.__name__}")
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.log(level, f"Completed {func.__name__} in {execution_time:.3f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Failed {func.__name__} after {execution_time:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator


def validate_params(**param_validators):
    """
    Decorator to validate function parameters
    
    Args:
        **param_validators: Dictionary of parameter names to validator functions
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get function signature to map args to parameter names
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Validate each parameter
            for param_name, validator in param_validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    if not validator(value):
                        raise ValueError(f"Invalid value for parameter '{param_name}': {value}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


__all__ = [
    'retry_async',
    'cache_result',
    'log_execution',
    'log_execution_async',
    'validate_params',
]
