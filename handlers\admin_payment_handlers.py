"""
Admin Payment Management Handlers
Provides admin interface for payment management, analytics, and monitoring
"""

from __future__ import annotations

from aiogram import Router, F, Bot
from aiogram.filters import Command
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext

from services.payment_service import get_payment_service
from config.settings import get_settings
from utils.texts import DEMO_WATERMARK

from utils.central_logger import get_logger

logger = get_logger()


class AdminPaymentHandlers:
    """Admin handlers for payment management"""

    def __init__(self):
        self.payment_service = get_payment_service()
        self.settings = get_settings()

    def is_admin(self, user_id: int) -> bool:
        """Check if user is admin"""
        try:
            admin_ids_str = self.settings.ADMIN_USER_IDS.strip()
            if admin_ids_str:
                admin_ids = [int(uid.strip()) for uid in admin_ids_str.split(",") if uid.strip()]
                return user_id in admin_ids
            return False
        except (ValueError, AttributeError):
            return False

    async def cmd_payment_admin(self, message: Message) -> None:
        """Handle /payment_admin command - admin payment management"""
        try:
            user = message.from_user
            if not user or not self.is_admin(user.id):
                await message.answer("❌ Access denied. Admin privileges required." + DEMO_WATERMARK)
                return

            if not self.payment_service.is_available():
                await message.answer(
                    "🚫 Payment system is currently unavailable.\n"
                    "Please check configuration and try again." + DEMO_WATERMARK
                )
                return

            admin_text = (
                "🔧 <b>Payment Admin Panel</b>\n\n"
                "Choose an option below to manage payments:\n\n"
                "📊 <b>Analytics & Monitoring:</b>\n"
                "• View payment statistics\n"
                "• Check verification status\n"
                "• Monitor system health\n\n"
                "🔍 <b>Payment Management:</b>\n"
                "• Search payments\n"
                "• View payment details\n"
                "• Handle issues\n\n"
                "⚙️ <b>System Controls:</b>\n"
                "• Start/stop auto verification\n"
                "• View system logs\n"
                "• Manage configurations"
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📊 Payment Statistics",
                            callback_data="admin:payment_stats"
                        ),
                        InlineKeyboardButton(
                            text="🔍 Search Payments",
                            callback_data="admin:search_payments"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="⚙️ Verification Status",
                            callback_data="admin:verification_status"
                        ),
                        InlineKeyboardButton(
                            text="🔄 Auto Verification",
                            callback_data="admin:auto_verification"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📈 Payment Analytics",
                            callback_data="admin:payment_analytics"
                        ),
                        InlineKeyboardButton(
                            text="🛠️ System Health",
                            callback_data="admin:system_health"
                        )
                    ]
                ]
            )

            await message.answer(
                admin_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )

        except Exception as e:
            logger.error(f"Error in payment admin command: {e}")
            await message.answer("❌ Error accessing admin panel" + DEMO_WATERMARK)

    async def cb_admin_payment_stats(self, callback: CallbackQuery) -> None:
        """Handle admin payment statistics"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            # Get comprehensive payment statistics
            stats = self.payment_service.get_payment_statistics(days=30)

            if "error" in stats:
                await callback.answer(f"❌ Error: {stats['error']}", show_alert=True)
                return

            # Format statistics
            stats_text = (
                f"📊 <b>Payment Statistics (Last 30 Days)</b>\n\n"
                f"💰 <b>Total Payments:</b> {stats.get('total_payments', 0)}\n"
                f"💵 <b>Total Amount:</b> ${stats.get('total_amount', 0):.2f}\n"
                f"✅ <b>Success Rate:</b> {stats.get('success_rate', 0):.1f}%\n"
                f"⚠️ <b>Underpayment Rate:</b> {stats.get('underpayment_rate', 0):.1f}%\n"
                f"💰 <b>Overpayment Rate:</b> {stats.get('overpayment_rate', 0):.1f}%\n\n"
            )

            # Add status breakdown
            by_status = stats.get('by_status', {})
            if by_status:
                stats_text += f"📈 <b>Status Breakdown:</b>\n"
                for status, data in by_status.items():
                    stats_text += f"• {status.title()}: {data.get('count', 0)} payments (${data.get('total_amount', 0):.2f})\n"

            stats_text += f"\n<i>Statistics updated in real-time</i>"

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh",
                            callback_data="admin:payment_stats"
                        ),
                        InlineKeyboardButton(
                            text="📈 Analytics",
                            callback_data="admin:payment_analytics"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin",
                            callback_data="admin:payment_admin"
                        )
                    ]
                ]
            )

            await callback.message.edit_text(
                stats_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin payment stats: {e}")
            await callback.answer("❌ Error retrieving statistics", show_alert=True)

    async def cb_admin_verification_status(self, callback: CallbackQuery) -> None:
        """Handle admin verification status"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            # Get verification statistics
            verification_stats = self.payment_service.get_verification_statistics()

            if "error" in verification_stats:
                await callback.answer(f"❌ Error: {verification_stats['error']}", show_alert=True)
                return

            # Format verification status
            status_text = (
                f"⚙️ <b>Verification System Status</b>\n\n"
                f"🔄 <b>System Status:</b> {'🟢 Running' if verification_stats.get('running', False) else '🔴 Stopped'}\n"
                f"📊 <b>Total Verifications:</b> {verification_stats.get('total_verifications', 0)}\n"
                f"✅ <b>Successful:</b> {verification_stats.get('successful_verifications', 0)}\n"
                f"❌ <b>Failed:</b> {verification_stats.get('failed_verifications', 0)}\n"
                f"📈 <b>Success Rate:</b> {verification_stats.get('success_rate', 0):.1f}%\n"
                f"🔄 <b>Active Tasks:</b> {verification_stats.get('active_tasks', 0)}\n\n"
            )

            # Add recent activity if available
            if verification_stats.get('recent_activity'):
                status_text += f"📅 <b>Recent Activity:</b>\n"
                for activity in verification_stats['recent_activity'][:5]:
                    status_text += f"• {activity}\n"

            status_text += f"\n<i>Status updated in real-time</i>"

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh",
                            callback_data="admin:verification_status"
                        ),
                        InlineKeyboardButton(
                            text="⚙️ Auto Verification",
                            callback_data="admin:auto_verification"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin",
                            callback_data="admin:payment_admin"
                        )
                    ]
                ]
            )

            await callback.message.edit_text(
                status_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin verification status: {e}")
            await callback.answer("❌ Error retrieving verification status", show_alert=True)

    async def cb_admin_auto_verification(self, callback: CallbackQuery) -> None:
        """Handle admin auto verification controls"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            # Get current verification status
            verification_stats = self.payment_service.get_verification_statistics()
            is_running = verification_stats.get('running', False)

            control_text = (
                f"⚙️ <b>Auto Verification Controls</b>\n\n"
                f"🔄 <b>Current Status:</b> {'🟢 Running' if is_running else '🔴 Stopped'}\n\n"
                f"📋 <b>Available Actions:</b>\n"
                f"• Start automatic verification\n"
                f"• Stop automatic verification\n"
                f"• View verification statistics\n\n"
                f"⚠️ <b>Note:</b> Auto verification handles payment status checking automatically"
            )

            keyboard_buttons = []
            if is_running:
                keyboard_buttons.append([
                    InlineKeyboardButton(
                        text="🛑 Stop Auto Verification",
                        callback_data="admin:stop_auto_verification"
                    )
                ])
            else:
                keyboard_buttons.append([
                    InlineKeyboardButton(
                        text="▶️ Start Auto Verification",
                        callback_data="admin:start_auto_verification"
                    )
                ])

            keyboard_buttons.append([
                InlineKeyboardButton(
                    text="📊 Verification Status",
                    callback_data="admin:verification_status"
                ),
                InlineKeyboardButton(
                    text="🔄 Refresh",
                    callback_data="admin:auto_verification"
                )
            ])
            keyboard_buttons.append([
                InlineKeyboardButton(
                    text="🔙 Back to Admin",
                    callback_data="admin:payment_admin"
                )
            ])

            keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

            await callback.message.edit_text(
                control_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin auto verification: {e}")
            await callback.answer("❌ Error accessing auto verification controls", show_alert=True)

    async def cb_admin_start_auto_verification(self, callback: CallbackQuery) -> None:
        """Start auto verification"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            success = self.payment_service.start_auto_verification()

            if success:
                await callback.answer("✅ Auto verification started successfully!")
                await self.cb_admin_auto_verification(callback)
            else:
                await callback.answer("❌ Failed to start auto verification", show_alert=True)

        except Exception as e:
            logger.error(f"Error starting auto verification: {e}")
            await callback.answer("❌ Error starting auto verification", show_alert=True)

    async def cb_admin_stop_auto_verification(self, callback: CallbackQuery) -> None:
        """Stop auto verification"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            success = self.payment_service.stop_auto_verification()

            if success:
                await callback.answer("✅ Auto verification stopped successfully!")
                await self.cb_admin_auto_verification(callback)
            else:
                await callback.answer("❌ Failed to stop auto verification", show_alert=True)

        except Exception as e:
            logger.error(f"Error stopping auto verification: {e}")
            await callback.answer("❌ Error stopping auto verification", show_alert=True)

    async def cb_admin_payment_analytics(self, callback: CallbackQuery) -> None:
        """Handle admin payment analytics"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            # Get comprehensive payment analytics
            analytics = self.payment_service.get_payment_analytics(days=30)

            if "error" in analytics:
                await callback.answer(f"❌ Error: {analytics['error']}", show_alert=True)
                return

            # Format analytics
            summary = analytics.get('summary', {})
            analytics_text = (
                f"📈 <b>Payment Analytics (Last 30 Days)</b>\n\n"
                f"📊 <b>Summary:</b>\n"
                f"• Total Payments: {summary.get('total_payments', 0)}\n"
                f"• Total Amount: ${summary.get('total_amount', 0):.2f}\n"
                f"• Success Rate: {summary.get('success_rate', 0):.1f}%\n"
                f"• Avg Daily Payments: {summary.get('avg_daily_payments', 0):.1f}\n"
                f"• Avg Daily Amount: ${summary.get('avg_daily_amount', 0):.2f}\n\n"
            )

            # Add trends if available
            daily_trends = analytics.get('daily_trends', [])
            if daily_trends:
                analytics_text += f"📅 <b>Recent Trends:</b>\n"
                for trend in daily_trends[-7:]:  # Show last 7 days
                    analytics_text += f"• {trend.get('date', 'N/A')}: {trend.get('count', 0)} payments (${trend.get('amount', 0):.2f})\n"

            analytics_text += f"\n<i>Analytics updated in real-time</i>"

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh",
                            callback_data="admin:payment_analytics"
                        ),
                        InlineKeyboardButton(
                            text="📊 Statistics",
                            callback_data="admin:payment_stats"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin",
                            callback_data="admin:payment_admin"
                        )
                    ]
                ]
            )

            await callback.message.edit_text(
                analytics_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin payment analytics: {e}")
            await callback.answer("❌ Error retrieving analytics", show_alert=True)

    async def cb_admin_system_health(self, callback: CallbackQuery) -> None:
        """Handle admin system health check"""
        try:
            user = callback.from_user
            if not user or not self.is_admin(user.id):
                await callback.answer("❌ Access denied", show_alert=True)
                return

            # Check system health
            payment_available = self.payment_service.is_available()
            verification_stats = self.payment_service.get_verification_statistics()
            verification_running = verification_stats.get('running', False)

            health_text = (
                f"🛠️ <b>Payment System Health</b>\n\n"
                f"💳 <b>Payment Service:</b> {'🟢 Available' if payment_available else '🔴 Unavailable'}\n"
                f"🔄 <b>Auto Verification:</b> {'🟢 Running' if verification_running else '🔴 Stopped'}\n"
                f"🔑 <b>API Key:</b> {'🟢 Configured' if self.settings.OXA_PAY_API_KEY else '🔴 Missing'}\n"
                f"🌐 <b>Callback URL:</b> {'🟢 Set' if self.settings.OXA_PAY_CALLBACK_URL else '🟡 Auto-generated'}\n"
                f"🔧 <b>Development Mode:</b> {'🟡 Enabled' if self.settings.PAYMENT_DEVELOPMENT_MODE else '🟢 Production'}\n\n"
            )

            # Add verification statistics
            if verification_stats and not verification_stats.get('error'):
                health_text += f"📊 <b>Verification Stats:</b>\n"
                health_text += f"• Total: {verification_stats.get('total_verifications', 0)}\n"
                health_text += f"• Success Rate: {verification_stats.get('success_rate', 0):.1f}%\n"
                health_text += f"• Active Tasks: {verification_stats.get('active_tasks', 0)}\n\n"

            health_text += f"<i>Health check performed at {callback.message.date.strftime('%Y-%m-%d %H:%M:%S')}</i>"

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh",
                            callback_data="admin:system_health"
                        ),
                        InlineKeyboardButton(
                            text="⚙️ Verification",
                            callback_data="admin:verification_status"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin",
                            callback_data="admin:payment_admin"
                        )
                    ]
                ]
            )

            await callback.message.edit_text(
                health_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in admin system health: {e}")
            await callback.answer("❌ Error checking system health", show_alert=True)


def get_admin_payment_router() -> Router:
    """Get admin payment router"""
    router = Router()
    handlers = AdminPaymentHandlers()
    
    # Commands
    router.message.register(handlers.cmd_payment_admin, Command("payment_admin"))
    
    # Callbacks
    router.callback_query.register(
        handlers.cb_admin_payment_stats,
        F.data == "admin:payment_stats"
    )
    router.callback_query.register(
        handlers.cb_admin_verification_status,
        F.data == "admin:verification_status"
    )
    router.callback_query.register(
        handlers.cb_admin_auto_verification,
        F.data == "admin:auto_verification"
    )
    router.callback_query.register(
        handlers.cb_admin_start_auto_verification,
        F.data == "admin:start_auto_verification"
    )
    router.callback_query.register(
        handlers.cb_admin_stop_auto_verification,
        F.data == "admin:stop_auto_verification"
    )
    router.callback_query.register(
        handlers.cb_admin_payment_analytics,
        F.data == "admin:payment_analytics"
    )
    router.callback_query.register(
        handlers.cb_admin_system_health,
        F.data == "admin:system_health"
    )
    
    return router
