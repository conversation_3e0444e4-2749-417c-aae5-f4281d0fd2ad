"""Catalog and browsing handlers."""

from __future__ import annotations

import asyncio
import logging
import re
from typing import Any, Dict, Iterable, Optional, List

from aiogram import Router, F
from aiogram.types import (
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    Message,
)
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from services.card_service import CardService
from services.cart_service import CartService
from services.user_service import UserService
from services.product_service import ProductService
from utils.keyboards import back_keyboard
from utils.texts import DEMO_WATERMARK, INFO_NO_RESULTS
from utils.validation import ValidationError, sanitize_text_input
from utils.ui_manager import ui_manager
from utils.product_display import product_formatter, collection_manager
from utils.ui_components import create_message, MessageType
from utils.loading_animations import (
    LoadingStages, BROWSE_STAGES, FILTER_STAGES, CART_STAGES, SEARCH_STAGES
)

# ============================================================================
# UNIFIED PAGINATION SYSTEM - Single Source of Truth
# ============================================================================
# This system provides consistent pagination across ALL browsing operations:
# - Browse all cards
# - Browse with filters
# - Search results
# - Category browsing
#
# How it works:
# 1. API returns 100 cards per call (CARDS_PER_API_PAGE)
# 2. UI displays 6 cards per page (CARDS_PER_UI_PAGE)
# 3. When user loads page 1-16, cards come from cache (1 API call = 16 UI pages)
# 4. When user approaches end of cached data, next 100 cards are prefetched
# 5. All operations use _render_cards_page() as the single entry point
#
# Key Methods (Single Source of Truth):
# - _render_cards_page(): Main pagination handler (used by all browse operations)
# - _get_cached_page(): Retrieves UI page from cache
# - _update_search_cache(): Stores API results in cache
# - _should_prefetch_next_batch(): Determines when to fetch more cards
# - _prefetch_next_batch(): Background fetch of next 100 cards
# ============================================================================

CARDS_PER_UI_PAGE = 6  # Number of cards shown per UI page
CARDS_PER_API_PAGE = 100  # Number of cards fetched per API call
from middleware import attach_common_middlewares
from config.settings import get_settings

from utils.central_logger import get_logger

logger = get_logger()


class CatalogHandlers:
    """Catalog and browsing handlers"""

    CATEGORY_INFO: Dict[str, Dict[str, Any]] = {
        "location": {
            "label": "📍 Location",
            "description": "Filter by geography or require ZIP verification.",
            "keys": ["country", "state", "city", "zip", "zipCheck"],
        },
        "card": {
            "label": "💳 Card Details",
            "description": "Narrow results by brand, type, level, bank, or BIN.",
            "keys": ["brand", "type", "level", "bank", "bin"],
        },
        "pricing": {
            "label": "💲 Pricing",
            "description": "Control minimum/maximum pricing.",
            "keys": ["priceFrom", "priceTo"],
        },
        "contact": {
            "label": "🧾 Contact Data",
            "description": "Require address, phone, or email data.",
            "keys": ["address", "phone", "email"],
        },
        "identity": {
            "label": "🛡 Identity Data",
            "description": "Require sensitive identity-related data points.",
            "keys": ["dob", "ssn", "mmn", "ip", "dl", "ua"],
        },
        "extras": {
            "label": "✨ Extras",
            "description": "Special options like no CVV, refunds, or discounts.",
            "keys": ["withoutcvv", "refundable", "expirethismonth", "discount"],
        },
    }

    # API v3 specific category info
    CATEGORY_INFO_V3: Dict[str, Dict[str, Any]] = {
        "location": {
            "label": "🌍 Location",
            "description": "Filter by continent, country, region, city, or postal code.",
            "keys": ["continent", "country", "region", "city", "postal_code"],
        },
        "card": {
            "label": "💳 Card Details",
            "description": "Filter by scheme, type, level, bank, or BIN number.",
            "keys": ["scheme", "type", "level", "selected_bank", "searched_bank", "bins"],
        },
        "contact": {
            "label": "📞 Contact Data",
            "description": "Require billing address, phone number, or date of birth.",
            "keys": ["with_billing", "with_phone", "with_dob"],
        },
        "quality": {
            "label": "💎 Quality & Status",
            "description": "Filter by card validity and expiration status.",
            "keys": ["show_medium_valid", "expiring_soon", "expiring_next"],
        },
        "advanced": {
            "label": "⚙️ Advanced",
            "description": "Advanced filtering options and ethnicity filters.",
            "keys": ["cc_per_bin", "ethnicity"],
        },
    }

    FILTER_LABELS: Dict[str, str] = {
        "bank": "Bank",
        "bin": "BIN",
        "country": "Country",
        "state": "State",
        "city": "City",
        "brand": "Brand",
        "type": "Card Type",
        "level": "Level",
        "zip": "ZIP",
        "price": "Price Range",
        "priceFrom": "Price From",
        "priceTo": "Price To",
        "zipCheck": "ZIP Check",
        "address": "Address",
        "phone": "Phone",
        "email": "Email",
        "withoutcvv": "Without CVV",
        "refundable": "Refundable",
        "expirethismonth": "Expire This Month",
        "dob": "DOB",
        "ssn": "SSN",
        "mmn": "MMN",
        "ip": "IP",
        "dl": "Driving Licence",
        "ua": "User Agent",
        "discount": "Discount",
    }

    # API v3 specific filter labels
    FILTER_LABELS_V3: Dict[str, str] = {
        "continent": "Continent",
        "country": "Country",
        "region": "Region",
        "city": "City",
        "postal_code": "Postal Code",
        "scheme": "Card Scheme",
        "type": "Card Type",
        "level": "Card Level",
        "selected_bank": "Bank",
        "searched_bank": "Bank Search",
        "bins": "BIN Number",
        "with_billing": "Billing Address",
        "with_phone": "Phone Number",
        "with_dob": "Date of Birth",
        "show_medium_valid": "Show Medium Valid",
        "expiring_soon": "Expiring Soon",
        "expiring_next": "Expiring Next Month",
        "cc_per_bin": "CC Per BIN",
        "ethnicity": "Ethnicity",
    }

    BOOLEAN_FILTER_KEYS = {
        "zipCheck",
        "address",
        "phone",
        "email",
        "withoutcvv",
        "refundable",
        "expirethismonth",
        "dob",
        "ssn",
        "mmn",
        "ip",
        "dl",
        "ua",
        "discount",
    }

    # API v3 specific boolean filter keys
    BOOLEAN_FILTER_KEYS_V3 = {
        "with_billing",
        "with_phone",
        "with_dob",
        "show_medium_valid",
        "expiring_soon",
        "expiring_next",
        "cc_per_bin",
    }

    DYNAMIC_FILTER_KEYS = {
        "country",
        "state",
        "city",
        "zip",
        "brand",
        "type",
        "level",
        "bank",
    }

    # API v3 specific dynamic filter keys
    DYNAMIC_FILTER_KEYS_V3 = {
        "continent",
        "country",
        "region",
        "city",
        "scheme",
        "type",
        "level",
        "selected_bank",
        "searched_bank",
    }
    DYNAMIC_FILTER_RESET_MAP = {
        "country": {"country", "state", "city", "zip", "bank"},
        "state": {"state", "city", "zip", "bank"},
        "city": {"city", "zip", "bank"},
        "zip": {"zip", "bank"},
        "brand": {"brand"},
        "type": {"type"},
        "level": {"level"},
        "bank": {"bank"},
    }

    # API v3 specific dynamic filter reset map
    DYNAMIC_FILTER_RESET_MAP_V3 = {
        "continent": {"continent", "country", "region", "city"},
        "country": {"country", "region", "city"},
        "region": {"region", "city"},
        "city": {"city"},
        "scheme": {"scheme"},
        "type": {"type"},
        "level": {"level"},
        "selected_bank": {"selected_bank"},
        "searched_bank": {"searched_bank"},
    }
    FILTER_OPTION_LIMIT = 20
    FILTER_OPTION_ROW_WIDTH = 2
    LOCATION_FILTER_ROW_WIDTH = 3
    COUNTRY_NAME_TO_CODE: Dict[str, str] = {
        "united states": "US",
        "usa": "US",
        "canada": "CA",
        "united kingdom": "GB",
        "uk": "GB",
        "australia": "AU",
        "germany": "DE",
        "france": "FR",
        "italy": "IT",
        "spain": "ES",
        "netherlands": "NL",
        "sweden": "SE",
        "norway": "NO",
        "denmark": "DK",
        "finland": "FI",
        "switzerland": "CH",
        "austria": "AT",
        "belgium": "BE",
        "ireland": "IE",
        "portugal": "PT",
        "poland": "PL",
        "czech republic": "CZ",
    }

    PRESET_COUNTRIES = ("US", "CA", "GB", "AU", "DE", "FR")
    PRESET_BRANDS = ("VISA", "MASTERCARD", "AMEX", "DISCOVER")
    PRESET_TYPES = ("CREDIT", "DEBIT", "PREPAID")
    PRESET_BANKS = (
        "CHASE",
        "CITI",
        "BANK OF AMERICA",
        "WELLS FARGO",
        "AMERICAN EXPRESS",
    )
    PRESET_BINS = ("452083", "601101", "411111", "438857", "520082", "548042")
    PRICE_PRESETS = (
        ("$0 - $2", 0, 2),
        ("$2 - $3", 2, 3),
        ("$3 - $5", 3, 5),
        ("$5 - $10", 5, 10),
    )

    FILTER_KEY_TO_CATEGORY: Dict[str, str] = {
        key: category
        for category, meta in CATEGORY_INFO.items()
        for key in meta["keys"]
    }
    FILTER_KEY_TO_CATEGORY.update({"price": "pricing"})

    # Add API v3 filter key mappings
    FILTER_KEY_TO_CATEGORY_V3: Dict[str, str] = {
        key: category
        for category, meta in CATEGORY_INFO_V3.items()
        for key in meta["keys"]
    }

    MANUAL_INPUT_CONFIG: Dict[str, Dict[str, str]] = {
        "state": {
            "title": "Set State/Region",
            "prompt": "Send the state or region to match (e.g., <code>CA</code> or <code>California</code>).",
            "mode": "text",
        },
        "region": {
            "title": "Set Region",
            "prompt": "Send the region to match (e.g., <code>California</code>).",
            "mode": "text",
        },
        "city": {
            "title": "Set City",
            "prompt": "Send the city to match (e.g., <code>Los Angeles</code>).",
            "mode": "text",
        },
        "zip": {
            "title": "Set ZIP",
            "prompt": "Send the ZIP or postal code (3–10 digits).",
            "mode": "zip",
        },
        "postal_code": {
            "title": "Set Postal Code",
            "prompt": "Send the postal code (e.g., <code>90210</code>).",
            "mode": "text",
        },
        "bank": {
            "title": "Custom Bank",
            "prompt": "Send the bank name to match (e.g., <code>Barclays</code>).",
            "mode": "text",
        },
        "selected_bank": {
            "title": "Bank Name",
            "prompt": "Send the bank name to search for (e.g., <code>Chase</code>, <code>Wells Fargo</code>).",
            "mode": "text",
        },
        "searched_bank": {
            "title": "Search Bank",
            "prompt": "Send the bank name to search for (e.g., <code>Chase</code>, <code>Wells Fargo</code>).",
            "mode": "text",
        },
        "bin": {
            "title": "Enter BIN",
            "prompt": "Send 4–8 digits for the BIN (e.g., <code>452083</code>).",
            "mode": "bin",
        },
        "bins": {
            "title": "Enter BINs",
            "prompt": "Send BIN numbers separated by commas (e.g., <code>452083,411111</code>).",
            "mode": "text",
        },
        "ethnicity": {
            "title": "Set Ethnicity",
            "prompt": "Send the ethnicity filter (e.g., <code>Asian</code>, <code>Latino</code>).",
            "mode": "text",
        },
        "priceFrom": {
            "title": "Minimum Price",
            "prompt": "Send the minimum price (e.g., <code>5</code>).",
            "mode": "price",
        },
        "priceTo": {
            "title": "Maximum Price",
            "prompt": "Send the maximum price (e.g., <code>25</code>).",
            "mode": "price",
        },
    }

    MAX_PRICE = 10000.0
    MAX_PRICE_SPREAD = 5000.0

    def __init__(self):
        # Initialize settings
        self.settings = get_settings()
        # Lazy init to avoid DB access on import
        self.user_card_services: dict[int, tuple[Optional[str], CardService]] = {}
        self.cart_service = None
        # Lazy to avoid DB access during simple handler init
        self.user_service = None
        self.product_service = ProductService()
        # In-memory filter state per user
        self.user_filters: dict[int, dict] = {}
        self.user_applied_filters: dict[int, dict] = {}
        # Track dynamic option tokens for compact callback data
        self.user_option_tokens: dict[int, dict[str, Dict[str, Any]]] = {}
        # Track which filter interface users are using (category vs direct)
        self.user_filter_interface: dict[int, str] = {}
        # Track navigation source to preserve context when returning from filters
        self.user_navigation_source: dict[int, str] = {}

        # Cache for current page cards to avoid re-fetching when adding to cart
        self.user_current_cards: dict[int, list[dict]] = {}
        
        # Enhanced search result cache for client-side pagination
        self.user_search_cache: dict[int, dict] = {}
        # Structure: {user_id: {
        #   'filters_hash': str,  # Hash of active filters to detect changes
        #   'cards': List[Dict],  # All fetched cards
        #   'total_count': int,   # Total available cards (server-side)
        #   'fetched_api_pages': Set[int],  # Which API pages we've already fetched (100 cards each)
        #   'page_size': int,     # Items per UI page (default 6)
        #   'api_page_size': int,  # Items per API page (default 100)
        #   'last_fetch_time': float,  # When was last fetch made
        #   'has_more_data': bool,  # Whether server has more data available
        #   'api_version': str,   # API version used for this cache
        #   'product_type': str   # Product type for this cache
        # }}

    async def _get_card_service(self, user_id: int, current_product: str = None, current_api: str = None) -> CardService:
        """Get card service configured for user's current product selection"""
        logger.debug(f"Getting card service for user {user_id}")

        # Periodically clean up old caches
        if len(self.user_card_services) > 50:  # Clean up when we have too many cached services
            self._cleanup_old_caches()

        # Use provided product info or fetch it
        if current_product is None or current_api is None:
            current_product, current_api = (
                await self.product_service.get_user_current_selection(user_id)
            )
        logger.debug(
            f"User {user_id} selection: product={current_product}, api={current_api}"
        )

        cached = self.user_card_services.get(user_id)
        if cached and cached[0] == current_api:
            logger.debug(
                f"Using cached CardService for user {user_id} (api={current_api})"
            )
            return cached[1]

        # Creating new CardService for user
        external_api_service = None
        if current_api:
            external_api_service = (
                await self.product_service.get_external_api_service_for_user(user_id)
            )

        card_service = CardService(external_api_service=external_api_service)
        # CardService initialized

        self.user_card_services[user_id] = (current_api, card_service)
        return card_service

    async def _get_user_api_version(self, user_id: int) -> str:
        """Get the API version for the user's current selection"""
        try:
            # Get API version from global settings first
            api_version = getattr(self.settings, 'EXTERNAL_API_VERSION', 'v1').lower()
            
            # Auto-detect API v3 if credentials are configured but version not explicitly set
            if api_version not in ("v3", "base3"):
                # Check if API v3 credentials are configured
                has_v3_config = (
                    (getattr(self.settings, 'EXTERNAL_V3_BASE_URL', '') or 
                     getattr(self.settings, 'API_V3_BASE_URL', '')) and
                    (getattr(self.settings, 'EXTERNAL_V3_USERNAME', '') or 
                     getattr(self.settings, 'API_V3_USERNAME', '')) and
                    (getattr(self.settings, 'EXTERNAL_V3_PASSWORD', '') or 
                     getattr(self.settings, 'API_V3_PASSWORD', ''))
                )
                
                if has_v3_config:
                    logger.info(f"🔍 Auto-detected API v3 configuration for user {user_id} (EXTERNAL_API_VERSION={api_version}, but v3 credentials found)")
                    api_version = "v3"
            
            # Normalize version strings
            if api_version in ("v3", "base3"):
                logger.debug(f"User {user_id} API version: v3 (from settings)")
                result = "v3"
            elif api_version in ("v2", "base2"):
                logger.debug(f"User {user_id} API version: v2 (from settings)")
                result = "v2"
            else:
                logger.debug(f"User {user_id} API version: v1 (from settings, default)")
                result = "v1"
            
            # Cache the result for filter normalization
            setattr(self, f'_cached_api_version_{user_id}', result)
            return result
            
        except Exception as e:
            logger.error(f"Error getting API version for user {user_id}: {e}")
            # Fallback: try to get from cached service
            try:
                cached = self.user_card_services.get(user_id)
                if cached:
                    api_version = cached[1]._get_current_api_version()
                    logger.debug(f"User {user_id} API version: {api_version} (fallback cached)")
                    return api_version
            except (AttributeError, KeyError, TypeError) as fallback_error:
                logger.debug(f"Fallback API version detection failed: {fallback_error}")
                pass
            return "v1"  # Ultimate fallback

    async def _get_v3_filter_options(self, filter_key: str, user_id: int) -> list[dict]:
        """Get filter options for API v3 from centralized filter data"""
        try:
            logger.debug(f"Getting API v3 filter options for {filter_key} (user {user_id})")

            # Use centralized filter manager for better performance
            from api_v3.utils.filter_manager import convert_filters_to_standard_format
            
            # Get standard format filters (cached internally)
            standard_filters = convert_filters_to_standard_format()
            
            # Map filter keys to standard filter names
            filter_mapping = {
                "country": "countries",
                "continent": "continents", 
                "brand": "schemes",
                "scheme": "schemes",
                "type": "types",
                "level": "levels",
                "bank": "banks"
            }
            
            standard_key = filter_mapping.get(filter_key, filter_key)
            options_list = standard_filters.get(standard_key, [])
            
            if options_list:
                logger.debug(f"Got {len(options_list)} options for {filter_key} from centralized data")
                return [{"label": option, "value": option} for option in options_list]  # Full dataset for pagination

            # No fallback - return empty if centralized data not available
            logger.error(f"No real API data available for {filter_key}")
            return []

        except Exception as e:
            logger.error(f"Failed to get API v3 filter options for {filter_key}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []  # No fallback - real API data only

    # Static filter options removed - only real API calls allowed
    # All filter requests must come from actual external API endpoints
    
    # Minimal fallback options method removed - only real API calls allowed

    # FSM state for manual filter input
    class CatalogFilterStates(StatesGroup):
        WAITING_VALUE = State()

    def _get_user_id(self, callback: CallbackQuery) -> int:
        """Extract user ID from callback query"""
        return callback.from_user.id if callback.from_user else 0

    def _get_filters_hash(self, filters: dict, product_type: str, api_version: str) -> str:
        """Generate a hash for the current filter combination to detect changes"""
        import hashlib
        import json
        
        # Create a stable string representation of filters, product, and API
        filter_str = json.dumps({
            'filters': filters,
            'product': product_type,
            'api': api_version
        }, sort_keys=True)
        
        return hashlib.md5(filter_str.encode()).hexdigest()
    
    def _is_cache_valid(self, user_id: int, filters: dict, product_type: str, api_version: str) -> bool:
        """Check if the current search cache is valid for the given filters"""
        cache = self.user_search_cache.get(user_id)
        if not cache:
            return False
        
        current_hash = self._get_filters_hash(filters, product_type, api_version)
        cache_hash = cache.get('filters_hash')
        
        # Check if filters match
        if cache_hash != current_hash:
            return False
        
        # Different cache durations based on API version
        import time
        cache_age = time.time() - cache.get('last_fetch_time', 0)
        
        if api_version == "v3":
            # API v3 cache expires faster due to dynamic data
            max_age = 600  # 10 minutes
        else:
            # Other APIs can cache longer
            max_age = 1800  # 30 minutes
        
        is_recent = cache_age < max_age
        
        if not is_recent:
            logger.debug(f"Cache expired for user {user_id}: {cache_age:.1f}s > {max_age}s (API {api_version})")
        
        return is_recent
    
    def _get_cached_page(self, user_id: int, page: int) -> tuple[list, int, bool]:
        """
        Get a page of results from cache if available.
        This is the single source of truth for pagination logic.
        
        Returns:
            tuple: (cards_for_page, displayable_count, cache_hit)
            - displayable_count: Shows cached count + indicator for more data
        """
        cache = self.user_search_cache.get(user_id)
        if not cache:
            return [], 0, False
        
        page_size = cache.get('page_size', CARDS_PER_UI_PAGE)
        all_cards = cache.get('cards', [])
        has_more_data = cache.get('has_more_data', False)
        
        # Count actual cached cards (non-None)
        cached_count = len([c for c in all_cards if c is not None])
        
        # Calculate displayable count:
        # - If we have more data available, show cached + 1 page to enable "next"
        # - If no more data, show exact cached count
        if has_more_data:
            # Add extra page worth to show there's more
            displayable_count = cached_count + page_size
        else:
            displayable_count = cached_count
        
        # Calculate page boundaries
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        # Check if we have the data for this page in cache
        if start_idx < len(all_cards):
            # Filter out None placeholders and get actual cards
            page_cards = [c for c in all_cards[start_idx:end_idx] if c is not None]
            if page_cards:
                return page_cards, displayable_count, True
        
        # Cache miss - need to fetch from API
        return [], displayable_count, False
    
    def _update_search_cache(self, user_id: int, filters: dict, product_type: str, 
                           api_version: str, new_cards: list, api_page: int, 
                           total_count: int, ui_page_size: int = None, api_page_size: int = None) -> None:
        """
        Update the search cache with new results.
        This is the single source of truth for cache management.
        
        Args:
            user_id: User ID
            filters: Applied filters
            product_type: Product type (e.g., 'cards_hq')
            api_version: API version (e.g., 'v1', 'v3')
            new_cards: List of new cards to add
            api_page: The API page number (1-indexed) 
            total_count: Total available count from API
            ui_page_size: How many cards to show per UI page (default: CARDS_PER_UI_PAGE)
            api_page_size: How many cards the API returns per page (default: CARDS_PER_API_PAGE)
        """
        import time
        
        # Use global constants if not specified
        if ui_page_size is None:
            ui_page_size = CARDS_PER_UI_PAGE
        if api_page_size is None:
            api_page_size = CARDS_PER_API_PAGE
        
        current_hash = self._get_filters_hash(filters, product_type, api_version)
        
        # Get existing cache or create new one
        cache = self.user_search_cache.get(user_id, {})
        
        # If filters changed, reset cache
        if cache.get('filters_hash') != current_hash:
            logger.info(f"Cache reset for user {user_id} - filters changed")
            cache = {
                'filters_hash': current_hash,
                'cards': [],
                'fetched_api_pages': set(),
                'page_size': ui_page_size,
                'api_page_size': api_page_size,
                'api_version': api_version,
                'product_type': product_type,
                'has_more_data': True
            }
        
        # Update cache data
        cache['total_count'] = total_count
        cache['last_fetch_time'] = time.time()
        
        # Validate and clean cards before storing in cache
        validated_cards = self._validate_and_clean_cards(new_cards)
        logger.info(f"🗄️ Storing {len(validated_cards)} validated cards in cache for API page {api_page}")
        
        # Calculate where to insert based on API page
        start_idx = (api_page - 1) * api_page_size
        
        # Extend cards list if needed (fill with None as placeholders)
        while len(cache['cards']) < start_idx:
            cache['cards'].append(None)
        
        # Insert validated cards at correct position
        for i, card in enumerate(validated_cards):
            idx = start_idx + i
            if idx < len(cache['cards']):
                cache['cards'][idx] = card
            else:
                cache['cards'].append(card)
        
        # Mark this API page as fetched
        cache['fetched_api_pages'].add(api_page)
        
        # Update has_more_data based on results
        if len(validated_cards) < api_page_size:
            cache['has_more_data'] = False
            logger.info(f"No more data available for user {user_id} (got {len(validated_cards)} < {api_page_size})")
        
        self.user_search_cache[user_id] = cache
        
        valid_count = len([c for c in cache['cards'] if c is not None])
        logger.info(f"✅ Cache updated for user {user_id}: API page {api_page}, "
                   f"cached={valid_count} cards (total available: {total_count}), "
                   f"has_more={cache['has_more_data']}")
    
    def _should_prefetch_next_batch(self, user_id: int, current_page: int) -> bool:
        """
        Determine if we should prefetch the next batch of results.
        Prefetch when user is approaching the end of cached data.
        
        Args:
            user_id: User ID
            current_page: Current UI page being viewed
            
        Returns:
            bool: True if we should prefetch next API page
        """
        cache = self.user_search_cache.get(user_id)
        if not cache or not cache.get('has_more_data', False):
            return False
        
        ui_page_size = cache.get('page_size', CARDS_PER_UI_PAGE)
        api_page_size = cache.get('api_page_size', CARDS_PER_API_PAGE)
        total_cached = len([c for c in cache.get('cards', []) if c is not None])
        
        # Calculate how many cards we need for the next 3 UI pages
        cards_needed_for_next_pages = (current_page + 3) * ui_page_size
        
        # Prefetch if we don't have enough cards for the next few pages
        should_prefetch = cards_needed_for_next_pages > total_cached
        
        if should_prefetch:
            logger.info(f"Should prefetch for user {user_id}: page {current_page}, "
                       f"cached={total_cached}, needed={cards_needed_for_next_pages}")
        
        return should_prefetch
    
    async def _prefetch_next_batch(self, user_id: int, filters: dict, 
                                 current_page: int) -> None:
        """
        Prefetch the next batch of results in the background.
        
        Args:
            user_id: User ID
            filters: Applied filters
            current_page: Current UI page
        """
        try:
            cache = self.user_search_cache.get(user_id)
            if not cache or not cache.get('has_more_data', False):
                return
            
            # Determine which API page to fetch next
            api_page_size = cache.get('api_page_size', CARDS_PER_API_PAGE)
            fetched_api_pages = cache.get('fetched_api_pages', set())
            
            # Find the next API page we haven't fetched yet
            next_api_page = max(fetched_api_pages) + 1 if fetched_api_pages else 1
            
            # Check if already fetched
            if next_api_page in fetched_api_pages:
                logger.debug(f"API page {next_api_page} already fetched for user {user_id}")
                return
            
            logger.info(f"🔄 Prefetching API page {next_api_page} for user {user_id}")
            
            # Get the current API selection
            current_product, current_api = await self.product_service.get_user_current_selection(user_id)
            
            # Fetch next batch from API
            card_service = await self._get_card_service(user_id, current_product, current_api)
            cards_data = await card_service.fetch_cards(
                page=next_api_page, limit=api_page_size, filters=filters
            )
            
            if cards_data.get("success", False):
                new_cards = cards_data.get("data", [])
                total_count = cards_data.get("totalCount", cache.get('total_count', 0))
                
                # Update cache with new batch using the proper method
                api_version = await self._get_user_api_version(user_id)
                product_str = str(current_product) if current_product else "unknown"
                
                # Use _update_search_cache to properly store the cards
                self._update_search_cache(
                    user_id, filters, product_str, api_version,
                    new_cards, next_api_page, total_count
                )
                
                logger.info(f"✅ Prefetched {len(new_cards)} cards (API page {next_api_page}) for user {user_id}")
                
        except Exception as e:
            logger.warning(f"⚠️ Prefetch failed for user {user_id}: {e}")

    def _validate_and_clean_cards(self, cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean card data - OPTIMIZED to skip pre-validated cards."""
        
        # OPTIMIZATION: Skip validation for cards from centralized extractor (they're already clean)
        if cards and isinstance(cards[0], dict):
            first_card = cards[0]
            # Check if it has the signature of a processed card from card_extractor
            has_id = first_card.get('_id') or first_card.get('card_id')
            has_many_fields = len(first_card) > 8
            has_processed_fields = any(key in first_card for key in ['bin', 'brand', 'country', 'price'])
            
            if has_id and has_many_fields and has_processed_fields:
                logger.info(f"✅ Using {len(cards)} pre-validated cards from centralized extractor (validation skipped)")
                return cards
        
        validated_cards = []
        logger.info(f"🔍 Starting validation of {len(cards)} raw cards")
        
        for i, card in enumerate(cards):
            try:
                # Accept any card that has at least one meaningful field
                if not card or len(card) == 0:
                    continue
                
                # Create a clean copy and remove invalid/empty fields
                clean_card = {}
                
                # Copy only valid, non-empty fields
                for key, value in card.items():
                    if self._is_valid_field_value(key, value):
                        clean_card[key] = self._clean_field_value(key, value)
                
                # Must have at least some useful data to display
                if len(clean_card) == 0:
                    continue
                
                # Ensure we have an ID for cart functionality (only if not present)
                if '_id' not in clean_card:
                    # Try to use existing identifiers, no fallback generation
                    if 'card_hash' in clean_card:
                        clean_card['_id'] = clean_card['card_hash']
                    elif 'bin' in clean_card:
                        clean_card['_id'] = clean_card['bin']
                    # If no identifier available, skip this card
                    else:
                        continue
                
                validated_cards.append(clean_card)
                
                # Log first few successful validations (debug only)
                if len(validated_cards) <= 3 and logger.isEnabledFor(logging.DEBUG):  # DEBUG level
                    available_fields = list(clean_card.keys())
                    logger.debug(f"✅ Card {len(validated_cards)} validated with fields: {available_fields[:6]}{'...' if len(available_fields) > 6 else ''}")
                
            except Exception as e:
                logger.warning(f"Error validating card {i+1}: {e}")
                continue
        
        logger.info(f"🎯 Validated {len(validated_cards)} cards from {len(cards)} raw cards (manual validation)")
        return validated_cards
    
    def _is_valid_field_value(self, key: str, value: Any) -> bool:
        """Check if a field value is valid and should be displayed."""
        if value is None:
            return False
        
        # Convert to string for validation
        str_value = str(value).strip()
        
        # Empty or whitespace-only values
        if not str_value:
            return False
        
        # Common invalid values
        invalid_values = {
            '', 'null', 'none', 'n/a', 'na', 'unknown', 'undefined', 
            'null', 'nil', '0', 'null', '[redacted]', 'redacted'
        }
        
        if str_value.lower() in invalid_values:
            return False
        
        # Field-specific validation
        if key in ['price', 'current_price', 'original_price']:
            try:
                price_val = float(str_value)
                return price_val >= 0  # Allow 0 price (free)
            except (ValueError, TypeError):
                return False
        
        return True
    
    def _clean_field_value(self, key: str, value: Any) -> Any:
        """Clean and normalize field values."""
        if isinstance(value, str):
            cleaned = value.strip()
            
            # Clean up common formatting issues
            if key in ['country', 'type', 'level', 'brand', 'scheme', 'bank']:
                return cleaned.upper()
            elif key in ['name', 'cardholder', 'cardholder_name', 'f_name', 'holder']:
                # Only clean names that don't look like countries
                country_names = {
                    'spain', 'germany', 'france', 'italy', 'usa', 'united states', 
                    'united kingdom', 'israel', 'turkey', 'poland', 'brazil', 
                    'colombia', 'chile', 'ireland', 'philippines', 'netherlands', 
                    'india', 'norway', 'uruguay', 'thailand', 'austria', 'cayman islands',
                    'azerbaijan', 'russia', 'ukraine', 'kazakhstan', 'belarus', 'georgia'
                }
                if cleaned.lower() in country_names:
                    return None  # Don't use country names as cardholder names
                return cleaned
            else:
                return cleaned
        
        elif isinstance(value, (int, float)):
            if key in ['price', 'current_price', 'original_price']:
                return float(value)
            return value
        
        elif isinstance(value, bool):
            return value
        
        return value

    def _invalidate_search_cache(self, user_id: int) -> None:
        """Invalidate the search cache for a user (called when filters change)"""
        if user_id in self.user_search_cache:
            logger.debug(f"Invalidating search cache for user {user_id}")
            del self.user_search_cache[user_id]
        
        # Also clear cache info if it exists
        if hasattr(self, 'user_search_cache_info') and user_id in self.user_search_cache_info:
            del self.user_search_cache_info[user_id]

    def _cleanup_old_caches(self) -> None:
        """Clean up old cache entries to prevent memory leaks"""
        try:
            import time
            current_time = time.time()
            
            # Clean up search cache (older than 1 hour)
            expired_users = []
            for user_id, cache in self.user_search_cache.items():
                if current_time - cache.get('last_fetch_time', 0) > 3600:  # 1 hour
                    expired_users.append(user_id)
            
            # Clean up current cards cache (older than 2 hours)  
            expired_cards_users = []
            for user_id, cards in self.user_current_cards.items():
                # Check if we have a timestamp for this cache
                if hasattr(self, 'user_cards_cache_time'):
                    timestamp = self.user_cards_cache_time.get(user_id, 0)
                    if current_time - timestamp > 7200:  # 2 hours
                        expired_cards_users.append(user_id)
            
            # Clean up card service caches (older than 1 hour)
            expired_service_users = []
            for user_id, (api_id, service) in self.user_card_services.items():
                # Remove oldest entries if we have too many
                if len(expired_service_users) < len(self.user_card_services) // 2:
                    expired_service_users.append(user_id)
            
            # Remove expired entries
            for user_id in expired_users:
                self.user_search_cache.pop(user_id, None)
                self.user_filters.pop(user_id, None)
                self.user_applied_filters.pop(user_id, None)
                self.user_option_tokens.pop(user_id, None)
                self.user_navigation_source.pop(user_id, None)
                self.user_filter_interface.pop(user_id, None)
                logger.debug(f"Cleaned up expired cache for user {user_id}")
            
            for user_id in expired_cards_users:
                self.user_current_cards.pop(user_id, None)
                if hasattr(self, 'user_cards_cache_time'):
                    self.user_cards_cache_time.pop(user_id, None)
            
            for user_id in expired_service_users:
                self.user_card_services.pop(user_id, None)
            
            if expired_users or expired_cards_users or expired_service_users:
                logger.info(f"Cache cleanup: removed {len(expired_users)} search caches, {len(expired_cards_users)} card caches, and {len(expired_service_users)} service caches")
            
        except Exception as e:
            logger.warning(f"Error during cache cleanup: {e}")
    
    # Fallback card generation removed - API data is always used when available
    
    def _get_cache_stats(self, user_id: int) -> dict:
        """Get cache statistics for debugging"""
        import time
        
        cache = self.user_search_cache.get(user_id)
        if not cache:
            return {"status": "no_cache"}
        
        cards = cache.get('cards', [])
        valid_cards = len([c for c in cards if c is not None])
        
        return {
            "status": "cached",
            "filters_hash": cache.get('filters_hash', 'unknown')[:8],
            "total_cards": valid_cards,
            "total_count": cache.get('total_count', 0),
            "fetched_api_pages": list(cache.get('fetched_api_pages', [])),
            "has_more_data": cache.get('has_more_data', False),
            "api_version": cache.get('api_version', 'unknown'),
            "age_minutes": round((time.time() - cache.get('last_fetch_time', 0)) / 60, 1)
        }

    async def _return_to_filter_interface(self, callback: CallbackQuery, from_back_button: bool = False) -> None:
        """Return to the appropriate interface based on user's navigation source and filter interface type"""
        user_id = self._get_user_id(callback)
        navigation_source = self.user_navigation_source.get(user_id, "browse")
        filter_interface = self.user_filter_interface.get(user_id, "category")
        
        logger.debug(f"🔍 _return_to_filter_interface: user {user_id}, navigation_source: {navigation_source}, filter_interface: {filter_interface}, from_back_button: {from_back_button}")
        
        if navigation_source == "search":
            # Check filter interface type to determine correct destination
            if filter_interface == "direct":
                # Return to search interface (Search & Filter Cards)
                logger.debug(f"🔍 Returning to search interface for user {user_id}")
                await self.cb_search(callback, force_update=from_back_button)
            elif filter_interface == "category":
                # Return to filters interface (API v3 Search Filters)
                logger.debug(f"🔍 Returning to filters interface for user {user_id}")
                await self.cb_filters(callback)
            else:
                # Fallback to search interface
                logger.debug(f"🔍 Returning to search interface (fallback) for user {user_id}")
                await self.cb_search(callback, force_update=from_back_button)
        elif navigation_source == "browse" and filter_interface == "direct":
            # User is in Search & Filter Cards (direct interface) - return to Search & Filter Cards
            logger.debug(f"🔍 Returning to Search & Filter Cards for user {user_id}")
            await self.cb_search(callback, force_update=from_back_button)
        else:
            # Return to browse menu (not filters interface)
            logger.debug(f"🔍 Returning to browse menu for user {user_id}")
            await self.cb_browse_menu(callback)

    def _get_temp_filters(self, user_id: int) -> dict:
        return self.user_filters.setdefault(user_id, {})
    
    async def _get_navigation_state(self, user_id: int) -> dict:
        """Get current navigation state for debugging"""
        return {
            "navigation_source": self.user_navigation_source.get(user_id, "browse"),
            "filter_interface": self.user_filter_interface.get(user_id, "category"),
            "has_temp_filters": bool(self._get_temp_filters(user_id)),
            "has_applied_filters": bool(await self._get_applied_filters(user_id))
        }

    async def _get_applied_filters(self, user_id: int) -> dict:
        """Get applied filters for a user, normalized for the current API version"""
        raw_filters = self.user_applied_filters.get(user_id, {})
        
        # No need to normalize if no filters
        if not raw_filters:
            return raw_filters
            
        # Check if user is using API v3 and normalize filter keys
        # Always get fresh API version to ensure correct normalization
        api_version = await self._get_user_api_version(user_id)
        
        logger.debug(f"Getting applied filters for user {user_id}, API version: {api_version}")
        logger.debug(f"Raw filters: {raw_filters}")
            
        # Normalize filters for API v3
        if api_version == "v3":
            normalized_filters = {}
            for key, value in raw_filters.items():
                # Map bin -> bins for API v3
                if key == "bin":
                    normalized_filters["bins"] = value
                    logger.debug(f"Normalized: {key} -> bins = {value}")
                # Map other filters that need conversion for API v3
                elif key == "bank":
                    normalized_filters["selected_bank"] = value
                    logger.debug(f"Normalized: {key} -> selected_bank = {value}")
                elif key == "state":
                    normalized_filters["region"] = value
                    logger.debug(f"Normalized: {key} -> region = {value}")
                elif key == "zip":
                    normalized_filters["postal_code"] = value
                    logger.debug(f"Normalized: {key} -> postal_code = {value}")
                elif key == "brand":
                    normalized_filters["scheme"] = value
                    logger.debug(f"Normalized: {key} -> scheme = {value}")
                elif key == "address":
                    normalized_filters["with_billing"] = value
                    logger.debug(f"Normalized: {key} -> with_billing = {value}")
                elif key == "phone":
                    normalized_filters["with_phone"] = value
                    logger.debug(f"Normalized: {key} -> with_phone = {value}")
                elif key == "dob":
                    normalized_filters["with_dob"] = value
                    logger.debug(f"Normalized: {key} -> with_dob = {value}")
                else:
                    # Keep the key as-is for direct mappings
                    normalized_filters[key] = value
            
            logger.debug(f"Normalized filters for API v3: {normalized_filters}")
            return normalized_filters
        else:
            # For API v1/v2, return filters as-is
            logger.debug(f"Using raw filters for API v{api_version}: {raw_filters}")
            return raw_filters

    @staticmethod
    def _chunk_list(items: list[Any], chunk_size: int) -> Iterable[list[Any]]:
        for idx in range(0, len(items), chunk_size):
            yield items[idx : idx + chunk_size]

    @staticmethod
    def _normalize_option_label(label: str) -> str:
        text = (label or "").strip()
        if not text:
            return "—"
        return text if len(text) <= 32 else text[:29] + "…"

    @staticmethod
    def _sort_dynamic_options(options: list[dict]) -> list[dict]:
        def cleaned_text(text: str) -> str:
            stripped = text.strip()
            stripped = re.sub(r"\s*\(\d+\)\s*$", "", stripped)
            stripped = re.sub(r"^[^A-Za-z0-9]+", "", stripped)
            return stripped

        def sort_key(option: dict) -> tuple[int, str, str]:
            label = str(option.get("label") or "")
            visible = cleaned_text(label)
            if not visible:
                visible = cleaned_text(str(option.get("value") or ""))
            return (0 if visible else 1, visible.casefold(), label.casefold())

        return sorted(options, key=sort_key)

    def _build_dynamic_option_rows(
        self,
        filter_key: str,
        options: list[dict],
        user_id: Optional[int],
        page: int,
    ) -> list[list[InlineKeyboardButton]]:
        rows: list[list[InlineKeyboardButton]] = []
        seen: set[str] = set()

        cleaned: list[tuple[str, str]] = []
        for option in options:
            value_raw = option.get("value")
            value = "" if value_raw is None else str(value_raw)
            if value and value in seen:
                continue
            seen.add(value)
            label = option.get("label") or value
            if filter_key == "country":
                label = self._ensure_country_flag_label(label, value)
            cleaned.append((self._normalize_option_label(label), value))

        tokenized = self._tokenize_dynamic_options(filter_key, cleaned, user_id, page)

        row_width = self._get_dynamic_row_width(filter_key)
        for chunk in self._chunk_list(tokenized, row_width):
            row: list[InlineKeyboardButton] = []
            for label, token in chunk:
                row.append(
                    InlineKeyboardButton(
                        text=label,
                        callback_data=f"filter:set:{filter_key}:{token}",
                    )
                )
            rows.append(row)

        return rows

    def _tokenize_dynamic_options(
        self,
        filter_key: str,
        options: list[tuple[str, str]],
        user_id: Optional[int],
        page: int,
    ) -> list[tuple[str, str]]:
        actual_user_id = user_id or 0
        user_tokens = self.user_option_tokens.setdefault(actual_user_id, {})
        token_map: dict[str, str] = {}
        user_tokens[filter_key] = {
            "page": page,
            "tokens": token_map,
        }

        tokenized: list[tuple[str, str]] = []
        for idx, (label, value) in enumerate(options):
            token = f"opt{page}_{idx}"
            token_map[token] = value
            tokenized.append((label, token))

        return tokenized

    def _ensure_country_flag_label(self, label: str, value: str) -> str:
        """Prefix country labels with flag emoji using dynamic detection."""
        text = (label or "").strip()
        if not text:
            return text

        if self._starts_with_flag_emoji(text):
            return text

        # Use dynamic flag system for better coverage
        from utils.dynamic_country_flags import get_dynamic_country_with_flag
        return get_dynamic_country_with_flag(value or text)

    @staticmethod
    def _starts_with_flag_emoji(text: str) -> bool:
        if len(text) < 2:
            return False
        first, second = ord(text[0]), ord(text[1])
        return 0x1F1E6 <= first <= 0x1F1FF and 0x1F1E6 <= second <= 0x1F1FF

    @staticmethod
    def _flag_from_code(code: str) -> Optional[str]:
        if not code or len(code) != 2 or not code.isalpha():
            return None
        code = code.upper()
        base = ord("A")
        try:
            return chr(0x1F1E6 + ord(code[0]) - base) + chr(
                0x1F1E6 + ord(code[1]) - base
            )
        except ValueError:
            return None

    def _flag_from_value(self, value: str) -> Optional[str]:
        code = (value or "").strip()
        return self._flag_from_code(code)

    def _get_comprehensive_country_flag(self, country_input: str) -> str:
        """Get country flag using dynamic detection from filter data."""
        from utils.dynamic_country_flags import get_dynamic_country_flag
        return get_dynamic_country_flag(country_input)

    def _flag_from_label(self, label: str) -> Optional[str]:
        """Use comprehensive flag system instead of limited one."""
        return self._get_comprehensive_country_flag(label)

    def _get_dynamic_row_width(self, filter_key: str) -> int:
        """Determine how many buttons to display per row for dynamic options."""
        if filter_key in {"country", "state", "city", "zip"}:
            return self.LOCATION_FILTER_ROW_WIDTH
        return self.FILTER_OPTION_ROW_WIDTH

    def _resolve_dynamic_option_value(
        self, user_id: Optional[int], filter_key: str, token: str
    ) -> Optional[str]:
        actual_user_id = user_id or 0
        user_tokens = self.user_option_tokens.get(actual_user_id)
        if not user_tokens:
            return None
        filter_entry = user_tokens.get(filter_key)
        if not filter_entry:
            return None
        token_map = filter_entry.get("tokens", {})
        return token_map.get(token)

    def _filters_summary_text(self, filters: dict, api_version: str = "v1") -> str:
        """Generate a human-readable summary of active filters."""

        if not filters:
            return "No active filters"

        parts: list[str] = []

        # Handle pricing for both API versions
        price_from = self._parse_price_value(filters.get("priceFrom"))
        price_to = self._parse_price_value(filters.get("priceTo"))

        if price_from is not None and price_to is not None:
            parts.append(f"• Price Range: ${price_from:.2f} – ${price_to:.2f}")
        elif price_from is not None:
            parts.append(f"• Price From: ${price_from:.2f}")
        elif price_to is not None:
            parts.append(f"• Price To: ${price_to:.2f}")

        handled_keys = {"priceFrom", "priceTo"}

        # Use appropriate category info and labels based on API version
        if api_version == "v3":
            category_info = self.CATEGORY_INFO_V3
            filter_labels = self.FILTER_LABELS_V3
            boolean_keys = self.BOOLEAN_FILTER_KEYS_V3
        else:
            category_info = self.CATEGORY_INFO
            filter_labels = self.FILTER_LABELS
            boolean_keys = self.BOOLEAN_FILTER_KEYS

        for category in category_info.values():
            for key in category["keys"]:
                if key in handled_keys:
                    continue
                if key not in filters:
                    continue

                value = filters[key]
                if key in boolean_keys:
                    if bool(value):
                        label = filter_labels.get(key, key.title())
                        parts.append(f"• {label}: On")
                    continue

                if value is None or value == "":
                    continue

                label = filter_labels.get(key, key.title())
                parts.append(f"• {label}: {value}")

        # Include any keys not covered above (future-proofing)
        for key, value in filters.items():
            if key in handled_keys or key in filter_labels:
                continue
            if not value:
                continue
            parts.append(f"• {key.title()}: {value}")

        return "\n".join(parts)

    def _parse_price_value(self, value: Any) -> Optional[float]:
        try:
            return float(value)
        except (TypeError, ValueError):
            return None

    def _category_status_flags(self, filters: dict, api_version: str = "v1") -> Dict[str, bool]:
        # Use appropriate category info based on API version
        category_info = self.CATEGORY_INFO_V3 if api_version == "v3" else self.CATEGORY_INFO

        return {
            category: any(key in filters for key in meta["keys"])
            for category, meta in category_info.items()
        }

    def _format_value_label(self, key: str, label: str, filters: dict) -> str:
        value = filters.get(key)
        if value in (None, ""):
            return label
        if key in {"priceFrom", "priceTo"}:
            parsed = self._parse_price_value(value)
            if parsed is not None:
                return f"{label}: ${parsed:.2f}"
        return f"{label}: {value}"

    def _format_toggle_button_text(self, key: str, label: str, filters: dict) -> str:
        return f"{'✅' if filters.get(key) else '⚪'} {label}"

    def _build_clear_buttons(
        self, filters: dict, keys: Iterable[str], api_version: str = "v1"
    ) -> list[list[InlineKeyboardButton]]:
        rows: list[list[InlineKeyboardButton]] = []
        current_row: list[InlineKeyboardButton] = []
        
        # Use appropriate label dictionary based on API version
        filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS

        for key in keys:
            if key not in filters:
                continue
            label = filter_labels.get(key, key.title())
            current_row.append(
                InlineKeyboardButton(
                    text=f"❌ {label}", callback_data=f"filter:unset:{key}"
                )
            )
            if len(current_row) == 2:
                rows.append(current_row)
                current_row = []

        if current_row:
            rows.append(current_row)

        return rows

    def _category_footer_rows(self) -> list[list[InlineKeyboardButton]]:
        return [
            [
                InlineKeyboardButton(text="⬅️ Filters", callback_data="filter:back"),
                InlineKeyboardButton(text="✅ Apply", callback_data="filter:apply"),
            ],
            [InlineKeyboardButton(text="🧹 Clear All", callback_data="filter:clear")],
        ]

    def _format_price_button_text(self, filters: dict) -> str:
        price_from = self._parse_price_value(filters.get("priceFrom"))
        price_to = self._parse_price_value(filters.get("priceTo"))

        if price_from is not None and price_to is not None:
            return f"💲 Range: ${price_from:.2f}–${price_to:.2f}"
        if price_from is not None:
            return f"💲 Min: ${price_from:.2f}"
        if price_to is not None:
            return f"💲 Max: ${price_to:.2f}"
        return "💲 Price Presets"

    def _category_label(self, category: str) -> str:
        info = self.CATEGORY_INFO.get(category)
        return info.get("label", category.title()) if info else category.title()

    def _category_return_keyboard(self, category: str, user_id: int = None) -> InlineKeyboardMarkup:
        label = self._category_label(category)
        
        # Check navigation source and filter interface to determine where to go back
        if user_id is not None:
            navigation_source = self.user_navigation_source.get(user_id, "browse")
            filter_interface = self.user_filter_interface.get(user_id, "category")
            logger.debug(f"🔍 _category_return_keyboard: user {user_id}, navigation_source: {navigation_source}, filter_interface: {filter_interface}")
            
            if navigation_source == "search":
                # If user came from search, go back to search interface
                back_callback = "filter:back"  # Use filter:back to trigger smart navigation
                back_text = "⬅️ Back to Search & Filter"
            elif navigation_source == "browse" and filter_interface == "direct":
                # If user is in Search & Filter Cards (direct interface), use filter:back
                back_callback = "filter:back"
                back_text = "⬅️ Back to Search & Filter"
            else:
                # Default behavior - go back to category menu
                back_callback = f"filter:category:{category}"
                back_text = f"⬅️ Back to {label}"
        else:
            # Fallback to original behavior if no user_id provided
            back_callback = f"filter:category:{category}"
            back_text = f"⬅️ Back to {label}"
        
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=back_text,
                        callback_data=back_callback,
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="✅ Apply Filters", callback_data="filter:apply"
                    )
                ],
            ]
        )

    def _manual_input_success_hint(self, key: str, filters: dict, api_version: str = "v1") -> str:
        category = self._category_from_key(key, api_version) or "card"
        label = self._category_label(category)

        base_hint = f'Tap "Back to {label}" to adjust these filters or "Apply Filters" to search.'

        if key == "priceFrom":
            if "priceTo" not in filters:
                return f'Minimum price saved. Use "Back to {label}" to set a maximum or tap "Apply Filters" when ready.'
            return f"Price range updated. {base_hint}"

        if key == "priceTo":
            if "priceFrom" not in filters:
                return f'Maximum price saved. Use "Back to {label}" to set a minimum or tap "Apply Filters" when ready.'
            return f"Price range updated. {base_hint}"

        if key == "zip":
            return f'ZIP saved. Tap "Back to {label}" to tweak location filters or "Apply Filters" to search.'

        if key == "bin":
            return f'BIN saved. Tap "Back to {label}" to adjust card filters or "Apply Filters" to search.'

        if key in {"state", "city", "country"}:
            return f'Location updated. Tap "Back to {label}" to refine or "Apply Filters" to search.'

        if key in {"bank", "brand", "type"}:
            return f'Card details saved. Tap "Back to {label}" to refine or "Apply Filters" to search.'

        return base_hint

    def _build_category_menu(
        self, category: str, filters: dict, api_version: str = "v1"
    ) -> tuple[str, InlineKeyboardMarkup]:
        # Use appropriate category info based on API version
        category_info = self.CATEGORY_INFO_V3 if api_version == "v3" else self.CATEGORY_INFO

        meta = category_info.get(category)
        if not meta:
            raise ValueError(f"Unknown filter category: {category}")

        buttons: list[list[InlineKeyboardButton]] = []

        # API v1 specific category implementations
        if api_version != "v3" and category == "location":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label(
                                "country", "🌍 Country", filters
                            ),
                            callback_data="filter:select:country",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label("state", "🏙 State", filters),
                            callback_data="filter:select:state",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label("city", "🏘 City", filters),
                            callback_data="filter:select:city",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label("zip", "🏷 ZIP", filters),
                            callback_data="filter:select:zip",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "zipCheck", "ZIP Check", filters
                            ),
                            callback_data="filter:toggle:zipCheck",
                        )
                    ],
                ]
            )
            buttons.extend(
                self._build_clear_buttons(filters, ["country", "state", "city", "zip"], api_version)
            )

        elif api_version != "v3" and category == "card":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text="✍️ Enter BIN",
                            callback_data="filter:input:bin",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label("brand", "🏷 Brand", filters),
                            callback_data="filter:select:brand",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label(
                                "type", "💳 Card Type", filters
                            ),
                            callback_data="filter:select:type",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_value_label("level", "🎚 Level", filters),
                            callback_data="filter:select:level",
                        ),
                        InlineKeyboardButton(
                            text=self._format_value_label("bank", "🏦 Bank", filters),
                            callback_data="filter:select:bank",
                        ),
                    ],
                ]
            )
            buttons.extend(
                self._build_clear_buttons(
                    filters, ["brand", "type", "level", "bank", "bin"], api_version
                )
            )

        elif api_version != "v3" and category == "pricing":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_price_button_text(filters),
                            callback_data="filter:select:price",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="⬆️ Set Min", callback_data="filter:input:priceFrom"
                        ),
                        InlineKeyboardButton(
                            text="⬇️ Set Max", callback_data="filter:input:priceTo"
                        ),
                    ],
                ]
            )
            buttons.extend(self._build_clear_buttons(filters, ["priceFrom", "priceTo"], api_version))

        elif api_version != "v3" and category == "contact":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "address", "Address", filters
                            ),
                            callback_data="filter:toggle:address",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "phone", "Phone", filters
                            ),
                            callback_data="filter:toggle:phone",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "email", "Email", filters
                            ),
                            callback_data="filter:toggle:email",
                        )
                    ],
                ]
            )

        elif api_version != "v3" and category == "identity":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("dob", "DOB", filters),
                            callback_data="filter:toggle:dob",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("ssn", "SSN", filters),
                            callback_data="filter:toggle:ssn",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("mmn", "MMN", filters),
                            callback_data="filter:toggle:mmn",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "dl", "Driving Licence", filters
                            ),
                            callback_data="filter:toggle:dl",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text("ip", "IP", filters),
                            callback_data="filter:toggle:ip",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "ua", "User Agent", filters
                            ),
                            callback_data="filter:toggle:ua",
                        ),
                    ],
                ]
            )

        elif api_version != "v3" and category == "extras":
            buttons.extend(
                [
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "withoutcvv", "Without CVV", filters
                            ),
                            callback_data="filter:toggle:withoutcvv",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "refundable", "Refundable", filters
                            ),
                            callback_data="filter:toggle:refundable",
                        ),
                    ],
                    [
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "expirethismonth", "Expire This Month", filters
                            ),
                            callback_data="filter:toggle:expirethismonth",
                        ),
                        InlineKeyboardButton(
                            text=self._format_toggle_button_text(
                                "discount", "Discount", filters
                            ),
                            callback_data="filter:toggle:discount",
                        ),
                    ],
                ]
            )

        # API v3 specific category implementations
        elif api_version == "v3":
            if category == "location":
                buttons.extend(self._build_v3_location_buttons(filters))
            elif category == "card":
                buttons.extend(self._build_v3_card_buttons(filters))
            elif category == "contact":
                buttons.extend(self._build_v3_contact_buttons(filters))
            elif category == "quality":
                buttons.extend(self._build_v3_quality_buttons(filters))
            elif category == "advanced":
                buttons.extend(self._build_v3_advanced_buttons(filters))
            else:
                raise ValueError(f"Unhandled API v3 filter category: {category}")
        else:
            raise ValueError(f"Unhandled filter category: {category}")

        buttons.extend(self._category_footer_rows())

        category_filters = {k: filters[k] for k in meta["keys"] if k in filters}
        summary = self._filters_summary_text(category_filters, api_version)

        text = f"{meta['label']} <b>Filters</b>\n\n{meta['description']}"
        if summary and summary != "No active filters":
            text += f"\n\n<b>Active:</b>\n{summary}"

        return text, InlineKeyboardMarkup(inline_keyboard=buttons)

    def _build_v3_location_buttons(self, filters: dict) -> list[list[InlineKeyboardButton]]:
        """Build API v3 location filter buttons"""
        buttons = [
            [
                InlineKeyboardButton(
                    text=self._format_value_label("continent", "🌍 Continent", filters),
                    callback_data="filter:select:continent",
                ),
                InlineKeyboardButton(
                    text=self._format_value_label("country", "🏳️ Country", filters),
                    callback_data="filter:select:country",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=self._format_value_label("region", "🗺️ Region", filters),
                    callback_data="filter:select:region",
                ),
                InlineKeyboardButton(
                    text=self._format_value_label("city", "🏘️ City", filters),
                    callback_data="filter:select:city",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=self._format_value_label("postal_code", "📮 Postal Code", filters),
                    callback_data="filter:input:postal_code",
                ),
            ],
        ]
        # Add clear buttons for active filters
        buttons.extend(
            self._build_clear_buttons(filters, ["continent", "country", "region", "city", "postal_code"], "v3")
        )
        return buttons

    def _build_v3_card_buttons(self, filters: dict) -> list[list[InlineKeyboardButton]]:
        """Build API v3 card filter buttons"""
        buttons = [
            [
                InlineKeyboardButton(
                    text="✍️ Enter BIN",
                    callback_data="filter:input:bins",
                )
            ],
            [
                InlineKeyboardButton(
                    text=self._format_value_label("scheme", "💳 Scheme", filters),
                    callback_data="filter:select:scheme",
                ),
                InlineKeyboardButton(
                    text=self._format_value_label("type", "🎫 Type", filters),
                    callback_data="filter:select:type",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=self._format_value_label("level", "🎚️ Level", filters),
                    callback_data="filter:select:level",
                ),
                InlineKeyboardButton(
                    text=self._format_value_label("selected_bank", "🏦 Bank", filters),
                    callback_data="filter:select:selected_bank",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔍 Search Bank",
                    callback_data="filter:input:searched_bank",
                ),
            ],
        ]
        # Add clear buttons for active filters
        buttons.extend(
            self._build_clear_buttons(filters, ["bins", "scheme", "type", "level", "selected_bank", "searched_bank"], "v3")
        )
        return buttons

    def _build_v3_contact_buttons(self, filters: dict) -> list[list[InlineKeyboardButton]]:
        """Build API v3 contact filter buttons"""
        buttons = [
            [
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("with_billing", "Billing Address", filters),
                    callback_data="filter:toggle:with_billing",
                ),
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("with_phone", "Phone Number", filters),
                    callback_data="filter:toggle:with_phone",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("with_dob", "Date of Birth", filters),
                    callback_data="filter:toggle:with_dob",
                ),
            ],
        ]
        # Add clear buttons for active filters
        buttons.extend(
            self._build_clear_buttons(filters, ["with_billing", "with_phone", "with_dob"], "v3")
        )
        return buttons

    def _build_v3_quality_buttons(self, filters: dict) -> list[list[InlineKeyboardButton]]:
        """Build API v3 quality filter buttons"""
        buttons = [
            [
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("show_medium_valid", "Show Medium Valid", filters),
                    callback_data="filter:toggle:show_medium_valid",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("expiring_soon", "Expiring Soon", filters),
                    callback_data="filter:toggle:expiring_soon",
                ),
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("expiring_next", "Expiring Next Month", filters),
                    callback_data="filter:toggle:expiring_next",
                ),
            ],
        ]
        # Add clear buttons for active filters
        buttons.extend(
            self._build_clear_buttons(filters, ["show_medium_valid", "expiring_soon", "expiring_next"], "v3")
        )
        return buttons

    def _build_v3_advanced_buttons(self, filters: dict) -> list[list[InlineKeyboardButton]]:
        """Build API v3 advanced filter buttons"""
        buttons = [
            [
                InlineKeyboardButton(
                    text=self._format_toggle_button_text("cc_per_bin", "CC Per BIN", filters),
                    callback_data="filter:toggle:cc_per_bin",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=self._format_value_label("ethnicity", "🌐 Ethnicity", filters),
                    callback_data="filter:input:ethnicity",
                ),
            ],
        ]
        # Add clear buttons for active filters
        buttons.extend(
            self._build_clear_buttons(filters, ["cc_per_bin", "ethnicity"], "v3")
        )
        return buttons

    async def _show_category_menu(self, callback: CallbackQuery, category: str) -> None:
        user_id = self._get_user_id(callback)
        filters = self._get_temp_filters(user_id)
        api_version = await self._get_user_api_version(user_id)
        text, keyboard = self._build_category_menu(category, filters, api_version)
        await callback.message.edit_text(
            text + DEMO_WATERMARK,
            reply_markup=keyboard,
        )

    def _category_from_key(self, key: str, api_version: str = "v1") -> Optional[str]:
        """Get category for a filter key based on API version"""
        if api_version == "v3":
            return self.FILTER_KEY_TO_CATEGORY_V3.get(key)
        else:
            return self.FILTER_KEY_TO_CATEGORY.get(key)

    async def _prompt_for_manual_input(
        self, callback: CallbackQuery, state: FSMContext, key: str
    ) -> None:
        config = self.MANUAL_INPUT_CONFIG.get(key)
        if not config:
            await callback.answer("Unsupported filter input", show_alert=True)
            return

        user_id = self._get_user_id(callback)
        api_version = await self._get_user_api_version(user_id)
        category = self._category_from_key(key, api_version) or "card"
        # Use appropriate label dictionary based on API version
        filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS
        title = config.get("title", filter_labels.get(key, key.title()))
        prompt = config.get("prompt", "Send a value")
        
        # Use smart navigation for back button
        navigation_source = self.user_navigation_source.get(user_id, "browse")
        filter_interface = self.user_filter_interface.get(user_id, "category")
        logger.debug(f"🔍 _prompt_for_manual_input: user {user_id} navigation_source={navigation_source}, filter_interface={filter_interface}")
        
        if navigation_source == "search":
            # If user came from search, use filter:back for smart navigation
            back_callback = "filter:back"
            logger.debug(f"🔍 _prompt_for_manual_input: Using back_callback='{back_callback}' for user {user_id} (from search)")
        elif navigation_source == "browse" and filter_interface == "direct":
            # If user is in Search & Filter Cards (direct interface), use filter:back
            back_callback = "filter:back"
            logger.debug(f"🔍 _prompt_for_manual_input: Using back_callback='{back_callback}' for user {user_id} (from browse/direct)")
        else:
            # Default behavior - go back to category menu
            back_callback = f"filter:category:{category}"
            logger.debug(f"🔍 _prompt_for_manual_input: Using back_callback='{back_callback}' for user {user_id} (default)")

        await callback.message.edit_text(
            f"✍️ <b>{title}</b>\n\n{prompt}" + DEMO_WATERMARK,
            reply_markup=back_keyboard(back_callback),
        )

        await state.set_state(self.CatalogFilterStates.WAITING_VALUE)
        await state.update_data(
            filter_key=key,
            category=category,
            input_mode=config.get("mode", "text"),
        )
        await callback.answer()

    def _set_manual_filter_value(
        self, user_id: int, key: str, raw_value: str, input_mode: str, api_version: str = "v1"
    ) -> tuple[bool, str]:
        raw_value = (raw_value or "").strip()
        if not raw_value:
            return False, "Value cannot be empty."

        temp = self._get_temp_filters(user_id)
        # Use appropriate label dictionary based on API version
        filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS
        label = filter_labels.get(key, key.title())

        if input_mode == "bin":
            digits = "".join(ch for ch in raw_value if ch.isdigit())
            if len(digits) < 4 or len(digits) > 8:
                return False, "BIN must be 4–8 digits."
            temp["bin"] = digits
            return True, f"BIN set to <b>{digits}</b>."

        if input_mode == "zip":
            digits = "".join(ch for ch in raw_value if ch.isdigit())
            if len(digits) < 3 or len(digits) > 10:
                return False, "ZIP must be 3–10 digits."
            temp[key] = digits  # Use key instead of hardcoded "zip" for postal_code support
            return True, f"{label} set to <b>{digits}</b>."

        if input_mode == "price":
            cleaned = raw_value.replace("$", "").replace(",", "")
            price = self._parse_price_value(cleaned)
            if price is None:
                return False, "Invalid price format."
            if price < 0:
                return False, "Price cannot be negative."
            if price > self.MAX_PRICE:
                return False, f"Price cannot exceed ${self.MAX_PRICE:,.0f}."

            other_key = "priceTo" if key == "priceFrom" else "priceFrom"
            other_value = self._parse_price_value(temp.get(other_key))

            if key == "priceFrom" and other_value is not None and price > other_value:
                return False, "Minimum price cannot exceed maximum price."
            if key == "priceTo" and other_value is not None and price < other_value:
                return False, "Maximum price cannot be below minimum price."

            if (
                other_value is not None
                and abs(other_value - price) > self.MAX_PRICE_SPREAD
            ):
                return False, "Price range too wide (max $5,000 difference)."

            temp[key] = f"{price:.2f}"
            return True, f"{label} set to <b>${price:.2f}</b>."

        try:
            sanitized = sanitize_text_input(raw_value, max_length=50)
        except ValidationError as exc:
            return False, str(exc)

        if key == "state" or key == "region":
            sanitized = sanitized.upper() if len(sanitized) <= 3 else sanitized.title()
            if not re.fullmatch(r"[A-Za-z0-9\-\s]{2,50}", sanitized):
                return (
                    False,
                    "State/Region should be 2–50 characters (letters, numbers, spaces, -).",
                )
        elif key == "city":
            sanitized = sanitized.title()
            if not re.fullmatch(r"[A-Za-z0-9\-\s]{2,50}", sanitized):
                return (
                    False,
                    "City should be 2–50 characters (letters, numbers, spaces, -).",
                )
        elif key == "postal_code":
            # Allow both numeric and alphanumeric postal codes
            if not re.fullmatch(r"[A-Z0-9\-\s]{2,15}", sanitized.upper()):
                return (
                    False,
                    "Postal code should be 2–15 characters (letters, numbers, spaces, -).",
                )
            sanitized = sanitized.upper()
        elif key in ("bank", "selected_bank", "searched_bank"):
            sanitized = sanitized.upper()
            if not re.fullmatch(r"[A-Z0-9&\.\-\s]{2,50}", sanitized):
                return (
                    False,
                    "Bank name should be 2–50 characters (letters, numbers, spaces, & . -).",
                )
        elif key == "base" or key == "ethnicity":
            sanitized = sanitized.title()
            if not re.fullmatch(r"[A-Za-z0-9\-\s]{2,50}", sanitized):
                return (
                    False,
                    f"{label} should be 2–50 characters (letters, numbers, spaces, -).",
                )
        elif key == "bins":
            # BINs separated by commas
            bins_list = [b.strip() for b in sanitized.split(",") if b.strip()]
            if not bins_list:
                return False, "At least one BIN must be provided."
            
            cleaned_bins = []
            for bin_val in bins_list:
                digits = "".join(ch for ch in bin_val if ch.isdigit())
                if len(digits) < 4 or len(digits) > 8:
                    return False, f"Invalid BIN '{bin_val}'. Each BIN must be 4–8 digits."
                cleaned_bins.append(digits)
            
            # Remove duplicates while preserving order
            seen = set()
            unique_bins = []
            for bin_val in cleaned_bins:
                if bin_val not in seen:
                    seen.add(bin_val)
                    unique_bins.append(bin_val)
            
            sanitized = ",".join(unique_bins)
            temp[key] = sanitized
            return True, f"{label} set to <b>{sanitized}</b>."

        temp[key] = sanitized
        return True, f"{label} set to <b>{sanitized}</b>."

    def _clear_user_filters(self, user_id: int) -> None:
        """Clear all filters for a user"""
        self.user_filters[user_id] = {}
        self.user_applied_filters[user_id] = {}
        self.user_option_tokens.pop(user_id, None)

    async def _has_active_filters(self, user_id: int) -> bool:
        """Check if user has any active filters"""
        applied = await self._get_applied_filters(user_id)
        return bool(applied)

    async def _build_filter_selection_menu(
        self,
        filter_key: str,
        category: str,
        filters: dict,
        user_id: Optional[int] = None,
        page: int = 0,
    ) -> tuple[str, InlineKeyboardMarkup]:
        rows: list[list[InlineKeyboardButton]] = []
        info_lines: list[str] = []

        # Detect API version for appropriate labels and handling
        api_version = await self._get_user_api_version(user_id) if user_id else "v1"
        filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS
        label = filter_labels.get(filter_key, filter_key.title())

        # Use appropriate dynamic filter keys based on API version
        dynamic_keys = self.DYNAMIC_FILTER_KEYS_V3 if api_version == "v3" else self.DYNAMIC_FILTER_KEYS

        if filter_key in dynamic_keys:
            request_filters = filters.copy()

            # Use appropriate reset map based on API version
            reset_map = self.DYNAMIC_FILTER_RESET_MAP_V3 if api_version == "v3" else self.DYNAMIC_FILTER_RESET_MAP

            for key_to_remove in reset_map.get(filter_key, {filter_key}):
                request_filters.pop(key_to_remove, None)

            # Add appropriate tips based on API version
            if api_version == "v3":
                if filter_key == "country" and not request_filters.get("continent"):
                    info_lines.append(
                        "Tip: select a continent first to refine country options."
                    )
                elif filter_key == "region" and not request_filters.get("country"):
                    info_lines.append("Tip: select a country first to refine region options.")
                elif filter_key == "city" and not request_filters.get("region"):
                    info_lines.append("Tip: select a region first to refine city options.")
            else:
                if filter_key == "state" and not request_filters.get("country"):
                    info_lines.append(
                        "Tip: select a country first to refine state options."
                    )
                elif filter_key == "city" and not request_filters.get("state"):
                    info_lines.append("Tip: select a state first to refine city options.")
                elif filter_key == "zip" and not request_filters.get("city"):
                    info_lines.append("Tip: select a city first to refine ZIP options.")

            # Handle API v3 filter options differently
            if api_version == "v3":
                options_data = await self._get_v3_filter_options(filter_key, user_id)
                if not options_data:
                    options_data = []  # Ensure options_data is always defined
                    info_lines.append(
                        "⚠️ Filter options unavailable. Try manual input or retry later."
                    )
            else:
                card_service = await self._get_card_service(user_id)
                response = await card_service.fetch_filter_options(
                    filter_name=filter_key,
                    filters=request_filters,
                    user_id=str(user_id) if user_id else None,
                )

                if not response.get("success"):
                    error_detail = response.get("error") or ""
                    if error_detail:
                        logger.warning(
                            f"Filter options request failed for {filter_key}: {error_detail}"
                        )
                    info_lines.append(
                        "⚠️ Live options unavailable. Try manual input or retry later."
                    )
                    options_data: list[dict] = []
                else:
                    options_data = self._sort_dynamic_options(response.get("data") or [])

            actual_user_id = user_id or 0
            limit = self.FILTER_OPTION_LIMIT
            total_options = len(options_data)
            total_pages = (
                max(1, (total_options + limit - 1) // limit) if options_data else 0
            )
            current_page = page if total_pages else 0
            if total_pages:
                current_page = max(0, min(current_page, total_pages - 1))

            if options_data:
                start_index = current_page * limit
                end_index = min(start_index + limit, total_options)
                page_options = options_data[start_index:end_index]

                rows.extend(
                    self._build_dynamic_option_rows(
                        filter_key, page_options, user_id, current_page
                    )
                )

                info_lines.append(
                    f"Showing results {start_index + 1}-{end_index} of {total_options}."
                )
                if total_pages > 1:
                    info_lines.append(f"Page {current_page + 1} of {total_pages}.")
            else:
                self.user_option_tokens.setdefault(actual_user_id, {}).pop(
                    filter_key, None
                )
                if not info_lines:
                    info_lines.append("No results returned. Try manual input.")

            if filter_key in self.MANUAL_INPUT_CONFIG:
                rows.append(
                    [
                        InlineKeyboardButton(
                            text="✍️ Enter Manually",
                            callback_data=f"filter:input:{filter_key}",
                        )
                    ]
                )

            if total_pages > 1:
                nav_row: list[InlineKeyboardButton] = []
                if current_page > 0:
                    nav_row.append(
                        InlineKeyboardButton(
                            text="⬅️ Previous",
                            callback_data=f"filter:page:{filter_key}:{current_page - 1}",
                        )
                    )
                if current_page < total_pages - 1:
                    nav_row.append(
                        InlineKeyboardButton(
                            text="Next ➡️",
                            callback_data=f"filter:page:{filter_key}:{current_page + 1}",
                        )
                    )
                if nav_row:
                    rows.append(nav_row)
        else:
            # Handle preset filters (non-dynamic)
            if filter_key == "brand":
                options = list(self.PRESET_BRANDS)
                for chunk in self._chunk_list(options, 2):
                    rows.append(
                        [
                            InlineKeyboardButton(
                                text=brand,
                                callback_data=f"filter:set:brand:{brand}",
                            )
                            for brand in chunk
                        ]
                    )
            elif filter_key == "type":
                rows.append(
                    [
                        InlineKeyboardButton(
                            text=card_type,
                            callback_data=f"filter:set:type:{card_type}",
                        )
                        for card_type in self.PRESET_TYPES
                    ]
                )
            elif filter_key == "price":
                for preset_label, price_from, price_to in self.PRICE_PRESETS:
                    rows.append(
                        [
                            InlineKeyboardButton(
                                text=preset_label,
                                callback_data=f"filter:set:price:{price_from}-{price_to}",
                            )
                        ]
                    )
            elif filter_key == "bin":
                options = list(self.PRESET_BINS)
                for chunk in self._chunk_list(options, 3):
                    rows.append(
                        [
                            InlineKeyboardButton(
                                text=bn, callback_data=f"filter:set:bin:{bn}"
                            )
                            for bn in chunk
                        ]
                    )
            else:
                # Filter not in dynamic keys or preset types
                # Check if manual input is available
                if filter_key in self.MANUAL_INPUT_CONFIG:
                    info_lines.append(
                        f"Manual input available for {label}."
                    )
                else:
                    # No options available for this filter
                    info_lines.append(
                        f"⚠️ {label} filter options are currently unavailable."
                    )
                    info_lines.append(
                        "This filter may not be supported in this version."
                    )

        # Always add manual input button if configuration exists
        if filter_key in self.MANUAL_INPUT_CONFIG and filter_key not in dynamic_keys:
            # For non-dynamic filters, add manual input button
            rows.append(
                [
                    InlineKeyboardButton(
                        text="✍️ Enter Manually",
                        callback_data=f"filter:input:{filter_key}",
                    )
                ]
            )

        if filter_key == "price":
            if "priceFrom" in filters or "priceTo" in filters:
                rows.append(
                    [
                        InlineKeyboardButton(
                            text="❌ Clear Range", callback_data="filter:unset:price"
                        )
                    ]
                )
        elif filter_key in filters:
            rows.append(
                [
                    InlineKeyboardButton(
                        text=f"❌ Clear {label}",
                        callback_data=f"filter:unset:{filter_key}",
                    )
                ]
            )

        rows.append(
            [
                InlineKeyboardButton(text="⬅️ Back", callback_data="filter:back"),
                InlineKeyboardButton(text="✅ Apply", callback_data="filter:apply"),
            ]
        )

        message_lines = [f"🎯 <b>{label}</b>", "", "Choose an option:"]
        if info_lines:
            message_lines.append("")
            message_lines.extend(info_lines)

        return "\n".join(message_lines), InlineKeyboardMarkup(inline_keyboard=rows)

    async def _render_cards_page(self, callback: CallbackQuery, page: int) -> None:
        """Render cards list for a given page using optimized client-side pagination."""
        try:
            user_id = self._get_user_id(callback)
            applied = await self._get_applied_filters(user_id)
            
            # Get current product selection once for the entire operation
            current_product, current_api = (
                await self.product_service.get_user_current_selection(user_id)
            )
            
            # First, try to get page from cache
            cached_cards, total_count, cache_hit = self._get_cached_page(user_id, page)
            
            # If cache hit, render immediately without loading animation
            if cache_hit and cached_cards:
                cache = self.user_search_cache.get(user_id, {})
                cached_count = len([c for c in cache.get('cards', []) if c is not None])
                logger.info(f"✨ Cache hit for user {user_id}, page {page}/{(cached_count + CARDS_PER_UI_PAGE - 1) // CARDS_PER_UI_PAGE} "
                           f"({cached_count} cards cached) - rendering instantly")
                
                # Format cached data directly (product selection already obtained)
                validated_cards = self._validate_cards_efficiently(cached_cards)
                cards_text = self._format_cards_optimized(validated_cards, applied, page, total_count)
                
                # Log what user will see
                total_pages = (total_count + CARDS_PER_UI_PAGE - 1) // CARDS_PER_UI_PAGE
                has_more = cache.get('has_more_data', False)
                logger.info(f"📄 Displaying page {page}/{total_pages} ({len(validated_cards)} cards) "
                           f"{'[More available]' if has_more else '[All loaded]'}")
                
                keyboard = product_formatter.create_enhanced_card_keyboard(
                    cards=validated_cards,
                    page=page,
                    total_count=total_count,
                    has_filters=bool(applied),
                    callback_prefix="catalog",
                )
                
                # Update current cards cache for cart operations
                self.user_current_cards[user_id] = cached_cards
                
                # Render instantly without loading animation
                await callback.message.edit_text(cards_text, reply_markup=keyboard, parse_mode="HTML")
                
                # Prefetch next batch in background if needed
                if self._should_prefetch_next_batch(user_id, page):
                    logger.info(f"Starting background prefetch for user {user_id}")
                    asyncio.create_task(self._prefetch_next_batch(user_id, applied, page))
                
                return
            
            # Cache miss - need to fetch from API with loading animation
            cache = self.user_search_cache.get(user_id, {})
            cached_count = len([c for c in cache.get('cards', []) if c is not None]) if cache else 0
            logger.info(f"📥 Cache miss for user {user_id}, page {page} "
                       f"({cached_count} cards currently cached) - fetching from API")
            
            # Get current product selection once for the entire operation
            current_product, current_api = (
                await self.product_service.get_user_current_selection(user_id)
            )
            
            # Create work coroutine for concurrent loading
            async def cards_work():
                # Calculate which API page we need to fetch based on UI page
                # Each API page has CARDS_PER_API_PAGE cards (100)
                # Each UI page shows CARDS_PER_UI_PAGE cards (6)
                # So we need to figure out which API page contains the data for this UI page
                
                ui_start_item = (page - 1) * CARDS_PER_UI_PAGE
                api_page = (ui_start_item // CARDS_PER_API_PAGE) + 1
                
                logger.info(f"Fetching API page {api_page} for UI page {page} (item {ui_start_item})")
                
                # Fetch from API (pass the already-obtained product info to avoid duplicate calls)
                card_service = await self._get_card_service(user_id, current_product, current_api)
                cards_data = await card_service.fetch_cards(
                    page=api_page, limit=CARDS_PER_API_PAGE, filters=applied
                )
                
                api_data = cards_data.get('data', [])
                api_total_count = cards_data.get('totalCount', 0)
                
                if not api_data:
                    return await self._handle_no_cards_work(applied)
                
                # Update cache with new data
                api_version = await self._get_user_api_version(user_id)
                product_str = str(current_product) if current_product else "unknown"
                self._update_search_cache(
                    user_id, applied, product_str, api_version, 
                    api_data, api_page, api_total_count
                )
                
                # Get the specific page from newly cached data
                page_cards, updated_total, _ = self._get_cached_page(user_id, page)
                
                if not page_cards:
                    return await self._handle_no_cards_work(applied)
                
                # Update current cards cache for cart operations
                self.user_current_cards[user_id] = page_cards
                
                # Format and return results
                validated_cards = self._validate_cards_efficiently(page_cards)
                cards_text = self._format_cards_optimized(validated_cards, applied, page, updated_total)
                
                keyboard = product_formatter.create_enhanced_card_keyboard(
                    cards=validated_cards,
                    page=page,
                    total_count=updated_total,
                    has_filters=bool(applied),
                    callback_prefix="catalog",
                )
                
                return cards_text, keyboard
            
            # Run loading stages concurrently with actual work (only for cache misses)
            result = await LoadingStages.run_concurrent_loading(
                callback,
                BROWSE_STAGES,
                cards_work(),
                operation_name="Browse Cards"
            )
            
            # Handle different return types
            if isinstance(result, tuple) and len(result) == 2:
                cards_text, keyboard = result
                await callback.message.edit_text(cards_text, reply_markup=keyboard, parse_mode="HTML")
            else:
                await callback.answer("❌ Error occurred", show_alert=True)
            
        except Exception as e:
            logger.error(f"Error rendering cards page: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)


    async def cb_browse_menu(self, callback: CallbackQuery) -> None:
        """Handle browse menu callback with product-aware interface"""
        try:
            user_id = self._get_user_id(callback)
            applied_filters = await self._get_applied_filters(user_id)

            # Get current product selection
            user = callback.from_user
            current_product, current_api = None, None
            if user:
                current_product, current_api = (
                    await self.product_service.get_user_current_selection(user.id)
                )

            # Build message using new UI components
            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Browse Catalog", "🛒")

            if current_product and current_api:
                message_builder.add_content(
                    f"Ready to browse <b>{current_product}</b> cards using <b>{current_api}</b> API."
                )
            else:
                message_builder.add_content(
                    "Please select a product and API to start browsing."
                )
                message_builder.add_section(
                    "Next Step",
                    "Use the 'Select Product' button below to choose your product and API.",
                    "👆",
                )

            # Add filter information if any
            if applied_filters:
                filter_count = len([k for k, v in applied_filters.items() if v])
                message_builder.add_section(
                    "Active Filters",
                    f"{filter_count} filter(s) currently applied. Use 'Browse Filtered' to see results.",
                    "🔍",
                )

            # Use enhanced keyboard from SmartKeyboardLayouts
            from utils.enhanced_keyboards import SmartKeyboardLayouts

            keyboard = SmartKeyboardLayouts.create_browse_keyboard(
                has_filters=bool(applied_filters),
                current_product=current_product,
                current_api=current_api,
            )

            await ui_manager.edit_message_safely(
                callback, message_builder.build(), keyboard
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in browse menu: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _build_browse_menu_message(
        self, current_product, current_api, applied_filters
    ) -> str:
        """Build the browse menu message with product and filter status"""
        message = "🛒 <b>Browse Catalog</b>\n\n"

        # Show current product/API selection
        if current_product and current_api:
            api_info = await self.product_service.get_api_info(current_api)
            if api_info:
                status_emoji = "🟢" if api_info.status.value == "active" else "🔴"
                message += "📍 <b>Current Source:</b>\n"
                product_name = current_product.value.upper()
                api_name = api_info.name
                message += f"   {product_name} → {api_name} {status_emoji}\n\n"
            else:
                message += "⚠️ <b>No API Selected</b>\n"
                message += "Please select a product and API first.\n\n"
        else:
            message += "⚠️ <b>No Product Selected</b>\n"
            message += "Please select a product and API first.\n\n"

        # Add filter information
        if applied_filters:
            filter_summary = self._filters_summary_text(applied_filters)
            message += f"🔍 <b>Active Filters:</b>\n{filter_summary}\n\n"
            message += "Choose an option to explore available items:"
        else:
            message += "Choose an option to explore available items:"

        return message

    async def _build_browse_menu_keyboard(
        self, current_product, current_api, applied_filters
    ) -> InlineKeyboardMarkup:
        """Build the browse menu keyboard with context-aware options"""
        keyboard_buttons = [
            [
                InlineKeyboardButton(text="🔎 Search", callback_data="catalog:search"),
                InlineKeyboardButton(
                    text="🧰 Filters", callback_data="catalog:filters"
                ),
            ],
        ]

        # Add browse options based on filter state
        if applied_filters:
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="📄 Browse with Filters",
                        callback_data="catalog:browse_filtered",
                    ),
                    InlineKeyboardButton(
                        text="📋 Browse All", callback_data="catalog:browse_all"
                    ),
                ]
            )
        else:
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="📄 Browse All", callback_data="catalog:browse_all"
                    ),
                ]
            )

        # Add product selection option if no product selected
        if not current_product or not current_api:
            keyboard_buttons.insert(
                0,
                [
                    InlineKeyboardButton(
                        text="🛍️ Select Product", callback_data="menu:products"
                    )
                ],
            )

        # Add navigation with product context
        nav_buttons = []
        if current_product and current_api:
            nav_buttons.append(
                InlineKeyboardButton(
                    text="🔄 Switch API",
                    callback_data=f"product:apis:{current_product.value}",
                )
            )
        nav_buttons.append(
            InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")
        )
        keyboard_buttons.append(nav_buttons)

        return InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

    async def cb_search(self, callback: CallbackQuery, force_update: bool = False) -> None:
        """Handle search callback - show direct filter interface"""
        try:
            user_id = self._get_user_id(callback)
            temp_filters = self._get_temp_filters(user_id)
            applied_filters = await self._get_applied_filters(user_id)

            # Set interface type to direct
            self.user_filter_interface[user_id] = "direct"
            
            # Determine navigation source based on context
            # If coming from back button (force_update=True), preserve existing navigation source
            # If coming from browse menu (catalog:search), set to browse
            # If coming from filters, set to search
            if force_update:
                # Coming from back button - preserve existing navigation source
                current_nav_source = self.user_navigation_source.get(user_id, "browse")
                logger.debug(f"🔍 cb_search: Preserving navigation_source='{current_nav_source}' for user {user_id} (from back button)")
            elif callback.data == "catalog:search":
                # Coming directly from browse menu
                self.user_navigation_source[user_id] = "browse"
                logger.debug(f"🔍 cb_search: Set navigation_source='browse' for user {user_id} (from browse menu)")
            else:
                # Coming from filters or other search context
                self.user_navigation_source[user_id] = "search"
                logger.debug(f"🔍 cb_search: Set navigation_source='search' for user {user_id} (from search context)")

            # Detect API version for appropriate filter interface
            api_version = await self._get_user_api_version(user_id)

            # Merge temp and applied filters for display
            all_filters = {**applied_filters, **temp_filters}

            # Build message using new UI components
            from utils.ui_components import create_message, MessageType

            message_builder = create_message(MessageType.INFO)
            title_suffix = f" (API {api_version.upper()})" if api_version != "v1" else ""
            message_builder.set_title(f"Search & Filter Cards{title_suffix}", "🔍")

            message_builder.add_content(
                "Select filters to narrow your search, then click 'Apply & Browse' to find cards."
            )

            if temp_filters:
                temp_summary = self._filters_summary_text(temp_filters, api_version)
                message_builder.add_section("Pending Filters", temp_summary, "⚙️")

            if applied_filters:
                applied_summary = self._filters_summary_text(applied_filters, api_version)
                message_builder.add_section("Applied Filters", applied_summary, "✅")

            if not temp_filters and not applied_filters:
                message_builder.add_content(
                    "💡 <i>No filters set - all available cards will be shown</i>"
                )

            # Use appropriate direct filter keyboard based on API version
            from utils.enhanced_keyboards import SmartKeyboardLayouts

            if api_version == "v3":
                keyboard = SmartKeyboardLayouts.create_direct_filter_keyboard_v3(all_filters)
            else:
                keyboard = SmartKeyboardLayouts.create_direct_filter_keyboard(all_filters)

            await ui_manager.edit_message_safely(
                callback, message_builder.build(), keyboard, force_update=force_update
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in search: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_filters(self, callback: CallbackQuery) -> None:
        """Handle filters callback"""
        try:
            user_id = self._get_user_id(callback)
            temp_filters = self._get_temp_filters(user_id)
            applied_filters = await self._get_applied_filters(user_id)

            # Set interface type to category
            self.user_filter_interface[user_id] = "category"
            
            # Determine navigation source based on context
            # If coming from search interface, preserve "search" navigation source
            # If coming from browse menu (catalog:filters), set to "search" for proper back navigation
            # If already set to "search", preserve it
            current_nav_source = self.user_navigation_source.get(user_id)
            
            if current_nav_source == "search":
                # Already in search context - preserve it
                logger.debug(f"🔍 cb_filters: Preserving navigation_source='search' for user {user_id}")
            elif callback.data == "catalog:filters":
                # Coming directly from browse menu - set to search for proper back navigation
                self.user_navigation_source[user_id] = "search"
                logger.debug(f"🔍 cb_filters: Set navigation_source='search' for user {user_id} (from browse menu)")
            else:
                # Other context - set to browse as fallback
                self.user_navigation_source[user_id] = "browse"
                logger.debug(f"🔍 cb_filters: Set navigation_source='browse' for user {user_id} (fallback)")

            # Detect API version to show appropriate filters
            api_version = await self._get_user_api_version(user_id)
            logger.debug(f"Showing filters for API {api_version} for user {user_id}")

            # Build message using new UI components
            message_builder = create_message(MessageType.INFO)
            if api_version == "v3":
                message_builder.set_title("API v3 Search Filters", "🗂️")
                message_builder.add_content("Configure filters for API v3 card search.")
            else:
                message_builder.set_title("Search Filters", "🗂️")

            if temp_filters:
                temp_summary = self._filters_summary_text(temp_filters, api_version)
                message_builder.add_section("Pending Filters", temp_summary, "⚙️")

            if applied_filters:
                applied_summary = self._filters_summary_text(applied_filters, api_version)
                message_builder.add_section("Applied Filters", applied_summary, "✅")

            if not temp_filters and not applied_filters:
                message_builder.add_content("No filters are currently set.")

            message_builder.add_content(
                "Choose a category to adjust available filters."
            )

            # Use enhanced filter keyboard with API version awareness
            from utils.enhanced_keyboards import SmartKeyboardLayouts

            category_status = self._category_status_flags(temp_filters, api_version)
            if api_version == "v3":
                keyboard = SmartKeyboardLayouts.create_filter_keyboard_v3(category_status)
            else:
                keyboard = SmartKeyboardLayouts.create_filter_keyboard(category_status)

            await ui_manager.edit_message_safely(
                callback, message_builder.build(), keyboard, add_watermark=False, force_update=True
            )
            await callback.answer()

        except Exception as e:
            logger.error(f"Error in filters: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_filter_set(self, callback: CallbackQuery) -> None:
        """Compatibility wrapper for tests expecting cb_filter_set.

        Forwards to cb_filter_actions which handles filter:set:* callbacks.
        """
        return await self.cb_filter_actions(callback)

    async def cb_browse_filtered(self, callback: CallbackQuery) -> None:
        """Handle browse with filters callback"""
        try:
            user_id = self._get_user_id(callback)
            applied_filters = await self._get_applied_filters(user_id)

            # Validate that filters are actually applied
            active_filters = {k: v for k, v in applied_filters.items() if v and v != ""}
            
            if not active_filters:
                await callback.answer(
                    "No active filters found. Apply some filters first or use 'Browse All'.", 
                    show_alert=True
                )
                return

            # Show filter summary in loading message
            filter_count = len(active_filters)
            filter_summary = ", ".join([f"{k}={v}" for k, v in list(active_filters.items())[:2]])
            if len(active_filters) > 2:
                filter_summary += "..."
            
            loading_msg = f"🔍 Loading filtered results ({filter_count} filters: {filter_summary})"
            await callback.answer(loading_msg)
            
            # Invalidate cache to ensure fresh filtered data
            self._invalidate_search_cache(user_id)
            
            await self._render_cards_page(callback, page=1)

        except Exception as e:
            logger.error(f"Error in browse filtered: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_browse_all(self, callback: CallbackQuery) -> None:
        """Handle browse all callback (no filters)"""
        try:
            user_id = self._get_user_id(callback)

            # Store original filters for potential restoration
            original_filters = (await self._get_applied_filters(user_id)).copy()
            
            # Clear filters for this browse session only
            self.user_applied_filters[user_id] = {}
            
            # Invalidate cache to ensure fresh data
            self._invalidate_search_cache(user_id)

            await self._render_cards_page(callback, page=1)

            # Restore original filters after rendering so user's filter state is preserved
            # This allows them to return to filtered view later without re-applying filters
            if original_filters:
                self.user_applied_filters[user_id] = original_filters
                logger.debug(f"Restored {len(original_filters)} filters for user {user_id} after browse all")

        except Exception as e:
            logger.error(f"Error in browse all: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    def _validate_cards_efficiently(self, cards: List[Dict]) -> List[Dict]:
        """Efficiently validate cards with minimal overhead."""
        # Optimization: Skip validation for API v3 pre-extracted cards
        if cards and isinstance(cards[0], dict) and cards[0].get('card_id') and len(cards[0]) > 8:
            logger.debug(f"🚀 EFFICIENCY: Skipping validation for {len(cards)} pre-validated API v3 cards")
            return cards  # API v3 cards are already fully validated
        
        # Standard validation for other sources
        validated = []
        for card in cards:
            # Basic validation - ensure essential fields exist
            if (card.get('bin') or card.get('card_id') or card.get('_id')) and \
               (card.get('price') is not None or card.get('current_price') is not None):
                # Ensure required display fields
                self._ensure_display_fields(card)
                validated.append(card)
        return validated
    
    def _ensure_display_fields(self, card: Dict) -> None:
        """Ensure card has required fields for display."""
        # Ensure price field exists
        if 'price' not in card:
            card['price'] = card.get('current_price') or card.get('original_price') or 0
        
        # Ensure cardholder name field
        if 'cardholder' not in card:
            card['cardholder'] = card.get('name') or card.get('cardholder_name') or 'N/A'
        
        # Ensure _id field for cart operations
        if '_id' not in card:
            card['_id'] = card.get('card_id') or card.get('bin') or str(hash(str(card)))
        
        # Normalize API v3 specific fields
        # Map discount_percentage to discount for consistent display
        if 'discount_percentage' in card and 'discount' not in card:
            card['discount'] = card['discount_percentage']
        
        # Ensure is_expiring is properly set (already in API v3 response)
        # Keep as-is, already correctly named
    
    def _log_card_debug_info(self, validated_cards: List[Dict], user_id: int) -> None:
        """Log card debug info (debug mode only)."""
        if validated_cards:
            sample_card = validated_cards[0]
            logger.debug(f"🔍 Rendering {len(validated_cards)} cards for user {user_id}")
            logger.debug(f"📋 Sample keys: {list(sample_card.keys())[:10]}...")  # Limit keys shown
            logger.debug(f"💳 Sample: bin={sample_card.get('bin')}, price={sample_card.get('price')}")
    
    def _format_cards_optimized(self, validated_cards: List[Dict], applied: Dict, 
                               page: int, total_count: int) -> str:
        """Format cards with optimized display logic."""
        try:
            from utils.product_display import ProductDisplayFormatter
            product_formatter = ProductDisplayFormatter()
            
            cards_text = product_formatter.format_cards_with_filters(
                cards=validated_cards,
                active_filters=applied,
                page=page,
                total_count=total_count,
            )
            
            if logger.isEnabledFor(10):  # Debug mode only
                logger.debug(f"✅ Formatted text length: {len(cards_text)}")
            
            return cards_text
            
        except Exception as format_error:
            logger.error(f"❌ Error formatting cards: {format_error}")
            # Fallback to simple display
            return self._create_fallback_card_display(validated_cards, page)
    
    def _create_fallback_card_display(self, validated_cards: List[Dict], page: int) -> str:
        """Create a simple fallback card display when main formatter fails."""
        try:
            cards_text = f"🛒 <b>Cards Catalog</b>"
            if page > 1:
                cards_text += f" <i>(Page {page})</i>"
            cards_text += f"\n\n📄 <b>Showing:</b> {len(validated_cards)} cards\n\n"
            
            for i, card in enumerate(validated_cards[:6], 1):
                bin_number = card.get('bin', 'N/A')
                name = card.get('name', card.get('cardholder', 'Unknown'))
                price = card.get('price', 'N/A')
                country = card.get('country', 'N/A')
                
                cards_text += f"{i}. 💳 <b>BIN:</b> {bin_number}\n"
                if name and name != 'Unknown':
                    cards_text += f"   👤 <b>Name:</b> {name}\n"
                cards_text += f"   🌍 <b>Country:</b> {country}\n"
                cards_text += f"   💰 <b>Price:</b> ${price}\n"
                if i < len(validated_cards):
                    cards_text += "\n   ─────────────────\n"
            
            return cards_text
            
        except Exception as e:
            logger.error(f"❌ Even fallback display failed: {e}")
            return f"🛒 <b>Cards Catalog</b>\n\n⚠️ Error displaying {len(validated_cards)} cards"
    
    async def _handle_no_cards(self, callback: CallbackQuery, applied: Dict) -> None:
        """Handle case when no valid cards are found."""
        no_cards_message = create_message(MessageType.INFO)
        no_cards_message.set_title("No Valid Cards Found")
        no_cards_message.add_content("No cards with complete data are available on this page.")

        if applied:
            no_cards_message.add_section(
                "Suggestion", "Try adjusting your filters or browse all cards.", "💡"
            )

        await callback.message.edit_text(
            no_cards_message.build(), 
            reply_markup=back_keyboard("menu:browse"),
            parse_mode="HTML"
        )
    
    async def _handle_no_cards_work(self, applied: Dict) -> tuple[str, InlineKeyboardMarkup]:
        """Handle case when no valid cards are found - work version that returns values."""
        no_cards_message = create_message(MessageType.INFO)
        no_cards_message.set_title("No Valid Cards Found")
        no_cards_message.add_content("No cards with complete data are available on this page.")

        if applied:
            no_cards_message.add_section(
                "Suggestion", "Try adjusting your filters or browse all cards.", "💡"
            )

        return no_cards_message.build(), back_keyboard("menu:browse")

    async def cb_filter_actions(
        self, callback: CallbackQuery, state: Optional[FSMContext] = None
    ) -> None:
        """Handle filter action callbacks."""

        try:
            parts = callback.data.split(":")
            action = parts[1] if len(parts) > 1 else ""

            user_id = self._get_user_id(callback)
            temp_filters = self._get_temp_filters(user_id)
            
            logger.debug(f"🔍 cb_filter_actions: user {user_id} action='{action}' callback_data='{callback.data}'")

            if action == "clear":
                self._clear_user_filters(user_id)
                # Invalidate search cache since filters changed
                self._invalidate_search_cache(user_id)
                await callback.answer("Filters cleared")
                await self._return_to_filter_interface(callback)
                return

            if action == "apply":
                self.user_applied_filters[user_id] = temp_filters.copy()
                # Invalidate search cache since filters changed
                self._invalidate_search_cache(user_id)
                await callback.answer("Filters applied")
                await self._render_cards_page(callback, page=1)
                return

            if action == "back":
                nav_state = await self._get_navigation_state(user_id)
                logger.debug(f"🔍 cb_filter_actions: Processing 'back' action for user {user_id}, nav_state: {nav_state}")
                await self._return_to_filter_interface(callback, from_back_button=True)
                await callback.answer()
                return

            if action == "category" and len(parts) >= 3:
                category = parts[2]
                await self._show_category_menu(callback, category)
                await callback.answer()
                return

            if action == "select" and len(parts) >= 3:
                filter_key = parts[2]
                api_version = await self._get_user_api_version(user_id)
                category = self._category_from_key(filter_key, api_version) or "card"

                try:
                    menu_text, keyboard = await self._build_filter_selection_menu(
                        filter_key,
                        category,
                        temp_filters,
                        user_id=user_id,
                        page=0,
                    )
                    await callback.message.edit_text(
                        menu_text + "\n" + DEMO_WATERMARK,
                        reply_markup=keyboard,
                    )
                    await callback.answer()
                except Exception as e:
                    logger.error(
                        f"Error building filter selection menu for {filter_key}: {e}"
                    )
                    await callback.answer(
                        f"⚠️ Filter options temporarily unavailable for {filter_key}. Please try again later.",
                        show_alert=True,
                    )
                    # Return to the filter interface instead of leaving user stuck
                    await self._return_to_filter_interface(callback)
                return

            if action == "page" and len(parts) >= 4:
                filter_key = parts[2]
                try:
                    page_number = int(parts[3])
                except ValueError:
                    await callback.answer("Invalid page", show_alert=True)
                    return

                api_version = await self._get_user_api_version(user_id)
                category = self._category_from_key(filter_key, api_version) or "card"

                try:
                    menu_text, keyboard = await self._build_filter_selection_menu(
                        filter_key,
                        category,
                        temp_filters,
                        user_id=user_id,
                        page=page_number,
                    )
                    await callback.message.edit_text(
                        menu_text + "\n" + DEMO_WATERMARK,
                        reply_markup=keyboard,
                    )
                    await callback.answer()
                except Exception as e:
                    logger.error(
                        f"Error building filter selection menu for {filter_key} page {page_number}: {e}"
                    )
                    await callback.answer(
                        f"⚠️ Error loading page {page_number + 1}. Returning to filter interface.",
                        show_alert=True,
                    )
                    await self._return_to_filter_interface(callback)
                return

            if action == "input" and len(parts) >= 3:
                if state is None:
                    await callback.answer("State unavailable", show_alert=True)
                    return
                key = parts[2]
                await self._prompt_for_manual_input(callback, state, key)
                return

            if action == "bin_manual":  # Backward compatibility
                if state is None:
                    await callback.answer("State unavailable", show_alert=True)
                    return
                await self._prompt_for_manual_input(callback, state, "bin")
                return

            if action == "toggle" and len(parts) >= 3:
                key = parts[2]
                # Use appropriate label dictionary based on API version
                api_version = await self._get_user_api_version(user_id)
                filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS
                label = filter_labels.get(key, key.title())
                if temp_filters.get(key):
                    temp_filters.pop(key, None)
                    await callback.answer(f"{label} disabled")
                else:
                    temp_filters[key] = True
                    await callback.answer(f"{label} enabled")

                await self._return_to_filter_interface(callback)
                return

            if action == "unset" and len(parts) >= 3:
                key = parts[2]
                if key == "price":
                    temp_filters.pop("priceFrom", None)
                    temp_filters.pop("priceTo", None)
                else:
                    temp_filters.pop(key, None)

                # Use appropriate label dictionary based on API version
                api_version = await self._get_user_api_version(user_id)
                filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS
                label = filter_labels.get(key, key.title())
                await callback.answer(f"{label} cleared")

                await self._return_to_filter_interface(callback)
                return

            if action == "set" and len(parts) >= 4:
                key = parts[2]
                value = ":".join(parts[3:])

                # Detect API version for appropriate labels and dynamic keys
                api_version = await self._get_user_api_version(user_id)
                filter_labels = self.FILTER_LABELS_V3 if api_version == "v3" else self.FILTER_LABELS
                dynamic_keys = self.DYNAMIC_FILTER_KEYS_V3 if api_version == "v3" else self.DYNAMIC_FILTER_KEYS

                label = filter_labels.get(key, key.title())

                if key in dynamic_keys:
                    resolved_value = self._resolve_dynamic_option_value(
                        user_id, key, value
                    )
                    if resolved_value is not None:
                        value = resolved_value
                    else:
                        token_entry = self.user_option_tokens.get(user_id, {}).get(
                            key, {}
                        )
                        if token_entry.get("tokens"):
                            await callback.answer(
                                "Option expired. Open the filter again to refresh.",
                                show_alert=True,
                            )
                            return

                if key == "price" and "-" in value:
                    try:
                        p_from_str, p_to_str = value.rsplit("-", 1)
                        price_from = float(p_from_str.strip())
                        price_to = float(p_to_str.strip())
                    except ValueError:
                        await callback.answer("Invalid price range", show_alert=True)
                        return

                    if price_from < 0 or price_to < 0:
                        await callback.answer(
                            "Prices cannot be negative", show_alert=True
                        )
                        return
                    if price_from > price_to:
                        await callback.answer(
                            "Minimum price cannot exceed maximum price",
                            show_alert=True,
                        )
                        return
                    if price_from > self.MAX_PRICE or price_to > self.MAX_PRICE:
                        await callback.answer(
                            f"Price cannot exceed ${self.MAX_PRICE:,.0f}",
                            show_alert=True,
                        )
                        return
                    if price_to - price_from > self.MAX_PRICE_SPREAD:
                        await callback.answer(
                            "Price range too wide (max $5,000 difference)",
                            show_alert=True,
                        )
                        return

                    temp_filters["priceFrom"] = f"{price_from:.2f}"
                    temp_filters["priceTo"] = f"{price_to:.2f}"
                    await callback.answer(
                        f"Price range set: ${price_from:.2f}–${price_to:.2f}"
                    )
                    await self._return_to_filter_interface(callback)
                    return

                trimmed_value = value.strip()
                if key != "price" and trimmed_value == "":
                    temp_filters.pop(key, None)
                    await callback.answer(f"{label} cleared")
                    await self._return_to_filter_interface(callback)
                    return

                temp_filters[key] = trimmed_value
                await callback.answer(f"{label} updated")
                await self._return_to_filter_interface(callback)
                return

            await callback.answer("Unknown filter action", show_alert=True)

        except Exception as e:
            logger.error(f"Error in filter actions: {e}")
            try:
                await callback.answer("❌ Error occurred", show_alert=True)
            except Exception as answer_error:
                # If we can't even send an answer, log it
                logger.error(f"Failed to send error answer to user: {answer_error}")

    async def message_set_filter_value(
        self, message: Message, state: FSMContext
    ) -> None:
        """Handle manual filter input values."""

        try:
            user = message.from_user
            if not user:
                await message.answer("❌ Unable to identify user")
                return

            data = await state.get_data()
            key = data.get("filter_key")
            api_version = await self._get_user_api_version(user.id)
            category = data.get("category") or self._category_from_key(key or "", api_version)
            input_mode = data.get("input_mode", "text")

            if not key:
                await message.answer("❌ No filter is awaiting input.")
                await state.clear()
                return

            temp_filters = self._get_temp_filters(user.id)
            success, response = self._set_manual_filter_value(
                user.id, key, message.text or "", input_mode, api_version
            )

            if not success:
                await message.answer(f"❌ {response}")
                return

            await state.clear()
            api_version = await self._get_user_api_version(user.id)
            instructions = self._manual_input_success_hint(key, temp_filters, api_version)

            if category:
                reply_markup = self._category_return_keyboard(category, user.id)
            else:
                # Use appropriate filter keyboard based on API version
                from utils.enhanced_keyboards import SmartKeyboardLayouts
                category_status = self._category_status_flags(temp_filters, api_version)
                if api_version == "v3":
                    reply_markup = SmartKeyboardLayouts.create_filter_keyboard_v3(category_status)
                else:
                    reply_markup = SmartKeyboardLayouts.create_filter_keyboard(category_status)

            message_text = f"✅ {response}"
            if instructions:
                message_text += f"\n\n{instructions}"

            await message.answer(message_text, reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"Error setting filter value: {e}")
            await message.answer("❌ Error setting filter value")

    async def cb_view_cards(self, callback: CallbackQuery) -> None:
        """Handle view cards callback with optimized pagination"""
        try:
            # Extract page number from callback data
            parts = callback.data.split(":")
            page = int(parts[2]) if len(parts) > 2 else 1
            user_id = self._get_user_id(callback)

            # Quick check for cache hit - provide instant feedback
            cached_cards, total_count, cache_hit = self._get_cached_page(user_id, page)
            
            if cache_hit and cached_cards:
                # Instant response for cached pages
                await callback.answer("✨ Page ready!")

            # Use the optimized _render_cards_page method which handles cache/API logic
            await self._render_cards_page(callback, page)

        except Exception as e:
            logger.error(f"Error in view cards: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)


    async def cb_add_to_cart(self, callback: CallbackQuery) -> None:
        """Handle add to cart callback"""
        # Import UI components at the start
        from utils.ui_components import create_message, MessageType
        from utils.enhanced_keyboards import SmartKeyboardLayouts
        from datetime import datetime
        
        try:
            if self.user_service is None:
                self.user_service = UserService()
            if self.cart_service is None:
                self.cart_service = CartService()
            # Extract card ID from callback data
            parts = callback.data.split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[2]  # Card ID is a string hash, not an integer

            user = callback.from_user
            if not user:
                await callback.answer("❌ Unable to identify user", show_alert=True)
                return

            # Get user document
            user_doc = await self.user_service.get_user_by_telegram_id(user.id)
            if not user_doc:
                await callback.answer(
                    "❌ User not found. Please start the bot with /start",
                    show_alert=True,
                )
                return

            await callback.answer("🛒 Adding to cart...")

            # Find card data from cache
            user_id = self._get_user_id(callback)
            cached_cards = self.user_current_cards.get(user_id, [])
            card_data = None

            # Debug logging for cache lookup
            logger.info(f"Looking for card {card_id} (type: {type(card_id).__name__}) in cache for user {user_id}")
            logger.info(f"Cache contains {len(cached_cards)} cards")

            for card in cached_cards:
                cached_card_id = card.get("_id")
                logger.debug(f"Comparing with cached card ID: {cached_card_id} (type: {type(cached_card_id).__name__})")

                # Handle type mismatch between string callback data and integer card IDs
                if str(cached_card_id) == str(card_id):
                    card_data = card
                    logger.info(f"Found card {card_id} in cache: {card.get('bank', 'Unknown Bank')}")
                    break

            if not card_data:
                logger.warning(f"Card {card_id} not found in cache. Cache IDs: {[card.get('_id') for card in cached_cards[:6]]}")  # Show first 6 for debugging

            # Add to cart with card data if available
            success, message = await self.cart_service.add_to_cart(
                str(user_doc.id), card_id, 1, card_data
            )

            if success:
                # Get updated cart count and cart total
                cart_count = await self.cart_service.get_cart_item_count(
                    str(user_doc.id)
                )
                cart_total = await self.cart_service.get_cart_total(str(user_doc.id))

                await callback.answer(f"🎉 Added to cart! ({cart_count} items • ${cart_total:.2f})")

                # Use centralized card formatter for consistent UI
                from utils.card_ui_formatter import card_ui_formatter
                
                # Create success message with consistent card formatting
                success_text = card_ui_formatter.format_add_to_cart_success_message(
                    card_data if card_data else {},
                    cart_count,
                    cart_total
                )

                # Create consistent keyboard for post-add-to-cart actions
                keyboard = SmartKeyboardLayouts.create_add_to_cart_success_keyboard(
                    cart_count=cart_count,
                    cart_total=cart_total
                )

                # Send the success message
                await callback.message.answer(
                    success_text,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                    disable_web_page_preview=True
                )
            else:
                await callback.answer(f"❌ {message}", show_alert=True)
                
                # Create a proper error message for failed add to cart
                error_msg = create_message(MessageType.ERROR)
                error_msg.set_title("❌ Failed to Add to Cart", "❌")
                
                # Add main error content
                error_msg.add_content(
                    f"Unable to add the item to your cart.\n\n"
                    f"<b>Error:</b> {message}"
                )
                
                # Add troubleshooting section
                error_msg.add_list_section(
                    "🔧 Troubleshooting",
                    [
                        "Check if the item is still available",
                        "Verify your account is properly set up",
                        "Try refreshing and adding again",
                        "Contact support if the issue persists"
                    ],
                    "💡"
                )
                
                # Add action buttons
                error_msg.add_section(
                    "🚀 Next Steps",
                    "You can try browsing other items or check your cart status.",
                    "🎯"
                )

                # Create keyboard for error recovery
                keyboard = SmartKeyboardLayouts.create_browse_keyboard()

                # Send the error message
                await callback.message.answer(
                    error_msg.build(add_watermark=True),
                    reply_markup=keyboard,
                    parse_mode="HTML"
                )

        except Exception as e:
            logger.error(f"Error adding to cart: {e}")
            await callback.answer("❌ Error adding to cart", show_alert=True)
            
            # Create a proper error message for unexpected errors
            error_msg = create_message(MessageType.ERROR)
            error_msg.set_title("💥 Unexpected Error", "❌")
            
            # Add main error content
            error_msg.add_content(
                "An unexpected error occurred while adding the item to your cart.\n\n"
                "Our team has been notified and is working to resolve this issue."
            )
            
            # Add troubleshooting section
            error_msg.add_list_section(
                "🔧 What You Can Do",
                [
                    "Try adding the item again in a few moments",
                    "Check your internet connection",
                    "Browse other items while we fix this",
                    "Contact support if the problem continues"
                ],
                "💡"
            )
            
            # Add technical details (for debugging)
            error_msg.add_section(
                "🔍 Technical Details",
                f"Error ID: {hash(str(e)) % 10000:04d}\n"
                f"Time: {datetime.now().strftime('%H:%M:%S UTC')}",
                "⚙️"
            )

            # Create keyboard for error recovery
            keyboard = SmartKeyboardLayouts.create_browse_keyboard()

            # Send the error message
            await callback.message.answer(
                error_msg.build(add_watermark=True),
                reply_markup=keyboard,
                parse_mode="HTML"
            )

    async def cb_cache_debug(self, callback: CallbackQuery) -> None:
        """Handle cache debug callback - show cache statistics"""
        try:
            user_id = self._get_user_id(callback)
            
            # Get cache statistics
            cache_stats = self._get_cache_stats(user_id)
            
            # Build debug message
            message_builder = create_message(MessageType.INFO)
            message_builder.set_title("Cache Debug Information", "🔍")
            
            if cache_stats.get("status") == "no_cache":
                message_builder.add_content("No cache data available for this user.")
            else:
                message_builder.add_content("Current cache status:")
                message_builder.add_section("Cache Info", 
                    f"• Status: {cache_stats.get('status', 'unknown')}\n"
                    f"• Total Cards: {cache_stats.get('total_cards', 0)}\n"
                    f"• Total Count: {cache_stats.get('total_count', 0)}\n"
                    f"• API Version: {cache_stats.get('api_version', 'unknown')}\n"
                    f"• Age: {cache_stats.get('age_minutes', 0)} minutes\n"
                    f"• Has More Data: {cache_stats.get('has_more_data', False)}", 
                    "📊")
                
                fetched_api_pages = cache_stats.get('fetched_api_pages', [])
                if fetched_api_pages:
                    message_builder.add_section("Fetched API Pages", 
                        f"Pages: {', '.join(map(str, fetched_api_pages))} (100 cards each)", 
                        "📄")
            
            # Add user filter info
            temp_filters = self._get_temp_filters(user_id)
            applied_filters = await self._get_applied_filters(user_id)
            
            if temp_filters or applied_filters:
                message_builder.add_section("Filter Status",
                    f"• Temp Filters: {len(temp_filters)} items\n"
                    f"• Applied Filters: {len(applied_filters)} items",
                    "⚙️")
            
            # Create keyboard
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Refresh Cache", 
                            callback_data="catalog:cache_debug"
                        ),
                        InlineKeyboardButton(
                            text="🧹 Clear Cache", 
                            callback_data="filter:clear"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", 
                            callback_data="menu:browse"
                        )
                    ]
                ]
            )
            
            await ui_manager.edit_message_safely(
                callback,
                message_builder.build(),
                keyboard
            )
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error in cache debug: {e}")
            await callback.answer("❌ Error loading cache info", show_alert=True)


def get_catalog_router() -> Router:
    """Create and return catalog router"""
    router = Router()
    attach_common_middlewares(router)
    handlers = CatalogHandlers()

    # Callback handlers
    router.callback_query.register(handlers.cb_browse_menu, F.data == "menu:browse")
    router.callback_query.register(handlers.cb_search, F.data == "catalog:search")
    router.callback_query.register(handlers.cb_filters, F.data == "catalog:filters")

    # Browse handlers
    router.callback_query.register(
        handlers.cb_browse_filtered, F.data == "catalog:browse_filtered"
    )
    router.callback_query.register(
        handlers.cb_browse_all, F.data == "catalog:browse_all"
    )

    router.callback_query.register(
        handlers.cb_view_cards, F.data.startswith("catalog:view_cards:")
    )
    router.callback_query.register(
        handlers.cb_add_to_cart, F.data.startswith("catalog:add_to_cart:")
    )

    # Debug handlers  
    router.callback_query.register(
        handlers.cb_cache_debug, F.data == "catalog:cache_debug"
    )

    # Filter callbacks
    router.callback_query.register(
        handlers.cb_filter_actions, F.data.startswith("filter:")
    )
    # FSM message handler for manual BIN entry
    router.message.register(
        handlers.message_set_filter_value,
        StateFilter(CatalogHandlers.CatalogFilterStates.WAITING_VALUE),
    )

    logger.debug("Catalog handlers registered")
    return router
