"""
Core shared API components

This module contains the fundamental building blocks for the shared API system:
- Base client classes
- Protocol definitions
- Common exceptions
- Shared constants
"""

from .base_client import BaseAPIClient
from .interfaces import APIClientProtocol, APIConfigProtocol
from .exceptions import (
    SharedAPIException,
    ConfigurationError,
    AuthenticationError,
    HTTPClientError,
    ValidationError,
)
from .constants import (
    DEFAULT_TIMEOUT,
    DEFAULT_MAX_RETRIES,
    DEFAULT_RETRY_DELAY,
    SUPPORTED_HTTP_METHODS,
    SUPPORTED_AUTH_TYPES,
)

__all__ = [
    "BaseAPIClient",
    "APIClientProtocol",
    "APIConfigProtocol", 
    "SharedAPIException",
    "ConfigurationError",
    "AuthenticationError",
    "HTTPClientError",
    "ValidationError",
    "DEFAULT_TIMEOUT",
    "DEFAULT_MAX_RETRIES", 
    "DEFAULT_RETRY_DELAY",
    "SUPPORTED_HTTP_METHODS",
    "SUPPORTED_AUTH_TYPES",
]
