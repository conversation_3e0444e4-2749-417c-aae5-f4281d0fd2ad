"""
API v1 Services Module

Contains all business logic and service implementations for API v1.
Services are organized by functionality and provide clean interfaces
for API operations.

Note: http_client module has been removed as it was deprecated.
Use shared_api.http.client.ConfigurableHTTPClient instead.
"""

from .api_config import *
from .cart_service import CartService, get_cart_service, CartServiceError
from .checkout_processor_service import (
    CheckoutProcessorService,
    get_checkout_processor,
    CheckoutProcessorError
)

__all__ = [
    "api_config",
    "CartService",
    "get_cart_service",
    "CartServiceError",
    "CheckoutProcessorService",
    "get_checkout_processor",
    "CheckoutProcessorError",
]
