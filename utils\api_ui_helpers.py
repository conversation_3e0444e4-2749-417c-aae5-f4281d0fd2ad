"""
UI Helper functions for API Management system
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json

from models.api import (
    APIConfiguration,
    APIHealthStatus,
    APIUsageMetrics,
    APIStatus,
    APIEnvironment,
    AuthenticationType,
    HTTPMethod
)


def format_api_status_emoji(status: APIStatus) -> str:
    """Get emoji for API status"""
    return {
        APIStatus.ACTIVE: "🟢",
        APIStatus.INACTIVE: "🔴",
        APIStatus.ERROR: "⚠️",
        APIStatus.MAINTENANCE: "🔧",
        APIStatus.DEPRECATED: "📋"
    }.get(status, "❓")


def format_environment_emoji(environment: APIEnvironment) -> str:
    """Get emoji for API environment"""
    return {
        APIEnvironment.DEVELOPMENT: "🧪",
        APIEnvironment.STAGING: "🎭",
        APIEnvironment.PRODUCTION: "🚀",
        APIEnvironment.TESTING: "🔬"
    }.get(environment, "❓")


def format_auth_type_emoji(auth_type: AuthenticationType) -> str:
    """Get emoji for authentication type"""
    return {
        AuthenticationType.NONE: "❌",
        AuthenticationType.API_KEY: "🔑",
        AuthenticationType.BEARER_TOKEN: "🎫",
        AuthenticationType.BASIC_AUTH: "🔐",
        AuthenticationType.OAUTH2: "🔒",
        AuthenticationType.CUSTOM_HEADER: "📋"
    }.get(auth_type, "❓")


def format_http_method_emoji(method: HTTPMethod) -> str:
    """Get emoji for HTTP method"""
    return {
        HTTPMethod.GET: "📥",
        HTTPMethod.POST: "📤",
        HTTPMethod.PUT: "🔄",
        HTTPMethod.PATCH: "✏️",
        HTTPMethod.DELETE: "🗑️",
        HTTPMethod.HEAD: "👁️",
        HTTPMethod.OPTIONS: "⚙️"
    }.get(method, "❓")


def format_api_summary(config: APIConfiguration, health: Optional[APIHealthStatus] = None) -> str:
    """Format API configuration summary for display"""
    status_emoji = format_api_status_emoji(config.status)
    env_emoji = format_environment_emoji(config.environment)
    auth_emoji = format_auth_type_emoji(config.authentication.type)
    
    health_status = "🟢 Healthy" if health and health.is_healthy else "🔴 Unhealthy"
    
    return f"""
🔌 <b>{config.name}</b>

📋 <b>Basic Info:</b>
• Description: {config.description or 'N/A'}
• Base URL: <code>{config.base_url}</code>
• Version: {config.version}

🌍 <b>Environment:</b> {env_emoji} {config.environment.value.title()}
📊 <b>Status:</b> {status_emoji} {config.status.value.title()}
🏥 <b>Health:</b> {health_status}
🔐 <b>Auth:</b> {auth_emoji} {config.authentication.type.value.replace('_', ' ').title()}

⏱️ <b>Timeouts:</b>
• Connect: {config.timeout.connect_timeout}s
• Read: {config.timeout.read_timeout}s
• Total: {config.timeout.total_timeout}s

📅 <b>Created:</b> {config.created_at.strftime('%Y-%m-%d %H:%M')}
👤 <b>Created by:</b> {config.created_by}
"""


def format_api_list_item(config: APIConfiguration, index: int) -> str:
    """Format single API item for list display"""
    status_emoji = format_api_status_emoji(config.status)
    env_emoji = format_environment_emoji(config.environment)
    auth_emoji = format_auth_type_emoji(config.authentication.type)
    
    return f"{index}. {status_emoji} <b>{config.name}</b> {env_emoji}\n   📍 {config.base_url}\n   {auth_emoji} {config.authentication.type.value.replace('_', ' ').title()}"


def format_health_status_summary(health: APIHealthStatus) -> str:
    """Format health status summary"""
    status_emoji = "🟢" if health.is_healthy else "🔴"
    
    uptime_color = "🟢" if health.uptime_percentage >= 99 else "🟡" if health.uptime_percentage >= 95 else "🔴"
    
    return f"""
🏥 <b>Health Status</b> {status_emoji}

📊 <b>Current Status:</b>
• Health: {status_emoji} {'Healthy' if health.is_healthy else 'Unhealthy'}
• Last Check: {health.last_check_at.strftime('%Y-%m-%d %H:%M:%S')}
• Response Time: {health.response_time_ms or 'N/A'}ms

📈 <b>Statistics:</b>
• Total Checks: {health.total_checks}
• Total Failures: {health.total_failures}
• Uptime: {uptime_color} {health.uptime_percentage:.2f}%
• Consecutive Failures: {health.consecutive_failures}
• Consecutive Successes: {health.consecutive_successes}
"""


def format_usage_metrics_summary(metrics: List[APIUsageMetrics]) -> str:
    """Format usage metrics summary"""
    if not metrics:
        return "📊 <b>Usage Metrics</b>\n\nNo usage data available."
    
    # Calculate totals
    total_requests = sum(m.total_requests for m in metrics)
    total_successful = sum(m.successful_requests for m in metrics)
    total_failed = sum(m.failed_requests for m in metrics)
    
    avg_response_time = sum(m.avg_response_time_ms for m in metrics) / len(metrics) if metrics else 0
    success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
    
    # Get latest metrics for detailed view
    latest = metrics[0] if metrics else None
    
    text = f"""
📊 <b>Usage Metrics</b> (Last {len(metrics)} periods)

📈 <b>Overview:</b>
• Total Requests: {total_requests:,}
• Successful: {total_successful:,} ({success_rate:.1f}%)
• Failed: {total_failed:,}
• Avg Response Time: {avg_response_time:.1f}ms
"""
    
    if latest:
        text += f"""
📅 <b>Latest Period:</b>
• Period: {latest.period_start.strftime('%Y-%m-%d %H:%M')} - {latest.period_end.strftime('%Y-%m-%d %H:%M')}
• Requests: {latest.total_requests:,}
• Success Rate: {(latest.successful_requests/latest.total_requests*100) if latest.total_requests > 0 else 0:.1f}%
• Avg Response: {latest.avg_response_time_ms:.1f}ms
• P95 Response: {latest.p95_response_time_ms:.1f}ms
• P99 Response: {latest.p99_response_time_ms:.1f}ms

📊 <b>Status Codes:</b>
• 2xx: {latest.status_2xx_count:,}
• 3xx: {latest.status_3xx_count:,}
• 4xx: {latest.status_4xx_count:,}
• 5xx: {latest.status_5xx_count:,}

💾 <b>Data Transfer:</b>
• Sent: {format_bytes(latest.total_bytes_sent)}
• Received: {format_bytes(latest.total_bytes_received)}
"""
    
    return text


def format_bytes(bytes_count: int) -> str:
    """Format bytes in human readable format"""
    if bytes_count < 1024:
        return f"{bytes_count} B"
    elif bytes_count < 1024 * 1024:
        return f"{bytes_count / 1024:.1f} KB"
    elif bytes_count < 1024 * 1024 * 1024:
        return f"{bytes_count / (1024 * 1024):.1f} MB"
    else:
        return f"{bytes_count / (1024 * 1024 * 1024):.1f} GB"


def format_duration(seconds: int) -> str:
    """Format duration in human readable format"""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        return f"{seconds // 60}m {seconds % 60}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}h {minutes}m"


def format_api_test_result(result: Dict[str, Any]) -> str:
    """Format API test result for display"""
    if result["success"]:
        return f"""
✅ <b>API Test Successful</b>

📊 <b>Results:</b>
• Status Code: {result["status_code"]}
• Response Time: {result["response_time_ms"]}ms
• Response Size: {format_bytes(result["response_size_bytes"] or 0)}

🔍 <b>Response Headers:</b>
{format_headers_for_display(result.get("headers", {}))}

The API is responding correctly.
"""
    else:
        return f"""
❌ <b>API Test Failed</b>

📊 <b>Results:</b>
• Status Code: {result["status_code"] or 'N/A'}
• Response Time: {result["response_time_ms"] or 'N/A'}ms
• Error: {result["error_message"]}

Please check the API configuration and try again.
"""


def format_headers_for_display(headers: Dict[str, str], max_headers: int = 5) -> str:
    """Format headers for display, limiting the number shown"""
    if not headers:
        return "• None"
    
    lines = []
    for i, (key, value) in enumerate(headers.items()):
        if i >= max_headers:
            lines.append(f"• ... and {len(headers) - max_headers} more")
            break
        
        # Truncate long values
        display_value = value[:50] + "..." if len(value) > 50 else value
        lines.append(f"• {key}: {display_value}")
    
    return "\n".join(lines)


def format_api_analytics_overview(configs: List[APIConfiguration], health_statuses: Dict[str, APIHealthStatus]) -> str:
    """Format comprehensive API analytics overview"""
    total_apis = len(configs)
    if total_apis == 0:
        return "📊 <b>API Analytics</b>\n\nNo APIs configured yet."
    
    # Status breakdown
    status_counts = {}
    for config in configs:
        status = config.status.value
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Health breakdown
    healthy_count = sum(1 for h in health_statuses.values() if h.is_healthy)
    unhealthy_count = total_apis - healthy_count
    
    # Environment breakdown
    env_counts = {}
    for config in configs:
        env = config.environment.value
        env_counts[env] = env_counts.get(env, 0) + 1
    
    # Auth type breakdown
    auth_counts = {}
    for config in configs:
        auth_type = config.authentication.type.value
        auth_counts[auth_type] = auth_counts.get(auth_type, 0) + 1
    
    text = f"""
📊 <b>API Analytics Dashboard</b>

📈 <b>Overview:</b>
• Total APIs: {total_apis}
• Healthy: {healthy_count} 🟢
• Unhealthy: {unhealthy_count} 🔴
• Health Rate: {(healthy_count/total_apis*100):.1f}%

📊 <b>Status Breakdown:</b>
"""
    
    for status, count in status_counts.items():
        emoji = format_api_status_emoji(APIStatus(status))
        text += f"• {emoji} {status.title()}: {count}\n"
    
    text += f"\n🌍 <b>Environment Breakdown:</b>\n"
    for env, count in env_counts.items():
        emoji = format_environment_emoji(APIEnvironment(env))
        text += f"• {emoji} {env.title()}: {count}\n"
    
    text += f"\n🔐 <b>Authentication Types:</b>\n"
    for auth_type, count in auth_counts.items():
        emoji = format_auth_type_emoji(AuthenticationType(auth_type))
        text += f"• {emoji} {auth_type.replace('_', ' ').title()}: {count}\n"
    
    return text


def validate_api_config_input(field: str, value: str) -> tuple[bool, str]:
    """Validate API configuration input fields"""
    if field == "name":
        if not value or len(value.strip()) < 2:
            return False, "API name must be at least 2 characters long"
        if len(value) > 100:
            return False, "API name must be less than 100 characters"
        return True, ""
    
    elif field == "base_url":
        if not value.startswith(('http://', 'https://')):
            return False, "Base URL must start with http:// or https://"
        if len(value) > 500:
            return False, "Base URL is too long"
        return True, ""
    
    elif field == "description":
        if len(value) > 1000:
            return False, "Description must be less than 1000 characters"
        return True, ""
    
    elif field == "version":
        if len(value) > 20:
            return False, "Version must be less than 20 characters"
        return True, ""
    
    elif field == "timeout":
        try:
            timeout_val = int(value)
            if timeout_val < 1 or timeout_val > 300:
                return False, "Timeout must be between 1 and 300 seconds"
            return True, ""
        except ValueError:
            return False, "Timeout must be a valid number"
    
    elif field == "rate_limit":
        try:
            rate_val = int(value)
            if rate_val < 1 or rate_val > 10000:
                return False, "Rate limit must be between 1 and 10000 requests"
            return True, ""
        except ValueError:
            return False, "Rate limit must be a valid number"
    
    elif field == "custom_headers":
        try:
            json.loads(value)
            return True, ""
        except json.JSONDecodeError:
            return False, "Custom headers must be valid JSON format"
    
    return True, ""


def format_api_creation_progress(step: int, total_steps: int, data: Dict[str, Any]) -> str:
    """Format API creation progress display"""
    progress_bar = "█" * step + "░" * (total_steps - step)
    
    text = f"➕ <b>Creating New API</b>\n\n[{progress_bar}] Step {step}/{total_steps}\n\n"
    
    if step >= 1 and "api_name" in data:
        text += f"✅ Name: <b>{data['api_name']}</b>\n"
    
    if step >= 2 and "api_description" in data:
        desc = data['api_description'] or "No description"
        text += f"✅ Description: {desc}\n"
    
    if step >= 3 and "api_base_url" in data:
        text += f"✅ Base URL: <code>{data['api_base_url']}</code>\n"
    
    if step >= 4 and "api_environment" in data:
        env_emoji = format_environment_emoji(APIEnvironment(data['api_environment']))
        text += f"✅ Environment: {env_emoji} {data['api_environment'].title()}\n"
    
    if step >= 5 and "api_auth_type" in data:
        auth_emoji = format_auth_type_emoji(AuthenticationType(data['api_auth_type']))
        text += f"✅ Auth Type: {auth_emoji} {data['api_auth_type'].replace('_', ' ').title()}\n"
    
    return text


def format_bulk_operation_summary(operation: str, api_names: List[str], success_count: int) -> str:
    """Format bulk operation summary"""
    total_count = len(api_names)
    failed_count = total_count - success_count
    
    operation_emoji = {
        "activate": "🟢",
        "deactivate": "🔴",
        "delete": "🗑️",
        "test": "🧪",
        "export": "📤"
    }.get(operation, "⚙️")
    
    text = f"""
{operation_emoji} <b>Bulk {operation.title()} Results</b>

📊 <b>Summary:</b>
• Total APIs: {total_count}
• Successful: {success_count} ✅
• Failed: {failed_count} ❌
• Success Rate: {(success_count/total_count*100) if total_count > 0 else 0:.1f}%

📋 <b>Affected APIs:</b>
"""
    
    for i, name in enumerate(api_names[:10]):  # Show max 10 names
        text += f"• {name}\n"
    
    if len(api_names) > 10:
        text += f"• ... and {len(api_names) - 10} more\n"
    
    return text


def format_search_results_summary(query: str, configs: List[APIConfiguration], total: int) -> str:
    """Format search results summary"""
    if not configs:
        return f"🔍 <b>Search Results</b>\n\nNo APIs found matching '<i>{query}</i>'."
    
    text = f"🔍 <b>Search Results</b> ({total} found)\n\nQuery: <i>{query}</i>\n\n"
    
    for i, config in enumerate(configs):
        text += format_api_list_item(config, i + 1) + "\n\n"
    
    if total > len(configs):
        text += f"... and {total - len(configs)} more results\n"
    
    return text


def format_api_export_summary(total_exported: int, format_type: str = "json") -> str:
    """Format API export summary"""
    return f"""
📤 <b>API Export Complete</b>

📊 <b>Export Details:</b>
• Total APIs: {total_exported}
• Format: {format_type.upper()}
• Sensitive data: Redacted for security

The export file has been generated and sent to you.
Note: Sensitive authentication data has been redacted for security purposes.
"""


def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text with ellipsis if too long"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def format_error_message(error: str, context: str = "") -> str:
    """Format error message for user display"""
    context_text = f" ({context})" if context else ""
    return f"❌ <b>Error{context_text}</b>\n\n{error}\n\nPlease try again or contact support if the issue persists."
