"""
View-cart flow: reuse authenticated session to GET /cart and
save a JSON snapshot (including parsed sections) for later use.
"""

import json
from urllib.parse import urljoin

import requests

from login import (
    logger,
    REFERER,
    log_request,
    log_response,
    perform_login,
    fetch_login_and_extract,
    prune_cookie_duplicates,
)
from session_manager import (
    get_authenticated_session,
    save_session_cookies,
    is_unauthenticated,
)

# Reuse rich HTML -> JSON extraction from add_to_cart
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _cart_url() -> str:
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "cart")


def get_cart(session: requests.Session) -> requests.Response:
    url = _cart_url()
    logger.info("Fetching cart page: %s", url)
    r = session.get(url, allow_redirects=True, timeout=60)
    log_request(r)
    log_response(r)
    return r


def save_view_cart_result(
    resp: requests.Response, out_path: str = "view_cart_response.json"
) -> str:
    data = {"cart": _response_to_jsonable(resp)}
    
    # Print JSON response to console
    print("\n" + "=" * 60)
    print("VIEW CART RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")
    
    # Save to file
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return out_path


def main() -> None:
    session = get_authenticated_session(logger)
    resp = get_cart(session)

    # The session is guaranteed to be authenticated. If get_cart fails, it's a different issue.
    out = save_view_cart_result(resp)
    logger.info("Saved view-cart JSON to: %s", out)

    # Save updated cookies for continued reuse
    save_session_cookies(session, logger=logger)


if __name__ == "__main__":
    main()
