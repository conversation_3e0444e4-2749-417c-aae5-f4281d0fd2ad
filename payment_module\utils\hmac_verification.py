"""
Comprehensive HMAC Verification System

This module provides complete HMAC signature verification for payment callbacks,
including signature generation, validation, and security features.
"""

import hmac
import hashlib
import json
import logging
import os
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class HMACVerifier:
    """
    Comprehensive HMAC verification system for payment callbacks.
    """
    
    def __init__(self, api_key: str, testing_mode: bool = False):
        """
        Initialize HMAC verifier.
        
        Args:
            api_key: OXA Pay API key for signature verification
            testing_mode: If True, skip HMAC verification for testing
        """
        self.api_key = api_key
        self.testing_mode = testing_mode
        self.verification_log = []
        
    def generate_signature(self, data: Dict[str, Any]) -> str:
        """
        Generate HMAC signature for data.
        
        Args:
            data: Data to sign (dictionary that will be converted to JSON)
            
        Returns:
            HMAC signature as hex string
        """
        try:
            # Convert data to JSON string and encode as bytes
            data_bytes = json.dumps(data, ensure_ascii=True, sort_keys=True).encode("utf-8")
            
            # Generate HMAC signature using SHA512
            signature = hmac.new(
                self.api_key.encode("utf-8"), 
                data_bytes, 
                hashlib.sha512
            ).hexdigest()
            
            logger.debug(f"Generated HMAC signature for data: {signature[:16]}...")
            return signature
            
        except Exception as e:
            logger.error(f"Error generating HMAC signature: {e}")
            raise
    
    def verify_signature(self, data_bytes: bytes, signature: str) -> Tuple[bool, str]:
        """
        Verify HMAC signature from request.
        
        Args:
            data_bytes: Raw request data bytes
            signature: HMAC signature from request header
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if self.testing_mode:
            logger.info("Running in TESTING mode - skipping HMAC verification")
            return True, "Testing mode - verification skipped"
        
        if not signature:
            return False, "Missing HMAC signature"
        
        if not self.api_key:
            return False, "Missing API key for verification"
        
        try:
            # Calculate expected HMAC signature
            calculated_hmac = hmac.new(
                self.api_key.encode("utf-8"), 
                data_bytes, 
                hashlib.sha512
            ).hexdigest()
            
            # Use constant-time comparison to prevent timing attacks
            is_valid = hmac.compare_digest(calculated_hmac, signature)
            
            if is_valid:
                logger.debug("HMAC verification successful")
                self.verification_log.append({
                    "timestamp": datetime.now(),
                    "status": "success",
                    "signature_preview": signature[:16] + "..."
                })
                return True, "HMAC verification successful"
            else:
                logger.warning(f"HMAC verification failed - expected: {calculated_hmac[:16]}..., received: {signature[:16]}...")
                self.verification_log.append({
                    "timestamp": datetime.now(),
                    "status": "failed",
                    "expected_preview": calculated_hmac[:16] + "...",
                    "received_preview": signature[:16] + "..."
                })
                return False, "Invalid HMAC signature"
                
        except Exception as e:
            logger.error(f"Error during HMAC verification: {e}")
            return False, f"HMAC verification error: {str(e)}"
    
    def verify_callback_data(self, request_data: bytes, headers: Dict[str, str]) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Verify callback data with HMAC signature.
        
        Args:
            request_data: Raw request data bytes
            headers: Request headers dictionary
            
        Returns:
            Tuple of (is_valid, error_message, parsed_data)
        """
        try:
            # Get HMAC signature from headers
            hmac_header = headers.get("HMAC") or headers.get("hmac") or headers.get("X-HMAC")
            
            # Verify HMAC signature
            is_valid, error_msg = self.verify_signature(request_data, hmac_header)
            
            if not is_valid:
                return False, error_msg, None
            
            # Parse JSON data
            try:
                parsed_data = json.loads(request_data.decode("utf-8"))
                return True, "Verification successful", parsed_data
            except json.JSONDecodeError as e:
                return False, f"Invalid JSON format: {str(e)}", None
                
        except Exception as e:
            logger.error(f"Error in callback data verification: {e}")
            return False, f"Verification error: {str(e)}", None
    
    def get_verification_stats(self) -> Dict[str, Any]:
        """
        Get verification statistics.
        
        Returns:
            Dictionary with verification statistics
        """
        total_verifications = len(self.verification_log)
        successful_verifications = len([v for v in self.verification_log if v["status"] == "success"])
        failed_verifications = total_verifications - successful_verifications
        
        return {
            "total_verifications": total_verifications,
            "successful_verifications": successful_verifications,
            "failed_verifications": failed_verifications,
            "success_rate": (successful_verifications / total_verifications * 100) if total_verifications > 0 else 0,
            "testing_mode": self.testing_mode,
            "recent_verifications": self.verification_log[-10:] if self.verification_log else []
        }
    
    def reset_stats(self):
        """Reset verification statistics."""
        self.verification_log = []
        logger.info("HMAC verification statistics reset")


def create_hmac_verifier(api_key: Optional[str] = None, testing_mode: bool = False) -> HMACVerifier:
    """
    Create HMAC verifier instance.
    
    Args:
        api_key: OXA Pay API key (if None, will use environment variable)
        testing_mode: If True, skip HMAC verification for testing
        
    Returns:
        HMACVerifier instance
    """
    if api_key is None:
        api_key = os.environ.get("OXA_PAY_API_KEY", "")
    
    if not api_key and not testing_mode:
        logger.warning("No API key provided and not in testing mode")
    
    return HMACVerifier(api_key, testing_mode)


def verify_payment_callback(request_data: bytes, headers: Dict[str, str], api_key: Optional[str] = None) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
    """
    Verify payment callback with HMAC signature.
    
    Args:
        request_data: Raw request data bytes
        headers: Request headers dictionary
        api_key: OXA Pay API key (optional)
        
    Returns:
        Tuple of (is_valid, error_message, parsed_data)
    """
    verifier = create_hmac_verifier(api_key)
    return verifier.verify_callback_data(request_data, headers)


def generate_payment_signature(data: Dict[str, Any], api_key: Optional[str] = None) -> str:
    """
    Generate HMAC signature for payment data.
    
    Args:
        data: Payment data dictionary
        api_key: OXA Pay API key (optional)
        
    Returns:
        HMAC signature as hex string
    """
    verifier = create_hmac_verifier(api_key)
    return verifier.generate_signature(data)


# Flask integration helpers
def setup_flask_hmac_verification(app, api_key: Optional[str] = None):
    """
    Setup HMAC verification for Flask app.
    
    Args:
        app: Flask application instance
        api_key: OXA Pay API key (optional)
    """
    verifier = create_hmac_verifier(api_key)
    
    @app.before_request
    def verify_hmac_before_request():
        """Verify HMAC signature before processing request."""
        if request.endpoint == 'handle_callback':
            # Get raw request data
            request_data = request.get_data()
            headers = dict(request.headers)
            
            # Verify HMAC signature
            is_valid, error_msg, parsed_data = verifier.verify_callback_data(request_data, headers)
            
            if not is_valid:
                logger.error(f"HMAC verification failed: {error_msg}")
                return jsonify({"error": error_msg}), 401
            
            # Store verified data in request context
            g.verified_data = parsed_data
            g.hmac_verified = True
    
    return verifier


# Security utilities
def secure_compare(a: str, b: str) -> bool:
    """
    Secure string comparison to prevent timing attacks.
    
    Args:
        a: First string
        b: Second string
        
    Returns:
        True if strings are equal, False otherwise
    """
    return hmac.compare_digest(a, b)


def generate_secure_token(length: int = 32) -> str:
    """
    Generate secure random token.
    
    Args:
        length: Token length in bytes
        
    Returns:
        Secure random token as hex string
    """
    import secrets
    return secrets.token_hex(length)


def hash_sensitive_data(data: str, salt: Optional[str] = None) -> str:
    """
    Hash sensitive data with optional salt.
    
    Args:
        data: Data to hash
        salt: Optional salt for hashing
        
    Returns:
        Hashed data as hex string
    """
    if salt is None:
        salt = os.environ.get("HASH_SALT", "default_salt")
    
    return hashlib.sha256((data + salt).encode()).hexdigest()


# Testing utilities
def create_test_hmac_data(data: Dict[str, Any], api_key: str) -> Tuple[str, str]:
    """
    Create test HMAC data for testing purposes.
    
    Args:
        data: Data to sign
        api_key: API key for signing
        
    Returns:
        Tuple of (signature, json_data)
    """
    verifier = HMACVerifier(api_key, testing_mode=False)
    signature = verifier.generate_signature(data)
    json_data = json.dumps(data, ensure_ascii=True, sort_keys=True)
    return signature, json_data


def validate_hmac_integrity(signature: str, data: str, api_key: str) -> bool:
    """
    Validate HMAC integrity for testing.
    
    Args:
        signature: HMAC signature
        data: JSON data string
        api_key: API key
        
    Returns:
        True if integrity is valid, False otherwise
    """
    verifier = HMACVerifier(api_key, testing_mode=False)
    is_valid, _ = verifier.verify_signature(data.encode("utf-8"), signature)
    return is_valid
