"""
Transaction and Purchase models for MongoDB
"""

from __future__ import annotations

import hashlib
import time
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from pydantic import Field, field_validator, model_validator

from models.base import BaseDocument, TimestampMixin


class TransactionType(str, Enum):
    """Transaction type enumeration"""

    ADD_FUNDS = "ADD_FUNDS"
    PURCHASE = "PURCHASE"
    REFUND = "REFUND"


class PurchaseStatus(str, Enum):
    """Purchase status enumeration"""

    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    BLOCKED = "BLOCKED"
    REFUNDED = "REFUNDED"


class Transaction(BaseDocument):
    """Transaction document model with enhanced tracking"""

    user_id: str = Field(..., description="Reference to User document ID")
    type: TransactionType = Field(..., description="Transaction type")
    amount: float = Field(..., ge=0, description="Transaction amount")
    currency: str = Field(
        default="USD", max_length=8, description="Transaction currency"
    )
    reference: Optional[str] = Field(
        default=None, max_length=128, description="Transaction reference"
    )
    hash: str = Field(default_factory=lambda: "", description="Transaction hash for integrity")

    # Enhanced tracking for purchase-related transactions
    related_purchase_ids: Optional[list[str]] = Field(
        default=None, description="List of related purchase document IDs"
    )
    api_version: Optional[str] = Field(
        default=None, max_length=8, description="API version used (if applicable)"
    )

    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional transaction metadata"
    )

    @field_validator("amount")
    @classmethod
    def validate_amount(cls, v):
        return round(v, 2)

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    @model_validator(mode="after")
    def ensure_hash(self):
        """Ensure hash is always set for transaction integrity."""
        if not self.hash or self.hash.strip() == "":
            # Generate hash from transaction data
            timestamp = time.time()
            random_salt = hashlib.sha256(f"{timestamp}:{id(self)}".encode()).hexdigest()[:8]
            data = f"{self.type}:{self.user_id}:{self.amount}:{self.currency}:{self.reference or ''}:{timestamp}:{random_salt}"
            self.hash = hashlib.sha256(data.encode()).hexdigest()
        return self

    model_config = {"collection_name": "transactions"}


class PurchaseProductType(str, Enum):
    """Product type enumeration for purchases"""

    CARD = "card"
    DUMP = "dump"


class Purchase(BaseDocument):
    """Purchase document model with enhanced API version tracking"""

    user_id: str = Field(..., description="Reference to User document ID")
    sku: str = Field(..., max_length=64, description="Product SKU")
    price: float = Field(..., ge=0, description="Purchase price")
    currency: str = Field(default="USD", max_length=8, description="Purchase currency")
    status: PurchaseStatus = Field(
        default=PurchaseStatus.SUCCESS, description="Purchase status"
    )

    # Enhanced API version tracking
    api_version: str = Field(
        default="v1", max_length=8, description="API version used for this purchase (v1, v2, v3)"
    )
    product_type: PurchaseProductType = Field(
        default=PurchaseProductType.CARD, description="Type of product purchased (card or dump)"
    )

    # External API integration
    api_req_id: Optional[str] = Field(
        default=None, max_length=64, description="External API request ID"
    )
    external_order_id: Optional[str] = Field(
        default=None, max_length=64, description="External order ID from API"
    )
    external_product_id: Optional[str] = Field(
        default=None, max_length=64, description="External product/card/dump ID from API"
    )

    # Order management
    idempotency_key: Optional[str] = Field(
        default=None, max_length=128, description="Idempotency key"
    )

    # Enhanced metadata with structured fields
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional purchase metadata"
    )

    # Complete order data for retrieval (especially important for API v3)
    order_items: Optional[list[Dict[str, Any]]] = Field(
        default=None, description="Complete order items data from checkout response"
    )
    order_metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Complete order metadata from external API response"
    )
    cart_snapshot: Optional[Dict[str, Any]] = Field(
        default=None, description="Cart snapshot at time of purchase for reference"
    )

    # Order state tracking (for cards)
    is_viewed: bool = Field(default=False, description="Whether card details have been viewed")
    viewed_at: Optional[datetime] = Field(default=None, description="When card was first viewed")
    is_checked: bool = Field(default=False, description="Whether card status has been checked")
    checked_at: Optional[datetime] = Field(default=None, description="When card status was checked")
    check_status: Optional[str] = Field(default=None, description="Result of card status check")

    @field_validator("price")
    @classmethod
    def validate_price(cls, v):
        return round(v, 2)

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    @field_validator("api_version")
    @classmethod
    def validate_api_version(cls, v):
        valid_versions = {"v1", "v2", "v3", "base1", "base2", "base3"}
        if v not in valid_versions:
            raise ValueError(f"API version must be one of: {valid_versions}")
        return v.lower()

    def get_api_version_normalized(self) -> str:
        """Get normalized API version (v1, v2, or v3)"""
        version_map = {
            "v1": "v1",
            "v2": "v2",
            "v3": "v3",
            "base1": "v1",
            "base2": "v2",
            "base3": "v3"
        }
        return version_map.get(self.api_version, "v1")

    def is_api_v3(self) -> bool:
        """Check if this purchase was made using API v3"""
        return self.get_api_version_normalized() == "v3"

    def get_product_identifier(self) -> Optional[str]:
        """Get the external product identifier (card_id or dump_id)"""
        return self.external_product_id or (self.metadata or {}).get("card_id") or (self.metadata or {}).get("dump_id")

    def get_order_id(self) -> Optional[str]:
        """Get the external order ID for this purchase"""
        return self.external_order_id or (self.metadata or {}).get("external_order_id")

    def get_complete_order_data(self) -> Dict[str, Any]:
        """Get complete order data for API retrieval"""
        return {
            "order_id": self.get_order_id(),
            "product_id": self.get_product_identifier(),
            "api_version": self.get_api_version_normalized(),
            "product_type": self.product_type.value,
            "order_items": self.order_items or [],
            "order_metadata": self.order_metadata or {},
            "cart_snapshot": self.cart_snapshot or {},
            "purchase_metadata": self.metadata or {}
        }

    def has_sufficient_order_data(self) -> bool:
        """Check if this purchase has sufficient data for order retrieval"""
        return bool(
            self.get_order_id() and
            self.get_product_identifier() and
            self.api_version
        )

    def can_retrieve_card_details(self) -> bool:
        """Check if card details can be retrieved for this purchase"""
        if not self.has_sufficient_order_data():
            return False

        # For API v3, we need order_id to fetch from /orders/{order_id}
        if self.is_api_v3():
            return bool(self.get_order_id())

        # For API v1/v2, we can use various methods
        return True

    model_config = {"collection_name": "purchases"}


class CardDemo(BaseDocument):
    """Demo card document model"""

    purchase_id: str = Field(..., description="Reference to Purchase document ID")
    masked_pan: str = Field(..., max_length=32, description="Masked PAN")
    scheme: Optional[str] = Field(
        default=None, max_length=16, description="Card scheme (VISA, MASTERCARD, etc.)"
    )
    country: Optional[str] = Field(
        default=None, max_length=8, description="Issuing country"
    )
    expiry: Optional[str] = Field(
        default=None, max_length=8, description="Card expiry (MM/YY)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional card metadata"
    )

    @field_validator("masked_pan")
    @classmethod
    def validate_masked_pan(cls, v):
        # Basic validation for masked PAN format
        if not v or len(v) < 8:
            raise ValueError("Masked PAN too short")
        return v

    @field_validator("country")
    @classmethod
    def validate_country(cls, v):
        if v and (len(v) != 2 or not v.isalpha()):
            raise ValueError("Country must be a 2-letter code")
        return v.upper() if v else v

    @field_validator("scheme")
    @classmethod
    def validate_scheme(cls, v):
        if v:
            valid_schemes = {"VISA", "MASTERCARD", "AMEX", "DISCOVER"}
            if v.upper() not in valid_schemes:
                raise ValueError(f"Scheme must be one of: {valid_schemes}")
            return v.upper()
        return v

    model_config = {"collection_name": "cards"}
