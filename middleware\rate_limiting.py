"""
Rate limiting middleware
"""

from __future__ import annotations

import time
from collections import defaultdict
from typing import Any, Awaitable, Callable, Dict

from aiogram.dispatcher.middlewares.base import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery

from config.settings import get_settings

from utils.central_logger import get_logger

logger = get_logger()


class RateLimitMiddleware(BaseMiddleware):
    """Simple in-memory rate limiting middleware (per-user)."""

    def __init__(self):
        self.user_requests = defaultdict(list)
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
        self.settings = get_settings()

    def _cleanup_old_requests(self):
        """Clean up old request timestamps (keep last hour)."""
        current_time = time.time()
        if current_time - self.last_cleanup < self.cleanup_interval:
            return

        cutoff_time = current_time - 3600  # 1 hour
        for user_id in list(self.user_requests.keys()):
            self.user_requests[user_id] = [
                ts for ts in self.user_requests[user_id] if ts > cutoff_time
            ]
            if not self.user_requests[user_id]:
                del self.user_requests[user_id]

        self.last_cleanup = current_time

    def _is_rate_limited(self, user_id: int, limit: int, window: int) -> bool:
        """Check if user exceeded limit within window (seconds)."""
        current_time = time.time()
        cutoff_time = current_time - window

        recent_requests = [ts for ts in self.user_requests[user_id] if ts > cutoff_time]

        return len(recent_requests) >= limit

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any],
    ) -> Any:
        """Apply rate limiting"""
        if not isinstance(event, (Message, CallbackQuery)):
            return await handler(event, data)

        user = event.from_user
        if not user:
            return await handler(event, data)

        # Cleanup periodically
        self._cleanup_old_requests()

        user_id = user.id
        
        # Check for pagination and navigation callbacks - exclude from rate limiting
        is_pagination = False
        if isinstance(event, CallbackQuery) and event.data:
            pagination_patterns = [
                "catalog:view_cards:",
                "browse:page:",
                "nav:",
                "page_",
                "filter:back",  # Filter navigation
                "filter:category:",  # Filter category navigation
                "menu:",  # Menu navigation
            ]
            is_pagination = any(pattern in event.data for pattern in pagination_patterns)
        
        # Check limits (skip for pagination)
        if isinstance(event, Message):
            if self._is_rate_limited(user_id, self.settings.MESSAGES_PER_MINUTE, 60):
                logger.warning(f"Rate limit exceeded for user {user_id}")
                await event.answer("⏳ Please slow down. Try again in a moment.")
                return
        else:  # CallbackQuery
            if not is_pagination and self._is_rate_limited(user_id, self.settings.CALLBACKS_PER_MINUTE, 60):
                logger.warning(f"Callback rate limit exceeded for user {user_id}")
                await event.answer("⏳ Too many clicks. Please wait.", show_alert=True)
                return

        # Record request (but not for pagination to avoid inflating counters)
        if not is_pagination:
            self.user_requests[user_id].append(time.time())
            # Debug logging for rate limiting
            recent_count = len([ts for ts in self.user_requests[user_id] if time.time() - ts < 60])
            if recent_count > 200:  # Log when approaching limit
                logger.info(f"Rate limiting: User {user_id} has {recent_count} callbacks in last minute")

        return await handler(event, data)
