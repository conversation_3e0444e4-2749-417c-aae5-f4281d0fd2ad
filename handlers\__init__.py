"""
Telegram bot handlers setup
"""

from __future__ import annotations

from aiogram import Dispatcher

from handlers.user_handlers import get_user_router
from handlers.enhanced_wallet_handlers import get_enhanced_wallet_router
from handlers.payment_verification_handlers import get_payment_verification_router
from handlers.enhanced_payment_handlers import get_enhanced_payment_router
from handlers.admin_payment_handlers import get_admin_payment_router
from handlers.wallet_manual_verification import get_wallet_manual_verification_router
from handlers.catalog_handlers import get_catalog_router
from handlers.search_handlers import get_search_router
from handlers.admin_handlers import get_admin_router
from handlers.history_handlers import get_history_router
from handlers.cart_handlers import get_cart_router
from handlers.purchase_handlers import get_purchase_router
from handlers.orders_handlers import get_orders_router
from handlers.product_handlers import get_product_router
from handlers.dump_handlers import get_dump_router

# Import admin submodule routers
from admin.handlers import get_response_processor_admin_router

from utils.central_logger import get_logger

logger = get_logger()


def setup_handlers(dp: Dispatcher) -> None:
    """Setup all bot handlers"""
    try:
        # Include feature routers (aiogram 3.x best practice)
        dp.include_router(get_user_router())
        dp.include_router(get_enhanced_wallet_router())  # Enhanced wallet with payment integration
        dp.include_router(get_payment_verification_router())  # Payment verification handlers
        dp.include_router(get_enhanced_payment_router())  # Enhanced payment features (manual verification, analytics, etc.)
        dp.include_router(get_wallet_manual_verification_router())  # Manual payment verification in wallet
        dp.include_router(get_admin_payment_router())  # Admin payment management
        dp.include_router(get_product_router())  # Product selection before catalog
        dp.include_router(get_catalog_router())
        dp.include_router(get_search_router())  # Enhanced search functionality
        dp.include_router(get_cart_router())
        dp.include_router(get_dump_router())  # Dump API handlers
        dp.include_router(get_purchase_router())
        # Include general admin (contains API Configuration UI)
        dp.include_router(get_admin_router())
        
        # Include specialized admin routers
        dp.include_router(get_response_processor_admin_router())
        dp.include_router(get_history_router())
        dp.include_router(get_orders_router())

        logger.info("All handlers ready (including enhanced payment integration and admin controls)")

    except Exception as e:
        logger.error(f"Failed to setup handlers: {e}")
        raise
