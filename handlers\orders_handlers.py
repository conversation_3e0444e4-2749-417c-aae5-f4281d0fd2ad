"""
Optimized Orders-related Telegram handlers (view purchased card details)
Simplified and streamlined for efficient card viewing with minimal redundancy.
"""
from __future__ import annotations
import asyncio
import json
import os
import re
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple
import aiogram
from aiogram import F, Router
from aiogram.types import CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from config.settings import get_settings
from database.connection import get_collection
from middleware import attach_common_middlewares
from services.card_service import CardService
from services.cart_service import CartService
from services.external_api_service import get_external_api_service
from services.user_service import UserService
from utils.card_data_extractor import get_card_data_extractor
from utils.central_logger import get_logger
from utils.decorators import handle_errors
from utils.enhanced_keyboards import (
    ButtonPriority,
    KeyboardStyle,
    create_enhanced_keyboard,
    SmartKeyboardLayouts,
)
from utils.loading_animations import (
    DOWNLOAD_STAGES,
    UNMASK_STAGES,
    CHECK_STAGES,
    ORDER_VIEW_STAGES,
    ORDER_LIST_STAGES,
    CARD_DOWNLOAD_STAGES,
    CARD_CHECK_STAGES,
    RECENT_CARDS_STAGES,
    AnimationConfig,
    LoadingStages,
    get_animation_runner,
)
from utils.post_checkout_ui import (
    CardDetails,
    PostCheckoutUI,
    PaginationHelpers,
    PaginationInfo,
)
from utils.timer_ui import (
    AnimatedMessages,
    TimerConfig,
    TimerEmojis,
    TimerKeyboard,
    TimerManager,
    get_timer_manager,
)
from utils.ui_components import MessageType, create_message, data_formatter
from utils.ui_manager import ui_manager
from utils.card_formatter import (
    CentralizedCardFormatter,
    CardFormattingOptions,
    CardDisplayMode,
    format_card_full,
    format_card_masked,
    format_card_compact,
)

logger = get_logger()


def convert_to_camel_case(text: str) -> str:
    """
    Convert text to proper camel case (Title Case).
    Args:
        text: Input text that may be in various cases
    Returns:
        Text converted to Title Case (first letter of each word capitalized)
    """
    if not text or not isinstance(text, str):
        return text
    # Handle special cases
    if text.upper() in ["N/A", "UNKNOWN", "NULL", "NONE", ""]:
        return text
    # Split by common separators and convert each part
    words = re.split(r"[\s\-_]+", text.strip())
    # Convert each word to title case
    camel_case_words = []
    for word in words:
        if word:
            # Handle acronyms (all caps words with 2+ characters)
            if len(word) > 1 and word.isupper() and word.isalpha():
                camel_case_words.append(word.title())
            else:
                camel_case_words.append(word.title())
    return " ".join(camel_case_words)


def convert_card_data_to_camel_case(card_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert all text fields in card data to camel case for display.
    Args:
        card_data: Dictionary containing card data from API
    Returns:
        Dictionary with text fields converted to camel case
    """
    if not isinstance(card_data, dict):
        return card_data
    converted_data = card_data.copy()
    # Fields that should be converted to camel case
    text_fields = [
        "bank",
        "brand",
        "level",
        "country",
        "type",
        "base",
        "quality",
        "cardholder_name",
        "name",
        "f_name",
        "state",
        "city",
        "continent",
        "address",
        "phone",
        "email",
        "status",
        "card_type",
        "bin_info",
    ]
    for field in text_fields:
        if field in converted_data and converted_data[field]:
            converted_data[field] = convert_to_camel_case(str(converted_data[field]))
    return converted_data


# Use existing services instead of creating redundant classes
def format_card_preview(processed_response: dict, show_validation: bool = True) -> str:
    """
    Format stylish card preview (masked sensitive data).
    
    Delegates to centralized formatter for consistency.
    """
    try:
        if not processed_response.get("success"):
            return create_message(
                MessageType.ERROR, "Card Preview Error", "Unable to load card preview."
            )
        
        data = processed_response.get("processed_data", {})
        
        # Use centralized formatter in MASKED mode
        return format_card_masked(data)
        
    except Exception as e:
        logger.error(f"Error formatting card preview: {e}")
        return create_message(MessageType.ERROR, "Preview Error", f"Error: {str(e)}")


def format_card_api_response(
    processed_response: dict,
    show_sensitive: bool = True,
    compact: bool = False,
    show_issues: bool = True,
) -> str:
    """
    Format card data as full response with all details.
    
    Delegates to centralized formatter for consistency.
    """
    try:
        if not processed_response.get("success"):
            return create_message(
                MessageType.ERROR, "Card Display Error", "Unable to load card details."
            )
        
        data = processed_response.get("processed_data", {})
        
        # Use wrapper that delegates to centralized formatter
        return _format_enhanced_card_view(data, show_sensitive, compact, show_issues)
        
    except Exception as e:
        logger.error(f"Error formatting card API response: {e}")
        return create_message(MessageType.ERROR, "Display Error", f"Error: {str(e)}")


def _format_enhanced_card_view(
    data: dict,
    show_sensitive: bool = True,
    compact: bool = False,
    show_issues: bool = True,
) -> str:
    """
    🎨 Wrapper for centralized card formatter
    
    This function now delegates to the centralized formatter for consistency.
    All card formatting logic is in utils/card_formatter.py
    """
    try:
        # Prepare formatting options
        options = CardFormattingOptions(
            mode=CardDisplayMode.COMPACT if compact else CardDisplayMode.FULL,
            show_sensitive=show_sensitive,
            compact=compact,
            show_timestamps=True,
            show_technical_details=not compact,
            show_location=not compact
        )
        
        # Use centralized formatter
        return CentralizedCardFormatter.format_card_details(data, options)
        
    except Exception as e:
        logger.error(f"Error in _format_enhanced_card_view wrapper: {e}", exc_info=True)
        # Fallback to simple format
        bank = data.get("bank", "Card")
        status = data.get("status", "active")
        status_icon = "✅" if status.lower() in ["active", "live", "valid", "success"] else "❌"
        return f"💳 <b>{bank}</b> {status_icon}\n\n⚠️ Error formatting card details. Please try again."


class OrdersHandlers:
    def __init__(self):
        from services.external_api_service import ExternalAPIService
        from services.order_management_service import get_order_management_service

        self.user_service = UserService()
        self.settings = get_settings()
        self.card_service = CardService(
            external_api_service=ExternalAPIService(api_version="v1")
        )
        self.cart_service = CartService()
        self.external_api = get_external_api_service()
        self.purchases = get_collection("purchases")
        self.order_service = get_order_management_service()
        # Load filter configuration for data normalization
        self.filter_config = self._load_filter_config()
        # Enhanced caching system
        self._card_id_cache: Dict[str, str] = {}
        self._order_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl = 300  # 5 minutes
        # Simple request cache to prevent duplicates
        self._request_cache: Dict[str, Dict[str, Any]] = {}

    def _cache_order(
        self, cache_key: str, order_data: Dict[str, Any], ttl: Optional[int] = None
    ) -> None:
        """Cache order data with timestamp
        Args:
            cache_key: Key for caching the order data
            order_data: The order data to cache
            ttl: Time to live in seconds (currently ignored, uses global _cache_ttl)
        """
        self._order_cache[cache_key] = order_data
        self._cache_timestamps[cache_key] = datetime.now(timezone.utc).timestamp()

    def _get_cached_order(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached order if still valid"""
        if cache_key not in self._order_cache:
            return None
        timestamp = self._cache_timestamps.get(cache_key, 0)
        if datetime.now(timezone.utc).timestamp() - timestamp > self._cache_ttl:
            # Expired cache
            self._order_cache.pop(cache_key, None)
            self._cache_timestamps.pop(cache_key, None)
            return None
        return self._order_cache[cache_key]

    async def _fetch_order_optimized(
        self, user_id: str, order_id: str = None, card_id: str = None
    ) -> Optional[Dict[str, Any]]:
        """Optimized order fetching with caching and single query"""
        cache_key = f"{user_id}:{order_id or 'none'}:{card_id or 'none'}"
        # Check cache first
        cached_order = self._get_cached_order(cache_key)
        if cached_order:
            logger.debug(f"Using cached order for key: {cache_key}")
            return cached_order
        try:
            # Build optimized query that tries multiple conditions at once
            query_conditions = [{"user_id": user_id}]
            if order_id:
                query_conditions.append(
                    {"$or": [{"external_order_id": order_id}, {"_id": order_id}]}
                )
            if card_id:
                query_conditions.append(
                    {
                        "$or": [
                            {"external_product_id": card_id},
                            {"extracted_cards._id": card_id},
                            {"extracted_cards.card_id": card_id},
                        ]
                    }
                )
            # Combine all conditions
            final_query = (
                {"$and": query_conditions}
                if len(query_conditions) > 1
                else query_conditions[0]
            )
            # Single optimized database query
            order_doc = await self.purchases.find_one(
                final_query,
                sort=[("created_at", -1)],  # Get most recent if multiple matches
            )
            if order_doc:
                # Cache the result
                self._cache_order(cache_key, order_doc)
                logger.debug(f"Fetched and cached order: {order_doc.get('_id')}")
                return order_doc
        except Exception as e:
            logger.error(f"Error in optimized order fetch: {e}")
        return None

    async def _get_recent_orders_batch(
        self, user_id: str, lookup_id: str = None
    ) -> List[Dict[str, Any]]:
        """Optimized batch method to get recent orders with single database query."""
        try:
            query = {"user_id": user_id}
            if lookup_id:
                # Try to find specific order first
                specific_query = {
                    "user_id": user_id,
                    "$or": [
                        {"metadata.transaction_id": lookup_id},
                        {"external_order_id": lookup_id},
                        {"_id": lookup_id},
                    ],
                }
                # Single query for specific orders
                specific_orders = (
                    await self.purchases.find(specific_query)
                    .sort("created_at", -1)
                    .limit(10)
                    .to_list(length=10)
                )
                if specific_orders:
                    return specific_orders
            # Fallback: get most recent orders for this user
            recent_orders = (
                await self.purchases.find(query)
                .sort("created_at", -1)
                .limit(6)
                .to_list(length=6)
            )
            return recent_orders
        except Exception as e:
            logger.error(f"Error in _get_recent_orders_batch: {e}")
            return []

    def _create_check_status_header(self, card_data: dict) -> str:
        """Create highlighted header showing check status"""
        from utils.ui_components import create_message, MessageType

        status = card_data.get("status", "Unknown").lower()
        checked_at = card_data.get("checkedAt", "")
        # Determine status icon and message type
        if status == "nonrefundable":
            icon = "✅"
            status_text = "NonRefundable"
            msg_type = MessageType.SUCCESS
            description = "Card was checked and found to be valid. No refund issued."
        elif status == "refunded":
            icon = "💰"
            status_text = "Refunded"
            msg_type = MessageType.WARNING
            description = (
                "Card was checked and declined by issuer. Full purchase amount automatically refunded to your balance. You can purchase another card."
            )
        elif status == "started" or status == "pending":
            icon = "⏳"
            status_text = "Started"
            msg_type = MessageType.INFO
            description = "Card check is in progress or pending."
        else:
            icon = "ℹ️"
            status_text = status.title()
            msg_type = MessageType.INFO
            description = f"Current card status: {status_text}"
        # Create message with highlighted status
        msg = create_message(msg_type)
        msg.set_title(f"🔍 Card Check Complete", icon)
        # Status line with emphasis
        msg.add_content(
            f"<b>━━━━━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>📊 STATUS:</b> {icon} <b>{status_text.upper()}</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━━━━━</b>"
        )
        # Description
        msg.add_content(f"\n{description}")
        # Timestamp if available
        if checked_at:
            msg.add_content(f"\n⏰ <b>Checked:</b> {checked_at[:19]}")
        return msg.build(add_watermark=False)

    def _create_simple_status_message(
        self, extracted_cards: List[dict], card_id: str
    ) -> str:
        """Create clean status message focusing on card status and reasons"""
        try:
            # Find the card data
            card_data = None
            if len(extracted_cards) == 1:
                card_data = extracted_cards[0]
            elif len(extracted_cards) > 1:
                # Try to find matching card
                for card in extracted_cards:
                    card_ids = [
                        card.get("card_id"),
                        card.get("id"),
                        card.get("_id"),
                        card.get("card_number"),
                        card.get("number"),
                    ]
                    if card_id in card_ids or any(
                        card_id == str(cid) for cid in card_ids if cid
                    ):
                        card_data = card
                        break
                # Fallback to first card
                if not card_data:
                    card_data = extracted_cards[0]
            # Clean card display with improved visual layout
            short_card_id = card_id[:8] if len(card_id) > 8 else card_id
            separator = "┄" * 28
            message_parts = []
            message_parts.append("🔍 Card Status")
            message_parts.append(separator)
            message_parts.append(f"💳 Card: {short_card_id}")
            shown_reasons = set()
            if card_data:
                if card_data.get("status"):
                    status = card_data["status"]
                    if status.lower() == "live":
                        message_parts.append("🏷️ Status: ✅ LIVE")
                        message_parts.append("🟢 Card is active and ready")
                    elif status.lower() == "refunded":
                        message_parts.append("🏷️ Status: 💰 REFUNDED")
                        message_parts.append("🔄 Money has been returned")
                        # (Reasons will be shown in the aggregated section below)
                    elif (
                        "expired" in status.lower()
                        or card_data.get("check_status") == "expired"
                    ):
                        message_parts.append("🏷️ Status: ⏰ CHECK EXPIRED")
                        message_parts.append("🚫 Check period has ended")
                    elif "declined" in status.lower() or "dead" in status.lower():
                        message_parts.append("🏷️ Status: ❌ DECLINED")
                        message_parts.append("🚫 Card was rejected")
                        # (Reasons will be shown in the aggregated section below)
                    else:
                        message_parts.append(f"🏷️ Status: ℹ️ {status.upper()}")
                else:
                    message_parts.append("⚠️ STATUS UNKNOWN")
                # Card details section (minimal essential info only)
                if card_data.get("cardholder_name"):
                    message_parts.append("")
                    message_parts.append(f"👤 {card_data['cardholder_name']}")
                # Show expiry if available
                expiry = card_data.get("expiry_date") or card_data.get("expiry")
                if expiry:
                    message_parts.append(f"📅 Expires: {expiry}")
                # Show all available reasons (deduplicated)
                try:
                    possible_reason_keys = [
                        "refund_reason",
                        "decline_reason",
                        "reason",
                        "status_reason",
                        "status_details",
                        "raw_reason",
                        "error",
                        "api_error",
                        "api_message",
                        "message_detail",
                    ]
                    collected_reasons = []
                    seen = set(shown_reasons)
                    for key in possible_reason_keys:
                        val = card_data.get(key)
                        if isinstance(val, str):
                            reason_text = val.strip()
                            if (
                                reason_text
                                and reason_text.lower() != str(status).lower()
                            ):
                                if reason_text.lower() not in seen:
                                    collected_reasons.append(reason_text)
                                    seen.add(reason_text.lower())
                        elif isinstance(val, list):
                            for item in val:
                                if isinstance(item, str):
                                    t = item.strip()
                                    if t and t.lower() not in seen:
                                        collected_reasons.append(t)
                                        seen.add(t.lower())
                    if collected_reasons:
                        message_parts.append("")
                        message_parts.append("📋 Details")
                        message_parts.append(separator)
                        message_parts.append("📝 Reasons:")
                        for r in collected_reasons:
                            message_parts.append(f"• {r}")
                except Exception as _:
                    # Non-critical; skip reasons aggregation on error
                    pass
            else:
                message_parts.append("⚠️ NO DATA AVAILABLE")
            message_parts.append("")
            message_parts.append(separator)
            message_parts.append("✅ Check completed")
            return "\n".join(message_parts)
        except Exception as e:
            logger.error(f"Error creating simple status message: {e}")
            return f"🔍 Card Status Check\n💳 {card_id}\n❌ Check failed"

    def _load_filter_config(self) -> Dict[str, Any]:
        """Load filter configuration for data normalization and validation."""
        try:
            # Try to load from centralized filter data
            from api_v3.utils.filter_manager import get_centralized_filters

            filter_response = get_centralized_filters()
            if filter_response.get("success") and filter_response.get("filters"):
                filter_data = filter_response.get("filters", [])
                # Convert to lookup dictionaries for fast validation
                config = {}
                for filter_group in filter_data:
                    filter_name = filter_group.get("name", "").replace("[]", "")
                    options = filter_group.get("options", [])
                    # Create lookup sets for fast validation
                    config[filter_name] = {
                        "values": {
                            opt["value"].upper() for opt in options if opt.get("value")
                        },
                        "labels": {
                            opt["label"].upper() for opt in options if opt.get("label")
                        },
                        "options": options,
                    }
                logger.info(f"Loaded filter config with {len(config)} filter groups")
                return config
            else:
                # Fallback to basic configuration
                logger.warning(
                    "Filter configuration data not available, using basic config"
                )
                return self._get_basic_filter_config()
        except Exception as e:
            logger.error(f"Error loading filter config: {e}")
            return self._get_basic_filter_config()

    def _get_basic_filter_config(self) -> Dict[str, Any]:
        """Get basic filter configuration as fallback."""
        return {
            "country": {
                "values": {
                    "UNITED STATES",
                    "CANADA",
                    "UNITED KINGDOM",
                    "GERMANY",
                    "FRANCE",
                },
                "labels": {
                    "UNITED STATES",
                    "CANADA",
                    "UNITED KINGDOM",
                    "GERMANY",
                    "FRANCE",
                },
                "options": [],
            },
            "scheme": {
                "values": {"VISA", "MASTERCARD", "AMERICAN EXPRESS", "DISCOVER"},
                "labels": {"VISA", "MASTERCARD", "AMERICAN EXPRESS", "DISCOVER"},
                "options": [],
            },
            "type": {
                "values": {"CREDIT", "DEBIT", "PREPAID"},
                "labels": {"CREDIT", "DEBIT", "PREPAID"},
                "options": [],
            },
            "level": {
                "values": {
                    "CLASSIC",
                    "GOLD",
                    "PLATINUM",
                    "SIGNATURE",
                    "WORLD",
                    "INFINITE",
                },
                "labels": {
                    "CLASSIC",
                    "GOLD",
                    "PLATINUM",
                    "SIGNATURE",
                    "WORLD",
                    "INFINITE",
                },
                "options": [],
            },
        }

    def _get_short_id(self, item_id) -> str:
        """Generate a short, safe ID for callback data to prevent BUTTON_DATA_INVALID errors."""
        import hashlib

        item_id_str = str(item_id)
        # Remove any potentially problematic characters
        safe_item_id = re.sub(r"[^a-zA-Z0-9]", "", item_id_str)
        # If it's short enough and safe, use it directly
        if len(safe_item_id) <= 10 and safe_item_id:
            return safe_item_id
        # Generate a short hash for longer IDs
        short_hash = hashlib.sha256(item_id_str.encode()).hexdigest()[:6]
        self._card_id_cache[short_hash] = item_id_str
        return short_hash

    def _resolve_card_id(self, short_id: str) -> str:
        """Resolve a short ID back to the full card ID."""
        return self._card_id_cache.get(short_id, short_id)
    
    def _find_matching_card_by_id(
        self, 
        cards: List[Dict[str, Any]], 
        target_card_id: str
    ) -> Tuple[Optional[Dict[str, Any]], int]:
        """
        Find a card matching the target_card_id in a list of cards.
        
        IMPORTANT: When matching from database records:
        - Database stores: external_product_id = card's _id from API
        - So we match: target_card_id (from DB external_product_id) against card's _id
        
        Args:
            cards: List of card dictionaries (from API response or extracted_cards)
            target_card_id: The card ID to match (typically from DB's external_product_id)
            
        Returns:
            Tuple of (matched_card, index) or (None, -1) if not found
        """
        if not cards or not target_card_id:
            return None, -1
        
        # Try exact _id match first (most reliable)
        # This handles: DB external_product_id → API card._id
        for idx, card in enumerate(cards):
            card_id = card.get("_id", "")
            if card_id and card_id == target_card_id:
                logger.debug(f"✅ Found matching card at index {idx} by _id={target_card_id[:12] if len(target_card_id) >= 12 else target_card_id}")
                return card, idx
        
        # Try other ID fields as fallback (product_id, external_product_id, id)
        for idx, card in enumerate(cards):
            for id_field in ["product_id", "external_product_id", "id", "productId"]:
                card_id = card.get(id_field, "")
                if card_id and card_id == target_card_id:
                    logger.debug(f"✅ Found matching card at index {idx} by {id_field}={target_card_id[:12] if len(target_card_id) >= 12 else target_card_id}")
                    return card, idx
        
        # If only one card, return it as fallback (single-card checkout)
        if len(cards) == 1:
            logger.warning(f"⚠️ No exact match for card_id={target_card_id[:12] if len(target_card_id) >= 12 else target_card_id}, returning single card as fallback")
            return cards[0], 0
        
        logger.warning(f"⚠️ No matching card found for card_id={target_card_id[:12] if len(target_card_id) >= 12 else target_card_id} in {len(cards)} cards")
        return None, -1
    
    def _get_card_from_order(
        self,
        order: Dict[str, Any],
        target_card_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Extract the correct card data from an order by matching the target_card_id.
        Handles both single-card and multi-card orders.
        
        CRITICAL: Database stores external_product_id which is the card's _id from API.
        So when we search, we're matching: target_card_id (from DB) → card._id (in API response)
        
        Args:
            order: Order document (from database)
            target_card_id: The card ID to match (typically order.external_product_id)
            
        Returns:
            Matching card dictionary or None if not found
        """
        if not order or not target_card_id:
            logger.warning(f"⚠️ _get_card_from_order called with empty order or card_id")
            return None
        
        # Log what we're searching for
        logger.debug(f"🔍 Searching for card with ID: {target_card_id[:12] if len(target_card_id) >= 12 else target_card_id}")
        
        # Check extracted_cards first (most common case)
        extracted_cards = order.get("extracted_cards", [])
        if extracted_cards:
            logger.debug(f"📋 Searching in {len(extracted_cards)} extracted_cards")
            matched_card, idx = self._find_matching_card_by_id(extracted_cards, target_card_id)
            if matched_card:
                logger.info(f"✅ Found target card in extracted_cards at index {idx}")
                return matched_card
            
            # If only one card, use it as fallback (single-card checkout)
            if len(extracted_cards) == 1:
                logger.info(f"📌 Single card in order, using it as fallback")
                return extracted_cards[0]
            
            # Log all card IDs for debugging multi-card mismatches
            card_ids = [c.get("_id", "no_id")[:12] for c in extracted_cards]
            logger.warning(f"⚠️ Target card {target_card_id[:12]} not found in extracted_cards. Available cards: {card_ids}")
        
        # Fallback: try raw_data or api_response
        for data_field in ["raw_data", "api_response"]:
            raw_data = order.get(data_field, {})
            if isinstance(raw_data, dict):
                data_content = raw_data.get("data", raw_data)
                if isinstance(data_content, dict):
                    # Check if this single card matches
                    if data_content.get("_id") == target_card_id:
                        logger.info(f"✅ Found target card in {data_field}")
                        return data_content
                    # Check if it's an array of cards
                    elif isinstance(data_content, list):
                        matched_card, idx = self._find_matching_card_by_id(data_content, target_card_id)
                        if matched_card:
                            logger.info(f"✅ Found target card in {data_field} array at index {idx}")
                            return matched_card
        
        # Final fallback: return first card with warning
        if extracted_cards:
            logger.warning(f"⚠️ Could not find card {target_card_id[:12]} in order, returning first card as last resort")
            return extracted_cards[0]
        
        logger.error(f"❌ No cards found in order for card_id {target_card_id[:12]}")
        return None

    async def _find_full_card_id_in_order(
        self, user_id: str, order_id: str, short_card_id: str
    ) -> Optional[str]:
        """Find the full card ID in order data by searching through extracted cards."""
        try:
            # First, try to get the order by card ID (short_card_id is often the product_id)
            order_doc = await self.order_service.get_order_by_card_id(
                user_id, short_card_id
            )
            # If not found, try by external order ID
            if not order_doc:
                order_doc = await self.order_service.get_order_by_external_id(
                    user_id, order_id
                )
            if not order_doc:
                logger.warning(f"Order {order_id} not found for user {user_id}")
                return None
            # For API v1, the short_card_id is actually the full product_id
            # Return it directly if we found the order
            external_product_id = order_doc.get("external_product_id")
            if external_product_id:
                logger.info(f"Found card ID from order: {external_product_id}")
                return str(external_product_id)
            # Fallback: Search through extracted cards
            extracted_cards = order_doc.get("extracted_cards", [])
            for card in extracted_cards:
                # Check various ID fields
                card_id_fields = [
                    card.get("_id"),
                    card.get("product_id"),
                    card.get("id"),
                ]
                for card_full_id in card_id_fields:
                    if card_full_id and (
                        str(card_full_id) == str(short_card_id)
                        or str(card_full_id).endswith(str(short_card_id))
                    ):
                        logger.info(
                            f"Found matching card ID: {card_full_id} for short: {short_card_id}"
                        )
                        return str(card_full_id)
            logger.warning(
                f"No matching card ID found for short: {short_card_id} in order {order_id}"
            )
            return None
        except Exception as e:
            logger.error(f"Error finding full card ID: {e}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _is_api_v3_order(self, order_data: Dict[str, Any]) -> bool:
        """Check if order is from API v3."""
        if not order_data:
            return False
        api_version = order_data.get("api_version")
        if api_version:
            return api_version.lower() == "v3"
        # Check card ID format
        card_id = (
            order_data.get("external_product_id")
            or order_data.get("metadata", {}).get("card_id")
            or order_data.get("product_id")
        )
        if card_id and isinstance(card_id, str) and not card_id.isdigit():
            return True
        return False

    def _extract_card_data(
        self, order_data: dict, card_id: str = None, endpoint: str = None
    ) -> dict:
        """CENTRALIZED card data extraction using card_extractor.py only."""
        try:
            # Use the centralized card data extractor
            from utils.card_data_extractor import extract_comprehensive_card_data

            # First, check if card data is already stored in the order document
            metadata = order_data.get("metadata", {}) or {}
            stored_card_data = metadata.get("card_data", {})
            # If we have stored card data, use it directly
            if stored_card_data and isinstance(stored_card_data, dict):
                return stored_card_data
            # Check for card_data at the root level
            if "card_data" in order_data and order_data["card_data"]:
                return order_data["card_data"]
            # Use centralized extraction for API response data
            result = extract_comprehensive_card_data(order_data, card_id, endpoint)
            # Ensure we always return a dictionary, not a list
            if isinstance(result, list):
                if result and isinstance(result[0], dict):
                    return result[0]
                else:
                    logger.warning(f"Extracted card data returned empty list")
                    return {
                        "bank": "Unknown Bank",
                        "brand": "",
                        "card_id": card_id or "unknown",
                    }
            elif isinstance(result, dict):
                return result
            else:
                logger.warning(
                    f"Extracted card data returned unexpected type: {type(result)}"
                )
                return {
                    "bank": "Unknown Bank",
                    "brand": "",
                    "card_id": card_id or "unknown",
                }
        except Exception as e:
            logger.error(f"Error using centralized card extractor: {e}")
            # Minimal fallback
            return {
                "card_id": card_id or "unknown",
                "bank": "Unknown Bank",
                "api_version": "v3",
                "_extraction_error": str(e),
            }

    def _parse_api_response(
        self, raw_data: dict, card_id: str, full_response: dict
    ) -> dict:
        """CENTRALIZED API response parsing using card_extractor.py only."""
        try:
            # Use the centralized extraction method from card_extractor
            from utils.card_data_extractor import extract_comprehensive_card_data

            # Try full response first (might have pre-extracted cards)
            result = extract_comprehensive_card_data(
                full_response, card_id, "unmask_full"
            )
            if (
                result and isinstance(result, dict) and len(result) > 2
            ):  # Has meaningful data
                return result
            # Fall back to raw_data if full response didn't work
            result = extract_comprehensive_card_data(raw_data, card_id, "unmask_raw")
            if result and isinstance(result, dict):
                return result
            logger.warning(f"No valid card data extracted for card {card_id}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": "No data extracted",
            }
        except Exception as e:
            logger.error(f"Error parsing API response with extractor: {e}")
            import traceback

            logger.error(f"Full traceback: {traceback.format_exc()}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": str(e),
            }

    def _parse_sections_format(
        self, raw_data: dict, card_id: str, full_response: dict
    ) -> dict:
        """Parse API v3 sections format using centralized extractor."""
        try:
            from utils.card_data_extractor import extract_comprehensive_card_data

            return extract_comprehensive_card_data(raw_data, card_id, "sections")
        except Exception as e:
            logger.error(f"Error parsing sections format: {e}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": str(e),
            }

    def _extract_card_from_table(self, headers: list, rows: list, card_id: str) -> dict:
        """CENTRALIZED table extraction using card_extractor.py only."""
        try:
            from utils.card_data_extractor import extract_from_table_format

            return extract_from_table_format(headers, rows, card_id)
        except Exception as e:
            logger.error(f"Error in centralized table extraction: {e}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": str(e),
            }

    def _extract_from_api_v3_row(self, row: list, headers: list) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _extract_from_api_v3_row - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            return extractor.extract_from_row_data(row, headers)
        except Exception as e:
            logger.error(f"Error in deprecated row extraction: {e}")
            return {"api_version": "v3", "_extraction_error": str(e)}

    def _extract_from_api_v3_row_original(self, row: list, headers: list) -> dict:
        """Extract card data from API v3 row format based on actual response structure."""
        try:
            card_data = {}
            # Based on the actual API response structure:
            # Headers: ["", "Card", "Base", "F. Name", "Bin Info", "Address/Phone/DOB"]
            # Row: [checkbox, card_details, base_info, name, bin_info, contact_info]
            if len(row) >= 6:
                # Extract card details from column 1 (Card)
                card_cell = row[1] if len(row) > 1 else {}
                if isinstance(card_cell, dict):
                    card_text = card_cell.get("text", "")
                elif isinstance(card_cell, str):
                    card_text = card_cell
                else:
                    card_text = str(card_cell)
                # Parse card details: "****************, 10/25, 836 Check Expired!"
                if card_text:
                    parts = card_text.split(", ")
                    if len(parts) >= 3:
                        card_data["card_number"] = parts[0].strip()
                        card_data["expiry"] = parts[1].strip()
                        # Parse CVV and status
                        cvv_status = parts[2].strip()
                        cvv_parts = cvv_status.split(" ")
                        if cvv_parts:
                            card_data["cvv"] = cvv_parts[0].strip()
                            if len(cvv_parts) > 1:
                                card_data["status"] = " ".join(cvv_parts[1:]).strip()
                # Extract base info from column 2 (Base)
                base_cell = row[2] if len(row) > 2 else {}
                if isinstance(base_cell, dict):
                    base_text = base_cell.get("text", "")
                elif isinstance(base_cell, str):
                    base_text = base_cell
                else:
                    base_text = str(base_cell)
                card_data["base"] = base_text
                # Extract cardholder name from column 3 (F. Name)
                name_cell = row[3] if len(row) > 3 else {}
                if isinstance(name_cell, dict):
                    name_text = name_cell.get("text", "")
                elif isinstance(name_cell, str):
                    name_text = name_cell
                else:
                    name_text = str(name_cell)
                card_data["cardholder_name"] = name_text
                # Extract BIN info from column 4 (Bin Info)
                bin_cell = row[4] if len(row) > 4 else {}
                if isinstance(bin_cell, dict):
                    bin_text = bin_cell.get("text", "")
                elif isinstance(bin_cell, str):
                    bin_text = bin_cell
                else:
                    bin_text = str(bin_cell)
                # Parse BIN info: "ITALY , Europe MASTERCARD DEBIT PREPAID INTESA SANPAOLO SPA"
                if bin_text:
                    bin_parts = bin_text.split()
                    if len(bin_parts) >= 1:
                        card_data["country"] = bin_parts[0].replace(",", "").strip()
                    if "MASTERCARD" in bin_text:
                        card_data["brand"] = "MASTERCARD"
                    elif "VISA" in bin_text:
                        card_data["brand"] = "VISA"
                    if "DEBIT" in bin_text:
                        card_data["type"] = "DEBIT"
                    elif "CREDIT" in bin_text:
                        card_data["type"] = "CREDIT"
                    if "PREPAID" in bin_text:
                        card_data["level"] = "PREPAID"
                    # Extract bank name (usually at the end)
                    if "SPA" in bin_text:
                        bank_start = bin_text.find("INTESA")
                        if bank_start != -1:
                            card_data["bank"] = bin_text[bank_start:].strip()
                # Extract contact info from column 5 (Address/Phone/DOB)
                contact_cell = row[5] if len(row) > 5 else {}
                if isinstance(contact_cell, dict):
                    contact_text = contact_cell.get("text", "")
                elif isinstance(contact_cell, str):
                    contact_text = contact_cell
                else:
                    contact_text = str(contact_cell)
                # Parse contact info: "No address +39 ************"
                if contact_text:
                    if "No address" not in contact_text:
                        # Extract address if present
                        address_parts = contact_text.split("+")
                        if len(address_parts) > 1:
                            card_data["address"] = address_parts[0].strip()
                    # Extract phone number
                    phone_match = re.search(r"\+\d+\s*\d+\s*\d+\s*\d+", contact_text)
                    if phone_match:
                        card_data["phone"] = phone_match.group().strip()
            return card_data
        except Exception as e:
            logger.error(f"Error extracting from API v3 row: {e}")
            return {}

    def _extract_card_from_content(self, content: str, card_id: str) -> dict:
        """CENTRALIZED content extraction using card_extractor.py only."""
        try:
            from utils.card_data_extractor import extract_from_content_text

            return extract_from_content_text(content, card_id)
        except Exception as e:
            logger.error(f"Error in centralized content extraction: {e}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": str(e),
            }
        try:
            # This is a fallback method for extracting data from text content
            # Look for common patterns in the content
            card_data = {}
            # Simple pattern matching for common fields
            # Look for card number patterns
            card_pattern = r"(\d{4}[\s\*]*\d{4}[\s\*]*\d{4}[\s\*]*\d{4})"
            card_match = re.search(card_pattern, content)
            if card_match:
                card_data["card_number"] = (
                    card_match.group(1).replace("*", "").replace(" ", "")
                )
            # Look for expiry patterns
            expiry_pattern = r"(\d{2}/\d{2})"
            expiry_match = re.search(expiry_pattern, content)
            if expiry_match:
                card_data["expiry"] = expiry_match.group(1)
            # Look for CVV patterns
            cvv_pattern = r"CVV[:\s]*(\d{3,4})"
            cvv_match = re.search(cvv_pattern, content, re.IGNORECASE)
            if cvv_match:
                card_data["cvv"] = cvv_match.group(1)
            return card_data
        except Exception as e:
            logger.warning(f"Error extracting from content: {e}")
            return {}

    def _normalize_and_validate_data(self, data: dict) -> dict:
        """Normalize and validate extracted data using centralized extractor."""
        try:
            extractor = get_card_data_extractor()
            return extractor.normalize_and_validate_data(data)
        except Exception as e:
            logger.error(f"Error normalizing data: {e}")
            return data

    def _clean_data_for_bson(
        self, data: any, max_depth: int = 10, current_depth: int = 0, visited: set = None
    ) -> any:
        """Clean data structure to prevent BSON encoding errors from circular references or deep nesting."""
        # Initialize visited set on first call to track circular references
        if visited is None:
            visited = set()
        
        # Check depth limit
        if current_depth >= max_depth:
            return "<<MAX_DEPTH_REACHED>>"
        
        # Handle dictionaries
        if isinstance(data, dict):
            # Use object id to detect circular references
            obj_id = id(data)
            if obj_id in visited:
                return "<<CIRCULAR_REFERENCE>>"
            
            visited.add(obj_id)
            clean_dict = {}
            for key, value in data.items():
                try:
                    # Skip keys that might cause circular references
                    if key in ["_parent", "parent", "self", "__dict__", "__class__", "session", "connection", "client"]:
                        continue
                    clean_dict[key] = self._clean_data_for_bson(
                        value, max_depth, current_depth + 1, visited
                    )
                except Exception as e:
                    # If we can't process a value, replace it with a safe string
                    clean_dict[key] = f"<<BSON_ERROR: {str(e)[:50]}>>"
            visited.remove(obj_id)
            return clean_dict
        
        # Handle lists
        elif isinstance(data, list):
            # Use object id to detect circular references in lists
            obj_id = id(data)
            if obj_id in visited:
                return "<<CIRCULAR_REFERENCE>>"
            
            visited.add(obj_id)
            clean_list = []
            for item in data:
                try:
                    clean_list.append(
                        self._clean_data_for_bson(item, max_depth, current_depth + 1, visited)
                    )
                except Exception:
                    clean_list.append("<<BSON_ERROR>>")
            visited.remove(obj_id)
            return clean_list
        
        # Handle primitive types
        elif isinstance(data, (str, int, float, bool)) or data is None:
            return data
        
        # Handle datetime objects (keep as datetime for MongoDB sorting)
        elif isinstance(data, datetime):
            # Keep datetime objects as-is - MongoDB supports them natively
            # This is critical for proper sorting by created_at, updated_at, etc.
            return data
        
        # Handle other types (convert to string as last resort)
        else:
            # For other types, convert to string safely
            try:
                return str(data)
            except Exception:
                return "<<UNCONVERTIBLE_TYPE>>"

    def _extract_from_row(self, row: list, headers: list) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _extract_from_row - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            return extractor.extract_from_row_data(row, headers)
        except Exception as e:
            logger.error(f"Error in deprecated row extraction: {e}")
            return {"api_version": "v3", "_extraction_error": str(e)}
        """Extract data from a single row using centralized extractor."""
        try:
            extractor = get_card_data_extractor()
            return extractor.extract_from_row_data(row, headers)
        except Exception as e:
            logger.warning(f"Error extracting from row: {e}")
            return {}

    def _extract_standard_api_v3_row(self, row: list) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _extract_standard_api_v3_row - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            # Create standard headers for API v3
            standard_headers = [
                "ID",
                "Card",
                "Base",
                "F. Name",
                "Bin Info",
                "Address/Phone/DOB",
                "Price",
            ]
            return extractor.extract_from_row_data(row, standard_headers)
        except Exception as e:
            logger.error(f"Error in deprecated standard row extraction: {e}")
            return {"api_version": "v3", "_extraction_error": str(e)}
        """Extract data from standard API v3 row format (6+ columns)."""
        result = {}
        try:
            # Column 0: Checkbox with card hash
            if len(row) > 0 and isinstance(row[0], dict):
                result["card_hash"] = row[0].get("input_value", "")
            # Column 1: Card details (number, expiry, CVV, status)
            if len(row) > 1 and isinstance(row[1], dict):
                card_text = row[1].get("text", "")
                card_details = self._parse_comprehensive_card_text(card_text)
                result.update(card_details)
            # Column 2: Base information
            if len(row) > 2 and isinstance(row[2], dict):
                base_text = row[2].get("text", "")
                result["base"] = base_text.strip()
                # Extract base type and quality indicators
                result.update(self._parse_base_information(base_text))
            # Column 3: Cardholder name
            if len(row) > 3 and isinstance(row[3], dict):
                name_text = row[3].get("text", "")
                result["cardholder_name"] = name_text.strip()
                result["name"] = name_text.strip()  # Alias
            # Column 4: BIN information (bank, brand, level, country)
            if len(row) > 4 and isinstance(row[4], dict):
                bin_text = row[4].get("text", "")
                result["bin_info"] = bin_text.strip()
                bin_details = self._parse_comprehensive_bin_info(bin_text)
                result.update(bin_details)
            # Column 5: Address and contact information
            if len(row) > 5 and isinstance(row[5], dict):
                contact_text = row[5].get("text", "")
                contact_details = self._parse_comprehensive_contact_info(contact_text)
                result.update(contact_details)
            # Additional columns if present
            if len(row) > 6:
                for i in range(6, len(row)):
                    if isinstance(row[i], dict):
                        additional_text = row[i].get("text", "")
                        if additional_text.strip():
                            result[f"additional_field_{i}"] = additional_text.strip()
            return result
        except Exception as e:
            logger.error(f"Error extracting standard API v3 row: {e}")
            return {}

    def _extract_header_based_row(self, row: list, headers: list) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _extract_header_based_row - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            return extractor.extract_from_row_data(row, headers)
        except Exception as e:
            logger.error(f"Error in deprecated header-based extraction: {e}")
            return {"api_version": "v3", "_extraction_error": str(e)}
        """Extract data using header-based mapping for flexible row formats."""
        result = {}
        try:
            for col_idx, cell in enumerate(row):
                if col_idx >= len(headers):
                    break
                header = headers[col_idx].strip().lower()
                cell_text = (
                    cell.get("text", "").strip()
                    if isinstance(cell, dict)
                    else str(cell).strip()
                )
                # Map headers to extraction methods
                if "card" in header and col_idx > 0:
                    card_details = self._parse_comprehensive_card_text(cell_text)
                    result.update(card_details)
                elif "base" in header:
                    result["base"] = cell_text.replace("⚓", "").strip()
                    result.update(self._parse_base_information(cell_text))
                elif "name" in header or "f. name" in header:
                    if any(
                        kw in cell_text.upper()
                        for kw in ["MASTERCARD", "VISA", "BANK", "CREDIT", "DEBIT"]
                    ):
                        # This is BIN info in name column
                        bin_details = self._parse_comprehensive_bin_info(cell_text)
                        result.update(bin_details)
                    else:
                        result["cardholder_name"] = cell_text
                        result["name"] = cell_text
                elif "bin" in header:
                    result["bin_info"] = cell_text
                    if (
                        "address" in cell_text.lower()
                        or "phone" in cell_text.lower()
                        or "+" in cell_text
                    ):
                        contact_details = self._parse_comprehensive_contact_info(
                            cell_text
                        )
                        result.update(contact_details)
                    else:
                        bin_details = self._parse_comprehensive_bin_info(cell_text)
                        result.update(bin_details)
                elif "address" in header or "phone" in header or "dob" in header:
                    contact_details = self._parse_comprehensive_contact_info(cell_text)
                    result.update(contact_details)
                else:
                    # Store unknown fields for debugging
                    if cell_text and header:
                        result[f"unknown_{header.replace(' ', '_')}"] = cell_text
            return result
        except Exception as e:
            logger.error(f"Error in header-based extraction: {e}")
            return {}

    def _parse_comprehensive_card_text(self, card_text: str) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _parse_comprehensive_card_text - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import extract_from_content_text

            return extract_from_content_text(card_text)
        except Exception as e:
            logger.error(f"Error in deprecated text parsing: {e}")
            return {"api_version": "v3", "_extraction_error": str(e)}
        """Parse comprehensive card text using centralized extractor."""
        try:
            extractor = get_card_data_extractor()
            return extractor.parse_card_text(card_text)
        except Exception as e:
            logger.error(f"Error parsing card text: {e}")
            return {}
            # Split by commas to get different parts
            parts = [part.strip() for part in card_text.split(",")]
            if len(parts) >= 1:
                # First part: Card number (masked or unmasked)
                card_number = parts[0].strip()
                if "*" in card_number:
                    # Masked card number
                    result["card_number"] = card_number
                else:
                    # Check if this is a full card number
                    card_digits = re.sub(r"[^\d]", "", card_number)
                    if len(card_digits) >= 13 and card_digits.isdigit():
                        # Full unmasked card number
                        result["card_number"] = card_digits
                        result["cc"] = card_digits  # Alias for legacy compatibility
                    else:
                        # Partial or formatted number
                        result["card_number"] = card_number
            if len(parts) >= 2:
                # Second part: Expiry date
                expiry_part = parts[1].strip()
                # Remove common suffixes and clean
                expiry_clean = re.sub(r"\s*-\s*$", "", expiry_part)
                if expiry_clean and expiry_clean != "-":
                    result["expiry_date"] = expiry_clean
                    result["expiry"] = expiry_clean
                    result["exp"] = expiry_clean  # Alias
            if len(parts) >= 3:
                # Third part: CVV and/or status information
                cvv_status_part = parts[2].strip()
                # Extract CVV (3-4 digits)
                cvv_match = re.search(r"(\d{3,4})", cvv_status_part)
                if cvv_match:
                    result["cvv"] = cvv_match.group(1)
                # Extract status information
                status_text = cvv_status_part.upper()
                if "LIVE" in status_text:
                    result["status"] = "Live"
                    result["card_status"] = "Live"
                elif "REFUNDED" in status_text:
                    result["status"] = "Refunded"
                    result["card_status"] = "Refunded"
                elif "DEAD" in status_text:
                    result["status"] = "Dead"
                    result["card_status"] = "Dead"
                elif "CHECK" in status_text:
                    result["status"] = "Check Available"
                    result["card_status"] = "Check Available"
                    # Extract check timer if present
                    timer_match = re.search(
                        r"(\d+)\s*seconds?\s*left", status_text.lower()
                    )
                    if timer_match:
                        result["check_timer"] = int(timer_match.group(1))
            # Look for additional status information in any part
            full_text = card_text.upper()
            if "LIVE!" in full_text and "status" not in result:
                result["status"] = "Live"
                result["card_status"] = "Live"
            elif "REFUNDED!" in full_text and "status" not in result:
                result["status"] = "Refunded"
                result["card_status"] = "Refunded"
            return result
        except Exception as e:
            logger.error(f"Error parsing comprehensive card text '{card_text}': {e}")
            return {}

    def _parse_base_information(self, base_text: str) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _parse_base_information - use card_extractor.py directly"
        )
        return {"base": base_text, "api_version": "v3"}
        """Parse base information to extract quality and type indicators."""
        result = {}
        try:
            if not base_text:
                return result
            # Clean the base text
            clean_base = base_text.replace("⚓", "").strip()
            result["base_clean"] = clean_base
            # Extract quality indicators
            base_upper = clean_base.upper()
            # Quality levels
            if "SUPER_VALID" in base_upper:
                result["quality"] = "SUPER_VALID"
                result["quality_score"] = 0.95
            elif "VALID" in base_upper:
                result["quality"] = "VALID"
                result["quality_score"] = 0.85
            elif "MEDIUM" in base_upper:
                result["quality"] = "MEDIUM"
                result["quality_score"] = 0.70
            elif "LOW" in base_upper:
                result["quality"] = "LOW"
                result["quality_score"] = 0.50
            else:
                result["quality"] = "UNKNOWN"
                result["quality_score"] = 0.60
            # Extract features
            features = []
            if "BILL" in base_upper:
                features.append("BILLING")
            if "PHONE" in base_upper:
                features.append("PHONE")
            if "EMAIL" in base_upper:
                features.append("EMAIL")
            if "DOB" in base_upper:
                features.append("DOB")
            if "SSN" in base_upper:
                features.append("SSN")
            if "WORLDMIX" in base_upper:
                features.append("WORLDMIX")
            if "ANCHOR" in base_upper:
                features.append("ANCHOR")
            if features:
                result["features"] = features
                result["features_text"] = ", ".join(features)
            return result
        except Exception as e:
            logger.error(f"Error parsing base information '{base_text}': {e}")
            return {}

    def _parse_comprehensive_bin_info(self, bin_text: str) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _parse_comprehensive_bin_info - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            # Use extractor's scheme parsing
            temp_data = {"raw_scheme": bin_text}
            extractor._parse_scheme_info(temp_data)
            return {
                "bin_text": bin_text,
                "country": temp_data.get("country", ""),
                "brand": temp_data.get("brand", ""),
                "type": temp_data.get("type", ""),
                "level": temp_data.get("level", ""),
                "bank": temp_data.get("bank", ""),
                "api_version": "v3",
            }
        except Exception as e:
            logger.error(f"Error in deprecated BIN parsing: {e}")
            return {"bin_text": bin_text, "api_version": "v3"}
        """Parse comprehensive BIN information using centralized extractor."""
        try:
            extractor = get_card_data_extractor()
            return extractor.parse_bin_info(bin_text)
        except Exception as e:
            logger.error(f"Error parsing BIN info: {e}")
            return {}
            # Extract country information
            countries = []
            # Look for country patterns
            country_patterns = [
                r"\b(UNITED STATES|USA|US)\b",
                r"\b(UNITED KINGDOM|UK|BRITAIN|ENGLAND)\b",
                r"\b(CANADA|CAN)\b",
                r"\b(GERMANY|DEUTSCHLAND|DE)\b",
                r"\b(FRANCE|FR)\b",
                r"\b(ITALY|IT)\b",
                r"\b(SPAIN|ES)\b",
                r"\b(AUSTRALIA|AU)\b",
                r"\b([A-Z]{2,}(?:\s+[A-Z]{2,})*)\b",  # General country pattern
            ]
            for pattern in country_patterns:
                matches = re.findall(pattern, text_upper)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    if len(match) > 2 and match not in [
                        "DEBIT",
                        "CREDIT",
                        "PREPAID",
                        "MASTERCARD",
                        "VISA",
                    ]:
                        countries.append(match)
            if countries:
                result["country"] = countries[0]  # Use first found country
                if len(countries) > 1:
                    result["countries_all"] = countries
            # Extract continent information
            continents = [
                "NORTH AMERICA",
                "SOUTH AMERICA",
                "EUROPE",
                "ASIA",
                "AFRICA",
                "OCEANIA",
            ]
            for continent in continents:
                if continent in text_upper:
                    result["continent"] = continent
                    break
            # Extract card brand/scheme
            brands = [
                "MASTERCARD",
                "VISA",
                "AMERICAN EXPRESS",
                "AMEX",
                "DISCOVER",
                "JCB",
                "DINERS",
                "UNION PAY",
            ]
            for brand in brands:
                if brand in text_upper:
                    result["brand"] = brand
                    result["scheme"] = brand  # Alias
                    break
            # Extract card type
            types = ["CREDIT", "DEBIT", "PREPAID", "CHARGE CARD"]
            for card_type in types:
                if card_type in text_upper:
                    result["card_type"] = card_type
                    result["type"] = card_type  # Alias
                    break
            # Extract card level
            levels = [
                "WORLD ELITE",
                "WORLD BLACK",
                "WORLD",
                "INFINITE",
                "SIGNATURE",
                "PLATINUM",
                "GOLD",
                "CLASSIC",
                "STANDARD",
                "BASIC",
                "PREMIUM",
                "BUSINESS",
                "CORPORATE",
                "COMMERCIAL",
                "PREPAID RELOADABLE",
            ]
            for level in levels:
                if level in text_upper:
                    result["level"] = level
                    break
            # Extract bank name (usually at the end after commas)
            if "," in bin_text:
                parts = bin_text.split(",")
                # Bank name is typically the last meaningful part
                for part in reversed(parts):
                    part_clean = part.strip()
                    if len(part_clean) > 3 and not any(
                        kw in part_clean.upper()
                        for kw in ["DEBIT", "CREDIT", "PREPAID", "MASTERCARD", "VISA"]
                    ):
                        # This might be the bank name
                        bank_candidate = part_clean.title()
                        if (
                            "BANK" in bank_candidate.upper()
                            or "ASSOCIATION" in bank_candidate.upper()
                            or "CREDIT UNION" in bank_candidate.upper()
                        ):
                            result["bank"] = bank_candidate
                            break
            # If no bank found, try to extract from the whole text
            if "bank" not in result:
                bank_patterns = [
                    r"\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:BANK|ASSOCIATION|CREDIT UNION)\b",
                    r"\b([A-Z][A-Z\s]+)(?:,|\s+BANK|\s+ASSOCIATION)",
                ]
                for pattern in bank_patterns:
                    match = re.search(pattern, bin_text)
                    if match:
                        result["bank"] = match.group(1).strip().title()
                        break
            # Set default bank if none found
            if "bank" not in result:
                result["bank"] = "Unknown Bank"
            return result
        except Exception as e:
            logger.error(f"Error parsing comprehensive BIN info '{bin_text}': {e}")
            return {}

    def _parse_comprehensive_contact_info(self, contact_text: str) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _parse_comprehensive_contact_info - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import extract_from_content_text

            return extract_from_content_text(contact_text)
        except Exception as e:
            logger.error(f"Error in deprecated contact parsing: {e}")
            return {"contact_text": contact_text, "api_version": "v3"}
        """Parse comprehensive contact information using centralized extractor."""
        try:
            extractor = get_card_data_extractor()
            return extractor.parse_contact_info(contact_text)
        except Exception as e:
            logger.error(f"Error parsing contact info: {e}")
            return {}

    def _is_unmask_data(self, order: Dict[str, Any]) -> bool:
        """Check if the order contains unmasked card data."""
        try:
            extractor = get_card_data_extractor()
            return extractor.is_unmask_data(order)
        except Exception as e:
            logger.error(f"Error checking unmask data: {e}")
            return False

    def _extract_comprehensive_api_v3_data(
        self, sections: list, card_id: str = None
    ) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _extract_comprehensive_api_v3_data - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import extract_from_sections_response

            api_data = {"sections": sections}
            return extract_from_sections_response(api_data, card_id)
        except Exception as e:
            logger.error(f"Error in deprecated comprehensive extraction: {e}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": str(e),
            }

    def _extract_comprehensive_api_v3_data_original(
        self, order: Dict[str, Any], include_sensitive: bool = False
    ) -> dict:
        """
        Extract comprehensive card data from API v3 order response using centralized extractor.
        """
        try:
            logger.info(
                f"Extracting comprehensive API v3 data, include_sensitive: {include_sensitive}"
            )
            extractor = get_card_data_extractor()
            card_info = extractor.extract_from_order(order)
            # Handle sensitive data based on include_sensitive flag
            if not include_sensitive:
                # Remove sensitive fields if not requested
                sensitive_fields = ["card_number", "cc", "cvv"]
                for field in sensitive_fields:
                    if field in card_info:
                        del card_info[field]
            # Check for direct API v3 fields in order
            api_v3_fields = [
                "cc",
                "exp",
                "cvv",
                "name",
                "email",
                "phone",
                "address",
                "bank",
                "brand",
                "level",
                "country",
                "type",
                "base",
            ]
            for field in api_v3_fields:
                if field in order and order[field]:
                    if field == "cc":
                        if include_sensitive:
                            card_info["card_number"] = order[field]
                        else:
                            # Create masked version when not including sensitive data
                            if len(order[field]) >= 10:
                                card_info["card_number"] = (
                                    order[field][:6] + "**********" + order[field][-4:]
                                )
                    elif field == "exp":
                        card_info["expiry_date"] = order[field]
                        card_info["expiry"] = order[field]
                    elif field == "name":
                        card_info["cardholder_name"] = order[field]
                    else:
                        card_info[field] = order[field]
            card_info["api_version"] = "v3"
            logger.info(
                f"Extracted {len(card_info)} fields from comprehensive API v3 data"
            )
            return card_info
        except Exception as e:
            logger.error(f"Error extracting comprehensive API v3 data: {e}")
            return self._extract_card_data(order)

    def _extract_from_api_v3_response(
        self, response: dict, card_id: str = None
    ) -> dict:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _extract_from_api_v3_response - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import extract_comprehensive_card_data

            return extract_comprehensive_card_data(
                response, card_id, "deprecated_api_v3"
            )
        except Exception as e:
            logger.error(f"Error in deprecated API v3 response extraction: {e}")
            return {
                "card_id": card_id,
                "api_version": "v3",
                "_extraction_error": str(e),
            }

    def _extract_from_api_v3_response_original(
        self, raw_data: dict, include_sensitive: bool = False
    ) -> dict:
        """Extract data from raw API v3 response structure."""
        try:
            result = {}
            # Handle different raw data structures
            if "sections" in raw_data:
                sections = raw_data["sections"]
            elif isinstance(raw_data, list):
                sections = raw_data
            else:
                # Try to find sections in nested structure
                sections = []
                for key, value in raw_data.items():
                    if isinstance(value, dict) and "sections" in value:
                        sections = value["sections"]
                        break
                    elif isinstance(value, list):
                        sections = value
                        break
            # Extract data from sections
            for section in sections:
                if isinstance(section, dict) and "tables" in section:
                    tables = section["tables"]
                    for table in tables:
                        if "header" in table and "rows" in table:
                            headers = table["header"]
                            rows = table["rows"]
                            # Convert headers to simple list if needed
                            if isinstance(headers, list) and headers:
                                if isinstance(headers[0], dict):
                                    headers = [h.get("text", "") for h in headers]
                            # Process each row
                            for row in rows:
                                if isinstance(row, list) and len(row) > 0:
                                    # Extract data from this row
                                    row_data = self._extract_from_row(row, headers)
                                    result.update(row_data)
            logger.info(f"Extracted {len(result)} fields from API v3 response")
            return result
        except Exception as e:
            logger.error(f"Error extracting from API v3 response: {e}")
            return {}

    def _parse_bin_info(self, text: str) -> tuple[str, str, str]:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _parse_bin_info - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            temp_data = {"raw_scheme": text}
            extractor._parse_scheme_info(temp_data)
            return (
                temp_data.get("country", ""),
                temp_data.get("brand", ""),
                temp_data.get("bank", ""),
            )
        except Exception:
            return ("", "", "")
        """Parse BIN information to extract bank, brand, level."""
        try:
            text_upper = text.upper()
            bank = "Unknown Bank"
            brand = ""
            level = ""
            # Extract brand
            if "MASTERCARD" in text_upper:
                brand = "MASTERCARD"
            elif "VISA" in text_upper:
                brand = "VISA"
            elif "AMEX" in text_upper:
                brand = "AMEX"
            # Extract level
            for keyword in [
                "PLATINUM",
                "GOLD",
                "CLASSIC",
                "STANDARD",
                "SIGNATURE",
                "WORLD",
            ]:
                if keyword in text_upper:
                    level = keyword
                    break
            # Extract bank
            if "," in text:
                bank_part = text.split(",")[-1].strip()
                if len(bank_part) > 3:
                    bank = bank_part
            else:
                words = text_upper.split()
                bank_indicators = ["BANK", "CREDIT UNION", "UNION", "FINANCIAL"]
                for i, word in enumerate(words):
                    if any(indicator in word for indicator in bank_indicators):
                        bank_words = words[i:]
                        if bank_words:
                            bank = " ".join(bank_words)
                            break
            if bank != "Unknown Bank":
                bank = " ".join(word.capitalize() for word in bank.split())
            return bank, brand, level
        except Exception:
            return "Unknown Bank", "", ""

    def _parse_scheme_type_level(self, text: str) -> tuple[str, str, str]:
        """DEPRECATED: Use card_extractor.py methods. This delegates to centralized extractor."""
        logger.warning(
            "⚠️ DEPRECATED: _parse_scheme_type_level - use card_extractor.py directly"
        )
        try:
            from utils.card_data_extractor import get_card_data_extractor

            extractor = get_card_data_extractor()
            temp_data = {"raw_scheme": text}
            extractor._parse_scheme_info(temp_data)
            return (
                temp_data.get("brand", ""),
                temp_data.get("type", ""),
                temp_data.get("level", ""),
            )
        except Exception:
            return ("", "", "")
        """Parse scheme/type/level field."""
        try:
            if not text:
                return "Unknown Bank", "", ""
            text_upper = text.upper()
            brand = ""
            level = ""
            bank = "Unknown Bank"
            if "MASTERCARD" in text_upper:
                brand = "MASTERCARD"
            elif "VISA" in text_upper:
                brand = "VISA"
            elif "AMEX" in text_upper:
                brand = "AMEX"
            level_keywords = [
                "PLATINUM",
                "GOLD",
                "CLASSIC",
                "STANDARD",
                "SIGNATURE",
                "WORLD",
            ]
            for keyword in level_keywords:
                if keyword in text_upper:
                    level = keyword
                    break
            if "," in text:
                bank_part = text.split(",")[-1].strip()
                if len(bank_part) > 3:
                    bank = bank_part
            else:
                words = text_upper.split()
                skip_words = [
                    "VISA",
                    "MASTERCARD",
                    "AMEX",
                    "CREDIT",
                    "DEBIT",
                    "PREPAID",
                ]
                remaining = [
                    w for w in words if w not in skip_words and w not in level_keywords
                ]
                if remaining:
                    bank = " ".join(remaining)
            if bank != "Unknown Bank":
                bank = " ".join(word.capitalize() for word in bank.split())
            return bank, brand, level
        except Exception:
            return "Unknown Bank", "", ""

    def _determine_card_state(self, order: Dict[str, Any]) -> str:
        """Always return 'already_checked' to show full card data immediately (preview mode removed)."""
        # CHANGED: Always return 'already_checked' to bypass preview mode
        # and show full card data directly from the API
        return "already_checked"

    def _get_cached_card_order(
        self, user_id: str, card_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached order if valid."""
        cache_key = f"{user_id}:{card_id}"
        cache_entry = self._request_cache.get(cache_key)
        if not cache_entry:
            return None
        timestamp = cache_entry.get("timestamp", 0)
        current_time = datetime.now(timezone.utc).timestamp()
        if (current_time - timestamp) < self._cache_ttl:
            logger.debug(f"Cache hit for {card_id[:8]}...")
            return cache_entry.get("data")
        return None

    def _cache_order_data(
        self, user_id: str, card_id: str, data: Dict[str, Any]
    ) -> None:
        """Cache order data."""
        cache_key = f"{user_id}:{card_id}"
        self._request_cache[cache_key] = {
            "timestamp": datetime.now(timezone.utc).timestamp(),
            "data": data,
        }

    async def _get_api_v3_order_service(self):
        """Get API v3 service if available."""
        if hasattr(self, "_api_v3_service") and self._api_v3_service:
            return self._api_v3_service
        try:
            from api_v3.services.order_service import APIV3OrderService

            settings = get_settings()
            if settings.EXTERNAL_API_VERSION != "v3":
                logger.debug(f"API v3 service not initialized: EXTERNAL_API_VERSION is '{settings.EXTERNAL_API_VERSION}', not 'v3'")
                return None
            base_url = getattr(settings, "EXTERNAL_V3_BASE_URL", "")
            username = getattr(settings, "EXTERNAL_V3_USERNAME", "")
            password = getattr(settings, "EXTERNAL_V3_PASSWORD", "")
            if not all([base_url, username, password]):
                logger.debug(f"API v3 service not initialized: Missing credentials (base_url={bool(base_url)}, username={bool(username)}, password={bool(password)})")
                return None
            self._api_v3_service = APIV3OrderService(
                base_url=base_url, username=username, password=password
            )
            logger.info(f"API v3 service initialized successfully")
            return self._api_v3_service
        except Exception as e:
            logger.warning(f"Failed to initialize API v3 service: {e}")
            return None

    async def _fetch_order_for_card(self, user_id: str, card_id: str) -> dict | None:
        """Efficiently fetch order data for a card."""
        logger.debug(
            f"_fetch_order_for_card called: user_id={user_id}, card_id={card_id}"
        )
        try:
            # Check cache first
            logger.debug(f"Checking cache for order data")
            cached_data = self._get_cached_card_order(user_id, card_id)
            if cached_data:
                logger.debug(f"Found cached order data")
                return cached_data
            logger.debug(f"No cached data, fetching from database")
            # Get from database
            doc = await self.order_service.get_order_by_card_id(user_id, card_id)
            if not doc:
                logger.warning(
                    f"No order found in database for card {card_id} - this may be expected for new cards"
                )
                return None
            logger.debug(f"Order found in database: {doc.get('_id', 'unknown')}")
            # Build base order data
            external_order_id = doc.get("external_order_id") or doc.get(
                "metadata", {}
            ).get("external_order_id")
            logger.debug(f"External order ID: {external_order_id}")
            api_version = doc.get("api_version", "v1")
            is_v3 = api_version == "v3" or self._is_api_v3_order(doc)
            logger.debug(f"API version: {api_version}, is_v3: {is_v3}")
            order_data = {
                "_id": str(doc.get("_id")),
                "external_order_id": external_order_id,
                "api_version": api_version,
                "price": doc.get("price", 0.0),
                "status": doc.get("status", "active"),
                "created_at": doc.get("created_at"),
                "createdAt": doc.get("created_at"),
                "product_id": doc.get("external_product_id"),
                "isviewed": doc.get("is_viewed", False),
                "viewedAt": doc.get("viewed_at"),
                "checkedAt": doc.get("checked_at"),
                "canCheck": not doc.get("is_checked", False),
                "refundable": doc.get("status") not in ["REFUNDED", "FAILED"],
                # CRITICAL: Include raw_data if it exists in the database
                "raw_data": doc.get("raw_data"),
            }
            # For API v3, prioritize database data if card has been unmasked or checked
            if is_v3 and external_order_id:
                # Check if card has been unmasked or checked in database
                has_unmasked_data = doc.get("is_unmasked", False)
                has_checked_data = doc.get("is_checked", False)
                extracted_cards = doc.get("extracted_cards", [])
                
                # If card has unmasked/checked data, use database instead of API
                if has_unmasked_data or has_checked_data:
                    logger.info(f"✅ [API v3] Card has {'unmasked' if has_unmasked_data else ''}{'/' if has_unmasked_data and has_checked_data else ''}{'checked' if has_checked_data else ''} data in database - using cached data")
                    if extracted_cards:
                        order_data["extracted_cards"] = extracted_cards
                        order_data["is_unmasked"] = has_unmasked_data
                        order_data["is_checked"] = has_checked_data
                        # Also restore raw_data if available
                        if doc.get("raw_data"):
                            order_data["raw_data"] = doc.get("raw_data")
                        # Skip API call since we have complete data
                        logger.info(f"💾 [API v3] Skipping API call - using complete database data")
                    else:
                        logger.warning(f"⚠️ [API v3] is_unmasked/is_checked flags set but no extracted_cards found")
                        # Try API call as fallback
                        logger.debug(f"Fetching live API v3 data for order {external_order_id}")
                        live_data = await self._fetch_api_v3_data(external_order_id, card_id)
                        if live_data:
                            order_data.update(live_data)
                            order_data["_api_v3_fetch_timestamp"] = datetime.now(timezone.utc).timestamp()
                        else:
                            logger.error(f"❌ [API v3] Failed to fetch live data and no extracted_cards in database")
                else:
                    # Card not unmasked/checked yet - fetch fresh data from API
                    logger.debug(f"Fetching live API v3 data for order {external_order_id}")
                    live_data = await self._fetch_api_v3_data(external_order_id, card_id)
                    if live_data:
                        logger.debug(
                            f"Live API v3 data fetched successfully, {len(live_data)} fields"
                        )
                        logger.debug(f"Live data keys: {list(live_data.keys())}")
                        logger.debug(
                            f"Live data has extracted_cards: {'extracted_cards' in live_data}"
                        )
                        order_data.update(live_data)
                        # Mark when this data was fetched to prevent duplicate calls
                        order_data["_api_v3_fetch_timestamp"] = datetime.now(
                            timezone.utc
                        ).timestamp()
                        logger.debug(
                            f"Order data after update has extracted_cards: {'extracted_cards' in order_data}"
                        )
                    else:
                        logger.warning(
                            f"Failed to fetch live API v3 data for order {external_order_id}, attempting fallback"
                        )
                        # If API fetch failed, check if we have extracted_cards in database
                        if extracted_cards:
                            logger.info(f"✅ [API v3 Fallback] Using extracted_cards from database")
                            order_data["extracted_cards"] = extracted_cards
                        # If no extracted_cards, check if we have raw_data in database
                        elif order_data.get("raw_data"):
                            logger.info(f"✅ [API v3 Fallback] Parsing stored raw_data from database")
                            # Extract card data from stored raw_data
                            card_data_from_stored = self._parse_api_response(
                                order_data["raw_data"], card_id, {}
                            )
                            if card_data_from_stored:
                                order_data.update(card_data_from_stored)
                            else:
                                logger.error(
                                    f"Failed to extract card data from stored raw_data"
                                )
                                return None
                        # Check if we have data in order_metadata (from checkout_response)
                        elif doc.get("order_metadata"):
                            logger.info(f"✅ [API v3 Fallback] Attempting to use order_metadata")
                            order_metadata = doc.get("order_metadata", {})
                            
                            # Try to get extracted_cards from order_metadata
                            metadata_extracted_cards = order_metadata.get("extracted_cards", [])
                            if metadata_extracted_cards:
                                logger.info(f"✅ [API v3 Fallback] Found {len(metadata_extracted_cards)} extracted_cards in order_metadata")
                                order_data["extracted_cards"] = metadata_extracted_cards
                            # Try to get checkout_response with raw_data
                            elif order_metadata.get("checkout_response"):
                                checkout_response = order_metadata.get("checkout_response")
                                if isinstance(checkout_response, dict) and checkout_response.get("raw_data"):
                                    logger.info(f"✅ [API v3 Fallback] Found raw_data in checkout_response")
                                    card_data_from_checkout = self._parse_api_response(
                                        checkout_response["raw_data"], card_id, {}
                                    )
                                    if card_data_from_checkout:
                                        order_data.update(card_data_from_checkout)
                                    else:
                                        logger.warning(f"Could not parse raw_data from checkout_response")
                                        # Still try to show basic order info
                                        order_data["bank"] = "Unknown Bank"
                                        order_data["card_number"] = "Data unavailable - please contact support"
                                else:
                                    logger.warning(f"No usable data in order_metadata")
                                    # Still try to show basic order info
                                    order_data["bank"] = "Unknown Bank"
                                    order_data["card_number"] = "Data unavailable - please contact support"
                            else:
                                logger.warning(f"No extracted_cards or checkout_response in order_metadata")
                                # Still try to show basic order info
                                order_data["bank"] = "Unknown Bank"
                                order_data["card_number"] = "Data unavailable - please contact support"
                        # Final fallback: try legacy metadata field
                        elif doc.get("metadata", {}).get("card_data"):
                            logger.info(f"✅ [API v3 Fallback] Using legacy metadata.card_data")
                            card_data = doc.get("metadata", {}).get("card_data", {})
                            order_data.update(card_data)
                        else:
                            logger.error(f"No stored data available as fallback for API v3 order")
                            # Don't return None - show basic order info instead
                            order_data["bank"] = "Data Unavailable"
                            order_data["card_number"] = "Card details are temporarily unavailable"
                            order_data["_data_unavailable"] = True
                            logger.info(f"Showing basic order info for order without stored data")
            else:
                # For API v1/v2, use stored data
                # Priority 1: Use extracted_cards if available (contains check status)
                extracted_cards = doc.get("extracted_cards", [])
                if extracted_cards:
                    logger.debug(f"✅ [API v1] Loading extracted_cards from database with check status")
                    order_data["extracted_cards"] = extracted_cards
                
                # Priority 2: Use stored raw_data if available
                if not extracted_cards and doc.get("raw_data"):
                    logger.debug(f"✅ [API v1] Using raw_data from database")
                    # raw_data should already be in order_data from line 1881
                    pass
                
                # Priority 3: Legacy metadata card_data
                card_data = doc.get("metadata", {}).get("card_data", {})
                if card_data:
                    order_data.update(card_data)
                    
                if "bank" not in order_data and not extracted_cards:
                    order_data["bank"] = "Unknown Bank"
                    
                # Also copy check status fields from doc to order_data
                order_data["is_checked"] = doc.get("is_checked", False)
                order_data["check_status"] = doc.get("check_status", "")
            # Cache result
            self._cache_order_data(user_id, card_id, order_data)
            return order_data
        except Exception as e:
            logger.error(f"Failed to fetch order for card {card_id}: {e}")
            return None

    async def _fetch_api_v3_data(
        self, external_order_id: str, card_id: str
    ) -> dict | None:
        """Fetch API v3 order data efficiently."""
        logger.debug(
            f"_fetch_api_v3_data called: order_id={external_order_id}, card_id={card_id}"
        )
        # Check for recent API calls to prevent duplicates
        api_cache_key = f"api_v3:{external_order_id}"
        cached_api_response = self._request_cache.get(api_cache_key)
        if cached_api_response:
            timestamp = cached_api_response.get("timestamp", 0)
            current_time = datetime.now(timezone.utc).timestamp()
            # If API call was made within last 10 seconds, return cached result
            if (current_time - timestamp) < 10:
                logger.info(
                    f"Using cached API v3 response for order {external_order_id} (cached {current_time - timestamp:.1f}s ago)"
                )
                return cached_api_response.get("data")
        try:
            logger.debug(f"Getting API v3 service")
            v3_service = await self._get_api_v3_order_service()
            if not v3_service:
                logger.debug(f"API v3 service not available, will use fallback data if available")
                return None
            logger.debug(f"Making API v3 call to view_order")
            response = await v3_service.view_order(order_id=external_order_id)
            logger.debug(
                f"API v3 response received: success={response.get('success') if response else 'None'}"
            )
            if not response or not response.get("success"):
                logger.warning(f"API v3 call for order {external_order_id} returned unsuccessful response")
                return None
            raw_data = response.get("raw_data", {})
            logger.debug(
                f"Raw data extracted, keys: {list(raw_data.keys()) if raw_data else 'None'}"
            )
            logger.debug(f"Raw data has rows: {'rows' in raw_data}")
            logger.debug(f"Raw data has sections: {'sections' in raw_data}")
            # Check for both old format (rows) and new format (sections)
            if not raw_data or ("rows" not in raw_data and "sections" not in raw_data):
                logger.error(f"No raw_data or rows/sections in API v3 response")
                logger.error(
                    f"Available keys in raw_data: {list(raw_data.keys()) if raw_data else 'None'}"
                )
                return None
            logger.debug(f"Parsing API response data")
            # First check if we have pre-extracted cards from the API response
            extracted_cards = response.get("extracted_cards", [])
            if extracted_cards:
                logger.info(
                    f"Using pre-extracted cards from API response: {len(extracted_cards)} cards"
                )
                # Use the first extracted card as the primary card data
                card_data = extracted_cards[0].copy()
                card_data["_extraction_complete"] = True
                card_data["api_version"] = "v3"
                card_data["raw_data"] = raw_data
                card_data[
                    "extracted_cards"
                ] = extracted_cards  # Store all extracted cards
            else:
                # Fallback to parsing if no pre-extracted cards
                logger.debug(f"No pre-extracted cards, falling back to parsing")
                card_data = self._parse_api_response(raw_data, card_id, response)
            if card_data:
                logger.debug(
                    f"Card data processed successfully, {len(card_data)} fields extracted"
                )
                card_data["_extraction_complete"] = True
                card_data["api_version"] = "v3"
                # Store raw_data for future use
                card_data["raw_data"] = raw_data
                # Save raw_data to database so it persists across views
                try:
                    # Clean raw_data for BSON serialization (remove circular references)
                    clean_raw_data = self._clean_data_for_bson(raw_data)
                    purchases_collection = get_collection("purchases")
                    update_data = {
                        "raw_data": clean_raw_data,
                        "last_api_fetch": datetime.now(timezone.utc),
                    }
                    # Also save extracted cards to the database for future access
                    if extracted_cards:
                        clean_extracted_cards = self._clean_data_for_bson(
                            extracted_cards
                        )
                        update_data["extracted_cards"] = clean_extracted_cards
                        logger.info(
                            f"Saving {len(extracted_cards)} extracted cards to database"
                        )
                    await purchases_collection.update_one(
                        {
                            "external_product_id": str(card_id),
                            "external_order_id": external_order_id,
                        },
                        {"$set": update_data},
                    )
                except Exception as db_error:
                    logger.error(f"Failed to save raw_data to database: {db_error}")
                    # Don't fail the request if DB update fails
                # Cache the API response to prevent duplicate calls
                self._request_cache[api_cache_key] = {
                    "timestamp": datetime.now(timezone.utc).timestamp(),
                    "data": card_data,
                }
            else:
                logger.error(
                    f"Failed to extract any card data from API response for card {card_id}"
                )
            return card_data
        except Exception as e:
            logger.error(f"API v3 fetch failed: {e}")
            return None

    async def _get_live_card_data(
        self, card_id: str, order_data: Dict[str, Any], user_id: str
    ) -> Dict[str, Any]:
        """Get live card data from API with comprehensive processing"""
        logger.debug(
            f"_get_live_card_data called: card_id={card_id}, user_id={user_id}"
        )
        try:
            # Check if order data already contains fresh API v3 data
            api_v3_timestamp = order_data.get("_api_v3_fetch_timestamp")
            current_time = datetime.now(timezone.utc).timestamp()
            # If we have fresh API v3 data (within last 30 seconds), use it directly
            if api_v3_timestamp and (current_time - api_v3_timestamp) < 30:
                logger.info(
                    f"Using existing fresh API v3 data for card {card_id} (fetched {current_time - api_v3_timestamp:.1f}s ago)"
                )
                # Use the existing order data which already contains fresh API v3 data
                live_api_data = {
                    k: v
                    for k, v in order_data.items()
                    if not k.startswith("_")
                    and k not in ["user_id", "created_at", "metadata"]
                }
            else:
                # Extract order information for fresh fetch
                external_order_id = order_data.get(
                    "external_order_id"
                ) or order_data.get("metadata", {}).get("external_order_id")
                logger.debug(f"External order ID extracted: {external_order_id}")
                if not external_order_id:
                    logger.error(f"No external order ID found for card {card_id}")
                    return {
                        "success": False,
                        "error": "No external order ID available",
                        "formatted_preview": None,
                        "formatted_full": None,
                    }
                logger.debug(
                    f"Fetching fresh API v3 data for order {external_order_id}, card {card_id}"
                )
                # Fetch fresh API v3 data
                live_api_data = await self._fetch_api_v3_data(
                    external_order_id, card_id
                )
            logger.debug(f"Live API data fetch result: {live_api_data is not None}")
            if not live_api_data:
                logger.error(
                    f"Failed to fetch live API data for order {external_order_id}"
                )
                return {
                    "success": False,
                    "error": "Failed to fetch live API data",
                    "formatted_preview": None,
                    "formatted_full": None,
                }
            # Apply normalization to the live data
            normalized_data = self._normalize_and_validate_data(live_api_data)
            # Create processed response structure
            processed_response = {
                "success": True,
                "card_id": str(card_id),
                "api_version": "v3",
                "processed_data": normalized_data,
                "validation_summary": {"issues_count": 0, "quality_score": 1.0},
            }
            # Format both preview and full views
            formatted_preview = format_card_preview(
                processed_response=processed_response, show_validation=True
            )
            formatted_full = format_card_api_response(
                processed_response=processed_response,
                show_sensitive=True,
                compact=False,
                show_issues=True,
            )
            logger.info(f"Successfully processed live API data for card {card_id}")
            return {
                "success": True,
                "card_id": card_id,
                "api_version": "v3",
                "processed_data": normalized_data,
                "formatted_preview": formatted_preview,
                "formatted_full": formatted_full,
                "validation_summary": {"issues_count": 0, "quality_score": 1.0},
            }
        except Exception as e:
            logger.error(f"Error getting live card data: {e}")
            return {
                "success": False,
                "error": str(e),
                "formatted_preview": None,
                "formatted_full": None,
            }

    @handle_errors(error_message="❌ Failed to view card details", show_alert=True)
    async def cb_view_purchased_card(self, callback: CallbackQuery) -> None:
        """Show card details with simplified flow."""
        try:
            user = callback.from_user
            if not user:
                logger.error(f"User not found in callback")
                await callback.answer("❌ User not found", show_alert=True)
                return
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                logger.error(f"DB user not found for telegram ID {user.id}")
                await callback.answer("❌ User not found", show_alert=True)
                return
            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                logger.error(f"Invalid callback data format: {callback.data}")
                await callback.answer("❌ Invalid request format", show_alert=True)
                return
                await callback.answer("❌ Invalid request", show_alert=True)
                return
            card_id = self._resolve_card_id(parts[2])
            logger.debug(f"Card ID resolved: {card_id}")
            logger.debug(f"Loading message sent to user")
            # Fetch order
            logger.debug(f"Fetching order for card {card_id}, user {db_user.id}")
            order = await self._fetch_order_for_card(str(db_user.id), card_id)
            if not order:
                logger.warning(
                    f"Order not found for card {card_id} - card may not be purchased yet"
                )
                # Create a helpful error message for the user
                error_msg = create_message(MessageType.WARNING)
                error_msg.set_title("Card Not Found", "🔍")
                error_msg.add_content(
                    f"The card <code>{card_id[:8]}</code> was not found in your order history.\n\n"
                    f"This could mean:\n"
                    f"• The card hasn't been purchased yet\n"
                    f"• The order is still being processed\n"
                    f"• The card ID is incorrect"
                )
                error_msg.add_section(
                    "💡 Suggestions",
                    "• Check your order history\n"
                    "• Verify the card ID\n"
                    "• Contact support if you believe this is an error",
                    "🔧",
                )
                await ui_manager.edit_message_safely(
                    callback,
                    error_msg.build(add_watermark=True),
                    create_enhanced_keyboard()
                    .add_button("📜 Orders", "menu:orders", ButtonPriority.PRIMARY)
                    .add_button("🛒 Browse", "menu:browse", ButtonPriority.SECONDARY)
                    .add_navigation_row(
                        back_text="🏠 Main Menu", back_callback="menu:main"
                    )
                    .build(),
                )
                await callback.answer("Card not found in your orders")
                return
            logger.debug(f"Order fetched successfully: {order.get('_id', 'unknown')}")
            api_version = order.get("api_version", "v1")
            logger.debug(f"Order API version: {api_version}")
            external_order_id = order.get("external_order_id", "unknown")
            logger.debug(f"Order external_order_id: {external_order_id}")
            # For API v1, call the view_card endpoint to get fresh data with full card details
            # The view_card endpoint expects the order's _id field (not product_id)
            # From database, we should have stored this as external_order_id
            # Try multiple possible ID fields: external_order_id, _id from order
            # First try to get _id from extracted_cards (most reliable)
            api_order_id = None
            if "extracted_cards" in order and order["extracted_cards"]:
                api_order_id = order["extracted_cards"][0].get("_id")
                if api_order_id:
                    logger.debug(
                        f"[View Card] Using _id from extracted_cards: {api_order_id}"
                    )
            # Fallback to other ID fields
            if not api_order_id:
                api_order_id = (
                    external_order_id
                    or order.get("external_product_id")
                    or order.get("_id")
                    or card_id
                )  # Use card_id as last resort
            
            # Check if card already has unmasked or checked data in database
            has_unmasked_data = False
            has_checked_data = False
            if "extracted_cards" in order and order["extracted_cards"]:
                card_data = order["extracted_cards"][0]
                # Check if card has full unmasked data (cc, cvv)
                if card_data.get("cc") or card_data.get("cvv"):
                    has_unmasked_data = True
                    logger.info(f"✅ [View Card] Card has unmasked data in database - using cached data")
                # Check if card has been checked
                if order.get("is_checked") or card_data.get("is_checked"):
                    has_checked_data = True
                    checked_status = card_data.get("status", "").lower()
                    logger.info(f"✅ [View Card] Card has been checked in database (status: {checked_status}) - using cached data")
            
            # Only make API call if card doesn't have unmasked/checked data
            should_fetch_api = api_version == "v1" and api_order_id and str(api_order_id) != "unknown"
            should_fetch_api = should_fetch_api and not (has_unmasked_data or has_checked_data)
            
            if should_fetch_api:
                logger.info(
                    f"📞 [View Card] Calling view_card endpoint for order _id={api_order_id}"
                )
                logger.debug(
                    f"[View Card] Available IDs: external_order_id={external_order_id}, _id={order.get('_id')}, card_id={card_id}"
                )
                try:
                    # Use the view_card endpoint to get fresh, complete card data
                    # Endpoint: POST /api/cards/hq/order/view with body {"id": order_id}
                    async with get_external_api_service() as api_service:
                        view_response = await api_service.view_card(int(api_order_id))
                        if view_response.success and view_response.data:
                            logger.info(
                                f"✅ [View Card] Retrieved fresh card data from view_card endpoint"
                            )
                            # Extract order data from the response
                            # view_card returns data in {'data': {...}} format
                            order_data_from_api = view_response.data.get("data", {})
                            if order_data_from_api:
                                # Store the API response directly
                                order["raw_data"] = {"data": order_data_from_api}
                                order["api_response"] = {"data": order_data_from_api}
                                # Mark as viewed/unmasked since API v1 view_order returns full data
                                order["is_unmasked"] = True
                                order["is_viewed"] = True
                                # Store the API data directly as extracted_cards
                                order["extracted_cards"] = [order_data_from_api]
                                # Update the database with fresh API data
                                try:
                                    update_data = {
                                        "raw_data": order["raw_data"],
                                        "api_response": order["api_response"],
                                        "extracted_cards": order["extracted_cards"],
                                        "is_unmasked": True,
                                        "is_viewed": True,
                                        "viewed_at": datetime.now(
                                            timezone.utc
                                        ).isoformat(),
                                        "status": order_data_from_api.get(
                                            "status", order.get("status")
                                        ),
                                    }
                                    # Update in database
                                    await self.order_service.update_order_status(
                                        user_id=str(db_user.id),
                                        card_id=card_id,
                                        status=order_data_from_api.get(
                                            "status", "unknown"
                                        ),
                                        additional_data=update_data,
                                    )
                                    logger.info(
                                        f"✅ [View Card] Updated database with fresh API data"
                                    )
                                except Exception as db_error:
                                    logger.error(
                                        f"⚠️ [View Card] Failed to update database: {db_error}"
                                    )
                                    # Continue even if DB update fails
                                logger.debug(
                                    f"API response fields: {list(order_data_from_api.keys())}"
                                )
                            else:
                                logger.warning(
                                    f"⚠️ [View Card] API response contained no order data"
                                )
                        else:
                            error_msg = (
                                view_response.error
                                if hasattr(view_response, "error")
                                else "Unknown error"
                            )
                            logger.warning(
                                f"⚠️ [View Card] view_card endpoint failed: {error_msg}"
                            )
                            logger.info(
                                f"💾 [View Card] Falling back to database data for order {api_order_id}"
                            )
                except Exception as api_error:
                    logger.error(
                        f"❌ [View Card] Unexpected error calling view_order API: {api_error}"
                    )
                    # Continue with database data as fallback
                    import traceback

                    logger.error(f"Traceback: {traceback.format_exc()}")
            elif has_unmasked_data or has_checked_data:
                # Card has cached unmasked/checked data - log that we're using it
                data_type = []
                if has_unmasked_data:
                    data_type.append("unmasked")
                if has_checked_data:
                    data_type.append("checked")
                logger.info(f"💾 [View Card] Using cached {' and '.join(data_type)} data from database - skipping API call")
                # Ensure order has proper flags set for cached data
                order["is_unmasked"] = order.get("is_unmasked", has_unmasked_data)
                order["is_checked"] = order.get("is_checked", has_checked_data)
            
            # Determine if card is unmasked
            is_unmasked = self._is_unmask_data(order)
            
            # For API v1, if we have extracted_cards with card data, it's already unmasked
            # Use the CORRECT card by matching card_id
            if (
                api_version == "v1"
                and "extracted_cards" in order
                and order["extracted_cards"]
            ):
                card_data = self._get_card_from_order(order, card_id)
                if card_data:
                    # If card has cc/cvv/exp fields, it's unmasked
                    if card_data.get("cc") or card_data.get("cvv"):
                        is_unmasked = True
                        logger.info(f"✅ [View Card] API v1 target card {card_id[:12]} is unmasked (has cc/cvv data)")
                else:
                    logger.warning(f"⚠️ [View Card] Could not find target card {card_id[:12]} for API v1")
            
            # For API v3, cards are ONLY unmasked if they have been explicitly unmasked (no asterisks in card number)
            # Use the CORRECT card by matching card_id
            elif api_version == "v3":
                # Check if card actually has unmasked data (not just extracted data)
                if "extracted_cards" in order and order["extracted_cards"]:
                    card_data = self._get_card_from_order(order, card_id)
                    if card_data:
                        card_number = card_data.get("card_number") or card_data.get("cc", "")
                        if card_number:
                            # If card has asterisks, it's masked (not unmasked)
                            has_asterisks = "*" in card_number
                            is_redacted = "[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number
                            if has_asterisks or is_redacted:
                                # Card has masked data - NOT unmasked
                                is_unmasked = False
                                logger.info(f"🔒 [API v3] Target card {card_id[:12]} has masked data (asterisks/redacted) - treating as masked: {card_number[:12]}...")
                            else:
                                # Card number is full (no asterisks) - it's unmasked
                                is_unmasked = True
                                logger.info(f"✅ [API v3] Target card {card_id[:12]} has full unmasked data (no asterisks)")
                        else:
                            # No card number - not unmasked
                            is_unmasked = False
                            logger.info(f"🔒 [API v3] Target card {card_id[:12]} - No card number found - treating as masked")
                    else:
                        logger.warning(f"⚠️ [API v3] Could not find target card {card_id[:12]} in extracted_cards")
                        is_unmasked = False
                else:
                    # No extracted cards - not unmasked
                    is_unmasked = False
                    logger.info(f"🔒 [API v3] No extracted cards - treating as masked")
            
            logger.info(f"🔓 [View Card] Final unmask state: {is_unmasked}")
            # Create card view message
            logger.debug(f"Creating full card view for card {card_id}")
            # DEBUG: Log order state before calling _create_full_card_view
            logger.debug(f"[DEBUG] Order dict before _create_full_card_view:")
            logger.debug(f"  - has extracted_cards: {'extracted_cards' in order}")
            logger.debug(f"  - has raw_data: {'raw_data' in order}")
            logger.debug(f"  - has api_response: {'api_response' in order}")
            logger.debug(f"  - api_version: {order.get('api_version', 'unknown')}")
            if "extracted_cards" in order:
                logger.debug(
                    f"  - extracted_cards length: {len(order['extracted_cards'])}"
                )
                if order["extracted_cards"]:
                    logger.debug(
                        f"  - extracted_cards[0] keys: {list(order['extracted_cards'][0].keys())[:10]}"
                    )
            # Check if card data ACTUALLY has unmasked data (not just the flag)
            # Use the CORRECT card by matching card_id
            actual_is_unmasked = is_unmasked
            if is_unmasked and "extracted_cards" in order and order["extracted_cards"]:
                card_data = self._get_card_from_order(order, card_id)
                if card_data:
                    card_number = card_data.get("card_number") or card_data.get("cc", "")
                    # Check if card number is actually unmasked (has full digits, no asterisks)
                    if card_number:
                        has_asterisks = "*" in card_number
                        is_redacted = "[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number
                        # If card number has asterisks or is redacted, it's NOT actually unmasked
                        if has_asterisks or is_redacted:
                            actual_is_unmasked = False
                            logger.warning(f"⚠️ [View Card] Target card {card_id[:12]} is_unmasked flag set but card data is masked: {card_number[:12]}...")
                    else:
                        actual_is_unmasked = False
                        logger.warning(f"⚠️ [View Card] Target card {card_id[:12]} is_unmasked flag set but no card number found")
                else:
                    actual_is_unmasked = False
                    logger.warning(f"⚠️ [View Card] Could not find target card {card_id[:12]} to verify unmask status")
            
            # 🔥 CRITICAL FIX: Filter extracted_cards to only contain the TARGET card
            # This ensures multi-card orders display the correct card
            if "extracted_cards" in order and order["extracted_cards"]:
                target_card = self._get_card_from_order(order, card_id)
                if target_card:
                    # Replace extracted_cards with ONLY the target card
                    order["extracted_cards"] = [target_card]
                    logger.info(f"✅ [View Card] Filtered extracted_cards to target card {card_id[:12]} for display")
                else:
                    logger.warning(f"⚠️ [View Card] Target card {card_id[:12]} not found, using existing extracted_cards")
            
            message = await self._create_full_card_view(
                order, str(db_user.id), show_sensitive=actual_is_unmasked
            )
            logger.debug(f"Full card view created, length: {len(message)} chars")
            # Create appropriate keyboard based on card state
            # For API v1, extract order_id from fresh API response in extracted_cards
            order_id = None
            if api_version == "v1" and "extracted_cards" in order and order["extracted_cards"]:
                # For API v1, the _id from the API response is the order ID we need
                order_id = order["extracted_cards"][0].get("_id")
                if order_id:
                    logger.debug(f"[View Card] Extracted API v1 order_id from API response: {order_id}")
            # Fallback to database order fields
            if not order_id:
                order_id = order.get("external_order_id", str(order.get("_id")))
            # Use short card ID to prevent BUTTON_DATA_INVALID errors
            short_card_id = self._get_short_id(card_id)
            # Get card status and check timer for proper button display
            check_status = None
            expiry_timestamp = None
            can_check = True
            
            # For API v3, check if we have a valid external_order_id
            if api_version == "v3":
                if not order.get("external_order_id"):
                    logger.warning(f"[API v3] No external_order_id found in database! This order was likely created before the fix. Check functionality will be disabled.")
                    logger.info(f"[API v3] Using fallback order_id: {order_id} (MongoDB _id)")
                    # Disable check for this order since we don't have the proper API order ID
                    can_check = False
                else:
                    logger.info(f"[API v3] Using external_order_id: {order_id}")
            logger.info(
                f"🔧 [View Card] Setting up keyboard - api_version={api_version}, is_unmasked={is_unmasked}"
            )
            # Set up timer-based check button (30 second timer) for both v1 and v3
            if api_version in ["v1", "v3"] and is_unmasked:
                logger.info(
                    f"✅ [View Card] API {api_version} unmasked path - setting up check button"
                )
                # Check if card can be checked (not already checked)
                if "extracted_cards" in order and order["extracted_cards"]:
                    card_data = order["extracted_cards"][0]
                    card_status = card_data.get("status", "").lower()
                    can_check_field = card_data.get("canCheck", 1)
                    logger.info(
                        f"📊 [View Card] Card data: status={card_status}, canCheck={can_check_field}"
                    )
                    
                    # For API v1, prioritize status over canCheck field
                    if api_version == "v1":
                        # Check if card has been checked and if check has expired
                        checked_at = card_data.get("checked_at") or card_data.get("checkedAt") or order.get("checked_at")
                        check_expired = True
                        remaining_time = 0
                        
                        if checked_at:
                            try:
                                # Parse checked_at timestamp
                                if isinstance(checked_at, str):
                                    from dateutil import parser
                                    checked_dt = parser.parse(checked_at)
                                else:
                                    checked_dt = checked_at
                                
                                # Calculate time since check (30 second window)
                                now = datetime.now(timezone.utc)
                                time_since_check = (now - checked_dt).total_seconds()
                                remaining_time = max(0, 30 - time_since_check)
                                check_expired = remaining_time <= 0
                                
                                if not check_expired:
                                    logger.info(f"⏰ [View Card] Check still valid - {remaining_time:.0f} seconds remaining")
                                else:
                                    logger.info(f"⏰ [View Card] Check expired ({time_since_check:.0f} seconds ago)")
                            except Exception as e:
                                logger.warning(f"⚠️ [View Card] Failed to parse checked_at timestamp: {e}")
                        
                        # Cards with final status are already checked - NEVER allow rechecking
                        if card_status in ["nonrefundable", "refunded", "declined", "dead", "invalid", "live", "active", "valid", "approved"]:
                            can_check = False
                            check_status = "completed"
                            logger.info(
                                f"🔒 [View Card] Card already checked with final status '{card_status}' - check button HIDDEN"
                            )
                        # For "Started" or "Pending" status, allow checking without timer
                        # The canCheck field might not be reliable for API v1
                        elif card_status in ["started", "pending", "checking", "processing"]:
                            can_check = True
                            check_status = "checking"  # Shows recheck button without timer
                            expiry_timestamp = None  # No timer for recheck
                            logger.info(
                                f"🔄 [View Card] Card status is '{card_status}' - showing recheck button without timer"
                            )
                        # For other statuses, respect the canCheck field
                        elif can_check_field == 0:
                            can_check = False
                            check_status = None
                            logger.info(
                                f"🔒 [View Card] Card not checkable (canCheck=0, status={card_status})"
                            )
                        else:
                            # Card can be checked - set 30 second timer
                            can_check = True
                            check_status = "active"
                            expiry_timestamp = (
                                int(datetime.now(timezone.utc).timestamp()) + 30
                            )
                            logger.info(
                                f"⏰ [View Card] Setting check timer: 30 seconds (expires at {expiry_timestamp})"
                            )
                    else:
                        # For API v3, check if card already has a final status
                        card_status = (card_data.get("status", "") or "").lower()
                        refund_status = (card_data.get("refund_status", "") or "").lower()
                        
                        # Use refund_status if status is empty
                        if not card_status and refund_status:
                            card_status = refund_status
                        
                        # Cards with final status are already checked - NEVER allow rechecking
                        if card_status in ["nonrefundable", "refunded", "declined", "dead", "invalid", "live", "active", "valid", "approved"]:
                            can_check = False
                            check_status = "completed"
                            logger.info(
                                f"🔒 [View Card] API v3 - Card already checked with final status '{card_status}' - check button HIDDEN"
                            )
                        # For cards still checking, allow recheck
                        elif card_status in ["started", "pending", "checking", "processing"]:
                            can_check = True
                            check_status = "checking"
                            expiry_timestamp = None
                            logger.info(
                                f"🔄 [View Card] API v3 - Card status is '{card_status}' - showing recheck button"
                            )
                        # For cards without a final status, check if there's a recent check
                        else:
                            checked_at = card_data.get("checked_at") or card_data.get("checkedAt") or order.get("checked_at")
                            check_expired = True
                            remaining_time = 0
                            
                            if checked_at:
                                try:
                                    # Parse checked_at timestamp
                                    if isinstance(checked_at, str):
                                        from dateutil import parser
                                        checked_dt = parser.parse(checked_at)
                                    else:
                                        checked_dt = checked_at
                                    
                                    # Calculate time since check (30 second window)
                                    now = datetime.now(timezone.utc)
                                    time_since_check = (now - checked_dt).total_seconds()
                                    remaining_time = max(0, 30 - time_since_check)
                                    check_expired = remaining_time <= 0
                                    
                                    if not check_expired:
                                        logger.info(f"⏰ [View Card] API v3 - Check still valid - {remaining_time:.0f} seconds remaining")
                                    else:
                                        logger.info(f"⏰ [View Card] API v3 - Check expired ({time_since_check:.0f} seconds ago)")
                                except Exception as e:
                                    logger.warning(f"⚠️ [View Card] API v3 - Failed to parse checked_at timestamp: {e}")
                            
                            # Determine if card can be checked based on check expiry
                            if not check_expired and remaining_time > 0:
                                # Check in progress - use remaining time
                                can_check = True
                                check_status = "active"
                                expiry_timestamp = int(datetime.now(timezone.utc).timestamp()) + int(remaining_time)
                                logger.info(f"⏰ [View Card] API v3 - Check in progress: {remaining_time:.0f} seconds remaining")
                            else:
                                # Allow checking with full 30 second timer
                                can_check = True
                                check_status = "active"
                                expiry_timestamp = int(datetime.now(timezone.utc).timestamp()) + 30
                                logger.info(f"⏰ [View Card] API v3 - Setting check timer: 30 seconds (expires at {expiry_timestamp})")
                else:
                    logger.warning(
                        f"⚠️ [View Card] No extracted_cards found for check button setup"
                    )
            elif (api_version == "v1" or api_version == "v3") and not is_unmasked:
                logger.warning(
                    f"⚠️ [View Card] API {api_version} but card not unmasked - check button will not show"
                )
            else:
                # For other API versions (v2, etc), use existing logic
                logger.info(
                    f"ℹ️ [View Card] Non-v1/v3 API ({api_version}) - using default check logic"
                )
                if "extracted_cards" in order and order["extracted_cards"]:
                    card_data = order["extracted_cards"][0]
                    check_status = card_data.get("check_status")
                    if card_data.get("check_timer"):
                        expiry_timestamp = (
                            int(datetime.now(timezone.utc).timestamp())
                            + card_data["check_timer"]
                        )
            # Log keyboard creation parameters
            logger.info(f"⌨️ [View Card] Creating keyboard with:")
            logger.info(f"   - card_id: {short_card_id}")
            logger.info(f"   - order_id: {order_id}")
            logger.info(f"   - can_check: {can_check}")
            logger.info(f"   - is_unmasked: {is_unmasked}")
            logger.info(f"   - check_status: {check_status}")
            logger.info(f"   - expiry_timestamp: {expiry_timestamp}")
            logger.info(f"   - user_id: {str(db_user.id)}")
            # For API v1, let's verify the conditions for check button
            if api_version == "v1":
                current_time = int(datetime.now(timezone.utc).timestamp())
                logger.info(f"🔍 [View Card] Check button conditions for API v1:")
                logger.info(f"   - can_check: {can_check}")
                logger.info(
                    f"   - check_status in ['active', 'available']: {check_status in ['active', 'available']}"
                )
                logger.info(f"   - expiry_timestamp: {expiry_timestamp}")
                logger.info(f"   - current_time: {current_time}")
                if expiry_timestamp:
                    logger.info(
                        f"   - timer valid (current <= expiry): {current_time <= expiry_timestamp}"
                    )
                    logger.info(
                        f"   - remaining time: {expiry_timestamp - current_time}s"
                    )
            # For API v1, create keyboard directly to ensure check button shows
            if api_version == "v1":
                logger.info(f"🔧 [View Card] Creating API v1 keyboard directly")
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                # Primary action: Unmask or Download
                if not is_unmasked:
                    kb.add_button(
                        "🔓 Unmask Card",
                        f"orders:unmask:{order_id}:{short_card_id}",
                        ButtonPriority.PRIMARY,
                    )
                else:
                    kb.add_button(
                        "📥 Download Card Data",
                        f"orders:download:{order_id}:{short_card_id}",
                        ButtonPriority.PRIMARY,
                    )
                # Check button with timer for API v1
                if can_check and check_status == "active" and expiry_timestamp:
                    current_time = int(datetime.now(timezone.utc).timestamp())
                    if current_time <= expiry_timestamp:
                        remaining_time = expiry_timestamp - current_time
                        timer_emoji = "⏰" if remaining_time <= 10 else "🔍"
                        kb.add_button(
                            f"{timer_emoji} Check Status ({remaining_time}s)",
                            f"orders:check:{order_id}:{short_card_id}:{expiry_timestamp}",
                            ButtonPriority.SECONDARY,
                        )
                        logger.info(
                            f"✅ [View Card] Added check button: {remaining_time}s remaining"
                        )
                        # Store timer info for later use
                        timer_needs_start = True
                        timer_expiry = expiry_timestamp
                    else:
                        kb.add_button(
                            "⏰ Check Expired",
                            f"orders:check_expired:{order_id}:{short_card_id}",
                            ButtonPriority.TERTIARY,
                        )
                        logger.info(f"⏰ [View Card] Added expired check button")
                        timer_needs_start = False
                        timer_expiry = None
                elif can_check and check_status in ["active", "available"]:
                    # No timer but can check
                    kb.add_button(
                        "🔍 Check Status",
                        f"orders:check:{order_id}:{short_card_id}",
                        ButtonPriority.SECONDARY,
                    )
                    logger.info(f"✅ [View Card] Added check button (no timer)")
                    timer_needs_start = False
                    timer_expiry = None
                else:
                    logger.warning(
                        f"❌ [View Card] Check button NOT added - can_check={can_check}, check_status={check_status}, expiry={expiry_timestamp}"
                    )
                    timer_needs_start = False
                    timer_expiry = None
                # Navigation buttons
                kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)
                kb_markup = kb.build()
                logger.info(f"✅ [View Card] API v1 keyboard created with check button")
            else:
                # Use PostCheckoutUI for other API versions
                kb_markup = PostCheckoutUI.create_card_view_keyboard(
                    card_id=short_card_id,
                    order_id=order_id,
                    can_check=can_check,
                    is_unmasked=is_unmasked,
                    check_status=check_status,
                    expiry_timestamp=expiry_timestamp,
                    user_id=str(db_user.id),
                )
                logger.info(f"✅ [View Card] PostCheckoutUI keyboard created")
            # Use the keyboard directly (already built by PostCheckoutUI)
            final_kb = kb_markup
            edit_result = await ui_manager.edit_message_safely(
                callback, message, final_kb, add_watermark=False
            )
            if edit_result:
                logger.debug("Message edited successfully")
                # Start timer management for API v1 if needed
                if (
                    api_version == "v1"
                    and "timer_needs_start" in locals()
                    and timer_needs_start
                    and timer_expiry
                ):
                    logger.info(
                        f"🔄 [View Card] Starting timer countdown for {timer_expiry - int(datetime.now(timezone.utc).timestamp())}s"
                    )
                    asyncio.create_task(
                        self._update_timer_display_realtime(
                            callback, order_id, card_id, timer_expiry
                        )
                    )
            else:
                logger.error(f"UI manager failed to edit message!")
        except Exception as e:
            logger.error(f"Exception in cb_view_purchased_card: {e}")
            import traceback

            logger.error(f"Full traceback: {traceback.format_exc()}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _create_full_card_view(
        self,
        order: Dict[str, Any],
        user_id: Optional[str] = None,
        show_sensitive: bool = None,
    ) -> str:
        """Create comprehensive full card view with proper sensitive data handling."""
        try:
            card_id = order.get("external_product_id", order.get("_id", "unknown"))
            api_version = order.get("api_version", "v1")
            # Always show sensitive data when available (don't lock it)
            if show_sensitive is None:
                show_sensitive = True  # Default to showing sensitive data
            # Create card details using PostCheckoutUI for consistent formatting
            from datetime import datetime, timezone

            # Extract card data using the updated card data extractor
            card_data = {}
            # For API v1 orders, use data directly from API response without extraction
            if api_version == "v1":
                logger.debug(f"Processing API v1 order, using direct API data")
                logger.debug(f"Order keys available: {list(order.keys())}")
                logger.debug(f"Has extracted_cards: {'extracted_cards' in order}")
                logger.debug(f"Has raw_data: {'raw_data' in order}")
                logger.debug(f"Has api_response: {'api_response' in order}")
                # Priority 1: Use extracted_cards directly (already processed from API)
                if order.get("extracted_cards") and len(order["extracted_cards"]) > 0:
                    card_data = order["extracted_cards"][0]
                    # Log what sensitive data is available
                    card_num = card_data.get("card_number", card_data.get("cc", ""))
                    has_cc = bool(card_num and "*" not in card_num and len(re.sub(r'[^\d]', '', card_num)) >= 13)
                    has_cvv = bool(card_data.get("cvv"))
                    logger.info(f"✅ Using direct API v1 data from extracted_cards - CC: {'FULL' if has_cc else 'MASKED/NONE'}, CVV: {'YES' if has_cvv else 'NO'}")
                # Priority 2: Use raw API data directly without field mapping
                elif order.get("raw_data") or order.get("api_response"):
                    api_data = order.get("raw_data") or order.get("api_response")
                    logger.info(f"✅ Using direct API v1 data from raw response")
                    # Use API data directly - no field mapping needed!
                    if api_data and isinstance(api_data, dict) and "data" in api_data:
                        card_data = api_data["data"]  # Use API response as-is
                        logger.info(
                            f"✅ Direct API v1 data loaded: {list(card_data.keys())}"
                        )
                    else:
                        logger.warning(
                            f"⚠️ API v1 data format unexpected: {type(api_data)}"
                        )
                # Priority 3: Fallback to metadata (legacy)
                else:
                    logger.debug(
                        f"No API v1 response data found, trying metadata fallback"
                    )
                    card_data = order.get("metadata", {}).get("card_data", {})
                    if card_data:
                        logger.info(
                            f"Using card data from metadata: {list(card_data.keys())}"
                        )
                # Log final result for API v1
                if not card_data:
                    logger.error(
                        f"❌ [API v1] No card_data available after all attempts!"
                    )
            # For API v3 orders, prioritize pre-extracted cards to avoid double extraction
            elif api_version == "v3":
                # Debug: Log what's available in the order object
                logger.debug(f"Order object keys: {list(order.keys())}")
                logger.debug(f"Order has extracted_cards: {'extracted_cards' in order}")
                logger.debug(f"Order has order_metadata: {'order_metadata' in order}")
                logger.debug(f"Order has metadata: {'metadata' in order}")
                # First check if order service passed extracted_cards (from API client or database)
                service_extracted = order.get("extracted_cards", [])
                if service_extracted:
                    card_data = service_extracted[0] if service_extracted else {}
                    # Log what sensitive data is available
                    card_num = card_data.get("card_number", card_data.get("cc", ""))
                    has_cc = bool(card_num and "*" not in card_num and len(re.sub(r'[^\d]', '', card_num)) >= 13)
                    has_cvv = bool(card_data.get("cvv"))
                    logger.info(
                        f"✅ Using pre-extracted card data from order: {list(card_data.keys())[:10]} - CC: {'FULL' if has_cc else 'MASKED/NONE'}, CVV: {'YES' if has_cvv else 'NO'}"
                    )
                    # Ensure we only process ONE card by removing the list to prevent loops
                    if len(service_extracted) > 1:
                        logger.warning(
                            f"Multiple cards found but only using the first one to prevent display duplication"
                        )
                else:
                    logger.warning(
                        f"No extracted_cards found in order, checking metadata and raw_data"
                    )
                    # First check order_metadata for extracted cards (new storage location)
                    order_metadata = order.get("order_metadata", {})
                    metadata_extracted = order_metadata.get("extracted_cards")
                    if metadata_extracted:
                        logger.info(
                            f"Found {len(metadata_extracted)} extracted cards in order_metadata"
                        )
                        card_data = metadata_extracted[0] if metadata_extracted else {}
                        if len(metadata_extracted) > 1:
                            logger.warning(
                                f"Multiple cards in order_metadata but only using first"
                            )
                    else:
                        # Check legacy metadata location
                        legacy_metadata = order.get("metadata", {})
                        legacy_extracted = legacy_metadata.get("extracted_cards")
                        if legacy_extracted:
                            logger.info(
                                f"Found {len(legacy_extracted)} extracted cards in legacy metadata"
                            )
                            card_data = legacy_extracted[0] if legacy_extracted else {}
                            if len(legacy_extracted) > 1:
                                logger.warning(
                                    f"Multiple cards in legacy metadata but only using first"
                                )
                        else:
                            # Check raw_data for extracted cards (fallback)
                            raw_data = order.get("raw_data") or {}
                            if raw_data and isinstance(raw_data, dict):
                                try:
                                    # Check for pre-extracted cards first (avoid redundant extraction)
                                    pre_extracted = raw_data.get(
                                        "extracted_cards"
                                    ) or raw_data.get("cards")
                                    if pre_extracted:
                                        card_data = (
                                            pre_extracted[0] if pre_extracted else {}
                                        )
                                        # Ensure single card processing
                                        if len(pre_extracted) > 1:
                                            logger.warning(
                                                f"Multiple cards available but only using first to prevent duplication"
                                            )
                                    else:
                                        # Only extract if not already done
                                        logger.warning(
                                            f"No pre-extracted cards found, performing extraction from raw_data"
                                        )
                                        extractor = get_card_data_extractor()
                                        extracted_cards = (
                                            extractor.extract_from_api_response(
                                                raw_data
                                            )
                                        )
                                        logger.info(
                                            f"Extracted {len(extracted_cards)} cards from raw_data"
                                        )
                                        # Use ONLY the first extracted card to prevent multiple card display
                                        if extracted_cards:
                                            card_data = extracted_cards[0]
                                            logger.info(
                                                f"Using SINGLE card (first of {len(extracted_cards)}): {list(card_data.keys())}"
                                            )
                                            if len(extracted_cards) > 1:
                                                logger.warning(
                                                    f"Multiple cards extracted but only displaying first to prevent duplication"
                                                )
                                        else:
                                            logger.warning(
                                                "No cards extracted from raw_data"
                                            )
                                except Exception as e:
                                    logger.error(
                                        f"Error extracting card data with extractor: {e}"
                                    )
                                    import traceback

                                    logger.error(f"Traceback: {traceback.format_exc()}")
                            else:
                                logger.warning(
                                    f"No raw_data found in order for card {card_id}"
                                )
            # Fallback to existing extraction if card data extractor didn't work
            if not card_data:
                logger.warning(f"No card data from extractor, trying fallback methods")
                card_data = order.get("metadata", {}).get("card_data", {})
                if not card_data:
                    card_data = self._extract_card_data(order, str(card_id), "unmask")
            # Final validation
            if not card_data:
                logger.error(
                    f"No card data available for display! Order keys: {list(order.keys())}"
                )
            # Ensure we have card data - the logs show extraction is working
            if not card_data and api_version == "v3":
                # Direct fallback: if we have raw_data, extract and use first card
                raw_data = order.get("raw_data") or {}
                if raw_data and isinstance(raw_data, dict):
                    try:
                        extractor = get_card_data_extractor()
                        emergency_cards = extractor.extract_from_api_response(raw_data)
                        if emergency_cards:
                            card_data = emergency_cards[
                                0
                            ]  # Use ONLY first available card
                            if len(emergency_cards) > 1:
                                logger.warning(
                                    f"Emergency extraction found {len(emergency_cards)} cards but using only first"
                                )
                    except Exception as e:
                        logger.error(f"Direct extraction failed: {e}")
            # Final check - if still no card data, create minimal data from available info
            if not card_data:
                logger.error(
                    f"No card data available after all attempts! Creating minimal fallback..."
                )
                # Create basic card data from what we can extract from order
                card_data = {
                    "card_id": str(card_id),
                    "bank": "Unknown Bank",
                    "brand": "Unknown Brand",
                    "type": "Unknown Type",
                    "country": "Unknown",
                    "cardholder_name": "Unknown",
                    "base": "Unknown",
                }
            # Convert card data to camel case for display
            converted_card_data = convert_card_data_to_camel_case(card_data)
            # Create CardDetails object for PostCheckoutUI
            # Handle both API v1 field names and internal field names
            card_details = CardDetails(
                card_id=str(card_id),
                bank=converted_card_data.get("bank", "Unknown Bank"),
                brand=converted_card_data.get("brand", "Unknown Brand"),
                level=converted_card_data.get("level", ""),
                country=converted_card_data.get("country", "Unknown"),
                price=float(order.get("price", 0.0)),
                status=converted_card_data.get("status", order.get("status", "active")),
                # Additional fields from extracted card data - handle API v1 field names
                card_type=converted_card_data.get("type"),
                state=converted_card_data.get("state"),
                city=converted_card_data.get("city"),
                zip_code=converted_card_data.get("zip"),
                address=converted_card_data.get("address"),
                # Handle both 'name' (API v1) and 'cardholder_name' (internal)
                cardholder_name=converted_card_data.get("cardholder_name")
                or converted_card_data.get("name"),
                email=converted_card_data.get("email"),
                phone=converted_card_data.get("phone"),
                # Timestamps
                created_at=order.get("created_at"),
                viewed_at=order.get("viewed_at"),
                last_checked=order.get("checked_at"),
                # Status flags
                can_check=not order.get("is_checked", False),
                refundable=order.get("status") not in ["REFUNDED", "FAILED"],
                is_viewed=order.get("is_viewed", False),
                # IDs
                order_id=order.get("external_order_id", str(order.get("_id"))),
                product_id=order.get("external_product_id"),
            )
            # Add sensitive data if available and should be shown
            if show_sensitive:
                logger.info(
                    f"🔓 UNMASKING: show_sensitive=True, extracting sensitive data"
                )
                logger.debug(f"Card data keys available: {list(card_data.keys())}")
                logger.debug(f"Order has unmasked_data: {'unmasked_data' in order}")
                logger.debug(
                    f"Order has is_unmasked: {order.get('is_unmasked', False)}"
                )
                # Priority 1: Use extracted card data which includes sensitive information when available
                # Handle both API v1 field names (cc, exp) and internal field names (card_number, expiry)
                card_number = card_data.get("card_number") or card_data.get("cc", "")
                # Special handling for unmasked cards that require download
                if card_number == "[UNMASKED - DOWNLOAD REQUIRED]":
                    logger.info(
                        f"🔓 UNMASKING RESULT: Card has been unmasked on backend but requires download for full data"
                    )
                    # Don't set the card number yet - we'll show a message that download is required
                    card_details.full_card_number = ""
                    card_details.unmasked_but_requires_download = True
                else:
                    card_details.full_card_number = card_number
                card_details.cvv = card_data.get("cvv")
                card_details.expiry_date = (
                    card_data.get("expiry")
                    or card_data.get("expiry_date")
                    or card_data.get("exp")
                )
                logger.debug(
                    f"Priority 1 - Card data: cc={bool(card_details.full_card_number)}, cvv={bool(card_details.cvv)}, exp={bool(card_details.expiry_date)}"
                )
                # Priority 2: If no sensitive data in card_data, check unmasked_data field
                if not card_details.full_card_number:
                    logger.debug(f"Priority 2 - Checking unmasked_data field")
                    unmasked_data = order.get("unmasked_data") or {}
                    if unmasked_data and isinstance(unmasked_data, dict):
                        logger.debug(
                            f"Found unmasked_data with keys: {list(unmasked_data.keys())}"
                        )
                        # Check if unmasked_data has extracted_cards
                        unmasked_cards = unmasked_data.get("extracted_cards", [])
                        if unmasked_cards:
                            logger.info(
                                f"✅ Found {len(unmasked_cards)} cards in unmasked_data.extracted_cards"
                            )
                            unmasked_card = unmasked_cards[0]  # Use first card
                            card_details.full_card_number = unmasked_card.get(
                                "card_number", ""
                            )
                            card_details.cvv = unmasked_card.get("cvv")
                            card_details.expiry_date = (
                                unmasked_card.get("expiry")
                                or unmasked_card.get("expiry_date")
                                or unmasked_card.get("exp")
                            )
                            logger.debug(
                                f"Priority 2A - Unmasked card: cc={bool(card_details.full_card_number)}, cvv={bool(card_details.cvv)}, exp={bool(card_details.expiry_date)}"
                            )
                        else:
                            # Fallback to direct unmasked_data fields
                            logger.debug(
                                f"No extracted_cards in unmasked_data, checking direct fields"
                            )
                            card_details.full_card_number = unmasked_data.get("cc")
                            card_details.cvv = unmasked_data.get("cvv")
                            card_details.expiry_date = unmasked_data.get("exp")
                            logger.debug(
                                f"Priority 2B - Direct unmasked: cc={bool(card_details.full_card_number)}, cvv={bool(card_details.cvv)}, exp={bool(card_details.expiry_date)}"
                            )
                    else:
                        logger.debug(f"No unmasked_data found in order")
                # Priority 3: If still no sensitive data, try to extract from raw response
                if not card_details.full_card_number:
                    logger.debug(f"Priority 3 - Checking raw_data for unmasked data")
                    raw_data = order.get("raw_data") or {}
                    if raw_data and isinstance(raw_data, dict):
                        logger.debug(
                            f"Found raw_data with keys: {list(raw_data.keys())}"
                        )
                        # Check if raw_data has extracted_cards with unmasked data
                        raw_extracted = raw_data.get("extracted_cards", [])
                        if raw_extracted:
                            logger.info(
                                f"✅ Found {len(raw_extracted)} cards in raw_data.extracted_cards"
                            )
                            raw_card = raw_extracted[0]  # Use first card
                            card_details.full_card_number = raw_card.get(
                                "card_number", ""
                            )
                            card_details.cvv = raw_card.get("cvv")
                            card_details.expiry_date = (
                                raw_card.get("expiry")
                                or raw_card.get("expiry_date")
                                or raw_card.get("exp")
                            )
                            logger.debug(
                                f"Priority 3A - Raw card: cc={bool(card_details.full_card_number)}, cvv={bool(card_details.cvv)}, exp={bool(card_details.expiry_date)}"
                            )
                        else:
                            # Try to extract from various possible locations in raw_data
                            sections = raw_data.get("sections", [])
                            for section in sections:
                                if isinstance(section, dict):
                                    content = section.get("content", "")
                                    if "Card Number:" in content or "CC:" in content:
                                        # Extract card number from content
                                        cc_match = re.search(
                                            r"(?:Card Number|CC):\s*([0-9\s]+)", content
                                        )
                                        if cc_match:
                                            card_details.full_card_number = (
                                                cc_match.group(1).strip()
                                            )
                                    if "CVV:" in content:
                                        cvv_match = re.search(
                                            r"CVV:\s*([0-9]+)", content
                                        )
                                        if cvv_match:
                                            card_details.cvv = cvv_match.group(
                                                1
                                            ).strip()
                # Final summary of what sensitive data was found
                has_cc = bool(
                    card_details.full_card_number
                    and card_details.full_card_number not in ["[PAN_REDACTED]", ""]
                )
                has_cvv = bool(card_details.cvv and str(card_details.cvv).strip())
                has_exp = bool(
                    card_details.expiry_date and card_details.expiry_date.strip()
                )
                logger.info(
                    f"🔓 UNMASKING RESULT: cc={'✅' if has_cc else '❌'} cvv={'✅' if has_cvv else '❌'} exp={'✅' if has_exp else '❌'}"
                )
                # If we have unmasked data, log the actual values (safely)
                if has_cc:
                    cc_display = card_details.full_card_number
                    if len(cc_display) > 8:
                        cc_display = cc_display[:4] + "********" + cc_display[-4:]
                    logger.info(f"📱 Card Number: {cc_display}")
                if has_exp:
                    logger.info(f"📅 Expiry: {card_details.expiry_date}")
                if has_cvv:
                    logger.info(f"🔐 CVV: {card_details.cvv}")
                # If we don't have full unmasked data but should, try to use masked data appropriately
                if not has_cc and show_sensitive:
                    # Use the masked version from card data but mark it appropriately
                    masked_cc = card_data.get("card_number", "")
                    if masked_cc and masked_cc != "[PAN_REDACTED]":
                        card_details.full_card_number = masked_cc
                        logger.info(f"📱 Using masked card number: {masked_cc}")
                if not has_cvv and show_sensitive:
                    # CVV should be available from extracted data
                    extracted_cvv = card_data.get("cvv")
                    if extracted_cvv:
                        card_details.cvv = extracted_cvv
                if card_details.full_card_number:
                    logger.info(
                        f"📱 Card Number: {card_details.full_card_number[:4]}****{card_details.full_card_number[-4:] if len(card_details.full_card_number) >= 8 else '****'}"
                    )
                if card_details.cvv:
                    logger.info(f"🔐 CVV: ***")
                if card_details.expiry_date:
                    logger.info(f"📅 Expiry: {card_details.expiry_date}")
            else:
                logger.debug(
                    f"🔒 MASKING: show_sensitive=False, showing masked data only"
                )
            # Create enhanced card data with unmasked information if available
            enhanced_card_data = card_data.copy()
            if show_sensitive and card_details.full_card_number:
                enhanced_card_data["card_number"] = card_details.full_card_number
                enhanced_card_data[
                    "cc"
                ] = card_details.full_card_number  # Legacy compatibility
            if show_sensitive and card_details.cvv:
                enhanced_card_data["cvv"] = card_details.cvv
            if show_sensitive and card_details.expiry_date:
                enhanced_card_data["expiry"] = card_details.expiry_date
                enhanced_card_data["expiry_date"] = card_details.expiry_date
                enhanced_card_data["exp"] = card_details.expiry_date
            # Create comprehensive card display showing ALL available data exactly once
            message = self._create_comprehensive_card_display(
                enhanced_card_data, order, show_sensitive
            )
            return message
        except Exception as e:
            logger.error(f"Error creating full card view: {e}")
            # Fallback to simple view
            return self._create_enhanced_simple_card_view(order)
            # Use comprehensive data extraction instead of api_processor
            logger.info(f"Using comprehensive data extraction for card {card_id}")
            # For API v3, use comprehensive data extraction with full details
            if api_version == "v3":
                # Check if this is an unmask operation (has full card data)
                is_unmask_data = self._is_unmask_data(order)
                if is_unmask_data:
                    # Extract comprehensive data including sensitive information
                    comprehensive_data = self._extract_comprehensive_api_v3_data(
                        order, include_sensitive=True
                    )
                else:
                    # Extract comprehensive data from stored order
                    comprehensive_data = self._extract_comprehensive_api_v3_data(order)
                # Apply normalization
                normalized_data = self._normalize_and_validate_data(comprehensive_data)
                # Create processed response structure
                processed_response = {
                    "success": True,
                    "card_id": str(card_id),
                    "api_version": api_version,
                    "processed_data": normalized_data,
                    "validation_summary": {"issues_count": 0, "quality_score": 1.0},
                }
                # Format with full details including sensitive data
                return format_card_api_response(
                    processed_response=processed_response,
                    show_sensitive=True,  # Show sensitive data in full view
                    compact=False,  # Use full detailed format
                    show_issues=True,  # Highlight any data issues
                )
            # For API v1/v2, use existing extraction with new formatting
            card_data = self._extract_card_data(order, str(card_id), "view")
            normalized_data = self._normalize_and_validate_data(card_data)
            processed_response = {
                "success": True,
                "card_id": str(card_id),
                "api_version": api_version,
                "processed_data": normalized_data,
                "validation_summary": {"issues_count": 0, "quality_score": 1.0},
            }
            return format_card_api_response(
                processed_response=processed_response,
                show_sensitive=True,
                compact=False,
                show_issues=True,
            )
        except Exception as e:
            logger.error(f"Error creating comprehensive full view: {e}")
            # Fallback to enhanced simple view
            return self._create_enhanced_simple_card_view(order)

    def _create_enhanced_simple_card_view(self, order: Dict[str, Any]) -> str:
        """Enhanced fallback card view with comprehensive information."""
        # Check if data is unavailable and show appropriate message
        if order.get("_data_unavailable"):
            lines = []
            lines.append(f"💳 <b>Order Details</b>")
            lines.append(f"⚠️ <b>DATA TEMPORARILY UNAVAILABLE</b>")
            lines.append("")
            lines.append("📋 <b>ORDER INFORMATION</b>")
            lines.append("─" * 25)
            lines.append(f"🆔 <b>Order ID:</b> <code>{order.get('external_order_id', 'N/A')}</code>")
            lines.append(f"💰 <b>Price:</b> ${order.get('price', '0.00'):.2f}")
            lines.append(f"📊 <b>Status:</b> {order.get('status', 'active').upper()}")
            lines.append(f"🔧 <b>API Version:</b> {order.get('api_version', 'v1').upper()}")
            lines.append("")
            lines.append("⚠️ <b>NOTICE</b>")
            lines.append("─" * 25)
            lines.append("Card details are temporarily unavailable.")
            lines.append("")
            lines.append("This can happen when:")
            lines.append("• API service is currently unavailable")
            lines.append("• Order data is being refreshed")
            lines.append("• Temporary connectivity issues")
            lines.append("")
            lines.append("💡 <b>What to do:</b>")
            lines.append("• Try the 🔓 Unmask button to fetch fresh data")
            lines.append("• Wait a few minutes and try again")
            lines.append("• Contact support if issue persists")
            
            created_at = order.get("createdAt") or order.get("created_at")
            if created_at:
                try:
                    if isinstance(created_at, str):
                        from dateutil import parser
                        date_obj = parser.parse(created_at)
                    else:
                        date_obj = created_at
                    date_str = date_obj.strftime("%b %d, %Y at %I:%M %p")
                    lines.append("")
                    lines.append(f"📅 <b>Purchased:</b> {date_str}")
                except Exception:
                    pass
            
            return "\n".join(lines)
        
        card_data = self._extract_card_data(order, endpoint="fallback")
        api_version = card_data.get("api_version", "v1")
        lines = []
        # Header with enhanced status
        bank = card_data.get("bank", "Unknown Bank")
        brand = card_data.get("brand", "")
        level = card_data.get("level", "")
        # Use card status, not order status
        status = card_data.get("status", order.get("status", "active"))
        status_icon = self._get_status_icon(status)
        lines.append(f"💳 <b>Card Details</b>")
        lines.append(f"🔓 <b>FULL ACCESS</b> {status_icon}")
        lines.append("")
        # Sensitive Data Section (Priority for viewed cards)
        sensitive_data = []
        if card_data.get("card_number"):
            sensitive_data.append(
                f"💳 <b>Card Number:</b> <code>{card_data['card_number']}</code>"
            )
        if card_data.get("expiry_date"):
            sensitive_data.append(
                f"📅 <b>Expiry:</b> <code>{card_data['expiry_date']}</code>"
            )
        if card_data.get("cvv"):
            sensitive_data.append(f"🔐 <b>CVV:</b> <code>{card_data['cvv']}</code>")
        if sensitive_data:
            lines.append("🔐 <b>SENSITIVE DATA</b>")
            lines.append("─" * 25)
            lines.extend(sensitive_data)
            lines.append("")
        # Cardholder Information
        if card_data.get("cardholder_name"):
            lines.append("👤 <b>CARDHOLDER</b>")
            lines.append("─" * 25)
            lines.append(f"👤 <b>Name:</b> <code>{card_data['cardholder_name']}</code>")
            if card_data.get("phone"):
                lines.append(f"📞 <b>Phone:</b> <code>{card_data['phone']}</code>")
            if card_data.get("address") and card_data["address"].strip():
                lines.append(f"📍 <b>Address:</b> {card_data['address']}")
            if card_data.get("email"):
                lines.append(f"📧 <b>Email:</b> {card_data['email']}")
            lines.append("")
        # Bank Information
        lines.append("🏦 <b>BANK INFORMATION</b>")
        lines.append("─" * 25)
        lines.append(f"🏦 <b>Bank:</b> {bank}")
        if brand:
            lines.append(f"💳 <b>Brand:</b> {brand}")
        if level:
            lines.append(f"⭐ <b>Level:</b> {level}")
        if card_data.get("country"):
            from utils.dynamic_country_flags import get_dynamic_country_with_flag

            country_with_flag = get_dynamic_country_with_flag(card_data["country"])
            # Extract flag emoji if present, otherwise use globe
            if " " in country_with_flag and len(country_with_flag.split(" ")[0]) == 2:
                flag, country_name = country_with_flag.split(" ", 1)
                lines.append(f"{flag} <b>Country:</b> {country_name}")
            else:
                lines.append(f"🌍 <b>Country:</b> {country_with_flag}")
        if card_data.get("card_type"):
            lines.append(f"📏 <b>Type:</b> {card_data['card_type']}")
        lines.append("")
        # API v3 Specific Information
        if api_version == "v3":
            v3_info = []
            if card_data.get("bin_info"):
                # Truncate long BIN info for display
                bin_display = card_data["bin_info"]
                if len(bin_display) > 100:
                    bin_display = bin_display[:97] + "..."
                v3_info.append(f"🔍 <b>BIN Info:</b> {bin_display}")
            if v3_info:
                lines.append("🔧 <b>API v3 DATA</b>")
                lines.append("─" * 25)
                lines.extend(v3_info)
                lines.append("")
        # Timestamps
        created_at = order.get("createdAt") or order.get("created_at")
        if created_at:
            try:
                if isinstance(created_at, str):
                    from dateutil import parser

                    date_obj = parser.parse(created_at)
                else:
                    date_obj = created_at
                date_str = date_obj.strftime("%b %d, %Y at %I:%M %p")
                lines.append(f"📅 <b>Purchased:</b> {date_str}")
            except Exception:
                lines.append(f"📅 <b>Purchased:</b> {created_at}")
        viewed_at = order.get("viewedAt") or order.get("viewed_at")
        if viewed_at:
            try:
                if isinstance(viewed_at, str):
                    from dateutil import parser

                    date_obj = parser.parse(viewed_at)
                else:
                    date_obj = viewed_at
                date_str = date_obj.strftime("%b %d, %Y at %I:%M %p")
                lines.append(f"👁️ <b>Last Viewed:</b> {date_str}")
            except Exception:
                lines.append(f"👁️ <b>Last Viewed:</b> {viewed_at}")
        checked_at = order.get("checkedAt")
        if checked_at:
            try:
                if isinstance(checked_at, str):
                    from dateutil import parser

                    date_obj = parser.parse(checked_at)
                else:
                    date_obj = checked_at
                date_str = date_obj.strftime("%b %d, %Y at %I:%M %p")
                lines.append(f"✅ <b>Last Checked:</b> {date_str}")
            except Exception:
                lines.append(f"✅ <b>Last Checked:</b> {checked_at}")
        # Security Notice
        lines.append("")
        lines.append("━━━━━━━━━━━━━━━━━━━━━━")
        lines.append("🔒 <i>Keep secure and private</i>")
        return "\n".join(lines)

    @handle_errors(error_message="❌ Failed to view card details", show_alert=True)
    async def cb_view_card_details(self, callback: CallbackQuery) -> None:
        """Handle view details button (marks card as viewed)."""
        try:
            user = callback.from_user
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            parts = (callback.data or "").split(":")
            card_id = self._resolve_card_id(parts[2])
            
            # Create work coroutine for loading card details
            async def view_card_work():
                # Mark as viewed
                await self.order_service.mark_card_as_viewed(str(db_user.id), card_id)
                # Show full details
                return await self._fetch_order_for_card(str(db_user.id), card_id)
            
            # Run loading animation concurrently with card loading
            order = await LoadingStages.run_concurrent_loading(
                callback,
                ORDER_VIEW_STAGES,
                view_card_work(),
                operation_name="View Card Details"
            )
            if order:
                # For API v1, call the view_order endpoint to get fresh data
                # Using the new api_v1.cart_service for better error handling and consistency
                api_version = order.get("api_version", "v1")
                external_order_id = order.get("external_order_id")
                # For API v1, use external_product_id as the order_id for the API call
                # Try multiple possible ID fields: external_product_id, product_id, _id
                api_order_id = (
                    external_order_id
                    or order.get("external_product_id")
                    or order.get("product_id")
                    or card_id
                )  # Use card_id as fallback
                
                # Check if card already has unmasked or checked data in database
                # Use the CORRECT card by matching card_id, not just first card
                has_unmasked_data = False
                has_checked_data = False
                if "extracted_cards" in order and order["extracted_cards"]:
                    # Find the specific card we're viewing
                    card_data = self._get_card_from_order(order, card_id)
                    if card_data:
                        # Check if card has full unmasked data (cc, cvv)
                        if card_data.get("cc") or card_data.get("cvv"):
                            has_unmasked_data = True
                            logger.info(f"✅ [My Orders] Target card {card_id[:12]} has unmasked data in database - using cached data")
                        # Check if card has been checked
                        if order.get("is_checked") or card_data.get("is_checked"):
                            has_checked_data = True
                            checked_status = card_data.get("status", "").lower()
                            logger.info(f"✅ [My Orders] Target card {card_id[:12]} has been checked in database (status: {checked_status}) - using cached data")
                    else:
                        logger.warning(f"⚠️ [My Orders] Could not find target card {card_id[:12]} in extracted_cards")
                
                # Only make API call if card doesn't have unmasked/checked data
                should_fetch_api = api_version == "v1" and api_order_id and str(api_order_id) != "unknown"
                should_fetch_api = should_fetch_api and not (has_unmasked_data or has_checked_data)
                
                if should_fetch_api:
                    logger.info(
                        f"📞 [My Orders] Fetching card details for order {api_order_id}"
                    )
                    try:
                        # For API v1, use the orders list endpoint directly - it already has all card details!
                        async with get_external_api_service() as api_service:
                            orders_response = await api_service.list_orders(
                                page=1, limit=50
                            )
                            if orders_response.success and orders_response.data:
                                orders_data = orders_response.data.get("data", [])
                                logger.info(
                                    f"📋 [My Orders] Found {len(orders_data)} orders in list"
                                )
                                # Find the order with matching product_id
                                matching_order = None
                                for order_item in orders_data:
                                    if str(order_item.get("product_id")) == str(
                                        api_order_id
                                    ):
                                        matching_order = order_item
                                        break
                                if matching_order:
                                    logger.info(
                                        f"✅ [My Orders] Found matching order with product_id={api_order_id}"
                                    )
                                    # Use the order data directly from the list - it has all the details!
                                    # No need for a second API call
                                    class MockResponse:
                                        def __init__(self, success, data):
                                            self.success = success
                                            self.data = data

                                    view_response = MockResponse(
                                        success=True,
                                        data={
                                            "data": matching_order
                                        },  # Use orders list data directly
                                    )
                                else:
                                    logger.warning(
                                        f"⚠️ [My Orders] No order found with product_id {api_order_id}"
                                    )

                                    class MockResponse:
                                        def __init__(self, success, data):
                                            self.success = success
                                            self.data = data

                                    view_response = MockResponse(success=False, data={})
                            else:
                                logger.error(
                                    f"❌ [My Orders] Failed to fetch orders list: {orders_response.message if hasattr(orders_response, 'message') else 'Unknown error'}"
                                )

                                class MockResponse:
                                    def __init__(self, success, data):
                                        self.success = success
                                        self.data = data

                                view_response = MockResponse(success=False, data={})
                        if view_response.success and view_response.data:
                            logger.info(f"✅ Retrieved fresh card data")
                            # Extract order data from the response
                            # ExternalAPIService.view_card() returns data in {'data': {...}} format
                            order_data_from_api = view_response.data.get("data", {})
                            if order_data_from_api:
                                # Store the API response directly - no field mapping needed!
                                # The API returns JSON with all fields, we use them as-is
                                order["raw_data"] = {"data": order_data_from_api}
                                order["api_response"] = {"data": order_data_from_api}
                                # Mark as viewed/unmasked since API v1 view_card returns full data
                                order["is_unmasked"] = True
                                order["is_viewed"] = True
                                # Store the API data directly as extracted_cards (no field extraction)
                                # The display code will use the fields directly from this data
                                order["extracted_cards"] = [order_data_from_api]
                                logger.debug(
                                    f"API response fields: {list(order_data_from_api.keys())}"
                                )
                            else:
                                logger.warning(
                                    f"⚠️ [My Orders] API v1 response contained no order data"
                                )
                        else:
                            logger.warning(
                                f"⚠️ [My Orders] API v1 view_card failed: {view_response.message if hasattr(view_response, 'message') else 'Unknown error'}"
                            )
                            # Continue with database data as fallback
                    except Exception as api_error:
                        logger.error(
                            f"❌ [My Orders] Unexpected error calling API v1 view_card: {api_error}"
                        )
                        # Continue with database data as fallback
                        import traceback

                        logger.error(f"Traceback: {traceback.format_exc()}")
                elif has_unmasked_data or has_checked_data:
                    # Card has cached unmasked/checked data - log that we're using it
                    data_type = []
                    if has_unmasked_data:
                        data_type.append("unmasked")
                    if has_checked_data:
                        data_type.append("checked")
                    logger.info(f"💾 [My Orders] Using cached {' and '.join(data_type)} data from database - skipping API call")
                    # Ensure order has proper flags set for cached data
                    order["is_unmasked"] = order.get("is_unmasked", has_unmasked_data)
                    order["is_checked"] = order.get("is_checked", has_checked_data)
                
                # Update viewed status
                order["isviewed"] = True
                order["is_viewed"] = True  # Also set root level flag
                order["viewedAt"] = datetime.now(timezone.utc).isoformat()
                order["viewed_at"] = datetime.now(timezone.utc).isoformat()
                # DEBUG: Log order state before calling _create_full_card_view
                logger.debug(
                    f"[DEBUG] Order dict before _create_full_card_view (My Orders):"
                )
                logger.debug(f"  - has extracted_cards: {'extracted_cards' in order}")
                logger.debug(f"  - has raw_data: {'raw_data' in order}")
                logger.debug(f"  - has api_response: {'api_response' in order}")
                logger.debug(f"  - api_version: {order.get('api_version', 'unknown')}")
                if "extracted_cards" in order:
                    logger.debug(
                        f"  - extracted_cards length: {len(order['extracted_cards'])}"
                    )
                    if order["extracted_cards"]:
                        logger.debug(
                            f"  - extracted_cards[0] keys: {list(order['extracted_cards'][0].keys())[:10]}"
                        )
                # Determine if card is unmasked AND has been viewed
                # For pending orders, only show download after viewing
                is_unmasked = self._is_unmask_data(order)
                
                # Check if card data ACTUALLY has unmasked data (not just the flag)
                # Use the CORRECT card by matching card_id
                actual_is_unmasked = is_unmasked
                if is_unmasked and "extracted_cards" in order and order["extracted_cards"]:
                    card_data = self._get_card_from_order(order, card_id)
                    if card_data:
                        card_number = card_data.get("card_number") or card_data.get("cc", "")
                        # Check if card number is actually unmasked (has full digits, no asterisks)
                        if card_number:
                            has_asterisks = "*" in card_number
                            is_redacted = "[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number
                            # If card number has asterisks or is redacted, it's NOT actually unmasked
                            if has_asterisks or is_redacted:
                                actual_is_unmasked = False
                                logger.warning(f"⚠️ [My Orders] Target card {card_id[:12]} is_unmasked flag set but card data is masked: {card_number[:12]}...")
                        else:
                            actual_is_unmasked = False
                            logger.warning(f"⚠️ [My Orders] Target card {card_id[:12]} is_unmasked flag set but no card number found")
                    else:
                        actual_is_unmasked = False
                        logger.warning(f"⚠️ [My Orders] Could not find target card {card_id[:12]} to check unmask status")
                
                # 🔥 CRITICAL FIX: Filter extracted_cards to only contain the TARGET card
                # This ensures multi-card orders display the correct card
                if "extracted_cards" in order and order["extracted_cards"]:
                    target_card = self._get_card_from_order(order, card_id)
                    if target_card:
                        # Replace extracted_cards with ONLY the target card
                        order["extracted_cards"] = [target_card]
                        logger.info(f"✅ [My Orders] Filtered extracted_cards to target card {card_id[:12]} for display")
                    else:
                        logger.warning(f"⚠️ [My Orders] Target card {card_id[:12]} not found, using existing extracted_cards")
                
                message = await self._create_full_card_view(order, str(db_user.id), show_sensitive=actual_is_unmasked)
                # Use proper card view keyboard with unmask button logic
                card_id_short = self._get_short_id(card_id)
                order_id = order.get("external_order_id", order.get("_id", "unknown"))
                # Check multiple possible locations for is_viewed flag
                is_viewed = (
                    order.get("is_viewed", False)
                    or order.get("metadata", {}).get("is_viewed", False)
                    or order.get("isviewed", 0) == 1
                )
                # Get card status from the CORRECT card by matching card_id
                card_status = None
                if "extracted_cards" in order and order["extracted_cards"]:
                    target_card = self._get_card_from_order(order, card_id)
                    if target_card:
                        card_status = target_card.get("status")
                        logger.debug(f"✅ [My Orders] Got status '{card_status}' from target card {card_id[:12]}")
                if not card_status and "raw_data" in order and isinstance(order.get("raw_data"), dict):
                    raw_card = order["raw_data"].get("data", {})
                    if raw_card.get("_id") == card_id:
                        card_status = raw_card.get("status")
                if not card_status and "api_response" in order and isinstance(order.get("api_response"), dict):
                    api_card = order["api_response"].get("data", {})
                    if api_card.get("_id") == card_id:
                        card_status = api_card.get("status")
                if not card_status:
                    card_status = order.get("status")
                # Check if status is pending (Started or Pending)
                is_pending = card_status and card_status.lower() in [
                    "started",
                    "pending",
                ]
                # Log pending status detection for debugging
                if is_pending:
                    logger.info(
                        f"🔍 Pending card detected (status: {card_status}) - showing View Card button instead of Download"
                    )
                # Get API version for proper button setup
                api_version = order.get("api_version", "v1")
                # Create keyboard - download button only shows if card is viewed
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                # For pending orders, show View Card button instead of Download button
                # Only for API v1 - API v3 has different handling
                if is_pending and api_version == "v1":
                    # View Card button calls the view_card endpoint for API v1
                    kb.add_button(
                        "🃏 View Card",
                        f"orders:view_card:{card_id_short}",
                        ButtonPriority.PRIMARY,
                    )
                    logger.info(
                        f"✅ [My Orders] Added View Card button for API v1 pending card"
                    )
                # Show download button ONLY if card has been viewed and not pending
                elif is_viewed and actual_is_unmasked:
                    kb.add_button(
                        "📥 Download Card Data",
                        f"orders:download:{order_id}:{card_id_short}",
                        ButtonPriority.PRIMARY,
                    )
                elif not actual_is_unmasked:
                    # Show unmask button if not yet unmasked
                    kb.add_button(
                        "🔓 Unmask Card",
                        f"orders:unmask:{order_id}:{card_id_short}",
                        ButtonPriority.PRIMARY,
                    )
                # Add check button for API v1 (with timer)
                timer_needs_start_details = False
                timer_expiry_details = None
                if api_version == "v1" and actual_is_unmasked and not is_pending:
                    # Check if card can be checked using the CORRECT card
                    if "extracted_cards" in order and order["extracted_cards"]:
                        card_data = self._get_card_from_order(order, card_id)
                        if card_data:
                            can_check_field = card_data.get("canCheck", 1)
                            card_status_lower = card_status.lower() if card_status else ""
                            
                            # Hide check button for final statuses (same logic as cb_view_purchased_card)
                            final_statuses = ["nonrefundable", "refunded", "declined", "dead", "invalid", "live", "active", "valid", "approved"]
                            if card_status_lower in final_statuses:
                                logger.info(
                                    f"🔒 [My Orders] Card {card_id[:12]} already has final status '{card_status_lower}' - hiding check button"
                                )
                            # Only show check button if canCheck=1 and not already checked
                            elif can_check_field == 1:
                                # Set 30 second timer for check button
                                expiry_timestamp = (
                                    int(datetime.now(timezone.utc).timestamp()) + 30
                                )
                                remaining_time = 30
                                timer_emoji = "🔍"
                                kb.add_button(
                                    f"{timer_emoji} Check Status ({remaining_time}s)",
                                    f"orders:check:{order_id}:{card_id_short}:{expiry_timestamp}",
                                    ButtonPriority.SECONDARY,
                                )
                                logger.info(
                                    f"✅ [My Orders] Added check button for card {card_id[:12]} with 30s timer"
                                )
                                # Store timer info to start countdown
                                timer_needs_start_details = True
                                timer_expiry_details = expiry_timestamp
                            elif can_check_field == 0:
                                logger.info(
                                    f"🔒 [My Orders] Card {card_id[:12]} not checkable (canCheck=0)"
                                )
                        else:
                            logger.warning(f"⚠️ [My Orders] Could not find target card {card_id[:12]} to check canCheck field")
                # Navigation buttons
                kb.add_button(
                    "📋 Order History", "menu:orders", ButtonPriority.SECONDARY
                )
                kb.add_button("🛒 Browse More", "menu:browse", ButtonPriority.SUCCESS)
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )
                kb_markup = kb.build()
                edit_result = await ui_manager.edit_message_safely(
                    callback, message, kb_markup, add_watermark=False
                )
                # Start timer management for API v1 if needed
                if edit_result and timer_needs_start_details and timer_expiry_details:
                    logger.info(
                        f"🔄 [My Orders] Starting timer countdown for {timer_expiry_details - int(datetime.now(timezone.utc).timestamp())}s"
                    )
                    asyncio.create_task(
                        self._update_timer_display_realtime(
                            callback, order_id, card_id, timer_expiry_details
                        )
                    )
            else:
                await callback.answer("❌ Error loading details", show_alert=True)
        except Exception as e:
            logger.error(f"Error viewing details: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    @handle_errors(error_message="❌ Failed to load orders menu", show_alert=True)
    async def cb_orders_menu(self, callback: CallbackQuery) -> None:
        """Enhanced orders page with improved UI, loading states, and consistent pagination."""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Parse page from callback data - handle both menu:orders and orders:page:X
            page = 1
            if callback.data and ":" in callback.data:
                if "page:" in callback.data:
                    try:
                        page = int(callback.data.split(":")[-1])
                        page = max(1, page)
                    except (ValueError, IndexError):
                        page = 1
            # Show loading animation only
            await callback.answer("📦 Loading your orders...", show_alert=False)
            user_id = str(db_user.id)
            page_size = 6  # Increased page size for better UX
            # Define the actual work to be done
            async def load_orders_work():
                # Get orders from database
                orders_data = await self.order_service.get_user_orders_paginated(
                    user_id=user_id,
                    page=page,
                    page_size=page_size,
                    sort_by="created_at",
                    sort_order=-1,
                )
                # Get order statistics
                stats = await self.order_service.get_user_order_statistics(user_id)
                return orders_data, stats

            # Run loading concurrently with actual work
            try:
                orders_data, stats = await LoadingStages.run_concurrent_loading(
                    callback,
                    [
                        {
                            "text": "Fetching order data",
                            "emoji": "🔍",
                            "duration_ratio": 1.0,
                        },
                        {
                            "text": "Calculating statistics",
                            "emoji": "📊",
                            "duration_ratio": 1.2,
                        },
                        {
                            "text": "Formatting display",
                            "emoji": "🎨",
                            "duration_ratio": 1.1,
                        },
                        {
                            "text": "Preparing interface",
                            "emoji": "✨",
                            "duration_ratio": 0.8,
                        },
                    ],
                    load_orders_work(),
                    operation_name="Load Orders",
                )
            except Exception as e:
                logger.error(f"Error loading orders: {e}")
                error_message = PostCheckoutUI.create_error_message(
                    error_title="Failed to Load Orders",
                    error_description="Unable to load your order history at this time.",
                    suggestions=[
                        "Check your internet connection",
                        "Try refreshing the page",
                        "Contact support if the issue persists",
                    ],
                )
                await ui_manager.edit_message_safely(
                    callback, error_message, reply_markup=None, add_watermark=False
                )
                await callback.answer("❌ Failed to load orders", show_alert=True)
                return
            total_orders = orders_data["total_orders"]
            total_pages = orders_data["total_pages"]
            docs = orders_data["orders"]
            if total_orders == 0:
                # Enhanced empty state
                empty_message = PostCheckoutUI.create_success_message(
                    title="No Orders Yet",
                    description="Your order history is empty. Ready to get started?",
                    next_steps=[
                        "🛍️ Browse our card catalog",
                        "🛒 Add cards to your cart",
                        "✅ Complete checkout to see orders here",
                    ],
                    show_celebration=False,
                )
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button("🛍️ Browse Cards", "menu:browse", ButtonPriority.PRIMARY)
                kb.add_button(
                    "🛒 View Cart", "local:cart:view", ButtonPriority.SECONDARY
                )
                kb.add_navigation_row(
                    back_text="🏠 Main Menu", back_callback="menu:main"
                )
                await ui_manager.edit_message_safely(
                    callback, empty_message, kb.build()
                )
                await callback.answer("📭 No orders found")
                return
            # Create a clean, unified orders display
            msg = create_message(MessageType.INFO)
            msg.set_title(f"My Orders", "📦")
            # Clean, essential analytics only
            analytics_content = f"""<b>Orders:</b> {total_orders} | <b>Spent:</b> ${stats['total_spent']:.2f} | <b>Page:</b> {page}/{total_pages}"""
            msg.add_content(analytics_content)
            # Add orders list with enhanced formatting and card buttons
            order_lines = []
            card_buttons = []
            for i, doc in enumerate(docs, (page - 1) * page_size + 1):
                # Extract card info with improved fallback logic
                meta = doc.get("metadata", {}) or {}
                card_data = meta.get("card_data", {}) or {}
                # Try multiple sources for card_id with better fallbacks
                card_id = (
                    doc.get("external_product_id")
                    or meta.get("card_id")
                    or card_data.get("_id")
                    or doc.get("product_id")
                    or doc.get("_id")  # Use order ID as fallback
                )
                # If still no card_id, create a fallback using order ID
                if not card_id:
                    card_id = f"order_{doc.get('_id', 'unknown')}"
                price = float(doc.get("price", 0.0))
                # Get status from multiple possible locations
                status = doc.get("status", "active")
                # Also check metadata.card_data.status if root status is generic
                meta_card_data = doc.get("metadata", {}).get("card_data", {})
                if (
                    status.lower() in ["active", "success", "completed"]
                    and meta_card_data
                ):
                    # Check if there's a more specific status in card_data
                    card_data_status = meta_card_data.get("status", "")
                    if card_data_status and card_data_status.lower() in [
                        "started",
                        "pending",
                    ]:
                        status = card_data_status
                        logger.info(
                            f"📋 Order {i}: Using card_data status '{card_data_status}' instead of root status"
                        )
                created = doc.get("created_at")
                # Get api_version early for status detection
                api_version = doc.get("api_version", "v1")  # Default to v1 if not set
                external_order_id = (
                    doc.get("external_order_id")
                    or doc.get("external_product_id")
                    or card_id
                )
                # Simplified log - only for pending orders
                if status in ["PENDING", "Pending"]:
                    logger.info(
                        f"⏳ Order {i}: PENDING - {bank if bank else 'Unknown Bank'}"
                    )
                # Get card info with improved fallback handling
                card_info = {}
                if meta and "card_data" in meta and meta["card_data"]:
                    card_info = meta["card_data"]
                elif card_data:
                    card_info = card_data
                else:
                    # Try to extract card data from the order document
                    card_info = self._extract_card_data(doc, str(card_id))
                # Ensure we have basic card info even if extraction failed
                if not card_info:
                    card_info = {
                        "bank": doc.get("bank", "Unknown Bank"),
                        "brand": doc.get("brand", ""),
                        "level": doc.get("level", ""),
                        "bin": doc.get("bin", ""),
                        "expiry": doc.get("expiry", ""),
                    }
                bank = card_info.get("bank", "Unknown Bank")
                brand = card_info.get("brand", "")
                level = card_info.get("level", "")
                bin_number = card_info.get("bin", "")
                expiry = card_info.get("expiry", "")
                # Build card name with enhanced details
                card_name = bank
                if brand and brand != bank:
                    card_name += f" {brand}"
                if level:
                    card_name += f" ({level})"
                # Format date (without time)
                date_str = "Unknown"
                if created:
                    try:
                        if isinstance(created, str):
                            from dateutil import parser

                            created_dt = parser.parse(created)
                        else:
                            created_dt = created
                        date_str = created_dt.strftime("%m/%d")
                    except Exception:
                        date_str = str(created)[:5] if created else "Unknown"
                # Enhanced status formatting
                status_icons = {
                    "completed": "✅",
                    "success": "✅",
                    "active": "🟢",
                    "started": "🟡",  # Added Started status
                    "pending": "🟡",
                    "processing": "🔄",
                    "failed": "❌",
                    "refunded": "💰",
                    "expired": "⏰",
                    "cancelled": "🚫",
                    "queued": "⏳",
                }
                status_icon = status_icons.get(str(status).lower(), "📋")
                # Enhanced card display with better hierarchy
                # Line 1: Index, BIN, and Expiry
                header_parts = [f"<b>{i}.</b>"]
                if bin_number:
                    header_parts.append(f"◆ <b>{bin_number}</b>")
                if expiry:
                    header_parts.append(f"📅 <b>{expiry}</b>")
                header_line = " ".join(header_parts)
                # Line 2: Bank Name (on its own line)
                bank_line = (
                    f"🏛️ <b>{bank}</b>"
                    if bank and bank != "Unknown Bank"
                    else "🏛️ <b>Unknown Bank</b>"
                )
                # Line 3: Card type/brand and level (card details)
                card_info_parts = []
                # Add brand if different from bank
                if brand and brand != bank:
                    card_info_parts.append(f"💳 {brand}")
                # Add card level
                if level:
                    card_info_parts.append(f"⭐️ {level}")
                card_info_line = " • ".join(card_info_parts) if card_info_parts else ""
                # Line 4: Status, badges, lock status, and price
                status_parts = []
                # Check if card is viewed and unmasked status
                is_viewed = doc.get("is_viewed", False)
                is_unmasked = self._is_unmask_data(doc)
                # Add status indicator (for Started/Pending orders)
                if status.lower() in ["started", "pending"]:
                    status_parts.append(f"{status_icon} {status.upper()}")
                # Add NEW badge for unviewed cards
                if not is_viewed:
                    status_parts.append("🆕 NEW")
                # Add lock status only for unviewed or masked cards
                if not is_viewed or not is_unmasked:
                    status_parts.append("🔒 Masked")
                # Enhanced price display matching catalog style
                if price < 0:
                    price_display = f"❌ ${price:.2f} <i>(Invalid)</i>"
                elif price == 0:
                    price_display = "🎁 FREE"
                elif price < 5:
                    price_display = f"💵 ${price:.2f} <i>(Budget)</i>"
                elif price < 15:
                    price_display = f"💰 ${price:.2f} <i>(Standard)</i>"
                else:
                    price_display = f"💎 ${price:.2f} <i>(Premium)</i>"
                status_parts.append(price_display)
                status_line = " • ".join(status_parts)
                # Combine into improved format with proper spacing
                if card_info_line:
                    order_display = (
                        f"{header_line}\n{bank_line}\n{card_info_line}\n{status_line}"
                    )
                else:
                    order_display = f"{header_line}\n{bank_line}\n{status_line}"
                order_lines.append(order_display)
                # Create button for order list - BIN first
                # All orders (including API v1 pending) show normal list buttons
                # The "View Card" button only appears in the details view, not the list
                # Regular button for all orders - show BIN on first line
                if bin_number:
                    button_text = f"• {bin_number}"
                    # Second line: bank name (truncated if needed)
                    button_text += f"\n• {bank[:12]}{'...' if len(bank) > 12 else ''}"
                else:
                    # Fallback if no BIN: show bank name first
                    button_text = f"• {bank[:12]}{'...' if len(bank) > 12 else ''}"
                    if price > 0:
                        button_text += f"\n• ${price:.2f}"
                    else:
                        button_text += f"\n• Order #{i}"
                # Add lock emoji only for unviewed or masked cards
                if not is_viewed or not is_unmasked:
                    button_text += " 🔒"
                # All orders use view_details callback
                # The handler will determine what to show based on status
                callback_data = f"orders:view_details:{card_id}"
                logger.debug(
                    f"➡️ Creating button for order {i}: API={api_version}, Status={status}"
                )
                card_buttons.append(
                    {
                        "text": button_text,
                        "callback": callback_data,
                        "priority": ButtonPriority.PRIMARY,
                    }
                )
            if order_lines:
                # Add orders list without extra divider
                orders_content = "\n\n".join(order_lines)
                msg.add_content(orders_content)
            else:
                msg.add_content("📭 No orders found on this page.")
            # Create enhanced keyboard with proper pagination (2 columns for better mobile layout)
            kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
            # Add card viewing buttons (6 cards per page for optimal layout)
            for i, button_info in enumerate(card_buttons[:6]):
                kb.add_button(
                    button_info["text"],
                    button_info["callback"],
                    button_info["priority"],
                )
            # Add pagination controls using the built-in pagination row method
            if total_pages > 1:
                kb.add_pagination_row(
                    current_page=page,
                    total_pages=total_pages,
                    callback_prefix="orders:page",
                    show_page_numbers=True,
                )
            # Add action buttons
            kb.add_button("🔄 Refresh", f"orders:page:{page}", ButtonPriority.SECONDARY)
            kb.add_button("🛍️ Browse", "menu:browse", ButtonPriority.SUCCESS)
            kb.add_button("🛒 Cart", "local:cart:view", ButtonPriority.SECONDARY)
            # Navigation
            kb.add_navigation_row(back_text="🏠 Main Menu", back_callback="menu:main")
            await ui_manager.edit_message_safely(
                callback, msg.build(add_watermark=False), kb.build()
            )
            # Callback already answered at the start - don't answer again to avoid timeout
        except Exception as e:
            logger.error(f"Error in orders menu: {e}")
            error_message = PostCheckoutUI.create_error_message(
                error_title="Unexpected Error",
                error_description="An unexpected error occurred while loading orders.",
                suggestions=[
                    "Try refreshing the page",
                    "Check your internet connection",
                    "Contact support if the problem continues",
                ],
            )
            try:
                await ui_manager.edit_message_safely(
                    callback, error_message, reply_markup=None, add_watermark=False
                )
            except Exception:
                pass
            # Try to answer callback if not already answered/expired
            try:
                await callback.answer("❌ Error occurred", show_alert=True)
            except Exception:
                # Callback already answered or expired - ignore
                pass

    async def cb_all_orders(self, callback: CallbackQuery) -> None:
        """Enhanced all orders view with improved UI and loading states."""
        # Use the same enhanced implementation as the main orders menu
        await self.cb_orders_menu(callback)

    async def _handle_check_result(
        self,
        callback: CallbackQuery,
        check_result: Dict[str, Any],
        order_id: str,
        card_id: str,
        short_card_id: str,
        expiry_timestamp: int,
        user_id: str,
        check_message,
    ) -> None:
        """Handle the check result and show appropriate response with recheck options."""
        try:
            if check_result.get("success"):
                # Parse the check response to determine card status
                check_data = check_result.get("check_data", {})
                card_status = self._parse_card_status_from_check_response(check_data)
                # Update the order with check result
                await self._update_order_with_check_result(
                    order_id, card_id, card_status, user_id
                )
                status = card_status["status"]
                message = card_status["message"]
                icon = card_status["icon"]
                # Create status message based on result
                is_final = card_status.get("final", False)
                if status == "live":
                    # Final status - card is live
                    status_msg = create_message(MessageType.SUCCESS)
                    status_msg.set_title("Card Status: Live", "✅")
                    status_msg.add_content(
                        "🎉 Great news! Your card is live and ready to use."
                    )
                    status_msg.add_content(
                        "💳 The card has been verified and is working properly."
                    )
                    kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                    kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                    kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)
                elif status == "refunded":
                    # Final status - card was refunded
                    reason = card_status.get("refund_reason", "No reason provided")
                    status_msg = create_message(MessageType.WARNING)
                    status_msg.set_title("Card Status: Refunded", "💰")
                    status_msg.add_content("Your card has been refunded.")
                    status_msg.add_content(f"📝 Reason: {reason}")
                    status_msg.add_content(
                        "💸 The refund should appear in your account balance."
                    )
                    kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                    kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                    kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)
                elif status == "declined":
                    # Final status - card was declined
                    reason = card_status.get("decline_reason", "No reason provided")
                    status_msg = create_message(MessageType.ERROR)
                    status_msg.set_title("Card Status: Declined", "❌")
                    status_msg.add_content("Unfortunately, your card was declined.")
                    status_msg.add_content(f"📝 Reason: {reason}")
                    status_msg.add_content("💸 You should receive a refund shortly.")
                    kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                    kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                    kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)
                elif status == "checking":
                    # Intermediate status - still being processed
                    status_msg = create_message(MessageType.INFO)
                    status_msg.set_title("Card Being Checked...", "⏳")
                    status_msg.add_content("🔍 Your card is still being processed.")
                    status_msg.add_content("⏱️ This may take a few more moments.")
                    status_msg.add_content(
                        "🔄 You can check again to get the latest status."
                    )
                    kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                    # Add Check Again button for checking status
                    kb.add_button(
                        "🔄 Check Again",
                        f"orders:check:{order_id}:{short_card_id}",
                        ButtonPriority.PRIMARY,
                    )
                    kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                elif status == "check_available":
                    # Check is available - show timer and cooldown info
                    timer = card_status.get("timer", 0)
                    status_msg = create_message(MessageType.INFO)
                    status_msg.set_title("Check Available", "🔍")
                    status_msg.add_content("✅ Card check is available.")
                    if timer > 0:
                        status_msg.add_content(f"⏰ Timer: {timer} seconds remaining")
                    status_msg.add_content(
                        "🔄 You can check again to get the latest status."
                    )
                    kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                    # Add Check Again button for check_available status
                    kb.add_button(
                        "🔄 Check Again",
                        f"orders:check:{order_id}:{short_card_id}",
                        ButtonPriority.PRIMARY,
                    )
                    kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                else:
                    # Unknown status - allow recheck after cooldown
                    status_msg = create_message(MessageType.INFO)
                    status_msg.set_title("Card Status", icon)
                    status_msg.add_content(f"ℹ️ {message}")
                    status_msg.add_content(
                        "🔄 You can check again to get the latest status."
                    )
                    kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                    # Add Check Again button for unknown status
                    kb.add_button(
                        "🔄 Check Again",
                        f"orders:check:{order_id}:{short_card_id}",
                        ButtonPriority.PRIMARY,
                    )
                    kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                await check_message.edit_text(
                    status_msg.build(add_watermark=False),
                    reply_markup=kb.build(),
                    parse_mode="HTML",
                )
            else:
                # API call failed
                error_msg = check_result.get("error", "Unknown error")
                logger.error(f"Card check failed: {error_msg}")
                status_msg = create_message(MessageType.ERROR)
                status_msg.set_title("Check Failed", "❌")
                status_msg.add_content("Unable to check card status at this time.")
                status_msg.add_content(f"📝 Error: {error_msg}")
                status_msg.add_content("🔄 Please try again in a moment.")
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button(
                    "🔄 Try Again",
                    f"orders:check:{order_id}:{short_card_id}:{expiry_timestamp}",
                    ButtonPriority.PRIMARY,
                )
                kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                await check_message.edit_text(
                    status_msg.build(add_watermark=False),
                    reply_markup=kb.build(),
                    parse_mode="HTML",
                )
        except Exception as e:
            logger.error(f"Error handling check result: {e}")
            error_msg = create_message(MessageType.ERROR)
            error_msg.set_title("Processing Error", "❌")
            error_msg.add_content(
                "An error occurred while processing the check result."
            )
            error_msg.add_content("🔄 Please try checking again.")
            kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
            kb.add_button(
                "🔄 Try Again",
                f"orders:check:{order_id}:{short_card_id}:{expiry_timestamp}",
                ButtonPriority.PRIMARY,
            )
            kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
            await check_message.edit_text(
                error_msg.build(add_watermark=False),
                reply_markup=kb.build(),
                parse_mode="HTML",
            )

    def _parse_card_status_from_check_response(
        self, check_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse card status from API v3 check response."""
        try:
            # First try to get extracted cards from the response (like in the logs)
            if "extracted_cards" in check_data and check_data["extracted_cards"]:
                card = check_data["extracted_cards"][0]  # Use first card
                status = card.get("status", "").lower()
                check_status = card.get("check_status", "").lower()
                # Handle final states first
                if status == "live":
                    return {
                        "status": "live",
                        "message": "Card is live and ready to use",
                        "icon": "✅",
                        "color": "green",
                        "final": True,
                    }
                elif "refund" in status:
                    return {
                        "status": "refunded",
                        "message": "Card has been refunded",
                        "refund_reason": card.get(
                            "refund_reason", "No reason provided"
                        ),
                        "icon": "💰",
                        "color": "orange",
                        "final": True,
                    }
                elif "decline" in status or "dead" in status:
                    return {
                        "status": "declined",
                        "message": "Card was declined",
                        "decline_reason": card.get(
                            "decline_reason", "No reason provided"
                        ),
                        "icon": "❌",
                        "color": "red",
                        "final": True,
                    }
                # Handle intermediate states
                elif check_status == "processing" or status == "checking":
                    return {
                        "status": "checking",
                        "message": "Card is being checked, please wait",
                        "icon": "⏳",
                        "color": "blue",
                        "final": False,
                    }
                elif check_status == "active" or check_status == "available":
                    timer = card.get("check_timer", 0)
                    if timer > 0:
                        return {
                            "status": "check_available",
                            "message": f"Check available - {timer} seconds remaining",
                            "icon": "🔍",
                            "color": "blue",
                            "timer": timer,
                            "final": False,
                        }
                    else:
                        return {
                            "status": "check_available",
                            "message": "Check available - no timer",
                            "icon": "🔍",
                            "color": "blue",
                            "final": False,
                        }
            # Fallback: Extract from sections/rows format
            sections = check_data.get("sections", [])
            rows = check_data.get("rows", [])
            # Check rows first (as shown in logs)
            if rows:
                for row in rows:
                    if isinstance(row, list):
                        for cell in row:
                            if isinstance(cell, dict):
                                text = cell.get("text", "").lower()
                                # Parse format like "[PAN_REDACTED], 10/25, 827Live!"
                                if "live!" in text or "live" in text:
                                    return {
                                        "status": "live",
                                        "message": "Card is live and ready to use",
                                        "icon": "✅",
                                        "color": "green",
                                    }
                                elif "refund" in text:
                                    return {
                                        "status": "refunded",
                                        "message": "Card has been refunded",
                                        "icon": "💰",
                                        "color": "orange",
                                    }
                                elif (
                                    "decline" in text
                                    or "dead" in text
                                    or "failed" in text
                                ):
                                    return {
                                        "status": "declined",
                                        "message": "Card was declined",
                                        "icon": "❌",
                                        "color": "red",
                                    }
                                elif "check" in text and (
                                    "second" in text or "wait" in text
                                ):
                                    # Extract remaining time if available
                                    time_match = re.search(r"(\d+)\s*second", text)
                                    if time_match:
                                        seconds = time_match.group(1)
                                        return {
                                            "status": "checking",
                                            "message": f"Card is being checked, {seconds} seconds remaining",
                                            "icon": "⏳",
                                            "color": "blue",
                                        }
                                    else:
                                        return {
                                            "status": "checking",
                                            "message": "Card is being checked, please wait",
                                            "icon": "⏳",
                                            "color": "blue",
                                        }
            # Check sections format
            for section in sections:
                tables = section.get("tables", [])
                for table in tables:
                    table_rows = table.get("rows", [])
                    for row in table_rows:
                        if not row:
                            continue
                        for cell in row:
                            if isinstance(cell, dict):
                                text = cell.get("text", "").lower()
                                if "live!" in text or "live" in text:
                                    return {
                                        "status": "live",
                                        "message": "Card is live and ready to use",
                                        "icon": "✅",
                                        "color": "green",
                                    }
                                elif "refund" in text:
                                    return {
                                        "status": "refunded",
                                        "message": "Card has been refunded",
                                        "icon": "💰",
                                        "color": "orange",
                                    }
                                elif (
                                    "decline" in text
                                    or "dead" in text
                                    or "failed" in text
                                ):
                                    return {
                                        "status": "declined",
                                        "message": "Card was declined",
                                        "icon": "❌",
                                        "color": "red",
                                    }
                                elif "check" in text and (
                                    "second" in text or "wait" in text
                                ):
                                    return {
                                        "status": "checking",
                                        "message": "Card is being checked, please wait",
                                        "icon": "⏳",
                                        "color": "blue",
                                    }
            # Default status if no specific status found
            return {
                "status": "unknown",
                "message": "Card status could not be determined",
                "icon": "❓",
                "color": "gray",
            }
        except Exception as e:
            logger.error(f"Error parsing card status: {e}")
            return {
                "status": "error",
                "message": "Error parsing card status",
                "icon": "⚠️",
                "color": "red",
            }

    async def _update_order_with_check_result(
        self, order_id: str, card_id: str, card_status: Dict[str, Any], user_id: int
    ) -> None:
        """Update order with check result in database."""
        try:
            purchases_collection = get_collection("purchases")
            # First try to find the order by external_order_id
            order_doc = await purchases_collection.find_one(
                {"external_order_id": order_id}
            )
            if order_doc:
                # Update the order with check result
                update_data = {
                    "check_result": card_status,
                    "is_checked": True,
                    "checked_at": datetime.now(timezone.utc),
                }
                # If the order has extracted_cards, update the specific card's status
                if "extracted_cards" in order_doc and order_doc["extracted_cards"]:
                    for i, card in enumerate(order_doc["extracted_cards"]):
                        if card.get("_id") == card_id:
                            # Update the specific card's status
                            order_doc["extracted_cards"][i][
                                "card_status"
                            ] = card_status["status"]
                            order_doc["extracted_cards"][i][
                                "refund_status"
                            ] = card_status.get("refund_status", "")
                            order_doc["extracted_cards"][i][
                                "refund_reason"
                            ] = card_status.get("refund_reason", "")
                            order_doc["extracted_cards"][i][
                                "decline_reason"
                            ] = card_status.get("decline_reason", "")
                            break
                    update_data["extracted_cards"] = order_doc["extracted_cards"]
                await purchases_collection.update_one(
                    {"_id": order_doc["_id"]}, {"$set": update_data}
                )
            logger.info(
                f"Updated order {order_id} with check result: {card_status['status']}"
            )
        except Exception as e:
            logger.error(f"Error updating order with check result: {e}")

    async def _show_check_result(
        self,
        callback: CallbackQuery,
        order_id: str,
        card_id: str,
        card_status: Dict[str, Any],
        expiry_timestamp: int,
    ) -> None:
        """Show check result to user with full card details."""
        try:
            status = card_status["status"]
            message = card_status["message"]
            icon = card_status["icon"]
            color = card_status["color"]
            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(
                callback.from_user.id
            )
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Get the order with updated card data
            purchases_collection = get_collection("purchases")
            order_doc = await purchases_collection.find_one(
                {"external_order_id": order_id}
            )
            if not order_doc:
                logger.error(f"Order not found for check result: {order_id}")
                await callback.answer("❌ Order not found", show_alert=True)
                return
            # Check if order has extracted cards
            if not order_doc.get("extracted_cards"):
                logger.warning(
                    f"Order {order_id} has no extracted_cards, trying to get from unmasked_data"
                )
                # Try to get card data from unmasked_data
                unmasked_data = order_doc.get("unmasked_data", {})
                if "extracted_cards" in unmasked_data:
                    order_doc["extracted_cards"] = unmasked_data["extracted_cards"]
                    logger.info(
                        f"Retrieved {len(unmasked_data['extracted_cards'])} cards from unmasked_data"
                    )
                else:
                    logger.error(f"No card data available in order {order_id}")
                    await callback.answer("❌ No card data available", show_alert=True)
                    return
            # Create enhanced card view with updated status
            logger.info(
                f"Creating card view for order {order_id} with extracted_cards: {len(order_doc.get('extracted_cards', []))}"
            )
            message_text = await self._create_full_card_view(
                order_doc, str(db_user.id), show_sensitive=True
            )
            # Add check result header
            check_header = f"🔍 <b>Check Result</b>\n"
            check_header += f"┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
            check_header += f"<b>Status:</b> {message} {icon}\n"
            check_header += f"<b>Checked:</b> {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')} UTC\n\n"
            full_message = check_header + message_text
            # Create keyboard with card view options
            # Use short card ID for callback data
            short_card_id = self._get_short_id(card_id)
            
            # Use PostCheckoutUI with appropriate check status based on timer
            current_time = int(datetime.now(timezone.utc).timestamp())
            if current_time <= expiry_timestamp:
                remaining_time = expiry_timestamp - current_time
                check_status = "active"  # Allow checking again
            else:
                check_status = "expired"  # Timer expired
            
            kb_markup = PostCheckoutUI.create_card_view_keyboard(
                card_id=short_card_id,
                order_id=order_id,
                can_check=(current_time <= expiry_timestamp),  # Can check if timer not expired
                is_unmasked=True,
                check_status=check_status,
                expiry_timestamp=expiry_timestamp if current_time <= expiry_timestamp else None,
            )
            # Navigation already included in simplified keyboard
            await ui_manager.edit_message_safely(callback, full_message, kb_markup)
            # Show appropriate alert
            if status == "live":
                await callback.answer("✅ Card is live!", show_alert=False)
            elif status == "refunded":
                await callback.answer("💰 Card has been refunded", show_alert=False)
            elif status == "declined":
                await callback.answer("❌ Card was declined", show_alert=False)
            else:
                await callback.answer(f"ℹ️ {message}", show_alert=False)
        except Exception as e:
            logger.error(f"Error showing check result: {e}")
            await callback.answer("❌ Error displaying result", show_alert=True)

    async def cb_check_info(self, callback: CallbackQuery) -> None:
        """Handle check info buttons."""
        await callback.answer(
            "ℹ️ Card Check Info\n\nThis card has already been checked or the check window has expired.",
            show_alert=True,
        )

    async def cb_check_expired(self, callback: CallbackQuery) -> None:
        """Handle expired check button clicks."""
        await callback.answer(
            "⏰ Check window has expired\n\nYou can no longer check this card. Please contact support if needed.",
            show_alert=True,
        )

    async def cb_check_cooldown(self, callback: CallbackQuery) -> None:
        """Handle cooldown button clicks during check cooldown period."""
        try:
            # Format: orders:check_cooldown:order_id:card_id
            parts = (callback.data or "").split(":")
            if len(parts) >= 4:
                order_id_input = parts[2]
                short_card_id = parts[3]
                _ = await self._resolve_order_id(order_id_input)
                _ = self._resolve_card_id(short_card_id)
            await callback.answer(
                "⏳ Please wait before checking again.", show_alert=True
            )
        except Exception:
            await callback.answer("⏳ Please wait a moment.", show_alert=True)

    async def _update_timer_display(
        self,
        callback: CallbackQuery,
        order_id: str,
        card_id: str,
        expiry_timestamp: int,
    ) -> None:
        """Update timer display every few seconds until expired."""
        try:
            while True:
                current_time = int(datetime.now(timezone.utc).timestamp())
                remaining_time = expiry_timestamp - current_time
                if remaining_time <= 0:
                    # Timer expired, just update the keyboard to show expired button
                    try:
                        # Use short card ID to prevent BUTTON_DATA_INVALID errors
                        short_card_id = self._get_short_id(card_id)
                        # Create keyboard with expired check button (no message change)
                        kb_markup = PostCheckoutUI.create_card_view_keyboard(
                            card_id=short_card_id,
                            order_id=order_id,
                            can_check=True,
                            expiry_timestamp=expiry_timestamp,  # This will make the button show "Check Expired"
                            is_unmasked=True,
                        )
                        # Navigation already included in simplified keyboard
                        # Update only the keyboard, keep the original message
                        await ui_manager.edit_keyboard_safely(callback, kb_markup)
                    except Exception as e:
                        logger.error(f"Error updating expired timer display: {e}")
                    break
                # Update the button text to show remaining time
                try:
                    short_card_id = self._get_short_id(card_id)
                    kb_markup = PostCheckoutUI.create_card_view_keyboard(
                        card_id=short_card_id,
                        order_id=order_id,
                        can_check=True,
                        expiry_timestamp=expiry_timestamp,
                        is_unmasked=True,
                    )
                    # Navigation already included in simplified keyboard
                    # Update only the keyboard to show countdown
                    await ui_manager.edit_keyboard_safely(callback, kb_markup)
                except Exception as e:
                    logger.error(f"Error updating timer countdown: {e}")
                # Update every 5 seconds
                await asyncio.sleep(5)
        except Exception as e:
            logger.error(f"Error in timer update task: {e}")

    @handle_errors(error_message="❌ Failed to unmask card", show_alert=True)
    async def cb_unmask_card(self, callback: CallbackQuery) -> None:
        """Enhanced card unmasking with modern loading animations and timer"""
        logger.info(f"Unmask card request from user {callback.from_user.id}")
        try:
            # Answer callback early to prevent timeout
            await callback.answer("⏳ Processing unmask request...", show_alert=False)
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Parse callback data: orders:unmask:order_id:card_id
            parts = (callback.data or "").split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid unmask request", show_alert=True)
                return
            order_id = parts[2]
            short_card_id = parts[3]
            # Try to resolve short ID to full ID
            card_id = self._resolve_card_id(short_card_id)
            if card_id == short_card_id and len(short_card_id) < 20:
                logger.warning(
                    f"Short card ID {short_card_id} not found in cache, searching in order data"
                )
                # We'll resolve it later when we have the user ID
                card_id = None  # Will be resolved later
            logger.info(
                f"Unmasking card {card_id or short_card_id} (short: {short_card_id}) in order {order_id} for user {user.id}"
            )
            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Show loading animation only
            await callback.answer("🔓 Starting unmask process...", show_alert=False)
            # Define the actual work to be done
            async def unmask_work():
                # Use optimized order fetching with caching
                order_doc = await self._fetch_order_optimized(
                    str(db_user.id), order_id, card_id
                )
                if not order_doc:
                    raise ValueError("Order not found")
                # Resolve card_id if still needed
                actual_card_id = card_id
                if not actual_card_id and short_card_id:
                    # Use the helper method to find the full card ID
                    actual_card_id = await self._find_full_card_id_in_order(
                        str(db_user.id), order_id, short_card_id
                    )
                    if actual_card_id:
                        logger.info(
                            f"✅ Resolved card ID: {actual_card_id} from short: {short_card_id}"
                        )
                    else:
                        logger.error(
                            f"❌ Could not find full card ID for short: {short_card_id}"
                        )
                        raise ValueError(f"Card ID not found: {short_card_id}")
                # Use external API service to unmask the card
                async with get_external_api_service() as api_service:
                    response = await api_service.unmask_card(
                        order_id=order_id,
                        card_id=actual_card_id or short_card_id,
                        force_api_v3=True,
                    )
                return response, order_doc, actual_card_id

            # Run loading concurrently with actual work
            try:
                (
                    response,
                    order_doc,
                    actual_card_id,
                ) = await LoadingStages.run_concurrent_loading(
                    callback, UNMASK_STAGES, unmask_work(), operation_name="Unmask Card"
                )
            except ValueError as e:
                # Enhanced error handling with user-friendly messages
                error_message = PostCheckoutUI.create_error_message(
                    error_title="Card Unmask Failed",
                    error_description=f"Unable to unmask the card: {str(e)}",
                    suggestions=[
                        "Verify the card ID is correct",
                        "Check if the order is still valid",
                        "Try refreshing the order data",
                    ],
                )
                await ui_manager.edit_message_safely(
                    callback, error_message, reply_markup=None, add_watermark=False
                )
                await callback.answer(f"❌ {str(e)}", show_alert=True)
                return
            except Exception as e:
                logger.error(f"Error during unmask operation: {e}")
                # Enhanced error handling with recovery options
                error_message = PostCheckoutUI.create_error_message(
                    error_title="Unmask Operation Failed",
                    error_description="An unexpected error occurred while unmasking the card.",
                    suggestions=[
                        "Try the operation again",
                        "Check your internet connection",
                        "Contact support if the issue persists",
                    ],
                )
                await ui_manager.edit_message_safely(
                    callback, error_message, reply_markup=None, add_watermark=False
                )
                await callback.answer("❌ Failed to unmask card", show_alert=True)
                return
            if response.success and response.data:
                # Update order document in cache and database efficiently
                update_data = {"unmasked_data": response.data, "is_unmasked": True}
                # Add extracted cards if available
                if (
                    "extracted_cards" in response.data
                    and response.data["extracted_cards"]
                ):
                    # Log the extracted card data to debug what we're receiving
                    logger.info(f"🔍 Unmask API returned {len(response.data['extracted_cards'])} cards")
                    logger.info(f"🎯 Target card ID: {actual_card_id}")
                    
                    # Mark all cards as unmasked and log their status
                    for idx, card in enumerate(response.data["extracted_cards"]):
                        card_id = card.get("_id", "")
                        card_num = card.get("card_number", card.get("cc", ""))
                        
                        # Check if this card has REAL unmasked data (not redacted or masked)
                        has_full_number = (
                            card_num 
                            and card_num != "[PAN_REDACTED]"
                            and "*" not in card_num 
                            and len(re.sub(r'[^\d]', '', card_num)) >= 13
                        )
                        has_cvv = bool(card.get("cvv"))
                        is_match = card_id == actual_card_id
                        
                        logger.info(f"   Card {idx}: _id={card_id[:12]}{'...' if len(card_id) > 12 else ''}, match={'✅' if is_match else '❌'}, card_number={'FULL' if has_full_number else 'MASKED/EMPTY'}, cvv={'YES' if has_cvv else 'NO'}")
                        
                        # Mark all cards as unmasked since unmask operation was successful
                        card["backend_unmasked"] = True
                        card["unmask_operation_successful"] = True
                        card["card_number_status"] = "unmasked"
                    
                    # Find the SPECIFIC card we're trying to unmask by matching _id
                    target_card, target_card_idx = self._find_matching_card_by_id(
                        response.data["extracted_cards"], 
                        actual_card_id
                    )
                    
                    if target_card:
                        # Check if this card has full unmasked data
                        card_num = target_card.get("card_number", target_card.get("cc", ""))
                        has_full_data = (
                            card_num 
                            and card_num != "[PAN_REDACTED]"
                            and "*" not in card_num 
                            and len(re.sub(r'[^\d]', '', card_num)) >= 13
                        )
                        logger.info(f"   🎯 MATCH! This is the target card at index {target_card_idx}, has_full_data={'YES' if has_full_data else 'NO'}")
                    
                    # If we found the target card and it's not at index 0, move it to index 0
                    if target_card:
                        if target_card_idx > 0:
                            logger.info(f"🔄 Moving target card from index {target_card_idx} to index 0")
                            cards = response.data["extracted_cards"]
                            cards[0], cards[target_card_idx] = cards[target_card_idx], cards[0]
                            response.data["extracted_cards"] = cards
                        else:
                            logger.info(f"✅ Target card already at index 0")
                        
                        # Check if the target card has full data
                        card_num = target_card.get("card_number", target_card.get("cc", ""))
                        has_full_data = (
                            card_num 
                            and card_num != "[PAN_REDACTED]"
                            and "*" not in card_num 
                            and len(re.sub(r'[^\d]', '', card_num)) >= 13
                        )
                        
                        if not has_full_data:
                            logger.warning(f"⚠️ Target card found but requires DOWNLOAD for full data")
                            target_card["requires_download"] = True
                            target_card["unmask_note"] = "Card unmasked successfully, but full details require download"
                        else:
                            logger.info(f"✅ Target card has full unmasked data!")
                    else:
                        logger.error(f"❌ Target card with _id={actual_card_id} NOT FOUND in API response!")
                        # Set flag on all cards to indicate issue
                        for card in response.data["extracted_cards"]:
                            card["requires_download"] = True
                            card["unmask_note"] = "Target card not found in unmask response"
                    
                    order_doc["extracted_cards"] = response.data["extracted_cards"]
                    order_doc["unmasked_data"] = response.data
                    order_doc["is_unmasked"] = True  # CRITICAL: Mark order as unmasked
                    update_data["extracted_cards"] = response.data["extracted_cards"]
                    logger.info(f"✅ Updated order_doc with extracted_cards, is_unmasked=True")
                # Store raw_data if available
                if "raw_data" in response.data:
                    order_doc["raw_data"] = response.data["raw_data"]
                    update_data["raw_data"] = response.data["raw_data"]
                    logger.info(f"✅ Stored raw_data in order_doc")
                # Update database asynchronously to avoid blocking UI
                asyncio.create_task(
                    self._update_order_async(order_doc["_id"], update_data)
                )
                # Update cache immediately
                cache_key = f"{db_user.id}:{order_id}:{card_id or short_card_id}"
                self._cache_order(cache_key, order_doc)
                logger.info(f"✅ Updated cache with unmasked data for key: {cache_key}")
                # Create enhanced card view with sensitive data - FORCE show_sensitive=True
                message = await self._create_full_card_view(
                    order_doc, str(db_user.id), show_sensitive=True
                )
                logger.info(f"✅ Created unmasked card view with show_sensitive=True")
                
                # Check if card actually has full data or requires download
                has_full_card_data = False
                if order_doc.get("extracted_cards"):
                    first_card = order_doc["extracted_cards"][0]
                    card_num = first_card.get("card_number", first_card.get("cc", ""))
                    has_full_card_data = (
                        card_num 
                        and card_num != "[PAN_REDACTED]"
                        and "*" not in card_num
                        and len(re.sub(r'[^\d]', '', card_num)) >= 13
                    )
                
                # Create enhanced timer-based keyboard with 30-second check window
                current_time = int(datetime.now(timezone.utc).timestamp())
                expiry_timestamp = current_time + 30
                short_card_id = self._get_short_id(card_id or short_card_id)
                
                # If card doesn't have full data, show download button instead
                # Otherwise show check button for validation
                kb_markup = PostCheckoutUI.create_card_view_keyboard(
                    card_id=short_card_id,
                    order_id=order_id,
                    can_check=has_full_card_data,  # Only allow check if we have full data
                    expiry_timestamp=expiry_timestamp if has_full_card_data else None,
                    is_unmasked=has_full_card_data,  # Show download if not fully unmasked
                    check_status="active" if has_full_card_data else None,
                )
                # Send new message with unmasked card details instead of editing existing message
                new_message = await callback.message.answer(
                    message, reply_markup=kb_markup, parse_mode="HTML"
                )
                # Start timer management with the new message
                asyncio.create_task(
                    self._update_timer_display_realtime_new_message(
                        new_message, order_id, card_id or short_card_id, expiry_timestamp
                    )
                )
                await callback.answer(
                    "✅ Card unmasked! Check button available for 30 seconds."
                )
            else:
                # Enhanced failure handling with helpful information
                failure_message = PostCheckoutUI.create_error_message(
                    error_title="Card Unmask Failed",
                    error_description="The card could not be unmasked at this time.",
                    suggestions=[
                        "Verify the card is still valid",
                        "Check if the order is complete",
                        "Try again in a few moments",
                        "Contact support if the issue persists",
                    ],
                )
                await ui_manager.edit_message_safely(
                    callback, failure_message, reply_markup=None, add_watermark=False
                )
                await callback.answer("❌ Unmask failed", show_alert=True)
        except Exception as e:
            logger.error(f"Error in cb_unmask_card: {e}")
            # Handle Telegram callback timeout specifically
            if "query is too old" in str(e) or "response timeout expired" in str(e):
                logger.warning(
                    "Callback query timed out - this is normal for long operations"
                )
                try:
                    # Try to answer the callback to prevent further timeout errors
                    await callback.answer("⏳ Operation completed", show_alert=False)
                except Exception:
                    pass
                return
            # Enhanced general error handling
            error_message = PostCheckoutUI.create_error_message(
                error_title="Unexpected Error",
                error_description="An unexpected error occurred. Please try again.",
                suggestions=[
                    "Refresh the page and try again",
                    "Check your internet connection",
                    "Contact support if the problem continues",
                ],
            )
            try:
                await ui_manager.edit_message_safely(
                    callback, error_message, reply_markup=None, add_watermark=False
                )
            except Exception:
                # If we can't edit the message, just answer the callback
                pass
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _update_order_async(
        self, order_id: str, update_data: Dict[str, Any]
    ) -> None:
        """Asynchronously update order in database without blocking UI"""
        try:
            await self.purchases.update_one({"_id": order_id}, {"$set": update_data})
            logger.debug(f"Updated order {order_id} with unmask data")
        except Exception as e:
            logger.error(f"Error updating order async: {e}")

    async def _update_timer_callback(
        self,
        callback: CallbackQuery,
        card_id: str,
        order_id: str,
        remaining_seconds: int,
        expired: bool = False,
    ) -> None:
        """Enhanced timer callback with dynamic emoji changes"""
        try:
            # Create timer config for dynamic button creation
            timer_config = TimerConfig(
                duration=30,
                update_interval=2,
                warning_threshold=10,
                critical_threshold=5,
                disable_on_expire=True,
            )
            current_time = int(datetime.now(timezone.utc).timestamp())
            # Create updated keyboard with timer
            kb_markup = TimerKeyboard.create_enhanced_card_keyboard(
                card_id=card_id,
                order_id=order_id,
                is_unmasked=True,
                timer_config=timer_config,
                current_time=current_time,
                check_enabled=not expired,
                check_cooldown_remaining=0,
            )
            # Update keyboard safely
            await ui_manager.edit_keyboard_safely(callback, kb_markup)
        except Exception as e:
            logger.debug(f"Timer callback error (non-critical): {e}")

    async def _resolve_order_id(self, possible_short_id: str) -> str:
        """Resolve a possibly short order ID to the full external_order_id.
        If the provided ID is already long enough, return as-is. Otherwise,
        try to find an order whose external_order_id starts with the given prefix.
        """
        try:
            # Heuristic: v3 external order ids are typically long; keep if long enough
            if possible_short_id and len(possible_short_id) >= 20:
                return possible_short_id
            purchases_collection = get_collection("purchases")
            # Prefix search to resolve shortened IDs from callback payloads
            doc = await purchases_collection.find_one(
                {"external_order_id": {"$regex": f"^{re.escape(possible_short_id)}"}}
            )
            if doc and doc.get("external_order_id"):
                return doc["external_order_id"]
            return possible_short_id
        except Exception as e:
            logger.debug(f"Order ID resolution failed for {possible_short_id}: {e}")
            return possible_short_id

    @handle_errors(error_message="❌ Failed to check card", show_alert=True)
    async def cb_check_card(self, callback: CallbackQuery) -> None:
        """Simple card status checking"""
        logger.info(f"Card check request from user {callback.from_user.id}")
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Parse callback data: orders:check:order_id:card_id[:expiry]
            parts = (callback.data or "").split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid check request", show_alert=True)
                return
            order_id_input = parts[2]
            short_card_id = parts[3]
            # Resolve full order id if a shortened id was used in the callback
            order_id = await self._resolve_order_id(order_id_input)
            # Resolve short card ID to full card ID
            card_id = self._resolve_card_id(short_card_id)
            # If resolution failed and we have a short ID, try to find it in order data
            if card_id == short_card_id and len(short_card_id) < 20:
                logger.warning(
                    f"Short card ID {short_card_id} not found in cache, searching in order data"
                )
                # Get user from database to search for the order
                db_user = await self.user_service.get_user_by_telegram_id(user.id)
                if db_user:
                    # Try to find the full card ID in the order data
                    full_card_id = await self._find_full_card_id_in_order(
                        str(db_user.id), order_id, short_card_id
                    )
                    if full_card_id:
                        card_id = full_card_id
                        logger.info(
                            f"✅ Found full card ID: {card_id} from short: {short_card_id}"
                        )
                    else:
                        logger.error(
                            f"❌ Could not find full card ID for short: {short_card_id}"
                        )
                        await callback.answer("❌ Card ID not found", show_alert=True)
                        return
            # Show loading animation only (don't update the card message)
            await callback.answer("🔍 Starting card status check...", show_alert=False)
            # Log the card ID being used for the check
            logger.info(
                f"🔍 Checking card with ID: {card_id} (loaded from short: {short_card_id})"
            )
            # Get order to determine API version
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            order = None
            api_version = "v1"  # Default to v1
            if db_user:
                # Use card_id to fetch order (not order_id)
                order = await self._fetch_order_for_card(str(db_user.id), card_id)
                if order:
                    api_version = order.get("api_version", "v1")
                    order_id_from_order = None
                    
                    # For API v3, go directly to order-level fields
                    # (extracted_cards contains card data, not order data for v3)
                    if api_version == "v3":
                        logger.info("🔵 [API v3] Checking order-level fields for order_id")
                        order_id_from_order = (
                            order.get("external_order_id")
                            or order.get("order_id")
                            or order.get("_id")
                        )
                        if order_id_from_order:
                            logger.info(
                                f"✅ [API v3] Found order_id from order level: {order_id_from_order}"
                            )
                        else:
                            logger.error(
                                f"❌ [API v3] No external_order_id found! Order keys: {list(order.keys())[:15]}"
                            )
                    else:
                        # For API v1, we need the _id field from the API's order response
                        # This is stored in extracted_cards or raw_data
                        logger.info("🔴 [API v1] Checking extracted_cards and raw_data for order_id")
                        
                        # Try to get _id from extracted_cards first (API v1 only)
                        if "extracted_cards" in order and order["extracted_cards"]:
                            card_data = order["extracted_cards"][0]
                            # Try multiple possible _id locations
                            order_id_from_order = (
                                card_data.get("_id")
                                or card_data.get("id")
                                or card_data.get("order_id")
                            )
                            if order_id_from_order:
                                logger.info(
                                    f"✅ [API v1] Found _id from extracted_cards: {order_id_from_order}"
                                )
                            else:
                                logger.warning(
                                    f"⚠️ [API v1] extracted_cards[0] keys: {list(card_data.keys())[:10]}"
                                )
                        
                        # Fall back to raw_data (API v1 only)
                        if not order_id_from_order and "raw_data" in order:
                            raw_data = order["raw_data"]
                            if isinstance(raw_data, dict):
                                data_dict = raw_data.get("data", raw_data)
                                order_id_from_order = (
                                    data_dict.get("_id")
                                    or data_dict.get("id")
                                    or data_dict.get("order_id")
                                )
                                if order_id_from_order:
                                    logger.info(
                                        f"✅ [API v1] Found _id from raw_data: {order_id_from_order}"
                                    )
                                else:
                                    logger.warning(
                                        f"⚠️ [API v1] raw_data keys: {list(data_dict.keys())[:10]}"
                                    )
                        
                        # Fall back to order level (API v1 only)
                        if not order_id_from_order:
                            logger.info("🔴 [API v1] Falling back to order-level fields")
                            order_id_from_order = (
                                order.get("_id")
                                or order.get("external_order_id")
                                or order.get("order_id")
                            )
                            if order_id_from_order:
                                logger.info(
                                    f"✅ [API v1] Found ID from order level: {order_id_from_order}"
                                )
                    # Convert MongoDB ObjectId to string if needed
                    if order_id_from_order and hasattr(order_id_from_order, "__str__"):
                        order_id_from_order = str(order_id_from_order)
                    if order_id_from_order:
                        order_id = order_id_from_order
                        logger.info(
                            f"📋 Order found: API version={api_version}, order_id={order_id}, card_id={card_id}"
                        )
                    else:
                        logger.error(
                            f"❌ Could not find valid order_id! Order keys: {list(order.keys())}"
                        )
                        # Don't use card_id as fallback - better to fail gracefully
                        logger.error(f"❌ Cannot perform check without valid order_id")
                        await callback.answer("❌ Order ID not found - cannot check card", show_alert=True)
                        return
            # Define the actual work to be done
            async def check_work():
                async with get_external_api_service() as api_service:
                    if api_version == "v3":
                        response = await api_service.check_card_status(
                            order_id=order_id, card_id=card_id, force_api_v3=True
                        )
                    else:
                        # API v1: Use check_order endpoint
                        logger.info(
                            f"📞 [API v1] Calling check_order for order {order_id}"
                        )
                        response = await api_service.check_order(
                            order_id=order_id, card_id=None
                        )
                return response

            # Run loading concurrently with actual work (send new loading message to preserve card details)
            try:
                response = await LoadingStages.run_concurrent_loading(
                    callback,
                    CHECK_STAGES,
                    check_work(),
                    operation_name="Check Card Status",
                    send_new_message=True,  # Don't edit card details message
                )
            except Exception as e:
                logger.error(f"Error during check operation: {e}")
                # Enhanced error handling with recovery options
                error_message = PostCheckoutUI.create_error_message(
                    error_title="Card Check Failed",
                    error_description="Unable to check the card status at this time.",
                    suggestions=[
                        "Verify the card ID is correct",
                        "Check your internet connection",
                        "Try again in a few moments",
                        "Contact support if the issue persists",
                    ],
                )
                await ui_manager.edit_message_safely(
                    callback, error_message, reply_markup=None, add_watermark=False
                )
                await callback.answer("❌ Failed to check card status", show_alert=True)
                return
            if response.success and response.data:
                # Extract cards using card_data_extractor
                extracted_cards = []
                from utils.card_data_extractor import extract_comprehensive_card_data

                # Handle API v1 response format (direct card data in response.data.data)
                if api_version == "v1" and isinstance(response.data, dict):
                    logger.info(
                        f"📦 [API v1] Check response keys: {list(response.data.keys())}"
                    )
                    if "data" in response.data:
                        card_data = response.data["data"]
                        logger.info(f"✅ [API v1] Check response with card data")
                        logger.info(f"📋 Card data keys: {list(card_data.keys())[:15]}")
                        # Get and log card status
                        check_result_status = card_data.get("status", "unknown")
                        logger.info(f"📊 [API v1] Card status: {check_result_status}")
                        logger.info(f"🔑 [API v1] Card _id: {card_data.get('_id')}")
                        logger.info(
                            f"🆔 [API v1] Card product_id: {card_data.get('product_id')}"
                        )
                        logger.info(
                            f"⏰ [API v1] Checked at: {card_data.get('checkedAt')}"
                        )
                        # Update status to reflect check completion
                        if check_result_status == "NonRefundable":
                            card_data["check_status"] = "completed"
                            card_data["status_display"] = "✅ Checked - NonRefundable"
                        extracted_cards = [card_data]
                        logger.info(
                            f"✅ [API v1] Using direct card data from check response"
                        )
                    else:
                        logger.error(f"❌ [API v1] Check response missing 'data' key!")
                        logger.error(f"❌ Response keys: {list(response.data.keys())}")
                        logger.error(f"❌ Response preview: {str(response.data)[:200]}")
                        extracted_cards = []
                    # Update database with check results
                    if db_user and order and extracted_cards:
                        try:
                            # Enhance card data with check status fields before saving
                            enhanced_card_data = card_data.copy()
                            card_status = card_data.get("status", "").lower()
                            
                            # Add check status enrichment to card data
                            enhanced_card_data["check_status"] = "LIVE" if card_status == "nonrefundable" else "DEAD" if card_status == "refunded" else card_status.upper()
                            enhanced_card_data["card_status"] = card_status.upper()
                            enhanced_card_data["checked_at"] = card_data.get("checkedAt", datetime.now(timezone.utc).isoformat())
                            enhanced_card_data["is_checked"] = True
                            
                            # Add descriptive response message based on status
                            if card_status == "nonrefundable":
                                enhanced_card_data["response"] = "Card checked and valid"
                                enhanced_card_data["message"] = "✅ Card is working! No refund issued - ready to use"
                            elif card_status == "refunded":
                                enhanced_card_data["response"] = "Card declined by issuer"
                                enhanced_card_data["message"] = "💰 Card was declined - Full refund issued automatically to your balance. Try another card!"
                            elif card_status in ["started", "pending"]:
                                enhanced_card_data["response"] = "Check in progress"
                                enhanced_card_data["message"] = "Card check is pending"
                            else:
                                enhanced_card_data["response"] = f"Current status: {card_status}"
                            
                            update_data = {
                                "raw_data": {"data": enhanced_card_data},
                                "api_response": {"data": enhanced_card_data},
                                "extracted_cards": [enhanced_card_data],
                                "is_checked": True,
                                "checked_at": enhanced_card_data["checked_at"],
                                "checkedAt": card_data.get("checkedAt"),  # Keep API v1 field
                                "status": check_result_status,
                                "check_status": enhanced_card_data["check_status"],
                            }
                            # Update order in database
                            await self.order_service.update_order_status(
                                user_id=str(db_user.id),
                                card_id=card_id,
                                status=check_result_status,
                                additional_data=update_data,
                            )
                            # Also mark as checked using dedicated method
                            await self.order_service.mark_card_as_checked(
                                user_id=str(db_user.id),
                                card_id=card_id,
                                check_status=check_result_status,
                            )
                            logger.info(
                                f"✅ [API v1 Check] Updated database with enhanced check results: {check_result_status}"
                            )
                            logger.info(
                                f"✅ [API v1 Check] Enhanced card data keys: {list(enhanced_card_data.keys())}"
                            )
                        except Exception as db_error:
                            logger.error(
                                f"⚠️ [API v1 Check] Failed to update database: {db_error}"
                            )
                            # Continue even if DB update fails
                # The API v3 response structure shows check_data with extracted_cards
                elif "check_data" in response.data:
                    check_data = response.data["check_data"]
                    # Check if extracted_cards are already in check_data
                    if (
                        "extracted_cards" in check_data
                        and check_data["extracted_cards"]
                    ):
                        extracted_cards = check_data["extracted_cards"]
                        logger.info(
                            f"✅ Using pre-extracted cards from check_data: {len(extracted_cards)} cards"
                        )
                    else:
                        # Try to extract from the sections data
                        extraction_result = extract_comprehensive_card_data(
                            check_data, card_id
                        )
                        if extraction_result:
                            # If extraction_result is a dict (single card), wrap it in a list
                            if isinstance(extraction_result, dict):
                                extracted_cards = [extraction_result]
                            else:
                                extracted_cards = extraction_result
                            logger.info(
                                f"✅ Extracted cards using card_data_extractor: {len(extracted_cards)} cards"
                            )
                else:
                    # Fallback: try extracting from top-level response data
                    extraction_result = extract_comprehensive_card_data(
                        response.data, card_id
                    )
                    if extraction_result:
                        # If extraction_result is a dict (single card), wrap it in a list
                        if isinstance(extraction_result, dict):
                            extracted_cards = [extraction_result]
                        else:
                            extracted_cards = extraction_result
                        logger.info(
                            f"✅ Extracted cards from response.data: {len(extracted_cards)} cards"
                        )
                # Debug: Log the extracted card data
                if extracted_cards:
                    card_sample = extracted_cards[0]
                    logger.info(f"📋 Sample card data keys: {list(card_sample.keys())}")
                    logger.info(f"📋 Status: {card_sample.get('status', 'N/A')}")
                    logger.info(
                        f"📋 Check Status: {card_sample.get('check_status', 'N/A')}"
                    )
                else:
                    logger.warning(f"⚠️ No cards extracted from API response")
                    logger.debug(f"Response data keys: {list(response.data.keys())}")
                # For API v1, create comprehensive card view with highlighted status
                if api_version == "v1" and extracted_cards:
                    # Update order with fresh check data for display
                    if order:
                        order["extracted_cards"] = extracted_cards
                        order["raw_data"] = {"data": extracted_cards[0]}
                        order["is_checked"] = True
                        order["checked_at"] = datetime.now(timezone.utc).isoformat()
                    
                    # ✨ ENHANCE CARD DATA WITH CHECK STATUS BEFORE DISPLAY
                    card_status = extracted_cards[0].get("status", "").lower()
                    checked_at = extracted_cards[0].get("checkedAt", datetime.now(timezone.utc).isoformat())
                    
                    # Add check status fields to all extracted cards
                    for card in extracted_cards:
                        card["check_status"] = "LIVE" if card_status == "nonrefundable" else "DEAD" if card_status == "refunded" else card_status.upper()
                        card["card_status"] = card_status.upper()
                        card["checked_at"] = checked_at
                        card["is_checked"] = True
                        
                        # Add descriptive response message based on status
                        if card_status == "nonrefundable":
                            card["response"] = "Card checked and valid"
                            card["message"] = "✅ Card is working! No refund issued - ready to use"
                        elif card_status == "refunded":
                            card["response"] = "Card declined by issuer"
                            card["message"] = "💰 Card was declined - Full refund issued automatically to your balance. Try another card!"
                        elif card_status in ["started", "pending"]:
                            card["response"] = "Check in progress"
                            card["message"] = "Card check is pending"
                        else:
                            card["response"] = f"Current status: {card_status}"
                    
                    # Update order's extracted_cards with enhanced data
                    if order:
                        order["extracted_cards"] = extracted_cards
                    
                    # Create comprehensive message from API response (centralized formatter includes check status automatically!)
                    if order:
                        status_message = await self._create_full_card_view(
                            order, str(db_user.id), show_sensitive=True
                        )
                    else:
                        # Fallback: create order dict from enhanced card data
                        temp_order = {
                            "extracted_cards": extracted_cards,
                            "api_version": "v1",
                            "is_checked": True,
                            "checked_at": checked_at,
                        }
                        status_message = await self._create_full_card_view(
                            temp_order, str(db_user.id), show_sensitive=True
                        )
                    
                    logger.info(
                        f"✅ [API v1 Check] Showing unified card display with integrated check status: {card_status}"
                    )
                else:
                    # For other APIs or fallback
                    status_message = self._create_simple_status_message(
                        extracted_cards, card_id
                    )
                # Create keyboard based on check result
                short_card_id = self._get_short_id(card_id)
                short_order_id = self._get_short_id(order_id)
                
                # Determine final status from extracted cards - MATCH BY _ID FIRST!
                final_status = "completed"
                can_check_again = False
                
                if extracted_cards:
                    # Find the specific card that was checked by matching _id
                    checked_card, _ = self._find_matching_card_by_id(extracted_cards, card_id)
                    
                    if checked_card:
                        # Get status from the matched card (not just first card!)
                        card_status = checked_card.get("status", "").lower()
                        refund_status = checked_card.get("refund_status", "").lower()
                        
                        # Use refund_status if status is empty
                        if not card_status and refund_status:
                            card_status = refund_status
                        
                        logger.info(f"🎯 [Check] Found matching card with status='{card_status}', refund_status='{refund_status}'")
                        
                        # If status is still checking/pending/started, allow recheck without timer
                        if card_status in ["checking", "started", "pending", "processing", ""]:
                            final_status = "checking"
                            can_check_again = True
                            logger.info(f"✅ [Check Result] Status is '{card_status or 'empty'}' - enabling recheck button without timer")
                        # If status is completed (live/dead/etc), disable further checking
                        elif card_status in ["live", "active", "valid", "approved", "nonrefundable"]:
                            final_status = "completed"
                            can_check_again = False
                            logger.info(f"✅ [Check Result] Status is '{card_status}' (LIVE) - check completed, NO RECHECK BUTTON")
                        elif card_status in ["dead", "invalid", "declined", "refunded"]:
                            final_status = "completed"
                            can_check_again = False
                            logger.info(f"✅ [Check Result] Status is '{card_status}' (DEAD/REFUNDED) - check completed, NO RECHECK BUTTON")
                        else:
                            # For unknown status, treat as checking to allow recheck
                            final_status = "checking"
                            can_check_again = True
                            logger.warning(f"⚠️ [Check Result] Unknown status '{card_status}' - enabling recheck button")
                    else:
                        logger.error(f"❌ [Check] Could not find card with _id={card_id} in check response!")
                        # Allow recheck if we can't find the card
                        final_status = "checking"
                        can_check_again = True
                kb = PostCheckoutUI.create_card_view_keyboard(
                    card_id=short_card_id,
                    order_id=order_id,
                    can_check=can_check_again,  # Allow recheck if status is still checking
                    expiry_timestamp=None,  # No timer for recheck
                    is_unmasked=True,
                    check_status=final_status,  # Set status (checking or completed)
                    user_id=str(user.id),
                )
                # Always send NEW message (keep original card message)
                await callback.message.answer(
                    status_message, reply_markup=kb, parse_mode="HTML"
                )
                logger.info(f"Card check completed for {card_id}")
            else:
                # Show error message without additional UI updates
                error_message = "❌ **Check Failed**\n\nUnable to retrieve card status.\nPlease try again later."
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)
                # Send new message with error instead of editing existing message
                await callback.message.answer(
                    error_message, reply_markup=kb.build(), parse_mode="HTML"
                )
        except Exception as e:
            logger.error(f"Error in cb_check_card: {e}")
            # Provide proper error UI instead of just callback.answer()
            try:
                error_message = "❌ **System Error**\n\nSomething went wrong during the check.\nPlease try again or contact support."
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                kb.add_button("🏠 Main", "menu:main", ButtonPriority.SECONDARY)
                # Send new message with error instead of editing existing message
                await callback.message.answer(
                    error_message, reply_markup=kb.build(), parse_mode="HTML"
                )
            except Exception:
                # Fallback to simple callback answer if UI update fails
                await callback.answer("❌ Check failed", show_alert=True)

    async def cb_view_pending_card(self, callback: CallbackQuery) -> None:
        """
        View card details for pending/started status orders.
        Calls the view_order API endpoint to get fresh card data.
        """
        logger.info(f"View pending card request from user {callback.from_user.id}")
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Parse callback data: orders:view_pending:order_id
            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return
            order_id = parts[2]
            logger.info(f"📋 Viewing pending order: {order_id}")
            # Show loading
            await callback.answer("🔍 Loading card details...", show_alert=False)
            # Get user from database
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Fetch order to get API version
            order = await self._fetch_order_for_card(str(db_user.id), order_id)
            if not order:
                await callback.answer("❌ Order not found", show_alert=True)
                return
            api_version = order.get("api_version", "v1")
            logger.info(f"📋 Order API version: {api_version}")
            # Call view_order API endpoint
            async with get_external_api_service() as api_service:
                logger.info(f"📞 Calling view_order for order {order_id}")
                response = await api_service.view_order(order_id=order_id)
            if response.success and response.data:
                # Extract card data
                card_data = None
                if isinstance(response.data, dict) and "data" in response.data:
                    card_data = response.data["data"]
                    logger.info(
                        f"✅ Got card data with fields: {list(card_data.keys())}"
                    )
                else:
                    logger.warning(f"⚠️ Unexpected response format")
                    card_data = response.data
                # Mark order as viewed in database
                try:
                    await self.purchases.update_one(
                        {"_id": order.get("_id")},
                        {
                            "$set": {
                                "is_viewed": True,
                                "viewed_at": datetime.now(timezone.utc),
                            }
                        },
                    )
                    logger.info(f"✅ Marked order {order_id} as viewed")
                except Exception as e:
                    logger.error(f"⚠️ Failed to mark order as viewed: {e}")
                # Format card display using the enhanced formatter
                card_display = _format_enhanced_card_view(
                    card_data, show_sensitive=True, compact=False, show_issues=True
                )
                # Create keyboard with check button if applicable
                kb = create_enhanced_keyboard()
                # Check card status first - if it's checking/pending, show recheck without timer
                card_status = card_data.get("status", "").lower()
                if card_status in ["checking", "started", "pending", "processing"]:
                    # Show recheck button without timer for pending/checking cards
                    kb.add_button(
                        "🔄 Recheck Status",
                        f"orders:check:{order_id}:{order_id}",
                        ButtonPriority.PRIMARY,
                    )
                    logger.info(f"🔄 Card status is '{card_status}' - showing recheck button without timer")
                else:
                    # For other statuses, check if check is available (based on check_Date field)
                    can_check = card_data.get("canCheck", 0) == 1 or card_data.get(
                        "check_Date"
                    )
                    check_date_str = card_data.get("check_Date")
                    if can_check and check_date_str:
                        # Parse check_Date to get expiry timestamp
                        from dateutil import parser as date_parser

                        try:
                            check_date = date_parser.parse(check_date_str)
                            current_time = datetime.now(timezone.utc)
                            if check_date > current_time:
                                # Timer still valid
                                remaining_seconds = int(
                                    (check_date - current_time).total_seconds()
                                )
                                if remaining_seconds > 0:
                                    kb.add_button(
                                        f"🔍 Check Card ({remaining_seconds}s)",
                                        f"orders:check:{order_id}:{order_id}",
                                        ButtonPriority.PRIMARY,
                                    )
                                    logger.info(
                                        f"⏰ Check available with {remaining_seconds}s remaining"
                                    )
                                else:
                                    # Timer expired
                                    kb.add_button(
                                        "⏰ Check Expired",
                                        f"orders:check_expired:{order_id}:{order_id}",
                                        ButtonPriority.TERTIARY,
                                    )
                                    logger.info(f"⏰ Check timer expired")
                            else:
                                logger.info(f"⏰ Check date in the past: {check_date_str}")
                        except Exception as e:
                            logger.error(f"Error parsing check_Date: {e}")
                # Add download button - Now available after viewing
                kb.add_button(
                    "📥 Download Card Data",
                    f"orders:download:{order_id}",
                    ButtonPriority.SECONDARY,
                )
                # Add navigation
                kb.add_navigation_row(
                    back_text="📋 My Orders", back_callback="menu:orders"
                )
                await ui_manager.edit_message_safely(
                    callback, card_display, kb.build(), add_watermark=True
                )
                logger.info(f"✅ Displayed pending card details for order {order_id}")
            else:
                error = response.error or "Unknown error"
                logger.error(f"❌ view_order failed: {error}")
                await callback.answer(
                    f"❌ Failed to load card: {error}", show_alert=True
                )
        except Exception as e:
            logger.error(f"Error in cb_view_pending_card: {e}")
            await callback.answer("❌ Failed to load card details", show_alert=True)

    @handle_errors(error_message="❌ Check timer expired", show_alert=True)
    async def cb_check_expired(self, callback: CallbackQuery) -> None:
        """Handle expired check timer"""
        await callback.answer(
            "⏰ Check timer has expired for security reasons", show_alert=True
        )
        # Show expired message
        expired_message = AnimatedMessages.create_timer_expired_message()
        # Create basic keyboard without check button
        kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
        kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
        kb.add_navigation_row(back_text="🏠 Main", back_callback="menu:main")
        try:
            await ui_manager.edit_message_safely(
                callback, expired_message, kb.build(), add_watermark=False
            )
        except Exception as e:
            logger.debug(f"Error showing expired message: {e}")

    async def _update_order_async(
        self, order_id: str, update_data: Dict[str, Any]
    ) -> None:
        """Asynchronously update order in database without blocking UI"""
        try:
            await self.purchases.update_one({"_id": order_id}, {"$set": update_data})
            logger.debug(f"Updated order {order_id} with unmask data")
        except Exception as e:
            logger.error(f"Error updating order async: {e}")

    async def _update_timer_display_realtime(
        self,
        callback: CallbackQuery,
        order_id: str,
        card_id: str,
        expiry_timestamp: int,
    ) -> None:
        """Real-time timer display with 1-second updates"""
        try:
            while True:
                await asyncio.sleep(1)  # Update every second
                current_time = int(datetime.now(timezone.utc).timestamp())
                remaining_time = expiry_timestamp - current_time
                if remaining_time <= 0:
                    # Timer expired, update keyboard once
                    try:
                        short_card_id = self._get_short_id(card_id)
                        kb_markup = PostCheckoutUI.create_card_view_keyboard(
                            card_id=short_card_id,
                            order_id=order_id,
                            can_check=True,
                            expiry_timestamp=expiry_timestamp,
                            is_unmasked=True,
                            check_status="expired",  # Timer expired
                        )
                        await ui_manager.edit_keyboard_safely(callback, kb_markup)
                        logger.info(
                            f"Timer expired for card {card_id}, check button disabled"
                        )
                    except Exception as e:
                        logger.debug(f"Error updating expired timer: {e}")
                    break
                # Update keyboard with countdown every second
                try:
                    short_card_id = self._get_short_id(card_id)
                    kb_markup = PostCheckoutUI.create_card_view_keyboard(
                        card_id=short_card_id,
                        order_id=order_id,
                        can_check=True,
                        expiry_timestamp=expiry_timestamp,
                        is_unmasked=True,
                        check_status="active",  # Keep check button active
                    )
                    await ui_manager.edit_keyboard_safely(callback, kb_markup)
                    # Log every 5 seconds to avoid spam
                    if remaining_time % 5 == 0:
                        logger.debug(
                            f"Timer update: {remaining_time}s remaining for card {card_id}"
                        )
                except Exception as e:
                    logger.debug(f"Error updating timer display: {e}")
                    # Continue anyway, don't break the timer
        except Exception as e:
            logger.debug(f"Timer update error (non-critical): {e}")

    async def _update_timer_display_realtime_new_message(
        self,
        message,
        order_id: str,
        card_id: str,
        expiry_timestamp: int,
    ) -> None:
        """Real-time timer display with 1-second updates for new messages"""
        try:
            while True:
                await asyncio.sleep(1)  # Update every second
                current_time = int(datetime.now(timezone.utc).timestamp())
                remaining_time = expiry_timestamp - current_time
                if remaining_time <= 0:
                    # Timer expired, update keyboard once
                    try:
                        short_card_id = self._get_short_id(card_id)
                        kb_markup = PostCheckoutUI.create_card_view_keyboard(
                            card_id=short_card_id,
                            order_id=order_id,
                            can_check=True,
                            expiry_timestamp=expiry_timestamp,
                            is_unmasked=True,
                            check_status="expired",  # Timer expired
                        )
                        await message.edit_reply_markup(reply_markup=kb_markup)
                        logger.info(
                            f"Timer expired for card {card_id}, check button disabled"
                        )
                    except Exception as e:
                        logger.debug(f"Error updating expired timer: {e}")
                    break
                # Update keyboard with countdown every second
                try:
                    short_card_id = self._get_short_id(card_id)
                    kb_markup = PostCheckoutUI.create_card_view_keyboard(
                        card_id=short_card_id,
                        order_id=order_id,
                        can_check=True,
                        expiry_timestamp=expiry_timestamp,
                        is_unmasked=True,
                        check_status="active",  # Keep check button active
                    )
                    await message.edit_reply_markup(reply_markup=kb_markup)
                    # Log every 5 seconds to avoid spam
                    if remaining_time % 5 == 0:
                        logger.debug(
                            f"Timer update: {remaining_time}s remaining for card {card_id}"
                        )
                except Exception as e:
                    logger.debug(f"Error updating timer display: {e}")
                    # Continue anyway, don't break the timer
        except Exception as e:
            logger.debug(f"Timer update error (non-critical): {e}")

    async def cb_download_card(self, callback: CallbackQuery) -> None:
        """Handle card download to provide card data in text format."""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Parse callback data: orders:download:order_id:card_id
            parts = (callback.data or "").split(":")
            if len(parts) < 4:
                await callback.answer("❌ Invalid download request", show_alert=True)
                return
            order_id = parts[2]
            short_card_id = parts[3]
            card_id = self._resolve_card_id(
                short_card_id
            )  # Resolve short ID to full ID
            # If resolution failed and we have a short ID, try to find it in order data
            if card_id == short_card_id and len(short_card_id) < 20:
                logger.warning(
                    f"Short card ID {short_card_id} not found in cache, searching in order data"
                )
                # Get user from database first
                db_user = await self.user_service.get_user_by_telegram_id(user.id)
                if db_user:
                    # Try to find the full card ID in the order data
                    full_card_id = await self._find_full_card_id_in_order(
                        str(db_user.id), order_id, short_card_id
                    )
                    if full_card_id:
                        card_id = full_card_id
                        logger.info(
                            f"✅ Found full card ID: {card_id} from short: {short_card_id}"
                        )
                    else:
                        logger.error(
                            f"❌ Could not find full card ID for short: {short_card_id}"
                        )
                        await callback.answer("❌ Card ID not found", show_alert=True)
                        return
                else:
                    await callback.answer("❌ User not found", show_alert=True)
                    return
            else:
                # Get user from database
                db_user = await self.user_service.get_user_by_telegram_id(user.id)
                if not db_user:
                    await callback.answer("❌ User not found", show_alert=True)
                    return
            logger.info(
                f"Downloading card {card_id} (loaded from short: {short_card_id}) in order {order_id} for user {user.id}"
            )
            
            # Fetch order to determine API version and get correct order ID
            order = await self._fetch_order_for_card(str(db_user.id), card_id)
            api_version = order.get("api_version", "v1") if order else "v1"
            
            # For API v1, extract the correct order_id from the API response
            actual_order_id = order_id
            if api_version == "v1" and order and "extracted_cards" in order and order["extracted_cards"]:
                # For API v1, use the _id from the API response (stored in extracted_cards)
                api_order_id = order["extracted_cards"][0].get("_id")
                if api_order_id:
                    actual_order_id = str(api_order_id)
                    logger.info(f"📋 [Download] Using API v1 order_id from API response: {actual_order_id}")
            
            logger.info(f"📥 [Download] API version: {api_version}, order_id: {actual_order_id}")
            
            # Show loading stages concurrently with download work
            async def download_work():
                async with get_external_api_service() as api_service:
                    # Route to correct API version - don't force API v3
                    force_v3 = (api_version == "v3" or api_version == "base3")
                    logger.info(f"📞 [Download] Calling download_card with force_api_v3={force_v3}")
                    return await api_service.download_card(
                        order_id=actual_order_id, force_api_v3=force_v3
                    )

            response = await LoadingStages.run_concurrent_loading(
                callback,
                DOWNLOAD_STAGES,
                download_work(),
                operation_name="Card Download",
                send_new_message=True,  # Don't edit card details message
            )
            if response.success and response.data:
                # Get raw card data from response
                raw_data = response.data
                if not isinstance(raw_data, str):
                    raw_data = str(raw_data)
                # Create filename for the txt file
                from datetime import datetime

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"cards_order_{order_id}_{timestamp}.txt"
                # Create the text document
                from aiogram.types import BufferedInputFile

                file_content = raw_data.encode("utf-8")
                document = BufferedInputFile(file_content, filename=filename)
                # Create message with clean, minimal UI
                download_msg = create_message(MessageType.SUCCESS)
                download_msg.set_title("Card Data", "📥")
                # Show raw data in copyable format with better limits
                # Remove headers (first 2 lines: header row + blank line) for display
                lines = raw_data.split('\n', 2)  # Split into max 3 parts
                data_without_headers = lines[2] if len(lines) > 2 else raw_data
                
                max_display_length = 2000  # Better limit for readability
                if len(data_without_headers) > max_display_length:
                    # Show first part + indication of more in file
                    display_data = data_without_headers[:max_display_length]
                    # Find last complete line to avoid cutting in the middle
                    last_newline = display_data.rfind("\n")
                    if (
                        last_newline > max_display_length * 0.8
                    ):  # If we have a good break point
                        display_data = display_data[:last_newline]
                    display_data += f"\n\n📄 <i>+{len(data_without_headers) - len(display_data)} more characters in file</i>"
                else:
                    display_data = data_without_headers
                download_msg.add_content(f"<pre>{display_data}</pre>")
                download_msg.add_section(
                    "💡 Info",
                    "• Tap and hold above text to copy\n"
                    "• Complete data available in attached file\n"
                    "• Keep this information secure",
                    "ℹ️",
                )
                # Create clean keyboard with better styling
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.SPACIOUS, 1)
                kb.add_button("🔙 Back to Orders", "menu:orders", ButtonPriority.PRIMARY)
                # Send document with full message content as caption (combined in one message)
                try:
                    # Use the full message as caption for the document
                    full_message = download_msg.build(add_watermark=False)
                    # Telegram caption limit is 1024 chars, so we may need to truncate
                    if len(full_message) > 1024:
                        # Create clean, shorter caption (without headers)
                        preview_data = (
                            data_without_headers[:400] if len(data_without_headers) > 400 else data_without_headers
                        )
                        # Find last complete line
                        last_newline = preview_data.rfind("\n")
                        if last_newline > 300:
                            preview_data = preview_data[:last_newline]
                        short_caption = f"💳 <b>Card Data</b>\n\n<pre>{preview_data}</pre>\n\n📄 <i>Full data in file</i>"
                        await callback.message.answer_document(
                            document=document,
                            caption=short_caption,
                            reply_markup=kb.build(),
                            parse_mode="HTML",
                        )
                        # Send new message with full content instead of editing original message
                        await callback.message.answer(
                            full_message, reply_markup=kb.build(), parse_mode="HTML"
                        )
                    else:
                        # Caption is short enough, send everything in one document message
                        await callback.message.answer_document(
                            document=document,
                            caption=full_message,
                            reply_markup=kb.build(),
                            parse_mode="HTML",
                        )
                        # Keep the original card message (don't delete it)
                    await callback.answer("📥 Card data ready!", show_alert=False)
                except Exception as e:
                    logger.error(f"Error sending document: {e}")
                    # Fallback to message-only if document sending fails
                    # Send new message with fallback content instead of editing original message
                    await callback.message.answer(
                        download_msg.build(add_watermark=False),
                        reply_markup=kb.build(),
                        parse_mode="HTML",
                    )
                    await callback.answer(
                        "⚠️ File upload failed, data shown in message", show_alert=True
                    )
            else:
                error_msg = create_message(MessageType.ERROR)
                error_msg.set_title("Download Failed", "❌")
                error_msg.add_content(
                    f"Failed to download card data: {response.error or 'Unknown error'}"
                )
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button(
                    "🔄 Try Again",
                    f"orders:download:{order_id}:{short_card_id}",
                    ButtonPriority.PRIMARY,
                )
                kb.add_button("📋 Orders", "menu:orders", ButtonPriority.SECONDARY)
                # Send new message with error instead of editing existing message
                await callback.message.answer(
                    error_msg.build(add_watermark=False),
                    reply_markup=kb.build(),
                    parse_mode="HTML",
                )
                await callback.answer("❌ Download failed", show_alert=True)
        except Exception as e:
            logger.error(f"Error in cb_download_card: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_view_recent_cards(self, callback: CallbackQuery) -> None:
        """Handle viewing recently purchased cards after checkout completion."""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            # Parse callback data: orders:view_recent_cards:lookup_id
            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return
            lookup_id = parts[2]
            logger.info(
                f"Viewing recent cards for lookup_id {lookup_id} for user {user.id}"
            )
            # Check cache first
            cache_key = f"recent_cards:{user.id}:{lookup_id}"
            cached_orders = self._get_cached_order(cache_key)
            if cached_orders:
                recent_orders = cached_orders
            else:
                # Get user from database
                db_user = await self.user_service.get_user_by_telegram_id(user.id)
                if not db_user:
                    await callback.answer("❌ User not found", show_alert=True)
                    return
                
                # Create work coroutine for loading recent cards
                async def load_recent_cards_work():
                    # Get recent orders using optimized batch query
                    orders = await self._get_recent_orders_batch(
                        str(db_user.id), lookup_id
                    )
                    # Cache the results for 5 minutes
                    self._cache_order(cache_key, orders, ttl=300)
                    return orders
                
                # Run loading animation concurrently with fetching cards
                recent_orders = await LoadingStages.run_concurrent_loading(
                    callback,
                    RECENT_CARDS_STAGES,
                    load_recent_cards_work(),
                    operation_name="Load Recent Cards"
                )
            if recent_orders:
                # Create enhanced view of recent cards with individual buttons
                msg = create_message(MessageType.SUCCESS)
                msg.set_title("Your Recent Cards", "🃏")
                card_previews = []
                card_count = len(recent_orders)
                # Show all cards with detailed information
                for i, order in enumerate(recent_orders, 1):
                    # Extract card info using cached data
                    card_data = order.get("metadata", {}).get("card_data", {})
                    bank = card_data.get("bank", "Unknown Bank")
                    brand = card_data.get("brand", "")
                    level = card_data.get("level", "")
                    price = order.get("price", 0.0)
                    # Use card status, not order status
                    status = card_data.get("status", order.get("status", "active"))
                    card_name = bank
                    if brand and brand != bank:
                        card_name += f" {brand}"
                    if level:
                        card_name += f" {level}"
                    status_icon = (
                        "✅" if status.lower() in ["active", "completed"] else "📋"
                    )
                    # Format card preview with better styling
                    card_previews.append(
                        f"{i}. {status_icon} <b>{card_name}</b> -                 ${price:.2f}"
                    )
                msg.add_list_section(
                    f"Your Cards ({card_count} purchased)", card_previews, "💳"
                )
                msg.add_section(
                    "🚀 Card Actions",
                    "• <b>Click any card button</b> below to view full details\n• <b>View Details</b> to see complete card information\n• <b>Unmask</b> to reveal sensitive card data\n• <b>Download</b> card data for offline use",
                    "🚀",
                )
                # Create keyboard with individual card buttons
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 1)
                # Add individual card buttons (limit to 9 cards to avoid callback length issues)
                for i, order in enumerate(recent_orders[:9], 1):
                    card_id = order.get("external_product_id", str(order.get("_id")))
                    card_data = order.get("metadata", {}).get("card_data", {})
                    bank = card_data.get("bank", "Unknown Bank")
                    brand = card_data.get("brand", "")
                    level = card_data.get("level", "")
                    price = order.get("price", 0.0)
                    # Build better card name for button
                    card_name = bank
                    if brand and brand != bank:
                        card_name += f" {brand}"
                    if level:
                        card_name += f" {level}"
                    # Truncate card name for button text but keep it more informative
                    if len(card_name) > 20:
                        card_name = card_name[:17] + "..."
                    # All View Card buttons call the view_card endpoint
                    kb.add_button(
                        f"🃏 {i}. {card_name} -                 ${price:.2f}",
                        f"orders:view_card:{self._get_short_id(card_id)}",
                        ButtonPriority.PRIMARY,
                    )
                # If more than 9 cards, show a summary button
                if len(recent_orders) > 9:
                    kb.add_button(
                        f"📊 View All {len(recent_orders)} Cards",
                        "menu:orders",
                        ButtonPriority.SECONDARY,
                    )
                # Add navigation buttons
                kb.add_button("📋 All Orders", "menu:orders", ButtonPriority.SECONDARY)
                kb.add_navigation_row(back_text="🏠 Main", back_callback="menu:main")
                await ui_manager.edit_message_safely(
                    callback, msg.build(add_watermark=False), kb.build()
                )
                await callback.answer("✅ Cards loaded!")
            else:
                # No recent cards found
                msg = create_message(MessageType.INFO)
                msg.set_title("No Recent Cards", "🃏")
                msg.add_content(
                    "No recent card purchases found. Browse the catalog to find cards!"
                )
                kb = create_enhanced_keyboard().set_style(KeyboardStyle.COMPACT, 2)
                kb.add_button("🔎 Browse Cards", "menu:browse", ButtonPriority.PRIMARY)
                kb.add_button("📋 All Orders", "menu:orders", ButtonPriority.SECONDARY)
                kb.add_navigation_row(back_text="🏠 Main", back_callback="menu:main")
                await ui_manager.edit_message_safely(
                    callback, msg.build(add_watermark=False), kb.build()
                )
                await callback.answer()
        except Exception as e:
            logger.error(f"Error in cb_view_recent_cards: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    def _is_unmask_data(self, order: Dict[str, Any]) -> bool:
        """Check if order data contains unmasked (sensitive) card information."""
        try:
            # Check for unmasked_data field
            if order.get("unmasked_data"):
                return True
            # Check for is_unmasked flag
            if order.get("is_unmasked"):
                return True
            # Check extracted_cards for unmasked status
            extracted_cards = order.get("extracted_cards", [])
            if extracted_cards:
                for card in extracted_cards:
                    # Check if card has been unmasked on backend
                    if card.get("backend_unmasked") or card.get(
                        "unmask_operation_successful"
                    ):
                        return True
                    # Check card_number_status
                    if card.get("card_number_status") == "unmasked":
                        return True
            # Check card_data for sensitive information
            card_data = order.get("metadata", {}).get("card_data", {})
            if card_data:
                # Look for full card numbers (not masked)
                cc = card_data.get("cc") or card_data.get("card_number", "")
                if cc and len(cc.replace(" ", "")) > 10 and not "*" in cc:
                    return True
                # Look for CVV
                if card_data.get("cvv"):
                    return True
            # Check raw_data sections for unmasked content
            raw_data = order.get("raw_data", {})
            if raw_data:
                sections = raw_data.get("sections", [])
                for section in sections:
                    if isinstance(section, dict):
                        content = section.get("content", "")
                        # Look for patterns indicating unmasked data
                        if (
                            "Card Number:" in content or "CC:" in content
                        ) and not "*" in content:
                            return True
                        if "CVV:" in content:
                            return True
            return False
        except Exception as e:
            logger.warning(f"Error checking unmask data: {e}")
            return False

    def _get_status_icon(self, status: str) -> str:
        """Get icon for order status."""
        status_lower = status.lower() if status else ""
        if status_lower in ["live", "active", "valid", "success"]:
            return "✅"
        elif status_lower in ["dead", "invalid", "expired", "failed"]:
            return "❌"
        elif status_lower in ["refunded", "cancelled"]:
            return "🔄"
        elif status_lower in ["pending", "processing"]:
            return "⏳"
        else:
            return "📋"

    def _create_comprehensive_card_display(
        self,
        card_data: Dict[str, Any],
        order: Dict[str, Any],
        show_sensitive: bool = True,
    ) -> str:
        """Create stylish card display with clear, organized sections."""
        # Validation
        if not isinstance(card_data, dict):
            logger.error(f"Invalid card_data type: {type(card_data)}")
            return "❌ Invalid card data format"
        if not card_data:
            logger.warning("Empty card_data provided to display method")
            return "❌ No card data available"
        # Extract all available data fields
        all_fields = self._extract_all_card_fields(card_data, order)
        # Simple generic header - no detailed card information
        title = "Card Details"
        # Start message with clean header
        status_icon = self._get_status_icon(all_fields.get("order_status", ""))
        message = f"💳 <b>{title}</b> {status_icon}\n"
        message += "━━━━━━━━━━━━━━━━━━━━━━\n\n"
        # Section 1: PAYMENT CREDENTIALS (Most Important)
        creds = []
        # Use full_card_number if available (unmasked), otherwise use card_number (masked)
        if (
            all_fields.get("full_card_number")
            and all_fields["full_card_number"] != "[PAN_REDACTED]"
        ):
            creds.append(
                f"💳 <b>Card:</b> <code>{all_fields['full_card_number']}</code>"
            )
        elif (
            all_fields.get("card_number")
            and all_fields["card_number"] != "[PAN_REDACTED]"
        ):
            creds.append(f"💳 <b>Card:</b> <code>{all_fields['card_number']}</code>")
        if all_fields.get("expiry"):
            creds.append(f"📅 <b>Expiry:</b> <code>{all_fields['expiry']}</code>")
        if all_fields.get("cvv"):
            creds.append(f"🔐 <b>CVV:</b> <code>{all_fields['cvv']}</code>")
        if creds:
            message += "🔓 <b>Payment Credentials</b>\n"
            message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
            message += "\n".join(creds) + "\n\n"
        # Section 2: CARD INFO (Bank, Type, Country)
        card_info = []
        if all_fields.get("bank"):
            card_info.append(f"🏦 <b>Bank:</b> {all_fields['bank']}")
        if all_fields.get("type"):
            card_info.append(f"🃏 <b>Type:</b> {all_fields['type']}")
        if all_fields.get("level"):
            card_info.append(f"⭐ <b>Level:</b> {all_fields['level']}")
        if all_fields.get("country"):
            try:
                from utils.dynamic_country_flags import get_dynamic_country_with_flag

                country_with_flag = (
                    get_dynamic_country_with_flag(all_fields["country"])
                    or all_fields["country"]
                )
                # Extract flag emoji if present, otherwise use globe
                if (
                    " " in country_with_flag
                    and len(country_with_flag.split(" ")[0]) == 2
                ):
                    flag, country_name = country_with_flag.split(" ", 1)
                    card_info.append(f"{flag} <b>Country:</b> {country_name}")
                else:
                    card_info.append(f"🌍 <b>Country:</b> {country_with_flag}")
            except Exception:
                country_with_flag = all_fields["country"]
                card_info.append(f"🌍 <b>Country:</b> {country_with_flag}")
        if all_fields.get("bin"):
            card_info.append(f"🔢 <b>BIN:</b> <code>{all_fields['bin']}</code>")
        if card_info:
            message += "💎 <b>Card Information</b>\n"
            message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
            message += "\n".join(card_info) + "\n\n"
        # Section 2.5: CARD STATUS (Refund, Decline, etc.)
        # Only show if it's specific to the card itself (like Refunded, Declined, Live, Dead)
        # Don't show generic order statuses here
        # SKIP this section if we'll show the Check Status section later (to avoid duplication)
        
        # Check if we'll show check status section
        main_status_for_check = card_data.get("status", "") or all_fields.get("order_status", "")
        check_status_for_check = card_data.get("check_status", "") or card_data.get("card_status", "")
        is_checked_for_check = card_data.get("is_checked") or card_data.get("checked")
        status_lower_for_check = str(main_status_for_check).lower()
        is_check_result_for_check = status_lower_for_check in ["nonrefundable", "refunded", "declined", "approved", "valid", "invalid"]
        will_show_check_status = check_status_for_check or is_check_result_for_check or is_checked_for_check
        
        # Only show basic card status if we WON'T show the detailed check status
        status_info = []
        if not will_show_check_status and all_fields.get("card_status"):
            status = all_fields["card_status"]
            # Only show card-specific statuses here, not order-level statuses
            if status in ["Refunded", "Declined", "Live", "Dead"]:
                if status == "Refunded":
                    status_info.append(f"💰 <b>Status:</b> {status}")
                    if all_fields.get("refund_reason"):
                        status_info.append(
                            f"📝 <b>Reason:</b> {all_fields['refund_reason']}"
                        )
                    if all_fields.get("decline_reason"):
                        status_info.append(
                            f"❌ <b>Decline:</b> {all_fields['decline_reason']}"
                        )
                elif status == "Declined":
                    status_info.append(f"❌ <b>Status:</b> {status}")
                    if all_fields.get("decline_reason"):
                        status_info.append(
                            f"📝 <b>Reason:</b> {all_fields['decline_reason']}"
                        )
                elif status == "Live":
                    status_info.append(f"✅ <b>Status:</b> {status}")
                elif status == "Dead":
                    status_info.append(f"💀 <b>Status:</b> {status}")
        if status_info:
            message += "📊 <b>Card Status</b>\n"
            message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
            message += "\n".join(status_info) + "\n\n"
        # Section 3: CARDHOLDER (Name, Contact, Location)
        holder = []
        if all_fields.get("cardholder_name"):
            holder.append(f"👤 <b>Name:</b> \u200E{all_fields['cardholder_name']}")
        if all_fields.get("dob"):
            holder.append(f"🎂 <b>DOB:</b> {all_fields['dob']}")
        if all_fields.get("email"):
            holder.append(f"📧 <b>Email:</b> {all_fields['email']}")
        if all_fields.get("phone"):
            holder.append(f"📞 <b>Phone:</b> {all_fields['phone']}")
        if all_fields.get("address"):
            holder.append(f"📍 <b>Address:</b> \u200E{all_fields['address']}")
        # Location details (only if different from card country)
        location_parts = []
        if all_fields.get("state"):
            location_parts.append(all_fields["state"])
        if all_fields.get("city"):
            location_parts.append(all_fields["city"])
        if all_fields.get("zip_code"):
            location_parts.append(all_fields["zip_code"])
        if location_parts:
            holder.append(f"🏙️ <b>Location:</b> {', '.join(location_parts)}")
        if holder:
            message += "👥 <b>Cardholder Details</b>\n"
            message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
            message += "\n".join(holder) + "\n\n"
        # Section 4: ADDITIONAL IDENTITY INFORMATION (if available)
        identity_info = []
        if all_fields.get("ssn"):
            identity_info.append(f"🆔 <b>SSN:</b> <code>{all_fields['ssn']}</code>")
        if all_fields.get("drivers_license"):
            identity_info.append(
                f"🪪 <b>Driver's License:</b> <code>{all_fields['drivers_license']}</code>"
            )
        if all_fields.get("mothers_maiden_name"):
            identity_info.append(
                f"👪 <b>Mother's Maiden Name:</b> {all_fields['mothers_maiden_name']}"
            )
        if identity_info:
            message += "🔐 <b>Additional Identity Details</b>\n"
            message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
            message += "\n".join(identity_info) + "\n\n"
        # Section 5: TECHNICAL DETAILS (IP, User Agent, Additional Info)
        # Only show technical details if card status is not PENDING
        card_status = all_fields.get("card_status", "").upper()
        is_pending = card_status == "PENDING"
        if not is_pending:
            technical_info = []
            if all_fields.get("ip_address"):
                technical_info.append(
                    f"🌐 <b>IP Address:</b> <code>{all_fields['ip_address']}</code>"
                )
            if all_fields.get("user_agent"):
                # Truncate long user agent strings
                ua = all_fields["user_agent"]
                if len(ua) > 60:
                    ua = ua[:57] + "..."
                technical_info.append(f"🖥️ <b>User Agent:</b> <code>{ua}</code>")
            if all_fields.get("additional_info"):
                technical_info.append(
                    f"ℹ️ <b>Additional Info:</b> {all_fields['additional_info']}"
                )
            if technical_info:
                message += "🔧 <b>Technical Details</b>\n"
                message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
                message += "\n".join(technical_info) + "\n\n"
        
        # Section 6: CARD CHECK STATUS (After Technical Details)
        # Show check status if card has been checked (Refunded, NonRefundable, etc.)
        check_info = []
        main_status = card_data.get("status", "") or all_fields.get("order_status", "")
        check_status = card_data.get("check_status", "") or card_data.get("card_status", "")
        is_checked = card_data.get("is_checked") or card_data.get("checked")
        
        # Determine if we have check result data to show
        status_lower = str(main_status).lower()
        is_check_result = status_lower in ["nonrefundable", "refunded", "declined", "approved", "valid", "invalid"]
        
        if check_status or is_check_result or is_checked:
            # Determine status display
            if check_status:
                status_display = str(check_status).upper()
                is_live = str(check_status).lower() in ["live", "approved", "valid", "active", "nonrefundable"]
            elif is_check_result:
                status_display = str(main_status).upper()
                is_live = status_lower in ["nonrefundable", "approved", "valid", "active"]
            else:
                status_display = ""
                is_live = False
            
            # Add status line
            if status_display:
                if is_live:
                    check_info.append(f"🟢 <b>Status:</b> <b>{status_display}</b>")
                else:
                    check_info.append(f"🔴 <b>Status:</b> <b>{status_display}</b>")
            
            # Add timestamp if available
            checked_at = (
                card_data.get("checked_at") 
                or card_data.get("checkedAt") 
                or all_fields.get("checked_at")
            )
            if checked_at:
                try:
                    if isinstance(checked_at, str):
                        from datetime import datetime
                        try:
                            dt = datetime.fromisoformat(checked_at.replace('Z', '+00:00'))
                            checked_str = dt.strftime("%Y-%m-%d %H:%M:%S UTC")
                        except:
                            checked_str = str(checked_at)
                    else:
                        checked_str = str(checked_at)
                    check_info.append(f"✅ <b>Verified:</b> {checked_str}")
                except:
                    pass
            
            # Add default message based on status
            response = card_data.get("response") or card_data.get("message")
            if response:
                check_info.append(f"💬 <b>Message:</b> {response}")
            elif status_display:
                if status_lower == "nonrefundable":
                    check_info.append(f"💬 <b>Message:</b> ✅ Card is working! No refund issued - ready to use")
                elif status_lower == "refunded":
                    check_info.append(f"💬 <b>Message:</b> 💰 Card was declined - Full refund issued automatically to your balance")
            
            # Only show section if we have info
            if check_info:
                message += "🔍 <b>Card Check Status</b>\n"
                message += "┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈\n"
                message += "\n".join(check_info) + "\n\n"
        
        # Simple security notice
        message += "━━━━━━━━━━━━━━━━━━━━━━\n"
        message += "🔒 <i>Keep secure and private</i>\n"
        return message

    def _extract_all_card_fields(
        self, card_data: Dict[str, Any], order: Dict[str, Any]
    ) -> Dict[str, str]:
        """Extract and normalize card fields - simplified."""
        fields = {}
        # Extract from card_data (primary source)
        self._extract_from_card_data(fields, card_data)
        # Extract from order data (secondary source)
        self._extract_from_order_data(fields, order)
        # Clean invalid values
        cleaned = {}
        invalid_values = {
            "none",
            "null",
            "undefined",
            "",
            "[pan_redacted]",
            "no address",
        }
        for key, value in fields.items():
            if value:
                cleaned_value = str(value).strip()
                if cleaned_value.lower() not in invalid_values:
                    cleaned[key] = cleaned_value
        return cleaned

    def _extract_from_card_data(
        self, fields: Dict[str, str], card_data: Dict[str, Any]
    ) -> None:
        """Extract fields from card_data - handles both internal and API v1 field names."""
        # Core card info
        for key in ["bank", "brand", "type", "level"]:
            if card_data.get(key):
                fields[key] = card_data[key]
        # Card number - handle both 'card_number' and API v1 'cc'
        card_number = card_data.get("card_number") or card_data.get("cc")
        if card_number and card_number != "[PAN_REDACTED]":
            # Determine if it's masked or unmasked based on content
            if "*" in card_number or "[PAN_REDACTED]" in card_number:
                fields["card_number"] = card_number  # Masked
            else:
                # Check if it's a full card number
                card_digits = re.sub(r"[^\d]", "", card_number)
                if len(card_digits) >= 13 and card_digits.isdigit():
                    fields[
                        "full_card_number"
                    ] = card_digits  # Full unmasked (for display compatibility)
                    fields["card_number"] = card_digits  # Also store in main field
                else:
                    fields["card_number"] = card_number  # Partial or other format
        # BIN
        if card_data.get("bin"):
            fields["bin"] = card_data["bin"]
        elif card_number:
            # Extract BIN from card number (first 6 digits)
            card_digits = re.sub(r"[^\d]", "", card_number)
            if len(card_digits) >= 6:
                fields["bin"] = card_digits[:6]
        # Location
        for key in ["country", "continent", "state", "city"]:
            if card_data.get(key):
                fields[key] = card_data[key]
        if card_data.get("zip") or card_data.get("zip_code"):
            fields["zip_code"] = card_data.get("zip") or card_data.get("zip_code")
        # Cardholder info - handle both field name formats
        # API v1 uses 'name', internal uses 'cardholder_name'
        cardholder_name = card_data.get("cardholder_name") or card_data.get("name")
        if cardholder_name:
            fields["cardholder_name"] = cardholder_name
        # Also check for firstname/lastname
        if card_data.get("firstname") or card_data.get("lastname"):
            fname = card_data.get("firstname", "").strip()
            lname = card_data.get("lastname", "").strip()
            if fname or lname:
                fields["cardholder_name"] = f"{fname} {lname}".strip()
        for key in ["email", "phone"]:
            if card_data.get(key):
                fields[key] = card_data[key]
        if card_data.get("address") and card_data["address"] not in [
            "",
            "No address",
            None,
        ]:
            fields["address"] = card_data["address"]
        # DOB
        if card_data.get("dob_available") is not None:
            fields["dob"] = (
                "Available" if card_data["dob_available"] else "Not Available"
            )
        elif card_data.get("dob"):
            fields["dob"] = card_data["dob"]
        # Sensitive data - handle API v1 field names
        if card_data.get("cvv"):
            fields["cvv"] = str(card_data["cvv"])
        # Handle expiry: try 'expiry', 'expiry_date', 'exp', or construct from expmonth/expyear
        expiry = (
            card_data.get("expiry")
            or card_data.get("expiry_date")
            or card_data.get("exp")
        )
        if expiry:
            fields["expiry"] = str(expiry)
        elif card_data.get("expmonth") and card_data.get("expyear"):
            # Construct expiry from month/year
            month = str(card_data["expmonth"]).zfill(2)
            year = str(card_data["expyear"])
            if len(year) > 2:
                year = year[-2:]  # Take last 2 digits
            fields["expiry"] = f"{month}/{year}"
        # Status and related information
        if card_data.get("status"):
            fields["card_status"] = card_data["status"]
        # Refund information
        if card_data.get("refund_status"):
            fields["refund_status"] = card_data["refund_status"]
        if card_data.get("refund_reason"):
            fields["refund_reason"] = card_data["refund_reason"]
        if card_data.get("decline_reason"):
            fields["decline_reason"] = card_data["decline_reason"]
        # API v1 additional fields that may be useful
        # IP Address
        if card_data.get("ip"):
            fields["ip_address"] = card_data["ip"]
        # Additional identity fields (if available)
        if card_data.get("dl"):
            fields["drivers_license"] = card_data["dl"]
        if card_data.get("ssn"):
            fields["ssn"] = card_data["ssn"]
        if card_data.get("mmn"):
            fields["mothers_maiden_name"] = card_data["mmn"]
        # User agent
        if card_data.get("ua"):
            fields["user_agent"] = card_data["ua"]
        # Other/additional info
        if card_data.get("other"):
            fields["additional_info"] = card_data["other"]

    def _extract_from_order_data(
        self, fields: Dict[str, str], order: Dict[str, Any]
    ) -> None:
        """Extract fields from order data."""
        # Order info
        if order.get("status"):
            fields["order_status"] = order["status"].upper()
        if order.get("price"):
            fields["price"] = str(order["price"])
        # Extract timestamps if available (useful for API v1)
        # These won't be displayed in the card section but can be logged/tracked
        timestamp_fields = [
            "createdAt",
            "viewedAt",
            "checkedAt",
            "refundAt",
            "start_Date",
        ]
        for ts_field in timestamp_fields:
            if order.get(ts_field):
                # Store with a prefix to avoid conflicts
                fields[f"timestamp_{ts_field}"] = str(order[ts_field])
        # Extract sensitive data from unmasked_data if not already present
        unmasked = order.get("unmasked_data", {})
        if unmasked:
            if not fields.get("card_number") and unmasked.get("cc"):
                fields["card_number"] = unmasked["cc"]
            if not fields.get("cvv") and unmasked.get("cvv"):
                fields["cvv"] = str(unmasked["cvv"])
            if not fields.get("expiry") and unmasked.get("exp"):
                fields["expiry"] = str(unmasked["exp"])


def get_orders_router() -> Router:
    """Get orders router with all handlers."""
    router = Router()
    attach_common_middlewares(router)
    handlers = OrdersHandlers()
    # Main orders menu handler
    router.callback_query.register(handlers.cb_orders_menu, F.data == "menu:orders")
    # Pagination handlers - Now handled by cb_orders_menu for consistency
    router.callback_query.register(
        handlers.cb_orders_menu, F.data.startswith("orders:page:")
    )
    # All orders view
    router.callback_query.register(
        handlers.cb_all_orders, F.data == "orders:all_orders"
    )
    # Card view handlers
    router.callback_query.register(
        handlers.cb_view_purchased_card, F.data.startswith("orders:view_card:")
    )
    router.callback_query.register(
        handlers.cb_view_card_details, F.data.startswith("orders:view_details:")
    )
    router.callback_query.register(
        handlers.cb_view_pending_card, F.data.startswith("orders:view_pending:")
    )
    # Check handlers
    router.callback_query.register(
        handlers.cb_check_card, F.data.startswith("orders:check:")
    )
    router.callback_query.register(
        handlers.cb_check_info, F.data.startswith("orders:check_info:")
    )
    router.callback_query.register(
        handlers.cb_check_expired, F.data.startswith("orders:check_expired:")
    )
    # Cooldown handler
    router.callback_query.register(
        handlers.cb_check_cooldown, F.data.startswith("orders:check_cooldown:")
    )
    # Unmask and download handlers
    router.callback_query.register(
        handlers.cb_unmask_card, F.data.startswith("orders:unmask:")
    )
    router.callback_query.register(
        handlers.cb_download_card, F.data.startswith("orders:download:")
    )
    # Recent cards handler
    router.callback_query.register(
        handlers.cb_view_recent_cards, F.data.startswith("orders:view_recent_cards:")
    )
    logger.debug("Orders handlers registered")
    return router
