from flask import Flask, request, jsonify
import hmac
import hashlib
import json
import logging
from datetime import datetime
import os
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from waitress import serve

from .payment_config import OXA_PAY_API_KEY, generate_hmac, DEFAULT_HOST, DEFAULT_PORT
from ..database.sync_operations import (
    get_payment_by_track_id_sync,
    update_payment_status_sync,
    save_payment_details_sync,
    get_user_balance_sync,
    update_user_balance_sync,
    add_transaction_sync
)
from ..utils.payment_utils import format_crypto_amount, log_payment
from ..utils.payment_amount_handler import (
    check_payment_amounts, handle_underpayment, handle_overpayment, 
    handle_payment_completion, log_payment_amount_analysis
)


def format_crypto_amount(amount, currency=""):
    """
    Format cryptocurrency amount to avoid scientific notation for small values.

    Args:
        amount: The amount to format (float, int, or string)
        currency: The cryptocurrency code (optional)

    Returns:
        str: Properly formatted amount
    """
    try:
        # Convert to float first
        float_amount = float(amount)

        # For very small values (like BTC), use more decimal places
        if float_amount < 0.0001:
            # Format with up to 10 decimal places, removing trailing zeros
            formatted = f"{float_amount:.10f}".rstrip("0").rstrip(".")
        elif float_amount < 0.01:
            # For small values, use 8 decimal places
            formatted = f"{float_amount:.8f}".rstrip("0").rstrip(".")
        elif float_amount < 1:
            # For medium values, use 6 decimal places
            formatted = f"{float_amount:.6f}".rstrip("0").rstrip(".")
        else:
            # For larger values (like USDT), use 2 decimal places
            formatted = f"{float_amount:.2f}"

        # Add currency code if provided
        if currency:
            return f"{formatted} {currency}"
        return formatted

    except (ValueError, TypeError):
        # Return original value if conversion fails
        if currency:
            return f"{amount} {currency}"
        return str(amount)


# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create Flask app
app = Flask(__name__)
app.config['TESTING'] = os.environ.get('TESTING_MODE', 'false').lower() in ('true', '1', 'yes')


def verify_hmac_signature(data_bytes: bytes, hmac_header: str, api_secret_key: str) -> bool:
    """
    Verify HMAC signature for payment callbacks.

    Args:
        data_bytes: Raw request data
        hmac_header: HMAC signature from request header
        api_secret_key: API secret key for verification

    Returns:
        bool: True if signature is valid, False otherwise
    """
    try:
        # Generate expected HMAC signature
        expected_signature = hmac.new(
            api_secret_key.encode('utf-8'),
            data_bytes,
            hashlib.sha512
        ).hexdigest()

        # Compare signatures (constant-time comparison)
        return hmac.compare_digest(expected_signature, hmac_header)

    except Exception as e:
        logger.error(f"HMAC verification error: {e}")
        return False


def extract_user_id_from_query(request_url: str) -> Optional[int]:
    """
    Extract user_id from query parameters in the callback URL.

    Args:
        request_url: The full request URL

    Returns:
        Optional[int]: User ID if found, None otherwise
    """
    try:
        parsed_url = urlparse(request_url)
        query_params = parse_qs(parsed_url.query)
        
        # Try different possible parameter names
        user_id_param = (
            query_params.get('user_id', [None])[0] or
            query_params.get('userId', [None])[0] or
            query_params.get('client_id', [None])[0]
        )
        
        if user_id_param:
            return int(user_id_param)
            
    except (ValueError, TypeError, AttributeError) as e:
        logger.error(f"Error extracting user_id from URL: {e}")

    return None


def process_payment(
    data: Dict[str, Any],
    track_id: str,
    status: str,
    received_amount: Optional[str],
    request_url: Optional[str] = None,
) -> tuple:
    """
    Process payment based on payment status.

    Args:
        data: Payment callback data
        track_id: Payment tracking ID
        status: Payment status
        received_amount: Amount received in payment
        request_url: The original callback URL with possible query parameters

    Returns:
        tuple: (response_data, status_code)
    """
    logger.info(f"🔔 CALLBACK PROCESSING: track_id={track_id}, status={status}, amount={received_amount}")
    
    # Get existing payment record FIRST
    existing_payment = get_payment_by_track_id_sync(track_id)
    
    if not existing_payment:
        logger.error(f"❌ Payment not found in database: {track_id}")
        logger.error(f"This means the payment was not saved when the link was created!")
        return jsonify({
            "status": "error",
            "message": "Payment not found"
        }), 404
    
    logger.info(f"✅ Payment found in database: {track_id}")
    
    # Extract user_id from multiple sources (URL, existing payment, callback data)
    user_id = extract_user_id_from_query(request_url) if request_url else None
    
    # If not in URL, try existing payment record
    if not user_id and existing_payment:
        user_id = existing_payment.get("user_id")
        if user_id:
            logger.info(f"✅ user_id extracted from payment record: {user_id}")
    
    # If still not found, try callback data
    if not user_id and data:
        user_id = data.get("user_id") or data.get("userId") or data.get("client_id")
        if user_id:
            logger.info(f"✅ user_id extracted from callback data: {user_id}")
    
    if not user_id:
        logger.warning(f"⚠️ user_id not found for payment {track_id}")
    
    # Remove from auto-verification queue (callback received, no need for scheduled checks)
    try:
        from ..utils.auto_verification import get_verification_manager
        manager = get_verification_manager()
        if manager.remove_verification_task(track_id):
            logger.info(f"✅ Removed {track_id} from auto-verification queue (callback received)")
    except Exception as e:
        logger.debug(f"Could not remove from auto-verification: {e}")
    
    if status in ["completed", "confirmed", "success", "paid", "manual_accept"]:
        # Check if already processed
        if existing_payment.get("status") in ["completed", "verified", "confirmed", "success", "paid"]:
            logger.info(f"Payment {track_id} already processed")
            return jsonify({
                "status": "success",
                "message": "Payment already processed"
            }), 200
        
        # Process completed payment with underpayment/overpayment detection
        try:
            # Get payment amounts
            amount = float(received_amount or existing_payment.get("amount", 0))
            required_amount = float(existing_payment.get("amount", 0))
            
            # Check payment amounts for underpayment/overpayment
            payment_status, payment_details = check_payment_amounts(amount, required_amount, track_id)
            
            # Log payment amount analysis
            log_payment_amount_analysis(track_id, amount, required_amount, payment_status, payment_details)
            
            # Handle different payment scenarios
            if payment_status == "underpayment":
                # Handle underpayment
                update_payment_status_sync(
                    track_id=track_id,
                    new_status="underpaid",
                    actual_paid_amount=amount,
                    required_amount=required_amount,
                    remaining_amount=required_amount - amount,
                    underpayment_percent=payment_details["percentage"]
                )
                
                # Queue notification to user
                if user_id:
                    try:
                        from ..utils.notification_queue import add_notification
                        add_notification(
                            user_id=user_id,
                            notification_type="underpayment",
                            track_id=track_id,
                            received_amount=amount,
                            required_amount=required_amount
                        )
                    except Exception as e:
                        logger.error(f"Failed to queue underpayment notification: {e}")
                
                logger.warning(f"Underpayment detected for {track_id}: ${amount:.2f} < ${required_amount:.2f}")
                return jsonify({
                    "status": "underpaid",
                    "message": "Payment is underpaid",
                    "track_id": track_id,
                    "user_id": user_id,
                    "received_amount": amount,
                    "required_amount": required_amount,
                    "remaining_amount": required_amount - amount
                }), 200
                
            elif payment_status in ["overpayment", "significant_overpayment"]:
                # Handle overpayment - process normally but log the overpayment
                logger.info(f"Overpayment detected for {track_id}: ${amount:.2f} > ${required_amount:.2f}")
                
                # Update payment status atomically
                update_result = update_payment_status_sync(
                    track_id=track_id,
                    new_status="completed",
                    actual_paid_amount=amount,
                    required_amount=required_amount,
                    overpayment_amount=amount - required_amount,
                    overpayment_percent=payment_details["percentage"],
                    payment_verified=True,
                    completed_at=datetime.now()
                )
                
                if update_result and user_id:
                    # Update wallet balance with full amount (including overpayment) - single source of truth
                    try:
                        from payment_module.database.sync_operations import _get_database_connection
                        client, db = _get_database_connection()
                        users_collection = db.users
                        wallets_collection = db.wallets
                        
                        # Get user document to find user_id
                        user_doc = users_collection.find_one({"telegram_id": user_id})
                        
                        if user_doc and user_doc.get("_id"):
                            # Get current wallet balance
                            wallet_doc = wallets_collection.find_one({"user_id": str(user_doc.get("_id"))})
                            current_balance = wallet_doc.get("balance", 0.0) if wallet_doc else 0.0
                            logger.info(f"✅ User {user_id} current balance: ${current_balance:.2f}")
                            
                            # Calculate new balance with full amount (including overpayment)
                            new_balance = current_balance + amount
                            
                            # Update wallet balance (single source of truth)
                            wallet_result = wallets_collection.update_one(
                                {"user_id": str(user_doc.get("_id"))},
                                {"$set": {"balance": new_balance, "updated_at": datetime.now()}}
                            )
                            
                            if wallet_result.modified_count > 0:
                                balance_updated = True
                                logger.info(f"✅ Wallet balance updated to ${new_balance:.2f}")
                            else:
                                    # Wallet might not exist, create it
                                    logger.warning(f"⚠️ Wallet not found for user {user_id}, creating wallet...")
                                    wallet_doc = {
                                        "user_id": str(user_doc.get("_id")),
                                        "currency": "USD",
                                        "balance": new_balance,
                                        "daily_cap": 500.0,
                                        "monthly_cap": 2000.0,
                                        "locked": False,
                                        "created_at": datetime.now(),
                                        "updated_at": datetime.now()
                                    }
                                    wallets_collection.insert_one(wallet_doc)
                                    balance_updated = True
                                    logger.info(f"✅ Wallet created for user {user_id} with balance ${new_balance:.2f}")
                        else:
                            logger.warning(f"⚠️ User document not found for telegram_id {user_id}")
                            balance_updated = False
                    except Exception as e:
                        logger.error(f"Failed to update wallet balance: {e}")
                        balance_updated = False
                    
                    if balance_updated:
                        # Record transaction
                        add_transaction_sync(
                            user_id=user_id,
                            transaction_type="deposit",
                            amount=amount,
                            track_id=track_id,
                            payment_amount=amount,
                            required_amount=required_amount,
                            overpayment_amount=amount - required_amount,
                            status="completed"
                        )
                        
                        # Queue notification to user
                        try:
                            from ..utils.notification_queue import add_notification
                            add_notification(
                                user_id=user_id,
                                notification_type="overpayment",
                                track_id=track_id,
                                received_amount=amount,
                                required_amount=required_amount
                            )
                        except Exception as e:
                            logger.error(f"Failed to queue overpayment notification: {e}")
                        
                        logger.info(f"Overpayment processed for {track_id} - Balance updated: {current_balance:.2f} -> {new_balance:.2f}")
                
                return jsonify({
                    "status": "success",
                    "message": "Payment processed successfully (overpayment detected)",
                    "track_id": track_id,
                    "user_id": user_id,
                    "received_amount": amount,
                    "required_amount": required_amount,
                    "overpayment_amount": amount - required_amount,
                    "total_credited": amount
                }), 200
                
            else:
                # Normal payment - process as usual
                # Update payment status atomically
                update_result = update_payment_status_sync(
                    track_id=track_id,
                    new_status="completed",
                    expected_status="pending",
                    actual_paid_amount=amount,
                    payment_verified=True,
                    completed_at=datetime.now()
                )
                
                if update_result:
                    # Update wallet balance (single source of truth)
                    if user_id:
                        try:
                            from payment_module.database.sync_operations import _get_database_connection
                            client, db = _get_database_connection()
                            users_collection = db.users
                            wallets_collection = db.wallets
                            
                            # Get user document to find user_id
                            user_doc = users_collection.find_one({"telegram_id": user_id})
                            
                            if user_doc and user_doc.get("_id"):
                                # Get current wallet balance
                                wallet_doc = wallets_collection.find_one({"user_id": str(user_doc.get("_id"))})
                                current_balance = wallet_doc.get("balance", 0.0) if wallet_doc else 0.0
                                logger.info(f"✅ User {user_id} current balance: ${current_balance:.2f}")
                                
                                # Calculate new balance (amount only, no bonus)
                                new_balance = current_balance + amount
                                
                                # Update wallet balance (single source of truth)
                                wallet_result = wallets_collection.update_one(
                                    {"user_id": str(user_doc.get("_id"))},
                                    {"$set": {"balance": new_balance, "updated_at": datetime.now()}}
                                )
                                
                                if wallet_result.modified_count > 0:
                                    balance_updated = True
                                    logger.info(f"✅ Wallet balance updated to ${new_balance:.2f}")
                                else:
                                    # Wallet might not exist, create it
                                    logger.warning(f"⚠️ Wallet not found for user {user_id}, creating wallet...")
                                    wallet_doc = {
                                        "user_id": str(user_doc.get("_id")),
                                        "currency": "USD",
                                        "balance": new_balance,
                                        "daily_cap": 500.0,
                                        "monthly_cap": 2000.0,
                                        "locked": False,
                                        "created_at": datetime.now(),
                                        "updated_at": datetime.now()
                                    }
                                    wallets_collection.insert_one(wallet_doc)
                                    balance_updated = True
                                    logger.info(f"✅ Wallet created for user {user_id} with balance ${new_balance:.2f}")
                            else:
                                logger.warning(f"⚠️ User document not found for telegram_id {user_id}")
                                balance_updated = False
                        except Exception as e:
                            logger.error(f"Failed to update wallet balance: {e}")
                            balance_updated = False
                        
                        if balance_updated:
                            # Record transaction
                            add_transaction_sync(
                                user_id=user_id,
                                transaction_type="deposit",
                                amount=amount,  # Amount only, no bonus
                                track_id=track_id,
                                payment_amount=amount,
                                status="completed"
                            )
                            
                            # Send direct notification to user (if bot is available)
                            try:
                                # Try to send notification directly if bot is available
                                from ..utils.payment_amount_handler import create_payment_completion_message
                                
                                # Create the notification message
                                message, keyboard = create_payment_completion_message(
                                    track_id=track_id,
                                    received_amount=amount,
                                    required_amount=required_amount
                                )
                                
                                # Try to get bot instance and send message
                                try:
                                    from aiogram import Bot
                                    from config.settings import get_settings
                                    
                                    settings = get_settings()
                                    if settings.BOT_TOKEN:
                                        bot = Bot(token=settings.BOT_TOKEN)
                                        
                                        # Send message asynchronously
                                        import asyncio
                                        loop = asyncio.new_event_loop()
                                        asyncio.set_event_loop(loop)
                                        loop.run_until_complete(
                                            bot.send_message(
                                                chat_id=user_id,
                                                text=message,
                                                reply_markup=keyboard,
                                                parse_mode="HTML"
                                            )
                                        )
                                        loop.close()
                                        
                                        logger.info(f"✅ Payment completion notification sent to user {user_id}")
                                    else:
                                        logger.warning("No bot token available for notifications")
                                        
                                except Exception as bot_error:
                                    logger.error(f"Failed to send notification via bot: {bot_error}")
                                    
                                    # Fallback: Queue notification for later processing
                                    from ..utils.notification_queue import add_notification
                                    add_notification(
                                        user_id=user_id,
                                        notification_type="completed",
                                        track_id=track_id,
                                        received_amount=amount,
                                        required_amount=required_amount
                                    )
                                    logger.info(f"📬 Notification queued for user {user_id}")
                                    
                            except Exception as e:
                                logger.error(f"Failed to send completion notification: {e}")
                            
                            logger.info(f"Payment {track_id} completed successfully - Balance updated: {current_balance:.2f} -> {new_balance:.2f}")
                        else:
                            logger.error(f"Failed to update balance for user {user_id}")
                    
                    logger.info(f"Payment {track_id} completed successfully")
                    return jsonify({
                        "status": "success",
                        "message": "Payment processed successfully",
                        "track_id": track_id,
                        "user_id": user_id,
                        "amount": amount,
                        "total_credited": amount
                    }), 200
                else:
                    logger.error(f"Failed to update payment status for {track_id}")
                    return jsonify({
                        "status": "error",
                        "message": "Failed to update payment status"
                    }), 500
                
        except Exception as e:
            logger.error(f"Error processing completed payment {track_id}: {e}")
            return jsonify({
                "status": "error",
                "message": "Payment processing failed"
            }), 500
            
    elif status in ["pending", "confirming", "waiting", "new", "paying"]:
        # Update status for pending payments
        update_payment_status(track_id, status)
        logger.info(f"Payment {track_id} status updated to {status}")
        return jsonify({
            "status": "success",
            "message": "Payment status updated"
        }), 200
        
    elif status == "expired":
        update_payment_status(track_id, "expired")
        logger.warning(f"Payment {track_id} expired")
        return jsonify({
            "status": "success",
            "message": "Payment status updated"
        }), 200
        
    else:
        logger.warning(f"Unknown payment status: {status}")
        return jsonify({
            "status": "success",
            "message": "Payment status updated"
        }), 200


@app.route("/callback", methods=["POST"])
def handle_callback():
    """
    Handle payment callbacks from payment processor.
    Validates HMAC signature and processes payments.

    Returns:
        Response: HTTP response with appropriate status code
    """
    try:
        # Get raw request data for HMAC verification
        post_data_bytes = request.get_data()
        post_data = request.get_data(as_text=True)

        # Get the full request URL for extracting query parameters
        request_url = request.url

        # Enhanced logging for debugging
        callback_log_msg = f"🔔 CALLBACK RECEIVED at {datetime.now().isoformat()}"
        logger.info(callback_log_msg)
        logger.info(f"Received callback at URL: {request_url}")

        # Parse JSON data
        try:
            data = json.loads(post_data)
        except json.JSONDecodeError:
            logger.error("Invalid JSON in callback request")
            return jsonify({"error": "Invalid JSON format"}), 400

        # Log the received callback data
        logger.info(f"Received callback data: {json.dumps(data, indent=2, ensure_ascii=True)}")

        # Extract user_id directly from data if present
        user_id_from_data = (
            data.get("user_id") or data.get("userId") or data.get("client_id")
        )
        if user_id_from_data:
            logger.info(f"Found user_id in callback data: {user_id_from_data}")
            # Append user_id to URL for later extraction if needed
            if "?" in request_url:
                request_url += f"&user_id={user_id_from_data}"
            else:
                request_url += f"?user_id={user_id_from_data}"

        # Determine which API key to use based on callback type
        if data.get("type") in ["payment", "invoice"]:
            api_secret_key = OXA_PAY_API_KEY
        elif data.get("type") == "payout":
            api_secret_key = OXA_PAY_API_KEY
        else:
            logger.error(f"Invalid data type: {data.get('type')}")
            return jsonify({"error": "Invalid data type"}), 400

        # Validate HMAC signature if provided
        hmac_header = request.headers.get("HMAC")
        
        # Check if we're in sandbox mode (from callback data)
        is_sandbox = data.get("txs", [{}])[0].get("tx_hash") == "sandbox" if data.get("txs") else False

        # HMAC verification is required in production mode only
        # Skip HMAC in: TESTING mode, sandbox payments, or when PAYMENT_TESTING_MODE is enabled
        if app.config.get("TESTING") or is_sandbox or os.environ.get("PAYMENT_TESTING_MODE", "").lower() == "true":
            if is_sandbox:
                logger.info("⚠️ SANDBOX payment detected - skipping HMAC verification")
            elif app.config.get("TESTING"):
                logger.info("Running in TESTING mode - skipping HMAC verification")
            else:
                logger.info("PAYMENT_TESTING_MODE enabled - skipping HMAC verification")
        else:
            # HMAC header is required for all production requests
            if not hmac_header:
                logger.error("Missing HMAC header in payment callback")
                return jsonify({"error": "Missing HMAC signature"}), 401

            # Verify HMAC signature
            if not verify_hmac_signature(post_data_bytes, hmac_header, api_secret_key):
                logger.error("HMAC verification failed")
                return jsonify({"error": "Invalid HMAC signature"}), 401

            logger.debug("HMAC verification successful")

        # Extract key information from callback - support multiple field name formats
        track_id = (
            data.get("trackId")
            or data.get("track_id")
            or data.get("id")
            or data.get("invoice_id")
        )
        
        # Convert to string to ensure consistency
        track_id = str(track_id) if track_id else None
        
        status = data.get("status", "").lower()
        received_amount = (
            data.get("receivedAmount")
            or data.get("received_amount")
            or data.get("amount")
        )

        if not track_id:
            logger.error("Missing trackId/track_id in callback data")
            return jsonify({"error": "Missing track ID"}), 400

        logger.info(f"📋 Callback details: track_id={track_id}, status={status}, amount={received_amount}")
        
        # Process the payment with the full request URL
        return process_payment(data, track_id, status, received_amount, request_url)

    except Exception as e:
        logger.exception(f"Unexpected error in callback handler: {e}")
        return jsonify({"error": "Internal server error", "details": str(e)}), 500


@app.route("/health", methods=["GET"])
def health_check():
    """
    Health check endpoint for monitoring.

    Returns:
        str: Health status message
    """
    return jsonify({
        "status": "healthy",
        "service": "payment_callback",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })


def run_callback_server(host: str = DEFAULT_HOST, port: int = DEFAULT_PORT, debug: bool = False):
    """
    Run the payment callback server.

    Args:
        host: Host to bind to
        port: Port to bind to
        debug: Enable debug mode
    """
    logger.info(f"Starting payment callback server on {host}:{port}")
    
    if debug:
        app.run(host=host, port=port, debug=True)
    else:
        serve(app, host=host, port=port)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Payment Callback Server")
    parser.add_argument("--host", default=DEFAULT_HOST, help="Host to bind to")
    parser.add_argument("--port", type=int, default=DEFAULT_PORT, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    run_callback_server(args.host, args.port, args.debug)