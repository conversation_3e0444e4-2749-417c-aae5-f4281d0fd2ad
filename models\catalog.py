"""
Catalog and filter models for MongoDB
"""

from __future__ import annotations

from typing import Optional, Dict, Any, List
from enum import Enum
import datetime as dt

from pydantic import Field, field_validator

from models.base import BaseDocument, TimestampMixin


class CatalogItem(BaseDocument):
    """Catalog item document model"""

    sku: str = Field(..., max_length=64, description="Product SKU")
    bank: Optional[str] = Field(
        default=None, max_length=128, description="Issuing bank"
    )
    bin: Optional[str] = Field(
        default=None, max_length=16, description="Bank Identification Number"
    )
    country: Optional[str] = Field(
        default=None, max_length=8, description="Issuing country"
    )
    brand: Optional[str] = Field(default=None, max_length=64, description="Card brand")
    type: Optional[str] = Field(default=None, max_length=64, description="Card type")
    price: float = Field(..., ge=0, description="Item price")
    flags: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional flags and metadata"
    )
    active: bool = Field(default=True, description="Whether item is active")

    @field_validator("price")
    @classmethod
    def validate_price(cls, v):
        return round(v, 2)

    @field_validator("country")
    @classmethod
    def validate_country(cls, v):
        if v and (len(v) != 2 or not v.isalpha()):
            raise ValueError("Country must be a 2-letter code")
        return v.upper() if v else v

    @field_validator("brand")
    @classmethod
    def validate_brand(cls, v):
        if v:
            valid_brands = {"VISA", "MASTERCARD", "AMEX", "DISCOVER"}
            if v.upper() not in valid_brands:
                raise ValueError(f"Brand must be one of: {valid_brands}")
            return v.upper()
        return v

    @field_validator("type")
    @classmethod
    def validate_type(cls, v):
        if v:
            valid_types = {"CREDIT", "DEBIT", "PREPAID"}
            if v.upper() not in valid_types:
                raise ValueError(f"Type must be one of: {valid_types}")
            return v.upper()
        return v

    @field_validator("bin")
    @classmethod
    def validate_bin(cls, v):
        if v and (not v.isdigit() or len(v) > 8):
            raise ValueError("BIN must be numeric and max 8 digits")
        return v

    class Config:
        collection_name = "catalog_items"


class SavedFilter(BaseDocument):
    """Saved filter document model"""

    user_id: str = Field(..., description="Reference to User document ID")
    criteria: Dict[str, Any] = Field(..., description="Filter criteria")

    @field_validator("criteria")
    @classmethod
    def validate_criteria(cls, v):
        if not isinstance(v, dict):
            raise ValueError("Criteria must be a dictionary")
        return v

    class Config:
        collection_name = "saved_filters"


class AuditLog(BaseDocument):
    """Audit log document model"""

    actor_id: str = Field(..., description="ID of the user who performed the action")
    action: str = Field(..., max_length=64, description="Action performed")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional action metadata"
    )
    hash: str = Field(..., description="Integrity hash")

    def generate_hash(self) -> str:
        """Generate integrity hash for audit log"""
        import hashlib
        import json

        data = {
            "actor_id": self.actor_id,
            "action": self.action,
            "metadata": self.metadata or {},
            "timestamp": self.created_at.isoformat(),
        }
        content = json.dumps(data, sort_keys=True)
        return hashlib.sha256(content.encode()).hexdigest()

    def __init__(self, **data):
        super().__init__(**data)
        if not self.hash:
            self.hash = self.generate_hash()

    class Config:
        collection_name = "audit_logs"


class AppSetting(BaseDocument):
    """Application setting document model"""

    key: str = Field(..., max_length=64, description="Setting key")
    value: str = Field(..., max_length=256, description="Setting value")

    @field_validator("key")
    @classmethod
    def validate_key(cls, v):
        if not v or not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Key must be alphanumeric with underscores/hyphens")
        return v.lower()

    class Config:
        collection_name = "app_settings"


class CartStatus(str, Enum):
    """Cart status enumeration"""

    ACTIVE = "active"
    CHECKED_OUT = "checked_out"
    EXPIRED = "expired"


class CartItem(BaseDocument):
    """Cart item document model"""

    user_id: str = Field(..., description="Reference to User document ID")
    card_id: str = Field(..., description="External card ID from API")
    card_data: Dict[str, Any] = Field(..., description="Cached card data from API")
    quantity: int = Field(default=1, ge=1, description="Item quantity")
    price_at_add: float = Field(..., ge=0, description="Price when item was added")
    currency: str = Field(default="USD", max_length=8, description="Price currency")

    @field_validator("card_id", mode="before")
    @classmethod
    def validate_card_id(cls, v):
        """Coerce incoming IDs to strings for legacy records and clean input"""
        if v is None:
            raise ValueError("Card ID is required")

        if isinstance(v, bytes):
            v = v.decode("utf-8", errors="ignore")

        if isinstance(v, (int, float)):
            # Preserve numeric IDs but store consistently as strings
            return str(int(v))

        if isinstance(v, str):
            v = v.strip()
            if not v:
                raise ValueError("Card ID cannot be empty")
            return v

        # Support ObjectId or UUID-like types by stringifying
        try:
            return str(v)
        except Exception as exc:  # pragma: no cover - defensive fallback
            raise ValueError("Unsupported card ID type") from exc

    @field_validator("price_at_add")
    @classmethod
    def validate_price(cls, v):
        return round(v, 2)

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    @field_validator("card_data")
    @classmethod
    def validate_card_data(cls, v):
        if not isinstance(v, dict):
            raise ValueError("Card data must be a dictionary")
        # Ensure essential fields are present
        required_fields = ["_id"]  # Only _id is truly required
        for field in required_fields:
            if field not in v:
                raise ValueError(f"Card data missing required field: {field}")
        return v

    def get_total_price(self) -> float:
        """Calculate total price for this cart item"""
        return round(self.price_at_add * self.quantity, 2)

    class Config:
        collection_name = "cart_items"


class Cart(BaseDocument):
    """Cart document model"""

    user_id: str = Field(..., description="Reference to User document ID")
    status: CartStatus = Field(default=CartStatus.ACTIVE, description="Cart status")
    items: List[str] = Field(default_factory=list, description="List of CartItem IDs")
    total_amount: float = Field(default=0.0, ge=0, description="Total cart amount")
    currency: str = Field(default="USD", max_length=8, description="Cart currency")
    expires_at: Optional[dt.datetime] = Field(
        default=None, description="Cart expiration"
    )

    @field_validator("total_amount")
    @classmethod
    def validate_total(cls, v):
        return round(v, 2)

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v):
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()

    def is_expired(self) -> bool:
        """Check if cart has expired"""
        if self.expires_at is None:
            return False
        from datetime import timezone

        current_time = dt.datetime.now(timezone.utc)
        expires_at = self.expires_at

        # Ensure both datetimes are timezone-aware for comparison
        if expires_at.tzinfo is None:
            # If expires_at is naive, assume it's UTC
            expires_at = expires_at.replace(tzinfo=timezone.utc)

        return current_time > expires_at

    def is_active(self) -> bool:
        """Check if cart is active and not expired"""
        return self.status == CartStatus.ACTIVE and not self.is_expired()

    class Config:
        collection_name = "carts"
