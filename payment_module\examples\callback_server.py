"""
Example: Callback Server Setup

This example shows how to set up and run the payment callback server.
"""

import os
import logging
from payment_module.core.flask_server import run_callback_server

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
HOST = "0.0.0.0"
PORT = 3000
DEBUG = os.environ.get("DEBUG_MODE", "false").lower() in ("true", "1", "yes")

# Environment variables (set these before running)
# export OXA_PAY_API_KEY="your_api_key_here"
# export OXA_PAY_CALLBACK_URL="https://yourdomain.com/callback"
# export DEVELOPMENT_MODE="false"

def main():
    """Main function to start the callback server."""
    try:
        logger.info(f"Starting payment callback server on {HOST}:{PORT}")
        logger.info(f"Debug mode: {DEBUG}")
        
        # Check if API key is set
        api_key = os.environ.get("OXA_PAY_API_KEY")
        if not api_key:
            logger.error("OXA_PAY_API_KEY environment variable is not set!")
            return
        
        logger.info("API key configured")
        
        # Start the server
        run_callback_server(
            host=HOST,
            port=PORT,
            debug=DEBUG
        )
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Error starting server: {e}")


if __name__ == "__main__":
    main()

