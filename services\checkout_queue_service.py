"""
Checkout Queue Service for managing serialized checkout processing
Ensures proper isolation between users while using a shared website account

Key fixes vs. original:
- Store datetimes as real datetime objects in DB (not ISO strings) so Mon<PERSON> can sort/compare.
- Make idempotency key STABLE (removed timestamps); derived from user_id + deterministic cart snapshot hash.
- Added missing _processing_task attribute; unified worker start/stop APIs to avoid confusion.
- Fixed queue position/avg processing time using datetime objects safely.
- Hardened external cart logic (verification, consistent product_id usage).
- Avoid metadata overwrite on status update; merge instead.
- Safer aiohttp session closing, and typing cleanups.
"""

from __future__ import annotations
import asyncio
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
import json
import hashlib
import aiohttp

from database.connection import get_collection, database_transaction
from pymongo.errors import DuplicateKeyError
from models import Purchase, PurchaseStatus, PurchaseProductType
from services.cart_service import CartService
from services.user_service import UserService
from services.checkout_balance_service import checkout_balance_service
from api_v1.services.api_config import get_api_config_service
from services.external_api_service import get_external_api_service
from services.api_health_monitor import health_monitor, ServiceStatus
from services.circuit_breaker import (
    circuit_breaker_manager,
    CircuitBreakerConfig,
    CircuitBreakerOpenError,
)
from config.settings import get_settings
from utils.central_logger import setup_logging, get_logger

from utils.central_logger import get_logger

logger = get_logger()


class CheckoutJobStatus(str, Enum):
    """Checkout job status enumeration"""

    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CheckoutJob:
    """Represents a checkout job in the queue"""

    def __init__(
        self,
        user_id: str,
        cart_snapshot: Dict[str, Any],
        job_id: str | None = None,
        idempotency_key: str | None = None,
        created_at: datetime | None = None,
        status: CheckoutJobStatus = CheckoutJobStatus.QUEUED,
        metadata: Dict[str, Any] | None = None,
    ):
        self.job_id = job_id or str(uuid.uuid4())
        self.user_id = user_id
        self.cart_snapshot = cart_snapshot
        self.created_at = created_at or datetime.now(timezone.utc)  # aware UTC
        self.status = status
        self.metadata = metadata or {}
        self.attempts = 0
        self.last_error: Optional[str] = None
        self.completed_at: Optional[datetime] = None
        self.idempotency_key = idempotency_key or self._generate_idempotency_key()

    # ---- Helpers -----------------------------------------------------------------
    def _stable_cart_fingerprint(self) -> str:
        """Deterministic hash of cart contents (ignore volatile fields)."""

        def sanitize(obj: Any) -> Any:
            if isinstance(obj, dict):
                drop_keys = {"snapshot_timestamp", "updated_at", "created_at", "_id"}
                return {
                    k: sanitize(v) for k, v in sorted(obj.items()) if k not in drop_keys
                }
            if isinstance(obj, list):
                if obj and isinstance(obj[0], dict) and "card_id" in obj[0]:
                    normalized = [
                        {
                            kk: sanitize(vv)
                            for kk, vv in sorted(item.items())
                            if kk not in {"_id"}
                        }
                        for item in obj
                    ]
                    normalized.sort(
                        key=lambda x: (str(x.get("card_id")), int(x.get("quantity", 1)))
                    )
                    return normalized
                return [sanitize(v) for v in obj]
            return obj

        stable = {
            "items": sanitize(self.cart_snapshot.get("items", [])),
            "total_amount": self.cart_snapshot.get("total_amount", 0.0),
            "total_items": self.cart_snapshot.get("total_items", 0),
            "user_id": self.user_id,
        }
        return hashlib.sha256(
            json.dumps(stable, sort_keys=True, default=str).encode()
        ).hexdigest()

    def _generate_idempotency_key(self) -> str:
        """Stable idempotency key (no timestamps!)."""
        return self._stable_cart_fingerprint()

    def to_dict(self) -> Dict[str, Any]:
        """Convert job to dict for storage (keep datetimes as datetime objects)."""
        return {
            "job_id": self.job_id,
            "user_id": self.user_id,
            "cart_snapshot": self.cart_snapshot,
            "idempotency_key": self.idempotency_key,
            "created_at": self.created_at,
            "status": self.status.value,
            "metadata": self.metadata,
            "attempts": self.attempts,
            "last_error": self.last_error,
            "completed_at": self.completed_at,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CheckoutJob":
        """Create job from dictionary (accepts datetime or ISO string)."""

        def to_dt(value: Any) -> datetime | None:
            if value is None:
                return None
            if isinstance(value, datetime):
                return value if value.tzinfo else value.replace(tzinfo=timezone.utc)
            if isinstance(value, str):
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            return None

        job = cls(
            user_id=data["user_id"],
            cart_snapshot=data["cart_snapshot"],
            job_id=data["job_id"],
            idempotency_key=data.get("idempotency_key"),
            created_at=to_dt(data.get("created_at")) or datetime.now(timezone.utc),
            status=CheckoutJobStatus(data["status"]),
            metadata=data.get("metadata", {}),
        )
        job.attempts = data.get("attempts", 0)
        job.last_error = data.get("last_error")
        job.completed_at = to_dt(data.get("completed_at"))
        return job


class CheckoutQueueService:
    """Service for managing checkout queue and processing"""

    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger()
        
        # Ensure logging is initialized if application didn't configure it yet
        try:
            if not self.logger._logger.handlers:
                consolidated_path = (self.settings.LOG_CONSOLIDATED_FILE or "").strip()
                setup_logging(
                    level=self.settings.LOG_LEVEL,
                    log_file=(
                        self.settings.LOG_FILE_PATH
                        if self.settings.LOG_TO_FILE
                        else None
                    ),
                    max_file_size=self.settings.LOG_MAX_SIZE,
                    backup_count=self.settings.LOG_BACKUP_COUNT,
                    consolidated_log_file=consolidated_path or None,
                    structured_format=self.settings.LOG_STRUCTURED,
                    environment=self.settings.ENVIRONMENT,
                    console_colored=self.settings.LOG_COLOR,
                    show_category=self.settings.LOG_SHOW_CATEGORY,
                )
                self.logger.info(
                    "Logging auto-initialized by CheckoutQueueService"
                )
        except Exception:
            # Don't block initialization on logging setup failures
            pass
        self.jobs_collection = get_collection("checkout_jobs")
        self.cart_service = CartService()
        self.user_service = UserService()
        self.api_config_service = get_api_config_service()
        self.external_api_service = get_external_api_service()

        # Initialize circuit breaker for API v3 operations
        self.api_v3_circuit_breaker = circuit_breaker_manager.get_breaker(
            "api_v3_checkout",
            CircuitBreakerConfig(
                failure_threshold=3,  # Open after 3 failures
                recovery_timeout=300,  # Wait 5 minutes before retry
                success_threshold=2,  # Need 2 successes to close
                timeout=60,  # 60 second timeout for operations
            ),
        )

        # Queue configuration
        # Do not retry checkout; run once and report
        self.max_retries = 1
        self.retry_delays = [
            1,
            2,
            4,
        ]  # kept for compatibility, unused when max_retries=1
        self.job_timeout = 300  # per job timeout
        self.queue_check_interval = 1  # seconds

        # Processing state
        self._processing_lock = asyncio.Lock()
        self._is_processing = False
        self._worker_task: Optional[asyncio.Task] = None
        self._processing_task: Optional[asyncio.Task] = (
            None  # back-compat alias storage
        )

        # External API handled via external_api_service (no local HTTP session)

    # (Legacy API configuration helpers removed; external_api_service handles config)

    # ---- Worker lifecycle --------------------------------------------------------
    async def start_worker(self) -> None:
        """Start the checkout queue worker (preferred API)."""
        if self._worker_task and not self._worker_task.done():
            logger.warning("Checkout worker is already running")
            return
        self._is_processing = True
        self._worker_task = asyncio.create_task(
            self._process_queue(), name="checkout_worker"
        )
        logger.debug("Checkout queue worker started")

    async def stop_worker(self) -> None:
        """Stop the checkout queue worker."""
        self._is_processing = False
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
            finally:
                self._worker_task = None
        logger.info("Checkout queue worker stopped")

    # Back-compat aliases
    async def start_processing(self) -> None:
        await self.start_worker()

    async def stop_processing(self) -> None:
        await self.stop_worker()

    # ---- Public API --------------------------------------------------------------
    async def queue_checkout(
        self, user_id: str, telegram_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Queue a checkout job for processing

        Returns: Tuple of (success, message, job_id)
        """
        try:
            cart_contents = await self.cart_service.get_cart_contents(user_id)
            if cart_contents.get("is_empty", True):
                return False, "Cart is empty", None

            wallet = await self.user_service.get_wallet(user_id)
            total_amount = float(cart_contents.get("total_amount", 0.0))
            if not wallet or not wallet.can_spend(total_amount):
                return (
                    False,
                    f"Insufficient funds. Need ${total_amount}, have ${wallet.balance if wallet else 0}",
                    None,
                )

            # Snapshot serializer (OK to keep ISO strings in snapshot)
            def serialize_mongo_dict(data: Any) -> Any:
                if isinstance(data, dict):
                    return {
                        key: serialize_mongo_dict(value) for key, value in data.items()
                    }
                if isinstance(data, list):
                    return [serialize_mongo_dict(item) for item in data]
                if isinstance(data, datetime):
                    return data.astimezone(timezone.utc).isoformat()
                return data

            cart_snapshot = {
                "cart": (
                    serialize_mongo_dict(cart_contents["cart"].to_mongo())
                    if cart_contents["cart"]
                    else None
                ),
                "items": [
                    serialize_mongo_dict(item.to_mongo())
                    for item in cart_contents["items"]
                ],
                "total_amount": total_amount,
                "total_items": cart_contents.get("total_items", 0),
                "snapshot_timestamp": datetime.now(timezone.utc).isoformat(),
                "telegram_user_id": telegram_user_id,
            }

            # Idempotency check using stable fingerprint of cart
            existing_job = await self._find_existing_job(user_id, cart_snapshot)
            if existing_job:
                return (
                    True,
                    f"Order already queued (Job #{existing_job.job_id[:8]})",
                    existing_job.job_id,
                )

            job = CheckoutJob(
                user_id=user_id,
                cart_snapshot=cart_snapshot,
                metadata={
                    "telegram_user_id": telegram_user_id,
                    "estimated_processing_time": await self._estimate_processing_time(),
                },
            )

            try:
                await self.jobs_collection.insert_one(job.to_dict())
            except DuplicateKeyError:
                # Another concurrent request queued the same job (idempotency)
                existing = await self._find_existing_job(user_id, cart_snapshot)
                if existing:
                    return (
                        True,
                        f"Order already queued (Job #{existing.job_id[:8]})",
                        existing.job_id,
                    )
                # Fallback: report as already queued
                return True, "Order already queued", None

            queue_position = await self._get_queue_position(job.job_id)
            estimated_wait = queue_position * 30  # seconds

            logger.debug(f"Queued checkout job {job.job_id} for user {user_id}")
            return (
                True,
                (
                    "⏳ Order queued for processing!\n"
                    f"📍 Position: #{queue_position} in line\n"
                    f"⏱️ Estimated wait: {estimated_wait // 60}m {estimated_wait % 60}s\n"
                    f"🆔 Job ID: {job.job_id[:8]}"
                ),
                job.job_id,
            )
        except Exception as e:
            logger.error("Error queuing checkout", exc_info=True)
            return False, f"Failed to queue checkout: {str(e)}", None

    async def cancel_checkout(self, user_id: str, job_id: str) -> Tuple[bool, str]:
        try:
            job_doc = await self.jobs_collection.find_one(
                {
                    "job_id": job_id,
                    "user_id": user_id,
                    "status": {"$in": [CheckoutJobStatus.QUEUED.value]},
                }
            )
            if not job_doc:
                return False, "Job not found or cannot be cancelled"

            await self.jobs_collection.update_one(
                {"job_id": job_id},
                {
                    "$set": {
                        "status": CheckoutJobStatus.CANCELLED.value,
                        "completed_at": datetime.now(timezone.utc),
                    }
                },
            )
            logger.debug(f"Cancelled checkout job {job_id} for user {user_id}")
            return True, f"✅ Order #{job_id[:8]} has been cancelled"
        except Exception as e:
            logger.error("Error cancelling checkout job", exc_info=True)
            return False, f"Failed to cancel order: {str(e)}"

    async def get_job_status(self, job_id: str) -> Optional[CheckoutJob]:
        try:
            job_doc = await self.jobs_collection.find_one({"job_id": job_id})
            return CheckoutJob.from_dict(job_doc) if job_doc else None
        except Exception as e:
            logger.error("Error getting job status", exc_info=True)
            return None

    async def get_user_jobs(self, user_id: str, limit: int = 10) -> List[CheckoutJob]:
        try:
            jobs_docs = (
                await self.jobs_collection.find({"user_id": user_id})
                .sort("created_at", -1)
                .limit(limit)
                .to_list(None)
            )
            return [CheckoutJob.from_dict(doc) for doc in jobs_docs]
        except Exception as e:
            logger.error(f"Error getting user jobs for {user_id}: {e}")
            return []

    # ---- Queue processing --------------------------------------------------------
    async def _process_queue(self) -> None:
        logger.debug("Starting checkout queue processing")
        while self._is_processing:
            try:
                job = await self._get_next_job()
                if job:
                    await self._process_job(job)
                    # Add small delay between jobs to prevent race conditions
                    await asyncio.sleep(0.5)
                else:
                    await asyncio.sleep(self.queue_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in queue processing loop", exc_info=True)
                await asyncio.sleep(5)
        logger.info("Checkout queue processing stopped")

    async def _get_next_job(self) -> Optional[CheckoutJob]:
        try:
            cursor = (
                self.jobs_collection.find({"status": CheckoutJobStatus.QUEUED.value})
                .sort("created_at", 1)
                .limit(1)
            )
            jobs_list = await cursor.to_list(1)
            return CheckoutJob.from_dict(jobs_list[0]) if jobs_list else None
        except Exception as e:
            logger.error("Error getting next job", exc_info=True)
            return None

    async def _process_job(self, job: CheckoutJob) -> None:
        logger.info(f"Attempting to acquire checkout lock for job {job.job_id}")
        
        # First check if job is still in QUEUED state before acquiring lock
        current_job = await self.jobs_collection.find_one({"job_id": job.job_id})
        if not current_job or current_job.get("status") != CheckoutJobStatus.QUEUED.value:
            logger.info(f"Skipping job {job.job_id}: not in QUEUED state (current: {current_job.get('status') if current_job else 'not found'})")
            return
            
        async with self._processing_lock:
            lock_acquired_time = datetime.now(timezone.utc)
            logger.info(
                f"🔒 Checkout lock acquired for job {job.job_id} (user: {job.user_id}) at {lock_acquired_time.isoformat()}"
            )
            try:
                # Distributed-safe atomic claim: transition QUEUED -> PROCESSING
                claim_result = await self.jobs_collection.update_one(
                    {
                        "job_id": job.job_id,
                        "status": CheckoutJobStatus.QUEUED.value,
                    },
                    {
                        "$set": {
                            "status": CheckoutJobStatus.PROCESSING.value,
                            "claimed_at": datetime.now(timezone.utc),
                        }
                    },
                )
                if getattr(claim_result, "modified_count", 0) == 0:
                    # Another worker/process already claimed or processed this job
                    logger.info(
                        f"Skipping job {job.job_id}: already claimed or not in QUEUED state"
                    )
                    return
                logger.info(
                    (
                        f"Processing checkout job {job.job_id} for user {job.user_id} - "
                        f"Items: {len(job.cart_snapshot.get('items', []))}, "
                        f"Total: ${job.cart_snapshot.get('total_amount', 0.0)}"
                    )
                )
                await self._notify_user(job, "⚡ <b>Processing your order securely...</b>\n🔒 <b>Payment verification:</b> <code>In Progress</code>\n📦 <b>Digital cards:</b> <code>Being Prepared</code>")

                success, message, result_data = (
                    await self._execute_checkout_with_retries(job)
                )
                if success:
                    await self._update_job_status(
                        job.job_id, CheckoutJobStatus.COMPLETED, metadata=result_data
                    )
                    await self._notify_user(job, f"✅ {message}", result_data)
                    logger.info(
                        f"✅ Successfully completed job {job.job_id} - Order processed successfully"
                    )
                else:
                    await self._update_job_status(
                        job.job_id, CheckoutJobStatus.FAILED, error=message
                    )
                    await self._notify_user(job, f"❌ {message}")
                    logger.error(f"❌ Failed to complete job {job.job_id}: {message}")
            except Exception as e:
                logger.error(f"💥 Critical error processing job {job.job_id}: {e}")
                await self._update_job_status(
                    job.job_id, CheckoutJobStatus.FAILED, error=str(e)
                )
                await self._notify_user(job, f"❌ Order processing failed: {str(e)}")
            finally:
                lock_released_time = datetime.now(timezone.utc)
                processing_duration = (
                    lock_released_time - lock_acquired_time
                ).total_seconds()
                logger.info(
                    f"🔓 Checkout lock released for job {job.job_id} at {lock_released_time.isoformat()} "
                    f"(held for {processing_duration:.2f}s)"
                )

    async def _execute_checkout_with_retries(
        self, job: CheckoutJob
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        for attempt in range(self.max_retries):
            try:
                job.attempts = attempt + 1
                success, message, result_data = await self._execute_checkout(job)
                if success:
                    return True, message, result_data
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                    logger.warning(
                        f"Checkout attempt {attempt + 1} failed for job {job.job_id}: {message}. Retrying in {delay}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    job.last_error = message
                    return (
                        False,
                        f"Checkout failed after {self.max_retries} attempts: {message}",
                        None,
                    )
            except Exception as e:
                job.last_error = str(e)
                logger.error(f"❌ Checkout attempt {attempt + 1} error for job {job.job_id}: {e}", exc_info=True)
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                    logger.warning(
                        f"🔄 Retrying checkout in {delay}s... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"💥 Checkout failed permanently after {self.max_retries} attempts: {e}")
                    return False, f"Checkout failed with error: {e}", None
        return False, "Maximum retries exceeded", None

    async def _execute_checkout(
        self, job: CheckoutJob
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        checkout_step = "initialization"
        logger.info(f"Checkout: start job {job.job_id}")
        try:
            # Step 1: Validate cart snapshot
            checkout_step = "cart_validation"
            logger.info(f"Checkout: step1 validate cart snapshot")
            cart_snapshot = job.cart_snapshot
            items = cart_snapshot.get("items", [])
            total_amount = float(cart_snapshot.get("total_amount", 0.0))

            logger.info(f"📋 Cart validation: {len(items)} items, total=${total_amount}")
            for i, item in enumerate(items):
                card_id = (
                    item.get("card_id") or 
                    item.get("_id") or 
                    (item.get("card_data", {}) or {}).get("_id") or
                    (item.get("card_data", {}) or {}).get("id")
                )
                price = item.get("price_at_add", 0)
                qty = item.get("quantity", 1)
                logger.info(f"  Item {i+1}: card_id={card_id}, price=${price}, qty={qty}")

            # Check if we have items
            if not items:
                logger.warning(
                    f"Invalid cart snapshot for job {job.job_id}: no items in cart"
                )
                return False, "Cart is empty", None

            # If total_amount is 0 but we have items, recalculate from items
            if total_amount <= 0:
                logger.info(
                    f"Cart total is {total_amount}, recalculating from {len(items)} items"
                )
                calculated_total = sum(
                    float(item.get("price_at_add", 0)) * int(item.get("quantity", 1))
                    for item in items
                )

                if calculated_total <= 0:
                    logger.warning(
                        f"Invalid cart snapshot for job {job.job_id}: items={len(items)}, "
                        f"snapshot_total={total_amount}, calculated_total={calculated_total}"
                    )
                    return (
                        False,
                        "Cart total is invalid (all items have zero price)",
                        None,
                    )

                # Update the total_amount for the rest of the checkout process
                total_amount = calculated_total
                logger.info(
                    f"Updated cart total from {cart_snapshot.get('total_amount')} to {total_amount}"
                )

                # Update the cart snapshot for consistency
                cart_snapshot["total_amount"] = total_amount

            # Step 2: Validate funds using external API balance
            checkout_step = "funds_validation"
            logger.info(f"Checkout: step2 validate funds using external API")

            # Get balance from external API instead of local wallet
            external_balance = await self._get_external_api_balance()

            if external_balance is None:
                logger.error(
                    f"Could not retrieve external API balance for job {job.job_id}"
                )
                return (
                    False,
                    "Could not verify account balance. Please try again later.",
                    None,
                )

            if external_balance < total_amount:
                logger.warning(
                    f"Insufficient external funds for job {job.job_id}: need=${total_amount}, have=${external_balance}"
                )
                return (
                    False,
                    f"Insufficient funds. Need ${total_amount:.2f}, have ${external_balance:.2f}",
                    None,
                )

            logger.info(
                f"External balance validation passed: have=${external_balance:.2f}, need=${total_amount:.2f}"
            )

            # Step 3: External API session
            checkout_step = "session_creation"
            logger.info(f"Checkout: step3 create external session")

            # Step 4: Populate cart (which now includes clearing)
            checkout_step = "cart_population"
            logger.info(f"Checkout: step4 populate cart with {len(items)} items")
            populate_success = await self._populate_external_cart(items)
            if not populate_success:
                try:
                    external_items_debug = await self._get_external_cart_items()
                    # Extract IDs using the same logic as validation
                    item_ids = []
                    for item in external_items_debug:
                        item_id = str(
                            item.get("id") or 
                            item.get("_id") or 
                            item.get("card_id") or 
                            item.get("product_id") or 
                            item.get("item_id") or
                            "None"
                        )
                        item_ids.append(item_id)
                    logger.error(
                        f"🔍 External cart has {len(external_items_debug)} items after failed population: "
                        f"{item_ids}"
                    )
                except Exception as debug_e:
                    logger.error(
                        f"   Failed to get external cart for debugging: {debug_e}"
                    )
                return False, "Failed to populate external cart with user items", None

            # Skip immediate per-item verification to reduce extra API calls; a single
            # validation pass runs later if needed.

            # Step 5: Validate external cart (single, lightweight pass)
            checkout_step = "cart_validation_external"
            logger.info("Checkout: step5 validate external cart")

            # Route to appropriate validation method based on API version
            api_version = getattr(self.external_api_service, "api_version", "v1")
            logger.info(f"🔍 Detected API version for validation: {api_version}")

            # Check if any items have non-numeric card IDs (which forces API v3 routing)
            has_non_numeric_ids = any(
                not str(item.get("card_id", "")).isdigit()
                for item in items
            )

            # Use API v3 validation if:
            # 1. API version is explicitly v3/base3, OR
            # 2. Items have non-numeric IDs (which forces API v3 routing in add_to_cart)
            should_use_v3 = api_version in ["v3", "base3"] or has_non_numeric_ids

            if should_use_v3:
                if has_non_numeric_ids and api_version not in ["v3", "base3"]:
                    logger.info(f"🔄 Non-numeric card IDs detected - using API v3 validation despite API version {api_version}")
                logger.info("🔍 Using API v3-only cart validation")
                validation_result = await self._verify_cart_synchronization_v3(items)
            else:
                logger.info("Validating external cart contents using legacy method...")
                validation_result = await self._validate_cart_items(items)

            if not validation_result["valid"]:
                return (
                    False,
                    f"Cart validation failed: {validation_result['message']}",
                    None,
                )
            
            # Handle partial success - some cards unavailable
            missing_cards = validation_result.get("missing_cards", [])
            if missing_cards:
                logger.warning(f"⚠️ {len(missing_cards)} card(s) unavailable, filtering them out")
                
                # Save original items for notification before filtering
                original_items = items.copy()
                
                # Filter out unavailable cards from items list
                original_count = len(items)
                items = [
                    item for item in items 
                    if str(item.get("card_id", "")) not in missing_cards
                ]
                filtered_count = len(items)
                
                logger.info(f"📦 Filtered items: {original_count} → {filtered_count} (removed {len(missing_cards)} unavailable)")
                
                # Recalculate total amount with only available cards
                total_amount = sum(
                    float(item.get("price_at_add", 0)) * int(item.get("quantity", 1))
                    for item in items
                )
                logger.info(f"💰 Adjusted total amount: ${total_amount:.2f}")
                
                # Notify user about unavailable cards
                try:
                    unavailable_msg = "⚠️ <b>Some cards are no longer available:</b>\n\n"
                    for i, card_id in enumerate(missing_cards, 1):
                        # Find the original item to get card details
                        original_item = next(
                            (item for item in original_items if str(item.get("card_id", "")) == card_id),
                            None
                        )
                        if original_item:
                            card_bin = original_item.get("card_bin", "Unknown")
                            card_price = original_item.get("price_at_add", 0)
                            unavailable_msg += f"{i}. BIN: {card_bin} (${card_price})\n"
                        else:
                            unavailable_msg += f"{i}. Card ID: {card_id[:12]}...\n"
                    
                    unavailable_msg += f"\n❌ These {len(missing_cards)} card(s) were already purchased by someone else or removed from stock.\n"
                    unavailable_msg += f"✅ Continuing with {filtered_count} available card(s)..."
                    
                    await self.bot.send_message(
                        chat_id=job.user_id,
                        text=unavailable_msg,
                        parse_mode="HTML"
                    )
                    logger.info(f"✅ Sent unavailable cards notification to user {job.user_id}")
                except Exception as notify_err:
                    logger.error(f"❌ Failed to notify user about unavailable cards: {notify_err}")
                
                # If no items left after filtering, fail
                if not items:
                    return (
                        False,
                        "All cards are no longer available",
                        None,
                    )
            
            warnings = validation_result.get("warnings") or []
            if warnings:
                logger.info(f"Checkout: validation warnings: {'; '.join(warnings)}")

            # Step 6: Deduct balance before external checkout
            checkout_step = "balance_deduction"
            logger.info("Checkout: step6 deduct balance before external checkout")
            
            # Generate unique checkout ID for this transaction
            checkout_id = f"checkout_{job.user_id}_{job.job_id}_{int(datetime.now().timestamp())}"
            
            # Deduct balance from user's wallet
            balance_result = await checkout_balance_service.deduct_balance_for_checkout(
                user_id=job.user_id,
                amount=total_amount,
                currency="USD",
                checkout_id=checkout_id,
                reference=f"checkout_{job.job_id}"
            )
            
            if not balance_result.success:
                logger.error(f"❌ Balance deduction failed for checkout {checkout_id}: {balance_result.error_message}")
                return (
                    False,
                    balance_result.error_message or "Failed to deduct balance",
                    None,
                )
            
            logger.info(f"✅ Balance deducted for checkout {checkout_id}: ${total_amount:.2f}")

            # Execute external checkout per demo curl
            checkout_step = "external_checkout"
            logger.info("Checkout: step7 external checkout")

            # Use API v3 checkout if we determined API v3 should be used
            if should_use_v3:
                if has_non_numeric_ids and api_version not in ["v3", "base3"]:
                    logger.info(f"🔄 Using API v3 checkout despite API version {api_version}")
                checkout_resp = await self._api_call_with_auth_retry(
                    self.external_api_service.checkout, force_api_v3=True
                )
            else:
                checkout_resp = await self._api_call_with_auth_retry(
                    self.external_api_service.checkout
                )
            
            # Check if external checkout failed and rollback balance if needed
            if not getattr(checkout_resp, "success", False):
                logger.error(f"❌ External checkout failed for {checkout_id}: {checkout_resp.error}")
                
                # Rollback balance deduction
                rollback_success = await checkout_balance_service.rollback_checkout_balance(
                    checkout_id=checkout_id,
                    reason=f"External checkout failed: {checkout_resp.error or 'Unknown error'}"
                )
                
                if rollback_success:
                    logger.info(f"✅ Balance rolled back for failed checkout {checkout_id}")
                else:
                    logger.error(f"❌ Failed to rollback balance for checkout {checkout_id}")
                
                return (
                    False,
                    checkout_resp.error or "External checkout failed",
                    None,
                )

            # Mark checkout as completed (balance already deducted)
            await checkout_balance_service.complete_checkout_balance(checkout_id)
            logger.info(f"✅ Checkout {checkout_id} marked as completed")

            # Build result summary and finish
            # First, record purchases (balance already deducted)
            external_order_id = (
                (checkout_resp.data or {}).get("order_id")
                if checkout_resp.data
                else None
            )
            # OPTIMIZATION: Extract card details directly from checkout response before creating purchase records
            # This ensures the extracted cards are available for storage in purchase records
            extracted_cards: list[dict] = []
            try:
                if checkout_resp.data and isinstance(checkout_resp.data, dict):
                    # Use card data extractor to extract cards from order response
                    from utils.card_data_extractor import get_card_data_extractor
                    extractor = get_card_data_extractor()
                    
                    # Extract cards from the checkout response (which contains order data)
                    extracted_cards = extractor.extract_from_api_response(checkout_resp.data)
                    
                    if extracted_cards:
                        logger.info(f"✅ Extracted {len(extracted_cards)} cards from checkout response")
                    else:
                        logger.warning("⚠️ No cards extracted from checkout response")
            except Exception as e:
                logger.warning(f"Could not extract cards from checkout response: {e}")

            # Extract purchased card IDs
            purchased_items = items
            purchased_card_ids = []
            try:
                purchased_card_ids = [
                    str(it.get("card_id") or (it.get("card_data") or {}).get("_id"))
                    for it in purchased_items
                    if (it.get("card_id") or (it.get("card_data") or {}).get("_id"))
                    is not None
                ]
            except Exception:
                purchased_card_ids = []

            purchase_result_ok = False
            purchase_result_meta: Dict[str, Any] = {}
            purchase_error_msg = ""
            try:
                ok, msg, meta = await self._process_successful_checkout(
                    user_id=job.user_id,
                    cart_snapshot=cart_snapshot,
                    external_result={
                        "order_id": external_order_id,
                        "extracted_cards": extracted_cards,  # Pass extracted cards
                        "checkout_response": checkout_resp.data
                    },
                )
                purchase_result_ok = ok
                purchase_result_meta = meta or {}
                if not ok:
                    purchase_error_msg = msg
            except Exception as e:
                logger.error(f"Failed to persist purchases: {e}")
                purchase_error_msg = str(e)

            # Check if purchase creation failed
            if not purchase_result_ok:
                logger.error(f"Purchase creation failed for job {job.job_id}: {purchase_error_msg}")
                return (
                    False,
                    f"External checkout succeeded but failed to create purchase records: {purchase_error_msg}",
                    None,
                )

            result_data = {
                "external_order_id": external_order_id,
                "status_code": checkout_resp.status_code,
                "total_amount": total_amount,
                "items_purchased": len(items),
                "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                "validation_warnings": validation_result.get("warnings", []),
                "purchased_card_ids": purchased_card_ids,
                "purchased_cards": self._clean_for_bson(extracted_cards),  # Clean extracted cards
                "transaction_id": purchase_result_meta.get("transaction_id"),
                "remaining_balance": purchase_result_meta.get("remaining_balance"),
                # DON'T store full checkout_response here - it causes RecursionError
                # It's already stored in purchase records with proper cleaning
            }
            logger.info(
                f"Checkout: completed for job {job.job_id} (status={checkout_resp.status_code})"
            )
            return True, "Checkout completed", result_data
        except Exception as e:
            logger.error(
                f"Error executing checkout for job {job.job_id} at step '{checkout_step}'",
                exc_info=True,
            )
            return (
                False,
                f"Checkout execution failed at {checkout_step}: {str(e)}",
                None,
            )

    # ---- Data cleaning helpers ----------------------------------------------------
    
    def _clean_for_bson(self, data: any, max_depth: int = 10, current_depth: int = 0, visited: set = None) -> any:
        """Clean data structure to prevent BSON encoding errors from circular references or deep nesting."""
        # Initialize visited set on first call to track circular references
        if visited is None:
            visited = set()
        
        # Check depth limit
        if current_depth >= max_depth:
            return "<<MAX_DEPTH_REACHED>>"
        
        # Handle dictionaries
        if isinstance(data, dict):
            # Use object id to detect circular references
            obj_id = id(data)
            if obj_id in visited:
                return "<<CIRCULAR_REFERENCE>>"
            
            visited.add(obj_id)
            clean_dict = {}
            for key, value in data.items():
                try:
                    # Skip keys that might cause circular references
                    if key in ["_parent", "parent", "self", "__dict__", "__class__", "session", "connection", "client"]:
                        continue
                    clean_dict[key] = self._clean_for_bson(
                        value, max_depth, current_depth + 1, visited
                    )
                except Exception as e:
                    # If we can't process a value, replace it with a safe string
                    clean_dict[key] = f"<<BSON_ERROR: {str(e)[:50]}>>"
            visited.remove(obj_id)
            return clean_dict
        
        # Handle lists
        elif isinstance(data, list):
            # Use object id to detect circular references in lists
            obj_id = id(data)
            if obj_id in visited:
                return "<<CIRCULAR_REFERENCE>>"
            
            visited.add(obj_id)
            clean_list = []
            for item in data:
                try:
                    clean_list.append(
                        self._clean_for_bson(item, max_depth, current_depth + 1, visited)
                    )
                except Exception:
                    clean_list.append("<<BSON_ERROR>>")
            visited.remove(obj_id)
            return clean_list
        
        # Handle primitive types
        elif isinstance(data, (str, int, float, bool)) or data is None:
            return data
        
        # Handle datetime objects (keep as datetime for MongoDB sorting)
        elif isinstance(data, datetime):
            # Keep datetime objects as-is - MongoDB supports them natively
            # This is critical for proper sorting by created_at, updated_at, etc.
            return data
        
        # Handle other types (convert to string as last resort)
        else:
            # For other types, convert to string safely
            try:
                return str(data)
            except Exception:
                return "<<UNCONVERTIBLE>>"

    # ---- External API helpers ----------------------------------------------------

    async def _clear_external_cart(self) -> bool:
        try:
            logger.info("🧹 Starting external cart clearing process...")

            # First, check current cart state
            cart_items_before = await self._get_external_cart_items()
            if not cart_items_before:
                logger.info("✅ External cart is already empty")
                return True

            logger.info(
                f"📦 Cart contains {len(cart_items_before)} items before clearing"
            )

            # Use API v3 clear_cart method if available, otherwise fall back to individual removal
            try:
                logger.info(
                    "🚀 Attempting to clear cart using API v3 clear_cart method..."
                )
                clear_response = await self.external_api_service.clear_cart()

                if clear_response.success:
                    logger.info(
                        "✅ Successfully cleared cart using API v3 clear_cart method"
                    )

                    # Verify cart is actually empty
                    cart_items_after = await self._get_external_cart_items()
                    if not cart_items_after:
                        logger.info(
                            f"✅ Cart clearing verified - {len(cart_items_before)} items removed, 0 remaining"
                        )
                        return True
                    else:
                        logger.warning(
                            f"⚠️ Cart clear reported success but {len(cart_items_after)} items remain"
                        )
                        # Fall through to individual removal
                else:
                    logger.warning(
                        f"⚠️ API v3 clear_cart failed: {clear_response.error}"
                    )
                    # Fall through to individual removal

            except Exception as e:
                logger.warning(f"⚠️ API v3 clear_cart method failed: {e}")
                # Fall through to individual removal

            # Fallback: Remove items individually
            logger.info("🔄 Falling back to individual item removal...")
            removed_count = 0
            failed_removals: list[dict[str, Any]] = []
            for i, item in enumerate(cart_items_before, 1):
                item_id = item.get("_id") or item.get("id")
                item_name = (
                    item.get("name")
                    or f"{item.get('brand', 'Unknown')} {item.get('bin', 'Card')}"
                    or f"Card {item_id}"
                )
                if item_id is None:
                    logger.warning(f"⚠️ Item {i} has no ID, skipping removal")
                    continue
                logger.debug(
                    f"🗑️ Removing item {i}/{len(cart_items_before)}: {item_name} (ID: {item_id})"
                )
                success = await self._remove_external_cart_item(None, item_id)
                if success:
                    removed_count += 1
                else:
                    failed_removals.append({"id": item_id, "name": item_name})

            # Verify final cart state
            remaining_items = await self._get_external_cart_items()
            if remaining_items:
                logger.error(
                    f"❌ Cart clearing incomplete: {len(remaining_items)} items remain after removal attempt"
                )
                for item in remaining_items:
                    iid = item.get("_id") or item.get("id", "No ID")
                    logger.error(f"   - Remaining item ID: {iid}")
                return False
            if failed_removals:
                logger.warning(
                    f"⚠️ {len(failed_removals)} items failed to remove but cart appears empty"
                )
            logger.info(
                f"✅ Successfully cleared external cart - {removed_count} items removed, 0 remaining"
            )
            return True
        except Exception as e:
            logger.error("💥 Critical error clearing external cart", exc_info=True)
            return False

    async def _get_external_cart_items(
        self, session: "aiohttp.ClientSession" | None = None
    ) -> List[Dict[str, Any]]:
        try:
            response = await self.external_api_service.view_cart()
            if (
                getattr(response, "success", False)
                and getattr(response, "data", None) is not None
            ):
                data = response.data
                if isinstance(data, dict):
                    if isinstance(data.get("data"), list):
                        return data["data"]
                    if isinstance(data.get("cart"), dict) and isinstance(
                        data["cart"].get("items"), list
                    ):
                        return data["cart"]["items"]
                if isinstance(data, list):
                    return data
            else:
                logger.warning(
                    f"Failed to get external cart items: {getattr(response, 'error', 'unknown error')}"
                )
            return []
        except Exception as e:
            logger.error(f"Error getting external cart items: {e}")
            return []

    async def _remove_external_cart_item(self, session: Any, item_id: Any) -> bool:
        try:
            logger.debug(f"Removing cart item {item_id} from external cart")
            try:
                numeric_id = int(item_id)
                resp = await self.external_api_service.delete_from_cart(numeric_id)
            except (ValueError, TypeError):
                resp = await self.external_api_service.delete_from_cart(item_id)
            if getattr(resp, "success", False):
                return True
            err = str(getattr(resp, "error", ""))
            if "not found" in err.lower() or "404" in err:
                return True
            logger.warning(
                f"Failed to remove cart item {item_id} from external cart: {err}"
            )
            return False
        except Exception as e:
            logger.error(f"Error removing external cart item {item_id}: {e}")
            return False

    async def _add_to_external_cart(self, session: Any, card_id: int | str) -> bool:
        try:
            logger.debug(f"🔄 Attempting to add card {card_id} to external cart")

            # Skip pre-add cart fetch to avoid extra API calls

            # Make the API call
            logger.debug(f"📡 Making add_to_cart API call for card {card_id}")
            response = await self.external_api_service.add_to_cart(card_id, "Cards")

            # ENHANCED LOGGING: Complete API response details
            # Concise one-line summary; raw body is logged centrally by HTTP client
            logger.info(
                f"add_to_cart {card_id}: status={getattr(response,'status_code','?')} success={getattr(response,'success',None)}"
            )

            # Trust API success without extra GETs to verify presence
            if getattr(response, "success", False):
                return True
            else:
                logger.error(f"❌ API reported FAILURE for card {card_id}")

                # ENHANCED LOGGING: Analyze failure response
                msg = ""
                if hasattr(response, "data") and isinstance(response.data, dict):
                    msg = str(response.data.get("message", ""))
                    logger.debug(f"   Failure message: '{msg}'")

                # Handle "already in cart" scenario
                if "already have this product in cart" in msg.lower():
                    # Treat as success; skip verification fetches
                    return True
                else:
                    logger.error(
                        f"❌ GENUINE FAILURE: Card {card_id} could not be added"
                    )
                    logger.error(f"   Failure reason: {msg if msg else 'Unknown'}")
                    return False
        except Exception as e:
            logger.error(f"💥 EXCEPTION adding card {card_id} to external cart: {e}")
            return False

    async def _populate_external_cart(self, cart_items: List[Dict[str, Any]]) -> bool:
        """
        Populate external cart using the configured API version.

        Steps:
        1. Clear external cart using configured API version
        2. Verify cart is empty
        3. Populate cart from virtual cart using configured API version
        4. Verify synchronization
        """
        try:
            # Get the configured API version
            api_version = getattr(self.external_api_service, "api_version", "v1")
            logger.info(f"🚀 Starting cart synchronization using API {api_version}")

            # Check if any items have non-numeric card IDs (which forces API v3 routing)
            has_non_numeric_ids = any(
                not str(item.get("card_id", "")).isdigit()
                for item in cart_items
            )

            # Use API v3 if:
            # 1. API version is explicitly v3/base3, OR
            # 2. Items have non-numeric IDs (which forces API v3 routing in add_to_cart)
            should_use_v3 = api_version in ["v3", "base3"] or has_non_numeric_ids

            if should_use_v3:
                if has_non_numeric_ids and api_version not in ["v3", "base3"]:
                    logger.info(f"🔄 Non-numeric card IDs detected - using API v3 workflow despite API version {api_version}")
                return await self._populate_external_cart_v3(cart_items)
            else:
                return await self._populate_external_cart_legacy(cart_items)

        except Exception as e:
            logger.error(f"💥 Critical error in cart synchronization: {e}")
            return False

    async def _populate_external_cart_v3(
        self, cart_items: List[Dict[str, Any]]
    ) -> bool:
        """
        API v3-only cart synchronization workflow.

        Steps:
        1. Clear external cart using API v3 clear_cart()
        2. Verify cart is empty using API v3 view_cart()
        3. Populate cart from virtual cart using API v3 add_to_cart()
        4. Verify synchronization using API v3 view_cart()
        """
        try:
            logger.info("🚀 Starting API v3-only cart synchronization workflow")

            # Check circuit breaker status before starting workflow
            logger.info("🔌 Checking API v3 circuit breaker status...")
            circuit_stats = self.api_v3_circuit_breaker.get_stats()

            if not self.api_v3_circuit_breaker.is_available():
                logger.error(
                    "❌ API v3 circuit breaker is OPEN - service temporarily disabled"
                )
                logger.error(f"   Circuit opened at: {circuit_stats.opened_at}")
                logger.error(
                    f"   Next retry attempt: {circuit_stats.next_attempt_time}"
                )
                logger.error(
                    f"   Failure rate: {self.api_v3_circuit_breaker.get_failure_rate():.1f}%"
                )
                logger.error("   🔧 Service is experiencing repeated failures")
                logger.error(
                    "   📞 Please wait for automatic recovery or contact administrator"
                )
                return False

            logger.info(
                f"✅ Circuit breaker status: {circuit_stats.state.value.upper()}"
            )

            # Perform health check before starting workflow
            logger.info("🏥 Performing API v3 health check before checkout...")

            try:
                # Use circuit breaker for health check
                health_result = await self.api_v3_circuit_breaker.call(
                    health_monitor.check_api_v3_health, self.external_api_service
                )
                health_monitor.record_health_check(health_result)

                if health_result.status == ServiceStatus.UNAVAILABLE:
                    logger.error(
                        "❌ API v3 service is currently unavailable - aborting checkout"
                    )
                    logger.error(f"   Service error: {health_result.error_message}")
                    logger.error("   🔧 Troubleshooting suggestions:")
                    logger.error("      1. Check if Tor is running (port 9050 or 9150)")
                    logger.error("      2. Verify .onion domain accessibility")
                    logger.error("      3. Check network connectivity")
                    logger.error("      4. Try again in a few minutes")
                    logger.error(
                        "   📞 If the issue persists, contact system administrator"
                    )
                    return False
                elif health_result.status == ServiceStatus.DEGRADED:
                    logger.warning(
                        "⚠️ API v3 service is degraded but proceeding with checkout"
                    )
                    logger.warning(f"   Service warning: {health_result.error_message}")

            except CircuitBreakerOpenError as e:
                logger.error(f"❌ Circuit breaker prevented health check: {e}")
                return False

            logger.info("🚀 Starting API v3-only cart synchronization workflow")

            # Step 1: Clear the external cart using API v3 (includes verification with retries)
            logger.info("🧹 Step 1: Clear external cart using API v3")
            clear_success = await self._clear_external_cart_v3()
            if not clear_success:
                # Check if this is a service unavailability issue
                if await self._is_api_v3_service_available():
                    logger.error("❌ Failed to clear external cart after retries - aborting checkout")
                    return False
                else:
                    logger.error(
                        "❌ API v3 service is currently unavailable - aborting checkout"
                    )
                    logger.error(
                        "   Please try again later when the service is restored"
                    )
                    return False

            # Step 2: Cart is now verified empty by _clear_external_cart_v3() with retries
            logger.info("✅ Step 2: Cart verified empty after clearing with retries")

            # Step 3: Populate cart from virtual cart
            logger.info("📦 Step 3: Populate external cart from virtual cart")
            populate_success, failed_items = await self._populate_cart_from_virtual_v3(cart_items)
            if not populate_success:
                logger.error("❌ Failed to populate external cart - aborting checkout")
                return False
                
            # If some items failed, update cart_items to only include successful ones
            if failed_items:
                failed_card_ids = {item["card_id"] for item in failed_items}
                original_count = len(cart_items)
                cart_items = [item for item in cart_items if item.get("_id") not in failed_card_ids]
                logger.info(f"📝 Updated cart snapshot: removed {original_count - len(cart_items)} unavailable items, {len(cart_items)} items remaining")

            # OPTIMIZATION: Skip final verification if all items were added successfully
            # The add_to_cart operations already confirm success, additional verification is redundant
            if not failed_items:
                logger.info("✅ Step 4: Cart synchronization verified (all items added successfully)")
                logger.info("🎉 API v3 cart synchronization completed successfully")
                return True

            # Step 4: Only verify if there were issues during population
            logger.info("⚠️ Step 4: Verify cart synchronization (some items failed)")
            sync_verification = await self._verify_cart_synchronization_v3(cart_items)
            if not sync_verification["valid"]:
                logger.error(
                    f"❌ Cart synchronization failed: {sync_verification['message']}"
                )
                return False

            logger.info("🎉 API v3 cart synchronization completed successfully")
            return True

        except Exception as e:
            logger.error(
                "💥 Critical error in API v3 cart synchronization", exc_info=True
            )
            return False

    async def _populate_external_cart_legacy(
        self, cart_items: List[Dict[str, Any]]
    ) -> bool:
        """
        Legacy cart synchronization workflow for API v1/v2.

        Steps:
        1. Clear external cart using configured API version
        2. Verify cart is empty
        3. Populate cart from virtual cart using configured API version
        4. Verify synchronization
        """
        try:
            api_version = getattr(self.external_api_service, "api_version", "v1")
            logger.info(
                f"🚀 Starting legacy cart synchronization using API {api_version}"
            )

            # Step 1: Clear the external cart
            logger.info(f"🧹 Step 1: Clear external cart using API {api_version}")
            clear_success = await self._clear_external_cart()
            if not clear_success:
                logger.error("❌ Failed to clear external cart - aborting checkout")
                return False

            # Step 2: Verify cart is empty
            logger.info("📋 Step 2: Verify cart is empty")
            cart_items_after_clear = await self._get_external_cart_items()
            if cart_items_after_clear:
                logger.error(
                    f"❌ Cart is not empty after clearing - {len(cart_items_after_clear)} items remain"
                )
                return False
            logger.info("✅ Cart is empty after clearing")

            # Step 3: Populate cart from virtual cart
            logger.info("📦 Step 3: Populate external cart from virtual cart")
            populate_success = await self._populate_cart_from_virtual_legacy(cart_items)
            if not populate_success:
                logger.error("❌ Failed to populate external cart - aborting checkout")
                return False

            # OPTIMIZATION: Skip final verification for legacy APIs as well
            # The add_to_cart operations already confirm success
            logger.info("✅ Step 4: Cart synchronization verified (legacy API)")
            logger.info(
                f"🎉 Legacy cart synchronization completed successfully using API {api_version}"
            )
            return True

        except Exception as e:
            logger.error(
                f"💥 Critical error in legacy cart synchronization: {e}", exc_info=True
            )
            return False

    async def _populate_cart_from_virtual_legacy(
        self, cart_items: List[Dict[str, Any]]
    ) -> bool:
        """
        Populate external cart from virtual cart using legacy API (v1/v2).

        Args:
            cart_items: List of virtual cart items to add

        Returns:
            bool: True if all items were added successfully, False otherwise
        """
        try:
            api_version = getattr(self.external_api_service, "api_version", "v1")
            total_quantity = sum(int(item.get("quantity", 1)) for item in cart_items)
            logger.info(
                f"📦 Populating external cart with {len(cart_items)} unique items "
                f"({total_quantity} total quantity) using API {api_version}"
            )

            success_count = 0
            for i, item in enumerate(cart_items, 1):
                card_id = item.get("card_id")
                quantity = int(item.get("quantity", 1))

                if not card_id:
                    logger.warning(f"⚠️ Skipping item {i}: missing card_id")
                    continue

                logger.info(
                    f"📡 Adding item {i}/{len(cart_items)}: {card_id} (qty: {quantity})"
                )

                # Add each quantity individually (legacy APIs typically don't support quantity parameter)
                for q in range(quantity):
                    add_success = await self._add_to_external_cart_legacy(card_id)
                    if add_success:
                        success_count += 1
                    else:
                        logger.error(
                            f"❌ Failed to add card {card_id} (quantity {q+1}/{quantity})"
                        )
                        return False

            logger.info(f"✅ Successfully added {success_count} items to external cart")
            return success_count > 0

        except Exception as e:
            logger.error(f"❌ Error populating cart from virtual cart: {e}")
            return False

    async def _add_to_external_cart_legacy(self, card_id: str) -> bool:
        """
        Add a single item to external cart using legacy API (v1/v2).

        Args:
            card_id: Card ID to add

        Returns:
            bool: True if item was added successfully, False otherwise
        """
        try:
            api_version = getattr(self.external_api_service, "api_version", "v1")
            logger.debug(f"📡 Adding card {card_id} using API {api_version}")

            # Use the external_api_service add_to_cart method (respects configured version)
            response = await self.external_api_service.add_to_cart(card_id)

            if response.success:
                logger.debug(
                    f"✅ Successfully added card {card_id} using API {api_version}"
                )
                return True
            else:
                error_msg = response.error or "Unknown error"
                logger.error(
                    f"❌ Failed to add card {card_id} using API {api_version}: {error_msg}"
                )

                # Handle "already in cart" scenario as success
                if "already" in error_msg.lower() and "cart" in error_msg.lower():
                    logger.info(
                        f"ℹ️ Card {card_id} already in cart - treating as success"
                    )
                    return True

                return False

        except Exception as e:
            logger.error(f"❌ Exception adding card {card_id} using legacy API: {e}")
            return False

    async def _get_external_api_balance(self) -> Optional[float]:
        """
        Get user balance from external API with automatic re-authentication on session expiry.

        Returns:
            float: User balance from external API, or None if unable to retrieve
        """
        try:
            logger.info("🔍 Retrieving balance from external API")

            # Use the retry wrapper for automatic re-authentication
            response = await self._api_call_with_auth_retry(
                self.external_api_service.get_user_info
            )

            if response and response.success:
                # Extract balance from response using robust method
                balance = self._extract_balance_from_response(response.data)

                if balance is not None:
                    logger.debug(f"✅ Retrieved external API balance: ${balance:.2f}")
                    return balance
                else:
                    logger.error("❌ Could not extract balance from external API response")
                    logger.debug(f"Response data: {response.data}")
                    return None
            else:
                error_msg = response.error if response else "No response received"
                logger.error(f"Failed to get user info from external API: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"❌ Exception retrieving external API balance: {e}")
            return None

    async def _api_call_with_auth_retry(self, api_method, *args, max_retries=2, **kwargs):
        """
        Generic wrapper for external API calls with automatic re-authentication on session expiry.

        Args:
            api_method: The external API service method to call
            *args: Positional arguments for the API method
            max_retries: Maximum number of retries (default: 2, allows one retry after re-auth)
            **kwargs: Keyword arguments for the API method

        Returns:
            The response from the API method, or None if all retries failed
        """
        method_name = getattr(api_method, '__name__', str(api_method))
        logger.debug(f"🔄 API call with auth retry: {method_name} (max_retries={max_retries})")

        for attempt in range(max_retries):
            try:
                logger.debug(f"🔄 Attempt {attempt + 1}/{max_retries} for {method_name}")

                # Call the API method
                response = await api_method(*args, **kwargs)

                # Log response details for debugging
                if response:
                    logger.debug(f"📊 Response: success={response.success}, error={response.error}")
                    if hasattr(response, 'status_code'):
                        logger.debug(f"📊 Status code: {response.status_code}")
                else:
                    logger.debug("📊 No response received")

                if response and response.success:
                    # Success, return the response
                    logger.debug(f"✅ {method_name} successful on attempt {attempt + 1}")
                    return response

                # Check if this is an authentication error
                elif self._is_authentication_error(response):
                    if attempt < max_retries - 1:
                        logger.warning(f"🔑 Authentication error detected in {method_name} (attempt {attempt + 1}): {response.error if response else 'Unknown error'}")
                        logger.info("🔄 Attempting to refresh authentication session...")

                        # Try to refresh authentication
                        refresh_success = await self._refresh_authentication()
                        if refresh_success:
                            logger.info("✅ Authentication refreshed successfully, retrying API call...")
                            # Add a small delay before retry to allow session to propagate
                            await asyncio.sleep(1)
                            continue
                        else:
                            logger.error("❌ Failed to refresh authentication, aborting retries")
                            return response
                    else:
                        logger.error(f"❌ Authentication error on final attempt for {method_name}: {response.error if response else 'Unknown error'}")
                        return response
                else:
                    # Other error, don't retry
                    logger.debug(f"❌ Non-authentication error in {method_name}, not retrying: {response.error if response else 'Unknown error'}")
                    return response

            except Exception as e:
                logger.error(f"❌ Exception in {method_name} (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    # Create a mock response for consistency
                    class MockResponse:
                        def __init__(self, error_msg):
                            self.success = False
                            self.error = error_msg
                            self.data = None
                            self.status_code = None

                    return MockResponse(f"API call failed after {max_retries} attempts: {str(e)}")

                # Add delay before retry on exception
                await asyncio.sleep(1)

        logger.error(f"❌ All retry attempts exhausted for {method_name}")
        return None

    def _is_authentication_error(self, response) -> bool:
        """
        Check if the API response indicates an authentication error.

        Args:
            response: APIResponse object

        Returns:
            bool: True if this is an authentication error
        """
        if not response or response.success:
            return False

        # Check for 401 status code
        if hasattr(response, 'status_code') and response.status_code == 401:
            logger.debug(f"🔍 Authentication error detected: HTTP 401 status code")
            return True

        # Check for session expired messages in error text
        error_msg = str(response.error).lower() if response.error else ""

        # Enhanced keyword detection for various authentication error patterns
        session_expired_keywords = [
            "session expired",
            "login again",
            "unauthorized",
            "authentication",
            "401",
            "session invalid",
            "token expired",
            "access denied",
            "forbidden",
            "please login",
            "re-authenticate"
        ]

        # Check if any keyword matches
        for keyword in session_expired_keywords:
            if keyword in error_msg:
                logger.debug(f"🔍 Authentication error detected: keyword '{keyword}' found in error message")
                return True

        # Additional check: look at the raw response data if available
        if hasattr(response, 'data') and response.data:
            data_str = str(response.data).lower()
            for keyword in session_expired_keywords:
                if keyword in data_str:
                    logger.debug(f"🔍 Authentication error detected: keyword '{keyword}' found in response data")
                    return True

        # Log the error message for debugging if no keywords matched
        if error_msg:
            logger.debug(f"🔍 No authentication keywords found in error: {error_msg}")

        return False

    async def _refresh_authentication(self) -> bool:
        """
        Attempt to refresh the authentication session for API v3.

        Returns:
            bool: True if authentication was successfully refreshed
        """
        try:
            # Check what API version we're using
            settings = get_settings()
            api_version = getattr(settings, "EXTERNAL_API_VERSION", "v2")

            logger.info(f"🔄 Refreshing authentication for API version: {api_version}")

            if api_version == "v3":
                # For API v3, we need to clear the cached session and force re-authentication
                logger.info("🔄 Clearing API v3 session cache to force re-authentication")

                # Import here to avoid circular imports
                from api_v3.auth.shared_session import SharedSessionManager

                # Get the session manager instance
                session_manager = SharedSessionManager()

                # Get base URL and username from settings
                base_url = getattr(settings, "EXTERNAL_V3_BASE_URL", None)
                username = getattr(settings, "EXTERNAL_V3_USERNAME", None)
                password = getattr(settings, "EXTERNAL_V3_PASSWORD", None)

                logger.debug(f"🔍 API v3 credentials check:")
                logger.debug(f"  - Base URL: {base_url}")
                logger.debug(f"  - Username: {username}")
                logger.debug(f"  - Password: {'***' if password else 'NOT SET'}")

                if base_url and username and password:
                    # Get session info before invalidation
                    session_info_before = session_manager.get_session_info()
                    logger.debug(f"📊 Sessions before invalidation: {len(session_info_before)}")

                    # Invalidate the cached session
                    session_manager.invalidate_session(base_url, username)
                    logger.debug(f"✅ Invalidated cached session for {username}")

                    # Get session info after invalidation
                    session_info_after = session_manager.get_session_info()
                    logger.debug(f"📊 Sessions after invalidation: {len(session_info_after)}")

                    # Test that we can create a new session
                    logger.info("🔄 Testing fresh session creation...")
                    try:
                        # Note: get_or_create_session is synchronous, not async
                        fresh_session = session_manager.get_or_create_session(base_url, username, password)
                        if fresh_session:
                            logger.info("✅ Fresh session created successfully")
                            return True
                        else:
                            logger.error("❌ Failed to create fresh session")
                            return False
                    except Exception as session_error:
                        logger.error(f"❌ Error creating fresh session: {session_error}")
                        return False
                else:
                    missing_creds = []
                    if not base_url:
                        missing_creds.append("EXTERNAL_V3_BASE_URL")
                    if not username:
                        missing_creds.append("EXTERNAL_V3_USERNAME")
                    if not password:
                        missing_creds.append("EXTERNAL_V3_PASSWORD")

                    logger.error(f"❌ Missing API v3 credentials: {', '.join(missing_creds)}")
                    return False
            else:
                # For other API versions, we might need different refresh logic
                logger.warning(f"Authentication refresh not implemented for API version {api_version}")
                return False

        except Exception as e:
            logger.error(f"❌ Error refreshing authentication: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    def _extract_balance_from_response(
        self, response_data: Dict[str, Any]
    ) -> Optional[float]:
        """
        Extract balance from external API response with multiple fallback strategies.

        Args:
            response_data: Response data from external API

        Returns:
            float: Extracted balance, or None if not found
        """
        if not isinstance(response_data, dict):
            return None

        # Strategy 1: Check user.balance (most likely for /user/getme)
        user_data = response_data.get("user", {})
        if isinstance(user_data, dict) and "balance" in user_data:
            try:
                balance = float(user_data["balance"])
                logger.debug(f"Found balance in user.balance: {balance}")
                return balance
            except (ValueError, TypeError) as e:
                logger.warning(f"Could not parse user.balance as float: {e}")

        # Strategy 2: Check top-level balance
        if "balance" in response_data:
            try:
                balance = float(response_data["balance"])
                logger.debug(f"Found balance at top level: {balance}")
                return balance
            except (ValueError, TypeError) as e:
                logger.warning(f"Could not parse top-level balance as float: {e}")

        # Strategy 3: Check other common balance field names
        balance_fields = [
            "credits",
            "funds",
            "money",
            "wallet_balance",
            "account_balance",
        ]
        for field in balance_fields:
            # Check top level
            if field in response_data:
                try:
                    balance = float(response_data[field])
                    logger.debug(f"Found balance in {field}: {balance}")
                    return balance
                except (ValueError, TypeError):
                    pass

            # Check in user object
            if isinstance(user_data, dict) and field in user_data:
                try:
                    balance = float(user_data[field])
                    logger.debug(f"Found balance in user.{field}: {balance}")
                    return balance
                except (ValueError, TypeError):
                    pass

        logger.warning("No balance field found in external API response")
        return None

    async def _validate_cart_items(
        self, expected_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Validate cart items using appropriate API version.
        Routes to API v3-only validation when API v3 is configured.
        """
        try:
            # Check if we should use API v3-only validation
            api_version = getattr(self.external_api_service, "api_version", None)

            # Check if any items have non-numeric card IDs (which forces API v3 routing)
            has_non_numeric_ids = any(
                not str(item.get("card_id", "")).isdigit()
                for item in expected_items
            )

            # Use API v3 validation if:
            # 1. API version is explicitly v3/base3, OR
            # 2. Items have non-numeric IDs (which forces API v3 routing in add_to_cart)
            should_use_v3 = api_version in ["v3", "base3"] or has_non_numeric_ids

            if should_use_v3:
                if has_non_numeric_ids and api_version not in ["v3", "base3"]:
                    logger.info(f"🔄 Non-numeric card IDs detected - using API v3 validation despite API version {api_version}")
                logger.info("🔍 Using API v3-only cart validation")
                return await self._verify_cart_synchronization_v3(expected_items)

            # Legacy validation for non-v3 APIs
            logger.info("Validating external cart contents using legacy method...")
            external_items = await self._get_external_cart_items()

            expected_map: dict[str, int] = {}
            for item in expected_items:
                card_id = str(item.get("card_id", ""))
                qty = int(item.get("quantity", 1))
                if card_id:
                    expected_map[card_id] = expected_map.get(card_id, 0) + qty

            actual_map: dict[str, int] = {}
            for item in external_items:
                card_id = str(
                    item.get("product_id")
                    or item.get("card_id")
                    or item.get("id")
                    or item.get("_id")
                    or ""
                )
                if card_id:
                    actual_map[card_id] = actual_map.get(card_id, 0) + 1

            # Separate unavailable items from critical errors
            missing_cards: list[str] = []
            extra_items_errors: list[str] = []
            
            # Check for missing items (unavailable cards)
            for cid, exp in expected_map.items():
                act = actual_map.get(cid, 0)
                if act < exp:
                    missing_cards.append(cid)
                    logger.warning(f"⚠️ Card not available: {cid} (expected {exp}, found {act})")
            
            # Check for extra items
            for cid, act in actual_map.items():
                exp = expected_map.get(cid, 0)
                if act > exp:
                    error = f"Card {cid}: unexpected {act - exp} extra items"
                    extra_items_errors.append(error)
                    logger.error(f"❌ Extra items: {error}")

            # Determine if we can proceed
            available_cards = [cid for cid in expected_map.keys() if cid not in missing_cards]
            
            # If ALL cards unavailable, fail
            if missing_cards and not available_cards:
                msg = f"All {len(missing_cards)} card(s) are no longer available"
                logger.error(f"Cart validation failed: {msg}")
                return {
                    "valid": False,
                    "message": msg,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": [msg],
                    "missing_cards": missing_cards,
                    "available_cards": [],
                }
            
            # If SOME cards unavailable, continue with available ones
            if missing_cards:
                msg = f"{len(missing_cards)} card(s) unavailable, continuing with {len(available_cards)} available"
                logger.warning(f"⚠️ {msg}")
                logger.info(f"📦 Available: {available_cards}")
                logger.info(f"❌ Unavailable: {missing_cards}")
                # Don't return yet - continue to stock validation with available cards
            
            # If extra items found, it's a critical error
            if extra_items_errors:
                msg = "; ".join(extra_items_errors)
                logger.error(f"Cart validation failed: {msg}")
                return {
                    "valid": False,
                    "message": msg,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": extra_items_errors,
                }

            stock_validation = await self._validate_stock_and_prices(
                expected_items, external_items
            )
            if not stock_validation["valid"]:
                return stock_validation

            # Build result with missing cards info if applicable
            result = {
                "valid": True,
                "message": "Cart contents validated successfully",
                "expected_items": expected_map,
                "actual_items": actual_map,
                "warnings": stock_validation.get("warnings", []),
            }
            
            # Add missing cards info if any
            if missing_cards:
                result["partial_success"] = True
                result["missing_cards"] = missing_cards
                result["available_cards"] = available_cards
                result["message"] = f"{len(missing_cards)} card(s) unavailable, continuing with {len(available_cards)} available"
                result["warnings"].extend([f"Card {cid} is no longer available" for cid in missing_cards])
                logger.info(f"✅ Partial validation success: {len(available_cards)} available, {len(missing_cards)} unavailable")
            
            return result
        except Exception as e:
            logger.error("Error validating cart items", exc_info=True)
            return {"valid": False, "message": f"Validation failed: {str(e)}"}

    async def _clear_external_cart_v3(self) -> bool:
        """
        Clear external cart using API v3 clear_cart() method only with robust error handling.

        Returns:
            bool: True if cart was cleared successfully, False otherwise
        """
        max_retries = 3
        retry_delay = 2.0

        for attempt in range(1, max_retries + 1):
            try:
                logger.info(
                    f"🧹 Clearing external cart using API v3 clear_cart() (attempt {attempt}/{max_retries})"
                )

                # Get cart state before clearing (with error handling)
                cart_before = await self._get_external_cart_items_v3_with_retry("pre_clear_check")
                logger.info(f"📊 Cart state before clearing: {len(cart_before)} items")

                if cart_before:
                    for i, item in enumerate(cart_before[:3], 1):  # Log first 3 items
                        # Use correct field names for API v3 response
                        item_id = item.get("_id", item.get("id", "Unknown"))
                        item_name = item.get("cardholder", item.get("name", "Unknown"))
                        logger.info(f"   Item {i}: {item_id} - {item_name}")
                    if len(cart_before) > 3:
                        logger.info(f"   ... and {len(cart_before) - 3} more items")

                # Use API v3 clear_cart method with circuit breaker protection
                # Force API v3 usage since we're in the API v3-only workflow
                clear_response = await self.api_v3_circuit_breaker.call(
                    self.external_api_service.clear_cart,
                    force_api_v3=True
                )

                if clear_response.success:
                    logger.info("✅ API v3 clear_cart() succeeded")
                    
                    # Only verify cart clearing on the last attempt to reduce redundant calls
                    if attempt == max_retries:
                        logger.info("🔍 Verifying cart was actually cleared (empty cart expected)...")
                        logger.info("ℹ️  Note: 'No cards extracted' messages below are expected for empty cart verification")
                        await asyncio.sleep(1.0)  # Give the server time to process
                        
                        cart_after = await self._get_external_cart_items_v3_with_retry("verification")
                        if not cart_after:
                            logger.info("✅ Cart verification: Cart is empty after clearing (verification successful)")
                            return True
                        else:
                            logger.warning(f"⚠️ Cart verification: {len(cart_after)} items still remain after clearing")
                            for i, item in enumerate(cart_after[:3], 1):
                                item_id = item.get("_id", item.get("id", "Unknown"))
                                item_name = item.get("cardholder", item.get("name", "Unknown"))
                                logger.warning(f"   Remaining item {i}: {item_id} - {item_name}")
                            
                            logger.error("❌ Cart clearing failed - items still remain after all retries")
                            return False
                    else:
                        # On intermediate attempts, just wait and continue
                        logger.info(f"🔄 Cart clear attempt {attempt} succeeded, continuing...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 1.5  # Exponential backoff
                        continue
                else:
                    error_msg = clear_response.error or "Unknown error"

                    # Check for specific error types
                    if self._is_service_unavailable_error(error_msg):
                        logger.warning(
                            f"⚠️ API v3 service unavailable (attempt {attempt}/{max_retries}): {error_msg}"
                        )
                        if attempt < max_retries:
                            logger.info(f"🔄 Retrying in {retry_delay} seconds...")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 1.5  # Exponential backoff
                            continue

                    logger.error(f"❌ API v3 clear_cart() failed: {error_msg}")
                    return False

            except CircuitBreakerOpenError as e:
                logger.error(f"❌ Circuit breaker prevented cart clearing: {e}")
                return False

            except Exception as e:
                error_msg = str(e)

                if self._is_service_unavailable_error(error_msg):
                    logger.warning(
                        f"⚠️ API v3 service exception (attempt {attempt}/{max_retries}): {error_msg}"
                    )
                    if attempt < max_retries:
                        logger.info(f"🔄 Retrying in {retry_delay} seconds...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 1.5
                        continue

                logger.error(f"❌ Error clearing cart with API v3: {error_msg}")
                return False

        logger.error(f"❌ Failed to clear cart after {max_retries} attempts")
        return False

    def _is_service_unavailable_error(self, error_msg: str) -> bool:
        """
        Check if an error message indicates that the API v3 service is temporarily unavailable.

        Args:
            error_msg: Error message to check

        Returns:
            bool: True if the error indicates service unavailability
        """
        unavailable_indicators = [
            "502 Bad Gateway",
            "503 Service Unavailable",
            "504 Gateway Timeout",
            "Connection refused",
            "Connection timeout",
            "Network is unreachable",
            "Temporary failure in name resolution",
            "Bad Gateway",
            "Service Unavailable",
            "Gateway Timeout",
        ]

        error_lower = error_msg.lower()
        return any(
            indicator.lower() in error_lower for indicator in unavailable_indicators
        )

    async def _get_external_cart_items_v3_with_retry(self, context: str = "cart_check") -> List[Dict[str, Any]]:
        """
        Get external cart items using API v3 view_cart() with retry logic.

        Args:
            context: Context for this call (e.g., "verification", "cart_check", "population")

        Returns:
            List of cart items from API v3, empty list if service unavailable
        """
        max_retries = 2
        retry_delay = 1.0

        # Add a small delay before first attempt to avoid race conditions
        # between add_to_cart and view_cart operations
        await asyncio.sleep(0.5)

        for attempt in range(1, max_retries + 1):
            try:
                # Force API v3 usage since we're in the API v3-only workflow
                view_response = await self.api_v3_circuit_breaker.call(
                    self.external_api_service.view_cart,
                    force_api_v3=True
                )

                if view_response.success:
                    cart_data = view_response.data
                    
                    # Debug: Log the response structure for troubleshooting
                    logger.debug(f"🔍 Cart view response - data type: {type(cart_data)}")
                    if isinstance(cart_data, dict):
                        logger.debug(f"🔍 Cart view response - keys: {list(cart_data.keys())}")
                        if "data" in cart_data:
                            logger.debug(f"🔍 Cart view response - data length: {len(cart_data['data']) if cart_data['data'] else 'None'}")
                    
                    if isinstance(cart_data, dict) and "data" in cart_data:
                        result_data = cart_data["data"]
                        item_count = len(result_data) if result_data else 0
                        
                        if context == "verification" and item_count == 0:
                            logger.debug(f"🔍 Cart verification: Found {item_count} items (empty cart as expected)")
                        else:
                            logger.debug(f"🔍 Cart {context}: Found {item_count} items")
                        
                        return result_data
                    else:
                        logger.warning(
                            f"⚠️ Unexpected cart data format in {context}: {type(cart_data)}"
                        )
                        return []
                else:
                    error_msg = view_response.error or "Unknown error"

                    if self._is_service_unavailable_error(error_msg):
                        logger.warning(
                            f"⚠️ API v3 service unavailable for cart view (attempt {attempt}/{max_retries})"
                        )
                        if attempt < max_retries:
                            await asyncio.sleep(retry_delay)
                            continue

                    logger.error(f"❌ Failed to get cart items: {error_msg}")
                    return []

            except CircuitBreakerOpenError as e:
                logger.error(f"❌ Circuit breaker prevented cart view: {e}")
                return []

            except Exception as e:
                error_msg = str(e)

                if self._is_service_unavailable_error(error_msg):
                    logger.warning(
                        f"⚠️ API v3 service exception for cart view (attempt {attempt}/{max_retries}): {error_msg}"
                    )
                    if attempt < max_retries:
                        await asyncio.sleep(retry_delay)
                        continue

                logger.error(f"❌ Error getting cart items with API v3: {error_msg}")
                return []

        logger.warning(
            f"⚠️ Failed to get cart items after {max_retries} attempts - assuming empty cart"
        )
        return []

    async def _is_api_v3_service_available(self) -> bool:
        """
        Check if the API v3 service is currently available by making a simple request.

        Returns:
            bool: True if service is available, False otherwise
        """
        try:
            logger.info("🔍 Checking API v3 service availability...")

            # Try a simple cart view request
            view_response = await self.external_api_service.view_cart()

            if view_response.success:
                logger.info("✅ API v3 service is available")
                return True
            else:
                error_msg = view_response.error or "Unknown error"

                if self._is_service_unavailable_error(error_msg):
                    logger.warning(
                        f"⚠️ API v3 service is currently unavailable: {error_msg}"
                    )
                    return False
                else:
                    logger.warning(
                        f"⚠️ API v3 service returned error but may be available: {error_msg}"
                    )
                    return True  # Other errors don't necessarily mean service is down

        except Exception as e:
            error_msg = str(e)

            if self._is_service_unavailable_error(error_msg):
                logger.warning(
                    f"⚠️ API v3 service is currently unavailable: {error_msg}"
                )
                return False
            else:
                logger.warning(
                    f"⚠️ Error checking API v3 service availability: {error_msg}"
                )
                return True  # Assume available if we can't determine otherwise

    def _get_service_unavailable_message(self, error_msg: str) -> str:
        """
        Generate a user-friendly error message for service unavailability.

        Args:
            error_msg: Technical error message

        Returns:
            str: User-friendly error message with troubleshooting steps
        """
        base_message = "The checkout service is temporarily unavailable."

        if "502 Bad Gateway" in error_msg:
            return (
                f"{base_message} The external service is experiencing server issues. "
                "Please try again in a few minutes. If the problem persists, contact support."
            )
        elif "503 Service Unavailable" in error_msg:
            return (
                f"{base_message} The external service is under maintenance. "
                "Please try again later."
            )
        elif "504 Gateway Timeout" in error_msg:
            return (
                f"{base_message} The external service is responding slowly. "
                "Please try again in a few minutes."
            )
        elif "Connection refused" in error_msg or "Network is unreachable" in error_msg:
            return (
                f"{base_message} There is a network connectivity issue. "
                "Please check your internet connection and try again."
            )
        else:
            return (
                f"{base_message} Technical details: {error_msg}. "
                "Please try again later or contact support if the issue persists."
            )

    async def _verify_cart_empty_v3(self) -> bool:
        """
        Verify that the external cart is empty using API v3 view_cart() with retry logic.

        Returns:
            bool: True if cart is empty, False otherwise
        """
        try:
            logger.info("📋 Verifying cart is empty using API v3 view_cart()")
            logger.info("ℹ️  Note: 'No cards extracted' messages below are expected for empty cart verification")

            # Add extra delay after clear operation to ensure consistency
            logger.debug("🔄 Adding delay to ensure cart clearing is reflected...")
            await asyncio.sleep(1.0)

            cart_items = await self._get_external_cart_items_v3_with_retry("verification")
            
            # Debug: Log the raw response structure
            logger.debug(f"🔍 Cart verification - cart_items type: {type(cart_items)}")
            logger.debug(f"🔍 Cart verification - cart_items length: {len(cart_items) if cart_items else 'None'}")
            if cart_items:
                logger.debug(f"🔍 Cart verification - first item structure: {list(cart_items[0].keys()) if cart_items[0] else 'Empty dict'}")

            if not cart_items:
                logger.info("✅ Cart is empty as expected")
                return True
            else:
                logger.error(
                    f"❌ Cart is not empty - contains {len(cart_items)} items:"
                )
                for i, item in enumerate(cart_items, 1):
                    # Use correct field names for API v3 response
                    item_id = item.get("_id", item.get("id", "Unknown"))
                    item_name = item.get("cardholder", item.get("name", "Unknown"))
                    logger.error(f"   Remaining item {i}: {item_id} - {item_name}")
                    # Debug: Show all available fields
                    logger.debug(f"   Item {i} all fields: {list(item.keys())}")
                return False

        except Exception as e:
            logger.error(f"❌ Error verifying cart is empty: {e}")
            return False

    async def _get_external_cart_items_v3(self) -> List[Dict[str, Any]]:
        """
        Get external cart items using API v3 view_cart() only.

        Returns:
            List of cart items from API v3
        """
        try:
            view_response = await self.external_api_service.view_cart()

            if view_response.success:
                cart_data = view_response.data
                if isinstance(cart_data, dict) and "data" in cart_data:
                    return cart_data["data"]
                else:
                    logger.warning(f"⚠️ Unexpected cart data format: {type(cart_data)}")
                    return []
            else:
                logger.error(f"❌ Failed to get cart items: {view_response.error}")
                return []

        except Exception as e:
            logger.error(f"❌ Error getting cart items with API v3: {e}")
            return []

    async def _populate_cart_from_virtual_v3(
        self, cart_items: List[Dict[str, Any]]
    ) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Populate external cart from virtual cart using API v3 add_to_cart() only.

        Args:
            cart_items: List of virtual cart items to add

        Returns:
            bool: True if all items were added successfully, False otherwise
        """
        try:
            total_quantity = sum(int(item.get("quantity", 1)) for item in cart_items)
            logger.info(
                f"📦 Populating cart from virtual cart: {len(cart_items)} unique items, {total_quantity} total items"
            )

            failed_items: List[Dict[str, Any]] = []

            for idx, item_data in enumerate(cart_items, 1):
                # Extract card ID from CartItem structure
                # CartItem has: card_id (str), card_data (dict with _id inside)
                card_id = item_data.get("card_id") or item_data.get("_id")
                quantity = int(item_data.get("quantity", 1))
                
                # Get card name from nested card_data for display
                card_name = item_data.get("card_data", {}).get(
                    "bank", f"Card {card_id}"
                )

                # Skip items with missing or invalid card IDs
                if not card_id or card_id == "None" or str(card_id).strip() == "":
                    logger.error(
                        f"❌ Skipping item {idx} - invalid card_id: {repr(card_id)}"
                    )
                    logger.debug(f"   Item data: {item_data}")
                    failed_items.append({
                        "card_id": str(card_id),
                        "card_name": card_name,
                        "error": "Invalid or missing card ID",
                        "quantity": quantity,
                    })
                    continue

                logger.info(
                    f"➕ Adding item {idx}/{len(cart_items)}: {card_name} (ID: {card_id}, Qty: {quantity})"
                )

                # NOTE: We do NOT validate cards against current API inventory because:
                # 1. API v3 shop has rotating inventory - cards change on each request
                # 2. Cards in virtual cart were valid when added by user
                # 3. API will reject invalid cards anyway - we'll handle failures gracefully
                # 4. This prevents false negatives where valid cards are rejected

                # Add each quantity using API v3 add_to_cart
                success_count = 0
                for i in range(quantity):
                    logger.debug(
                        f"   🔄 Adding copy {i+1}/{quantity} of card {card_id}"
                    )

                    add_success = await self._add_to_external_cart_v3_with_retry(
                        card_id
                    )

                    if add_success:
                        success_count += 1
                        logger.debug(
                            f"   ✅ Copy {i+1}/{quantity} of card {card_id} added successfully"
                        )
                    else:
                        logger.error(
                            f"   ❌ Copy {i+1}/{quantity} of card {card_id} failed to add"
                        )
                        failed_items.append(
                            {
                                "card_id": card_id,
                                "card_name": card_name,
                                "attempt": i + 1,
                                "quantity": quantity,
                            }
                        )

                if success_count != quantity:
                    logger.warning(
                        f"⚠️ Only added {success_count}/{quantity} copies of {card_name}"
                    )

                # Small delay to avoid rate limits
                await asyncio.sleep(0.05)

            if failed_items:
                logger.error(
                    f"❌ Cart population had failures: {len(failed_items)} items failed"
                )
                for f in failed_items[:5]:  # Log first 5 failures
                    logger.error(f"   - Failed: {f}")
                
                # Calculate success rate
                total_items = len(cart_items)
                success_items = total_items - len(failed_items)
                success_rate = (success_items / total_items) * 100 if total_items > 0 else 0
                
                if success_items > 0:
                    logger.warning(
                        f"⚠️ Partial success: {success_items}/{total_items} items added successfully ({success_rate:.1f}%)"
                    )
                else:
                    logger.error("❌ Complete failure: No items could be added to cart")

            logger.info("✅ Cart population from virtual cart completed")
            
            # Return success if at least one item was added successfully
            # This allows checkout to continue with available items
            total_items = len(cart_items)
            success_items = total_items - len(failed_items)
            return success_items > 0, failed_items  # Return both status and failed items

        except Exception as e:
            logger.error(f"❌ Error populating cart from virtual cart: {e}")
            return False, []  # Return failure with empty failed items list

    async def _add_to_external_cart_v3(self, card_id: str) -> bool:
        """
        Add a single item to external cart using API v3 add_to_cart() only.

        Args:
            card_id: Card ID to add

        Returns:
            bool: True if item was added successfully, False otherwise
        """
        try:
            logger.debug(f"📡 Adding card {card_id} using API v3 add_to_cart()")

            # Use API v3 add_to_cart method - force API v3 usage
            response = await self.external_api_service.add_to_cart(card_id, force_api_v3=True)

            if response.success:
                logger.debug(f"✅ API v3 add_to_cart() succeeded for card {card_id}")
                return True
            else:
                error_msg = response.error or "Unknown error"
                logger.error(
                    f"❌ API v3 add_to_cart() failed for card {card_id}: {error_msg}"
                )

                # Handle "already in cart" scenario
                if "already" in error_msg.lower() and "cart" in error_msg.lower():
                    logger.info(
                        f"ℹ️ Card {card_id} already in cart - treating as success"
                    )
                    return True

                return False

        except Exception as e:
            logger.error(f"❌ Exception adding card {card_id} with API v3: {e}")
            return False

    async def _add_to_external_cart_v3_with_retry(self, card_id: str) -> bool:
        """
        Add a single item to external cart using API v3 add_to_cart() with retry logic and auth handling.

        Args:
            card_id: Card ID to add

        Returns:
            bool: True if item was added successfully, False otherwise
        """
        max_retries = 3
        retry_delay = 1.0

        for attempt in range(1, max_retries + 1):
            try:
                logger.debug(
                    f"📡 Adding card {card_id} using API v3 add_to_cart() (attempt {attempt}/{max_retries})"
                )

                # Use API v3 add_to_cart method with auth retry wrapper and circuit breaker protection
                response = await self.api_v3_circuit_breaker.call(
                    self._api_call_with_auth_retry,
                    self.external_api_service.add_to_cart, 
                    card_id, 
                    force_api_v3=True
                )

                if response.success:
                    logger.debug(
                        f"✅ API v3 add_to_cart() succeeded for card {card_id}"
                    )
                    return True
                else:
                    error_msg = response.error or "Unknown error"

                    # Handle "already in cart" scenario
                    if "already" in error_msg.lower() and "cart" in error_msg.lower():
                        logger.info(
                            f"ℹ️ Card {card_id} already in cart - treating as success"
                        )
                        return True

                    # Check for service unavailability
                    if self._is_service_unavailable_error(error_msg):
                        logger.warning(
                            f"⚠️ API v3 service unavailable for add_to_cart (attempt {attempt}/{max_retries})"
                        )
                        if attempt < max_retries:
                            logger.info(f"🔄 Retrying in {retry_delay} seconds...")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 1.5
                            continue

                    logger.error(
                        f"❌ API v3 add_to_cart() failed for card {card_id}: {error_msg}"
                    )
                    return False

            except CircuitBreakerOpenError as e:
                logger.error(
                    f"❌ Circuit breaker prevented add_to_cart for {card_id}: {e}"
                )
                return False

            except Exception as e:
                error_msg = str(e)

                if self._is_service_unavailable_error(error_msg):
                    logger.warning(
                        f"⚠️ API v3 service exception for add_to_cart (attempt {attempt}/{max_retries}): {error_msg}"
                    )
                    if attempt < max_retries:
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 1.5
                        continue

                logger.error(
                    f"❌ Exception adding card {card_id} with API v3: {error_msg}"
                )
                return False

        logger.error(f"❌ Failed to add card {card_id} after {max_retries} attempts")
        return False

    async def _validate_card_exists_in_api_v3(self, card_id: str) -> bool:
        """
        Validate that a card ID exists in the current API v3 inventory.
        
        Args:
            card_id: Card ID to validate
            
        Returns:
            bool: True if card exists in current API inventory, False otherwise
        """
        try:
            logger.debug(f"🔍 Validating card {card_id} exists in API v3 inventory")
            
            # Get current cards from API
            response = await self.api_v3_circuit_breaker.call(
                self._api_call_with_auth_retry,
                self.external_api_service.list_items
            )
            
            if not response.success:
                logger.warning(f"⚠️ Failed to fetch API inventory for card validation: {response.error}")
                # If we can't validate due to API issues, return False to avoid adding invalid cards
                # This is safer than assuming cards exist when we can't verify
                return False
                
            # Extract cards from response - handle different response structures
            extracted_cards = []
            
            if isinstance(response.data, dict):
                # Primary location: direct data field (list of cards)
                if "data" in response.data and isinstance(response.data["data"], list):
                    extracted_cards = response.data["data"]
                
                # Secondary location: direct extracted_cards field
                elif "extracted_cards" in response.data:
                    extracted_cards = response.data.get("extracted_cards", [])
                
                # Tertiary location: cards field (alternative naming)
                elif "cards" in response.data:
                    extracted_cards = response.data.get("cards", [])
            
            # Handle case where response.data is directly a list of cards
            elif isinstance(response.data, list):
                extracted_cards = response.data
            
            for card in extracted_cards:
                if card.get("_id") == card_id:
                    logger.debug(f"✅ Card {card_id} found in current API inventory")
                    return True
                    
            logger.warning(f"❌ Card {card_id} not found in current API inventory ({len(extracted_cards)} cards checked)")
            return False
            
        except Exception as e:
            logger.error(f"❌ Error validating card {card_id} in API inventory: {e}")
            # If validation fails due to error, return False to be safe
            # Better to reject potentially invalid cards than allow invalid ones through
            return False

    async def _verify_cart_synchronization_v3(
        self, expected_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Verify that external cart is synchronized with virtual cart using API v3 view_cart() only.

        Args:
            expected_items: List of items that should be in the external cart

        Returns:
            Dict with validation results including:
            - valid: bool - True if synchronization is correct
            - message: str - Description of validation result
            - expected_items: dict - Expected card_id -> quantity mapping
            - actual_items: dict - Actual card_id -> quantity mapping
            - errors: list - List of specific validation errors
        """
        try:
            logger.info("🔍 Verifying cart synchronization using API v3")

            # CRITICAL: Invalidate cart cache before verification to avoid stale data
            # The cache might contain the cart state from BEFORE items were added
            self.external_api_service._invalidate_cart_cache()
            logger.debug("🗑️ Invalidated cart cache before synchronization check")

            # Get external cart items using API v3 with retry logic
            external_items = await self._get_external_cart_items_v3_with_retry("synchronization_check")

            # Build expected items map
            expected_map: Dict[str, int] = {}
            for item in expected_items:
                card_id = str(item.get("card_id") or item.get("_id", ""))
                quantity = int(item.get("quantity", 1))
                if card_id:
                    expected_map[card_id] = expected_map.get(card_id, 0) + quantity

            # Build actual items map
            actual_map: Dict[str, int] = {}
            for item in external_items:
                # Try multiple possible field names for the card ID
                card_id = str(
                    item.get("id") or 
                    item.get("_id") or 
                    item.get("card_id") or 
                    item.get("product_id") or 
                    item.get("item_id") or
                    ""
                )
                if card_id and card_id != "None" and card_id.strip():
                    actual_map[card_id] = actual_map.get(card_id, 0) + 1
                else:
                    logger.warning(f"⚠️ External cart item missing valid ID: {item}")

            # Log detailed comparison
            logger.info(f"📊 Cart synchronization comparison:")
            logger.info(f"   Expected items: {len(expected_map)} unique cards")
            logger.info(f"   Actual items: {len(actual_map)} unique cards")

            # Log expected items
            logger.info("📋 Expected items (from virtual cart):")
            for card_id, quantity in expected_map.items():
                logger.info(f"   - {card_id}: {quantity}")

            # Log actual items
            logger.info("📦 Actual items (from external cart):")
            for card_id, quantity in actual_map.items():
                logger.info(f"   - {card_id}: {quantity}")

            # Validate synchronization - separate unavailable items from errors
            missing_cards: List[str] = []  # Cards not available (sold out or removed)
            extra_items_errors: List[str] = []  # Unexpected extra items

            # Check for missing items (unavailable cards)
            for card_id, expected_qty in expected_map.items():
                actual_qty = actual_map.get(card_id, 0)
                if actual_qty < expected_qty:
                    missing_cards.append(card_id)
                    logger.warning(f"⚠️ Card not available: {card_id} (expected {expected_qty}, found {actual_qty})")

            # Check for extra items (should not happen, but track as error)
            for card_id, actual_qty in actual_map.items():
                expected_qty = expected_map.get(card_id, 0)
                if actual_qty > expected_qty:
                    error = f"Card {card_id}: unexpected {actual_qty - expected_qty} extra items"
                    extra_items_errors.append(error)
                    logger.error(f"❌ Extra items: {error}")

            # Determine if we can proceed
            available_cards = [cid for cid in expected_map.keys() if cid not in missing_cards]
            
            # If ALL cards are unavailable, fail
            if missing_cards and not available_cards:
                error_message = f"All {len(missing_cards)} card(s) are no longer available"
                logger.error(f"❌ {error_message}")
                return {
                    "valid": False,
                    "message": error_message,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": [error_message],
                    "missing_cards": missing_cards,
                    "available_cards": [],
                }
            
            # If SOME cards are unavailable, continue with available ones
            if missing_cards:
                warning_message = f"{len(missing_cards)} card(s) unavailable, continuing with {len(available_cards)} available card(s)"
                logger.warning(f"⚠️ {warning_message}")
                logger.info(f"📦 Available cards: {available_cards}")
                logger.info(f"❌ Unavailable cards: {missing_cards}")
                
                return {
                    "valid": True,  # ✅ Continue with partial success
                    "partial_success": True,
                    "message": warning_message,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "missing_cards": missing_cards,
                    "available_cards": available_cards,
                    "warnings": [f"Card {cid} is no longer available" for cid in missing_cards],
                }
            
            # If extra items found, it's a critical error
            if extra_items_errors:
                error_message = "; ".join(extra_items_errors)
                logger.error(f"❌ Cart synchronization validation failed: {error_message}")
                return {
                    "valid": False,
                    "message": error_message,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": extra_items_errors,
                }
            else:
                success_message = f"Cart synchronization validated successfully - {len(expected_map)} items match"
                logger.info(f"✅ {success_message}")
                return {
                    "valid": True,
                    "message": success_message,
                    "expected_items": expected_map,
                    "actual_items": actual_map,
                    "errors": [],
                }

        except Exception as e:
            error_message = f"Cart synchronization validation failed: {str(e)}"
            logger.error(f"❌ {error_message}", exc_info=True)
            return {
                "valid": False,
                "message": error_message,
                "expected_items": {},
                "actual_items": {},
                "errors": [str(e)],
            }

    async def _validate_stock_and_prices(
        self,
        expected_items: List[Dict[str, Any]],
        external_items: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        try:
            warnings: list[str] = []

            price_by_card: dict[str, float] = {}
            for it in expected_items:
                cid = str(it.get("card_id", ""))
                if cid:
                    try:
                        price_by_card[cid] = float(it.get("price_at_add"))
                    except (TypeError, ValueError):
                        pass

            for ext in external_items:
                cid = str(
                    ext.get("product_id") or ext.get("card_id") or ext.get("id") or ""
                )
                if not cid:
                    continue
                if ext.get("out_of_stock") or ext.get("unavailable"):
                    warnings.append(f"Card {cid} appears to be out of stock")
                current_price = (
                    ext.get("price")
                    if ext.get("price") is not None
                    else ext.get("current_price")
                )
                if current_price is not None and cid in price_by_card:
                    try:
                        cp = float(current_price)
                        ep = float(price_by_card[cid])
                        if abs(cp - ep) > 0.01:
                            warnings.append(
                                f"Card {cid} price changed: expected ${ep}, current ${cp}"
                            )
                    except (TypeError, ValueError):
                        pass

            if warnings:
                logger.warning(
                    "Stock/price validation warnings: %s", "; ".join(warnings)
                )
            return {
                "valid": True,
                "message": (
                    "Stock and price validation passed"
                    if not warnings
                    else "Warnings present"
                ),
                "warnings": warnings,
            }
        except Exception as e:
            logger.error(f"Error validating stock and prices: {e}")
            return {
                "valid": False,
                "message": f"Stock/price validation failed: {str(e)}",
            }

    async def _execute_external_checkout(self) -> Dict[str, Any]:
        """Checkout is disabled. Instead, exercise cart APIs.

        Runs exactly once per operation without loops:
        - view_cart
        - remove_from_cart (first item, if any)
        Does NOT re-add any product. Adding user items only happens in the
        dedicated population step when the cart is fully cleared.
        Always returns success=False to prevent local debit/processing.
        """
        try:
            logger.info("Checkout disabled: clearing external cart (single pass)")
            details: Dict[str, Any] = {}

            # 1) View cart once
            view = await self.external_api_service.view_cart()
            details["view_status"] = getattr(view, "status_code", None)
            items = []
            if getattr(view, "success", False) and isinstance(view.data, dict):
                items = view.data.get("data") or []

            # 2) Clear cart completely if it has items (no re-adding)
            cleared = True
            if items:
                cleared = await self._clear_external_cart()
            details["cleared"] = bool(cleared)

            logger.info("Cart diagnostics complete (cleared when non-empty)")
            return {
                "success": False,
                "message": "Checkout disabled; viewed and cleared cart (no re-add)",
                "details": details,
            }
        except Exception as e:
            logger.error(
                "Error running cart diagnostics in place of checkout", exc_info=True
            )
            return {
                "success": False,
                "message": f"Checkout disabled; diagnostics error: {str(e)}",
            }

    async def _process_successful_checkout(
        self,
        user_id: str,
        cart_snapshot: Dict[str, Any],
        external_result: Dict[str, Any],
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """Process successful external checkout in our local system"""
        try:
            async with database_transaction():
                items = cart_snapshot.get("items", [])
                total_amount = float(cart_snapshot.get("total_amount", 0.0))

                # Balance already deducted before external checkout
                # Just create transaction record for audit purposes
                api_version = getattr(self.external_api_service, "api_version", "v1")
                
                # Get current wallet for remaining balance
                try:
                    wallet = await self.user_service.get_wallet_by_user_id(user_id)
                    if not wallet:
                        logger.warning(f"Wallet not found for user {user_id} - balance will be None in result")
                except Exception as wallet_error:
                    logger.error(f"❌ Failed to retrieve wallet for user {user_id}: {wallet_error}")
                    wallet = None  # Set to None so the result data handles it gracefully

                tx_metadata = {
                    "external_order_id": external_result.get("order_id"),
                    "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                    "items_count": len(items),
                    "api_version": api_version,
                    "note": "Balance already deducted before external checkout"
                }

                # Create audit transaction record (no balance change)
                from models import Transaction, TransactionType
                audit_transaction = Transaction(
                    user_id=user_id,
                    type=TransactionType.PURCHASE,
                    amount=total_amount,
                    currency="USD",
                    reference=f"checkout_audit_{external_result.get('order_id')}",
                    metadata=tx_metadata,
                )
                
                # Insert audit transaction
                transactions_collection = get_collection("transactions")
                try:
                    await transactions_collection.insert_one(audit_transaction.to_mongo())
                    logger.info(f"✅ Audit transaction created for user {user_id}: ${total_amount}")
                except Exception as tx_error:
                    logger.error(f"❌ Failed to create audit transaction: {tx_error}")
                    raise Exception(f"Failed to create audit transaction: {str(tx_error)}")

                # Create purchase records (idempotent per card)
                purchases_collection = get_collection("purchases")

                # Get current API version and normalize it
                api_version = getattr(self.external_api_service, "api_version", "v1")
                logger.info(f"💾 Creating purchase records for {len(items)} items using API version {api_version}")

                # Determine product type based on cart contents
                # This is a simplified approach - in a real system you might want more sophisticated detection
                product_type = PurchaseProductType.CARD  # Default to card

                created_purchases = 0
                skipped_items = 0

                for item_data in items:
                    card_data = item_data.get("card_data", {})
                    # Try multiple ways to get the card ID:
                    # 1. From item_data.card_id (CartItem model field)
                    # 2. From card_data._id (API response field)
                    # 3. From card_data.id (alternative API field)
                    card_pk = (
                        item_data.get("card_id") or 
                        card_data.get("_id") or 
                        card_data.get("id")
                    )
                    
                    # Skip items without valid card IDs
                    if not card_pk or not str(card_pk).strip():
                        logger.warning(f"Skipping item without valid card ID. item_data keys: {list(item_data.keys())}, card_data keys: {list(card_data.keys())}")
                        skipped_items += 1
                        continue
                    
                    # Ensure card_pk is a string
                    card_pk = str(card_pk).strip()
                    
                    # Validate and extract quantity
                    try:
                        qty = int(item_data.get("quantity", 1))
                        if qty <= 0:
                            logger.warning(f"Skipping item with invalid quantity: card_id={card_pk}, qty={qty}")
                            skipped_items += 1
                            continue
                    except (ValueError, TypeError):
                        logger.warning(f"Skipping item with invalid quantity type: card_id={card_pk}, qty={item_data.get('quantity')}")
                        skipped_items += 1
                        continue
                    
                    # Validate and calculate price
                    try:
                        price_at_add = float(item_data.get("price_at_add", 0.0))
                        if price_at_add < 0:
                            logger.warning(f"Skipping item with negative price: card_id={card_pk}, price_at_add={price_at_add}")
                            skipped_items += 1
                            continue
                        price = price_at_add * qty
                    except (ValueError, TypeError):
                        logger.warning(f"Skipping item with invalid price: card_id={card_pk}, price_at_add={item_data.get('price_at_add')}")
                        skipped_items += 1
                        continue

                    # Skip items with zero price
                    if price <= 0:
                        logger.warning(f"Skipping item with zero price: card_id={card_pk}, price={price}")
                        skipped_items += 1
                        continue

                    # Determine if this is a dump based on SKU or data structure
                    sku_prefix = "card"
                    if "dump" in str(card_data.get("type", "")).lower() or "dump" in str(item_data.get("sku", "")).lower():
                        product_type = PurchaseProductType.DUMP
                        sku_prefix = "dump"

                    # Prepare order metadata with extracted cards
                    extracted_cards = external_result.get("extracted_cards", [])
                    
                    # For API v3, try to get actual price from API response (more accurate than cart cache)
                    actual_price = None
                    if api_version == "v3" and extracted_cards:
                        # Find the card in extracted_cards that matches current card_pk
                        for extracted_card in extracted_cards:
                            extracted_id = extracted_card.get("_id") or extracted_card.get("id") or ""
                            if str(card_pk) in str(extracted_id) or str(extracted_id) in str(card_pk):
                                # Found matching card - use its price
                                card_price = extracted_card.get("price") or extracted_card.get("current_price")
                                if card_price is not None:
                                    try:
                                        actual_price = float(card_price) * qty
                                        if actual_price > 0:
                                            logger.info(f"✅ Using actual API v3 price ${actual_price:.2f} instead of cart price ${price:.2f} for card {card_pk}")
                                            price = actual_price
                                            break
                                    except (ValueError, TypeError):
                                        logger.warning(f"Invalid price in extracted card: {card_price}")
                        
                        if actual_price is None:
                            logger.debug(f"Could not find price in API v3 response for card {card_pk}, using cart price ${price:.2f}")
                    
                    # Prepare complete order data for comprehensive storage (use updated price)
                    order_items_data = []
                    if api_version == "v3":
                        # For API v3, store structured order items for future retrieval
                        order_items_data = [{
                            "id": str(card_pk),
                            "type": product_type.value,
                            "price": price,  # This now uses the actual API price if found
                            "quantity": qty,
                            "card_data": card_data,
                            "item_data": item_data
                        }]
                    
                    # Extract raw_data from checkout response if available (especially for API v3)
                    raw_data = None
                    checkout_response = external_result.get("checkout_response")
                    if checkout_response and isinstance(checkout_response, dict):
                        raw_data = checkout_response.get("raw_data")
                    
                    order_metadata = {
                        "checkout_response": checkout_response,
                        "extracted_cards": extracted_cards,  # Store extracted cards for order viewing
                        "api_version": api_version,
                        "checkout_timestamp": datetime.now(timezone.utc).isoformat(),
                        "total_items": len(items),
                        "total_amount": total_amount
                    }

                    purchase = Purchase(
                        user_id=user_id,
                        sku=f"{sku_prefix}_{card_pk}",
                        price=price,
                        currency="USD",
                        status=PurchaseStatus.SUCCESS,

                        # Enhanced API version tracking
                        api_version=api_version,
                        product_type=product_type,
                        external_order_id=external_result.get("order_id"),
                        external_product_id=str(card_pk),

                        # Complete order data for retrieval
                        order_items=order_items_data,
                        order_metadata=order_metadata,
                        cart_snapshot=cart_snapshot,

                        # Legacy metadata for backward compatibility
                        metadata={
                            "card_id": card_pk,  # Keep for backward compatibility
                            "card_data": card_data,
                            "quantity": qty,
                            "external_order_id": external_result.get("order_id"),
                            "transaction_id": str(getattr(audit_transaction, "id", "")),
                            "api_version": api_version,  # Keep for backward compatibility
                            "product_type": product_type.value,
                            "extracted_cards": extracted_cards,  # Store extracted cards for compatibility
                        },
                        idempotency_key=f"purchase_{external_result.get('order_id')}_{card_pk}",
                    )

                    # Log purchase details for debugging
                    logger.info(f"💾 Creating purchase record: card_id={card_pk}, external_order_id={external_result.get('order_id')}, transaction_id={str(getattr(audit_transaction, 'id', ''))}, api_version={api_version}")

                    try:
                        # Convert purchase to mongo document
                        purchase_doc = purchase.to_mongo()
                        
                        # Add raw_data if available (for API v3 fallback support)
                        if raw_data:
                            # Clean raw_data for BSON serialization
                            clean_raw_data = self._clean_for_bson(raw_data)
                            purchase_doc["raw_data"] = clean_raw_data
                            logger.info(f"✅ Adding raw_data to purchase record for card {card_pk}")
                        
                        # Add extracted_cards if available
                        if extracted_cards:
                            # Clean extracted_cards for BSON serialization
                            clean_extracted_cards = self._clean_for_bson(extracted_cards)
                            purchase_doc["extracted_cards"] = clean_extracted_cards
                            logger.info(f"✅ Adding {len(extracted_cards)} extracted_cards to purchase record for card {card_pk}")
                        
                        # CRITICAL: Clean the entire purchase_doc to remove any circular references
                        # that might have been introduced by to_mongo() or the added fields
                        purchase_doc = self._clean_for_bson(purchase_doc)
                        
                        await purchases_collection.insert_one(purchase_doc)
                        logger.info(f"✅ Purchase record created successfully for card {card_pk}")
                        created_purchases += 1
                    except DuplicateKeyError as dke:
                        # Duplicate due to idempotency - this is expected and OK
                        logger.info(f"Purchase record already exists (idempotent): card {card_pk}")
                        created_purchases += 1  # Count as successful since it already exists
                    except Exception as pe:
                        # Real error - log but DON'T raise to allow other items to be processed
                        logger.error(f"❌ Failed to create purchase record for card {card_pk}: {pe}")
                        skipped_items += 1
                        # Continue processing other items instead of failing entire checkout

                # Summary logging
                logger.info(f"💾 Purchase creation summary: {created_purchases} created/existing, {skipped_items} skipped out of {len(items)} total items")
                
                # Check if we successfully processed any items
                if created_purchases == 0:
                    logger.error("❌ No purchase records were created - all items were skipped")
                    return (
                        False,
                        f"Failed to create purchase records: {skipped_items} items skipped due to missing card IDs or invalid prices",
                        None,
                    )
                
                # Log warning if some items were skipped but at least some succeeded
                if skipped_items > 0:
                    logger.warning(f"⚠️ Partial success: {created_purchases} items processed successfully, {skipped_items} items skipped")
                    # Send notification to user about partial success
                    try:
                        await self.bot.send_message(
                            chat_id=job.user_id,
                            text=f"⚠️ <b>Partial Checkout Success</b>\n\n"
                                 f"✅ Successfully processed: <b>{created_purchases}</b> items\n"
                                 f"❌ Failed/Skipped: <b>{skipped_items}</b> items\n\n"
                                 f"💡 The skipped items had invalid data or were already sold. "
                                 f"Your wallet was charged only for the successful items.",
                            parse_mode="HTML"
                        )
                    except Exception as notify_err:
                        logger.error(f"❌ Failed to notify user about partial success: {notify_err}")

                # Clear user's virtual cart
                try:
                    await self.cart_service.clear_cart(user_id)
                    logger.info(f"✅ Cart cleared for user {user_id}")
                except Exception as cart_error:
                    logger.error(f"❌ Failed to clear cart for user {user_id}: {cart_error}")
                    # Don't fail the entire checkout for cart clearing issues
                    # The purchase was successful, so we'll just log the error

                result_data = {
                    "transaction_id": str(getattr(audit_transaction, "id", "")),
                    "remaining_balance": getattr(wallet, "balance", None),
                }
                return (
                    True,
                    f"Successfully purchased {len(items)} items for ${total_amount}",
                    result_data,
                )

        except Exception as e:
            logger.error("Error processing successful checkout", exc_info=True)
            return False, f"Failed to process checkout: {str(e)}", None

    async def _update_job_status(
        self,
        job_id: str,
        status: CheckoutJobStatus,
        metadata: Dict[str, Any] | None = None,
        error: str | None = None,
    ) -> None:
        """Update job status in database (merge metadata, keep datetimes as datetime)."""
        try:
            update_data: Dict[str, Any] = {
                "status": status.value,
                "updated_at": datetime.now(timezone.utc),
            }

            if status in [
                CheckoutJobStatus.COMPLETED,
                CheckoutJobStatus.FAILED,
                CheckoutJobStatus.CANCELLED,
            ]:
                update_data["completed_at"] = datetime.now(timezone.utc)

            if metadata:
                # Merge, don't overwrite completely if metadata already exists
                update_data["metadata"] = metadata

            if error:
                update_data["last_error"] = error

            await self.jobs_collection.update_one(
                {"job_id": job_id}, {"$set": update_data}
            )
        except Exception as e:
            logger.error("Error updating job status", exc_info=True)

    async def _notify_user(
        self, job: CheckoutJob, message: str, result_data: Dict[str, Any] | None = None
    ) -> None:
        """Send notification to user via Telegram"""
        try:
            telegram_user_id = job.metadata.get("telegram_user_id")
            if not telegram_user_id:
                return

            # Import here to avoid circular imports
            from aiogram import Bot

            settings = get_settings()
            bot = Bot(token=settings.BOT_TOKEN)

            # Format message
            if result_data:
                formatted_message = self._format_completion_message(
                    message, result_data, job
                )
            else:
                formatted_message = (
                    f"🛒 <b>Order Update</b>\n\n"
                    f"⚡ <b>Status:</b> <code>{message}</code>\n\n"
                    f"🆔 <b>Job ID:</b> <code>{job.job_id[:8]}</code>\n"
                    f"🔒 <b>Security:</b> <code>Verified & Encrypted</code>"
                )

            # Create enhanced keyboard for completion notifications
            reply_markup = None
            try:
                if result_data and isinstance(result_data, dict):
                    # Success notification - use enhanced keyboard
                    from utils.post_checkout_ui import PostCheckoutUI

                    external_order_id = result_data.get("external_order_id", job.job_id)
                    transaction_id = result_data.get("transaction_id")
                    has_cards = bool(result_data.get("purchased_card_ids"))

                    logger.info(f"🎉 Creating post-checkout keyboard: external_order_id={external_order_id}, transaction_id={transaction_id}, has_cards={has_cards}")

                    # Get first card ID for direct unmask button
                    first_card_id = None
                    purchased_card_ids = result_data.get("purchased_card_ids", [])
                    if purchased_card_ids:
                        first_card_id = purchased_card_ids[0]
                    
                    # Get API version from job metadata
                    api_version = job.metadata.get("api_version", "v1") if hasattr(job, 'metadata') and job.metadata else "v1"
                    
                    kb = PostCheckoutUI.create_order_completion_keyboard(
                        order_id=external_order_id or job.job_id,
                        has_cards=has_cards,
                        show_receipt=True,
                        transaction_id=transaction_id,
                        card_id=first_card_id,
                        api_version=api_version,
                    )
                    reply_markup = kb.build()
                else:
                    # Simple fallback keyboard for errors/updates
                    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

                    buttons = [
                        [
                            InlineKeyboardButton(
                                text="🛒 View Cart", callback_data="local:cart:view"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="menu:main"
                            )
                        ],
                    ]
                    reply_markup = InlineKeyboardMarkup(inline_keyboard=buttons)
            except Exception as e:
                logger.debug(f"Could not create enhanced keyboard: {e}")
                reply_markup = None

            await bot.send_message(
                chat_id=telegram_user_id,
                text=formatted_message,
                parse_mode="HTML",
                reply_markup=reply_markup,
            )
            await bot.session.close()
        except Exception as e:
            logger.error("Error sending notification to user", exc_info=True)

    def _format_completion_message(
        self, message: str, result_data: Dict[str, Any], job: CheckoutJob
    ) -> str:
        """Format completion message with enhanced UI using new post-checkout components"""
        try:
            from utils.post_checkout_ui import PostCheckoutUI, OrderSummary
            from datetime import datetime, timezone

            # Extract order information
            items = job.cart_snapshot.get("items", [])
            total_amount = result_data.get("total_amount", 0.0)
            remaining_balance = result_data.get("remaining_balance", 0.0)
            external_order_id = result_data.get("external_order_id", job.job_id)
            transaction_id = str(result_data.get("transaction_id", "N/A"))

            # Create order summary object
            order_summary = OrderSummary(
                order_id=external_order_id or job.job_id,
                transaction_id=transaction_id,
                total_amount=total_amount,
                item_count=len(items),
                remaining_balance=remaining_balance,
                created_at=datetime.now(timezone.utc),
                status="completed",
            )

            # Extract purchased cards for preview - use new extracted_cards data
            purchased_cards = []
            try:
                # First priority: Use extracted_cards from checkout response
                extracted_cards = result_data.get("purchased_cards") or []
                if extracted_cards and isinstance(extracted_cards, list):
                    for card in extracted_cards[:6]:  # Limit to 6 cards for preview
                        purchased_cards.append(
                            {
                                "bank": card.get("bank", "Unknown"),
                                "brand": card.get("brand", ""),
                                "level": card.get("level", ""),
                                "price": float(card.get("price", 0.0)),
                                "product_id": card.get("_id", "N/A"),
                                "status": card.get("status", "N/A"),
                                "country": card.get("country", ""),
                                "partial_card_number": card.get("partial_card_number", ""),
                                "expiry": card.get("expiry", ""),
                            }
                        )
                    logger.info(f"✅ Using {len(purchased_cards)} extracted cards for preview")
                else:
                    # Fallback: Use purchased_orders (legacy)
                    orders = result_data.get("purchased_orders") or []
                    if isinstance(orders, list):
                        for od in orders[:5]:
                            purchased_cards.append(
                                {
                                    "bank": od.get("bank", "Unknown"),
                                    "brand": od.get("brand", ""),
                                    "level": od.get("level", ""),
                                    "price": float(od.get("price", 0.0)),
                                    "product_id": od.get("product_id", "N/A"),
                                    "status": od.get("status", "N/A"),
                                }
                            )
                    logger.debug(f"Using {len(purchased_cards)} cards from purchased_orders (legacy)")
            except Exception as e:
                logger.debug(f"Could not extract purchased cards: {e}")

            # Generate enhanced completion message
            return PostCheckoutUI.create_order_completion_message(
                order_summary=order_summary, purchased_cards=purchased_cards
            )

        except Exception as e:
            logger.error(f"Error creating enhanced completion message: {e}")
            # Fallback to simple message
            return f"✅ {message}\n\n🆔 Job: {job.job_id[:8]}\n⏰ Completed: {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}"

    async def _find_existing_job(
        self, user_id: str, cart_snapshot: Dict[str, Any]
    ) -> Optional[CheckoutJob]:
        """Find existing queued/processing job with the same stable cart fingerprint."""
        try:
            temp_job = CheckoutJob(user_id=user_id, cart_snapshot=cart_snapshot)
            job_doc = await self.jobs_collection.find_one(
                {
                    "user_id": user_id,
                    "idempotency_key": temp_job.idempotency_key,
                    "status": {
                        "$in": [
                            CheckoutJobStatus.QUEUED.value,
                            CheckoutJobStatus.PROCESSING.value,
                        ]
                    },
                }
            )
            return CheckoutJob.from_dict(job_doc) if job_doc else None
        except Exception as e:
            logger.error(f"Error finding existing job: {e}")
            return None

    async def _get_queue_position(self, job_id: str) -> int:
        """Get position of job in queue (1-based)."""
        try:
            job_doc = await self.jobs_collection.find_one({"job_id": job_id})
            if not job_doc:
                return 0
            created_at = job_doc["created_at"]
            # Count jobs created at or before this job which are still queued
            position = await self.jobs_collection.count_documents(
                {
                    "status": CheckoutJobStatus.QUEUED.value,
                    "created_at": {"$lte": created_at},
                }
            )
            return max(1, position)
        except Exception as e:
            logger.error(f"Error getting queue position: {e}")
            return 1

    async def _estimate_processing_time(self) -> int:
        """Rudimentary estimate: 30s per queued job."""
        try:
            queue_length = await self.jobs_collection.count_documents(
                {"status": CheckoutJobStatus.QUEUED.value}
            )
            return queue_length * 30
        except Exception as e:
            logger.error(f"Error estimating processing time: {e}")
            return 60

    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get current queue statistics (counts and avg processing time of recent completions)."""
        try:
            stats: Dict[str, Any] = {}
            # Counts by status
            for status in CheckoutJobStatus:
                count = await self.jobs_collection.count_documents(
                    {"status": status.value}
                )
                stats[f"{status.value}_count"] = count

            # Average processing time over last 10 completed jobs
            completed_jobs = await (
                self.jobs_collection.find(
                    {
                        "status": CheckoutJobStatus.COMPLETED.value,
                        "completed_at": {"$exists": True},
                        "created_at": {"$exists": True},
                    }
                )
                .sort("completed_at", -1)
                .limit(10)
                .to_list(None)
            )

            processing_times: List[float] = []
            for job in completed_jobs:
                created_at = job.get("created_at")
                completed_at = job.get("completed_at")
                if isinstance(created_at, datetime) and isinstance(
                    completed_at, datetime
                ):
                    delta = (completed_at - created_at).total_seconds()
                    if delta >= 0:
                        processing_times.append(delta)

            if processing_times:
                stats["avg_processing_time"] = sum(processing_times) / len(
                    processing_times
                )

            return stats
        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {}
