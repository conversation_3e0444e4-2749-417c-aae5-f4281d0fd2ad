#!/usr/bin/env python3
"""
Cleanup Script for Legacy API Management Code

This script safely removes legacy API management code after successful migration
to the new shared API management system.
"""

import asyncio
import json
import sys
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from utils.central_logger import get_logger

logger = get_logger()


class LegacyCodeCleanup:
    """
    Cleanup manager for legacy API management code
    
    This class safely removes legacy code while preserving important
    functionality and creating backups.
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.backup_dir = self.project_root / "backups" / f"legacy_cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.cleanup_log = []
        
        # Files and directories to remove
        self.legacy_files = [
            "handlers/admin_api_config_handlers.py",  # Legacy admin handlers
            "api_v1/services/api_config.py",  # Legacy API config service
            "api_v1/services/external_api_service.py",  # Legacy external API service
            "config/api_config.json",  # Legacy config files
            "legacy_api_configs.json"
        ]
        
        # Files to update (remove legacy imports/references)
        self.files_to_update = [
            "main.py",
            "handlers/__init__.py",
            "services/__init__.py",
            "api_v1/__init__.py"
        ]
        
        # Directories to remove if empty
        self.cleanup_dirs = [
            "api_v1/services",
            "config"
        ]
    
    async def cleanup_legacy_code(
        self,
        dry_run: bool = False,
        create_backup: bool = True,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Clean up legacy API management code
        
        Args:
            dry_run: If True, only analyze what would be cleaned up
            create_backup: Whether to create backup before cleanup
            force: Force cleanup even if validation fails
            
        Returns:
            Cleanup results summary
        """
        logger.info("Starting legacy code cleanup...")
        
        try:
            # Validate that migration was successful
            if not force:
                validation_result = await self._validate_migration_success()
                if not validation_result["success"]:
                    return {
                        "status": "failed",
                        "error": f"Migration validation failed: {validation_result['error']}",
                        "message": "Use --force to override validation"
                    }
            
            # Create backup if requested
            if create_backup and not dry_run:
                await self._create_backup()
                logger.info(f"Created backup at: {self.backup_dir}")
            
            # Analyze files to be cleaned up
            cleanup_plan = await self._analyze_cleanup()
            
            if dry_run:
                return {
                    "status": "dry_run_completed",
                    "cleanup_plan": cleanup_plan,
                    "total_files": len(cleanup_plan["files_to_remove"]),
                    "total_updates": len(cleanup_plan["files_to_update"]),
                    "message": "This is a dry run - no changes were made"
                }
            
            # Perform the cleanup
            cleanup_results = await self._perform_cleanup(cleanup_plan)
            
            # Generate summary
            summary = {
                "status": "completed",
                "backup_location": str(self.backup_dir) if create_backup else None,
                "cleanup_results": cleanup_results,
                "cleanup_log": self.cleanup_log,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info("Legacy code cleanup completed successfully")
            return summary
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    async def _validate_migration_success(self) -> Dict[str, Any]:
        """Validate that migration was successful before cleanup"""
        try:
            # Check if new admin system is working
            from admin.services.shared_api_admin_service import get_shared_api_admin_service
            from admin.services.shared_api_integration import get_shared_api_integration_service
            
            admin_service = get_shared_api_admin_service()
            integration_service = get_shared_api_integration_service()
            
            # Check if admin service can list configurations
            api_configs = await admin_service.list_api_configurations()
            
            # Check if integration service is initialized
            registry_status = await integration_service.get_registry_status()
            
            if not registry_status.get("initialized"):
                return {
                    "success": False,
                    "error": "Shared API integration not initialized"
                }
            
            if len(api_configs) == 0:
                return {
                    "success": False,
                    "error": "No API configurations found in new system"
                }
            
            # Check if shared API system is working
            from shared_api.config.registry import api_registry
            registered_apis = api_registry.list_apis()
            
            if len(registered_apis) == 0:
                return {
                    "success": False,
                    "error": "No APIs registered in shared API registry"
                }
            
            return {
                "success": True,
                "api_configs_count": len(api_configs),
                "registered_apis_count": len(registered_apis)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Validation failed: {str(e)}"
            }
    
    async def _analyze_cleanup(self) -> Dict[str, Any]:
        """Analyze what files and code need to be cleaned up"""
        files_to_remove = []
        files_to_update = []
        dirs_to_remove = []
        
        # Check which legacy files exist
        for file_path in self.legacy_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                files_to_remove.append({
                    "path": str(full_path),
                    "relative_path": file_path,
                    "size": full_path.stat().st_size,
                    "type": "file"
                })
        
        # Check which files need updates
        for file_path in self.files_to_update:
            full_path = self.project_root / file_path
            if full_path.exists():
                # Check if file contains legacy imports
                try:
                    with open(full_path, 'r') as f:
                        content = f.read()
                    
                    legacy_imports = [
                        "from handlers.admin_api_config_handlers",
                        "from api_v1.services.api_config",
                        "from api_v1.services.external_api_service",
                        "import handlers.admin_api_config_handlers",
                        "import api_v1.services.api_config",
                        "import api_v1.services.external_api_service"
                    ]
                    
                    has_legacy_imports = any(imp in content for imp in legacy_imports)
                    
                    if has_legacy_imports:
                        files_to_update.append({
                            "path": str(full_path),
                            "relative_path": file_path,
                            "legacy_imports_found": [imp for imp in legacy_imports if imp in content]
                        })
                        
                except Exception as e:
                    logger.warning(f"Could not analyze file {file_path}: {e}")
        
        # Check which directories can be removed
        for dir_path in self.cleanup_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                # Check if directory is empty or only contains files we're removing
                remaining_files = []
                for item in full_path.rglob("*"):
                    if item.is_file():
                        relative_item = str(item.relative_to(self.project_root))
                        if relative_item not in self.legacy_files:
                            remaining_files.append(relative_item)
                
                if not remaining_files:
                    dirs_to_remove.append({
                        "path": str(full_path),
                        "relative_path": dir_path,
                        "type": "directory"
                    })
        
        return {
            "files_to_remove": files_to_remove,
            "files_to_update": files_to_update,
            "dirs_to_remove": dirs_to_remove
        }
    
    async def _create_backup(self) -> None:
        """Create backup of files before cleanup"""
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup files that will be removed
            for file_path in self.legacy_files:
                source_path = self.project_root / file_path
                if source_path.exists():
                    backup_path = self.backup_dir / file_path
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source_path, backup_path)
                    
                    self.cleanup_log.append({
                        "timestamp": datetime.utcnow().isoformat(),
                        "action": "backed_up",
                        "file": str(source_path)
                    })
            
            # Backup files that will be updated
            for file_path in self.files_to_update:
                source_path = self.project_root / file_path
                if source_path.exists():
                    backup_path = self.backup_dir / f"{file_path}.backup"
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source_path, backup_path)
            
            # Create backup manifest
            manifest = {
                "backup_timestamp": datetime.utcnow().isoformat(),
                "backup_version": "1.0",
                "backed_up_files": self.legacy_files + self.files_to_update,
                "cleanup_log": self.cleanup_log
            }
            
            with open(self.backup_dir / "backup_manifest.json", 'w') as f:
                json.dump(manifest, f, indent=2, default=str)
            
            logger.info(f"Created backup with {len(self.legacy_files)} files")
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            raise
    
    async def _perform_cleanup(self, cleanup_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Perform the actual cleanup operations"""
        results = {
            "files_removed": [],
            "files_updated": [],
            "dirs_removed": [],
            "errors": []
        }
        
        try:
            # Remove legacy files
            for file_info in cleanup_plan["files_to_remove"]:
                try:
                    file_path = Path(file_info["path"])
                    if file_path.exists():
                        file_path.unlink()
                        results["files_removed"].append(file_info["relative_path"])
                        
                        self.cleanup_log.append({
                            "timestamp": datetime.utcnow().isoformat(),
                            "action": "removed_file",
                            "file": file_info["relative_path"]
                        })
                        
                        logger.info(f"Removed: {file_info['relative_path']}")
                        
                except Exception as e:
                    error_msg = f"Failed to remove {file_info['relative_path']}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)
            
            # Update files with legacy imports
            for file_info in cleanup_plan["files_to_update"]:
                try:
                    file_path = Path(file_info["path"])
                    if file_path.exists():
                        # Read current content
                        with open(file_path, 'r') as f:
                            content = f.read()
                        
                        # Remove legacy imports
                        updated_content = self._remove_legacy_imports(content)
                        
                        # Write updated content
                        with open(file_path, 'w') as f:
                            f.write(updated_content)
                        
                        results["files_updated"].append(file_info["relative_path"])
                        
                        self.cleanup_log.append({
                            "timestamp": datetime.utcnow().isoformat(),
                            "action": "updated_file",
                            "file": file_info["relative_path"],
                            "changes": "removed legacy imports"
                        })
                        
                        logger.info(f"Updated: {file_info['relative_path']}")
                        
                except Exception as e:
                    error_msg = f"Failed to update {file_info['relative_path']}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)
            
            # Remove empty directories
            for dir_info in cleanup_plan["dirs_to_remove"]:
                try:
                    dir_path = Path(dir_info["path"])
                    if dir_path.exists() and dir_path.is_dir():
                        # Check if directory is empty
                        if not any(dir_path.iterdir()):
                            dir_path.rmdir()
                            results["dirs_removed"].append(dir_info["relative_path"])
                            
                            self.cleanup_log.append({
                                "timestamp": datetime.utcnow().isoformat(),
                                "action": "removed_directory",
                                "directory": dir_info["relative_path"]
                            })
                            
                            logger.info(f"Removed empty directory: {dir_info['relative_path']}")
                        
                except Exception as e:
                    error_msg = f"Failed to remove directory {dir_info['relative_path']}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)
            
            return results
            
        except Exception as e:
            logger.error(f"Cleanup operation failed: {e}")
            results["errors"].append(f"Cleanup failed: {str(e)}")
            return results
    
    def _remove_legacy_imports(self, content: str) -> str:
        """Remove legacy import statements from file content"""
        lines = content.split('\n')
        updated_lines = []
        
        legacy_patterns = [
            "from handlers.admin_api_config_handlers",
            "from api_v1.services.api_config",
            "from api_v1.services.external_api_service",
            "import handlers.admin_api_config_handlers",
            "import api_v1.services.api_config",
            "import api_v1.services.external_api_service"
        ]
        
        for line in lines:
            # Skip lines that contain legacy imports
            if not any(pattern in line for pattern in legacy_patterns):
                updated_lines.append(line)
            else:
                # Add comment indicating removal
                updated_lines.append(f"# REMOVED: {line.strip()}")
        
        return '\n'.join(updated_lines)


async def main():
    """Main cleanup function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean up legacy API management code")
    parser.add_argument("--dry-run", action="store_true", help="Perform dry run without making changes")
    parser.add_argument("--no-backup", action="store_true", help="Skip creating backup")
    parser.add_argument("--force", action="store_true", help="Force cleanup even if validation fails")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Configure centralized logging
    from utils.central_logger import get_logger, setup_logging
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logging(level=log_level)
    logger = get_logger()
    )
    
    # Initialize cleanup manager
    cleanup_manager = LegacyCodeCleanup()
    
    try:
        # Run cleanup
        results = await cleanup_manager.cleanup_legacy_code(
            dry_run=args.dry_run,
            create_backup=not args.no_backup,
            force=args.force
        )
        
        # Print results
        print("\n" + "="*60)
        print("CLEANUP RESULTS")
        print("="*60)
        print(f"Status: {results['status']}")
        
        if results['status'] == 'failed':
            print(f"Error: {results.get('error', 'Unknown error')}")
            return 1
        
        if args.dry_run:
            cleanup_plan = results.get('cleanup_plan', {})
            print(f"Files to remove: {len(cleanup_plan.get('files_to_remove', []))}")
            print(f"Files to update: {len(cleanup_plan.get('files_to_update', []))}")
            print(f"Directories to remove: {len(cleanup_plan.get('dirs_to_remove', []))}")
            print("\n⚠️  This was a DRY RUN - no changes were made")
        else:
            cleanup_results = results.get('cleanup_results', {})
            print(f"Files removed: {len(cleanup_results.get('files_removed', []))}")
            print(f"Files updated: {len(cleanup_results.get('files_updated', []))}")
            print(f"Directories removed: {len(cleanup_results.get('dirs_removed', []))}")
            
            if cleanup_results.get('errors'):
                print(f"Errors: {len(cleanup_results['errors'])}")
                for error in cleanup_results['errors']:
                    print(f"  ❌ {error}")
            
            if results.get('backup_location'):
                print(f"\nBackup created at: {results['backup_location']}")
        
        # Save detailed results
        results_file = f"cleanup_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nDetailed results saved to: {results_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        print(f"\n❌ Cleanup failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
