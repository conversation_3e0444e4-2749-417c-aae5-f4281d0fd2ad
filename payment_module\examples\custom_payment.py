"""
Example: Custom Payment Processing

This example shows how to use the payment module functions directly for custom payment processing.
"""

import asyncio
import logging
from payment_module.core.payment_link import create_payment_link
from payment_module.core.oxa_verify import check_oxapay_payment
from payment_module.core.currency_converter import convert_currency

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
OXA_PAY_API_KEY = "YOUR_OXA_PAY_API_KEY_HERE"


async def create_custom_payment():
    """Example: Create a custom payment."""
    try:
        logger.info("Creating custom payment...")
        
        # Create payment link
        payment_data = await create_payment_link(
            amount=50.0,
            order_id="CUSTOM_ORDER_123",
            user_id="12345",
            description="Custom payment example",
            currency="USDT"
        )
        
        if payment_data["status"] == "success":
            logger.info(f"Payment created successfully!")
            logger.info(f"Track ID: {payment_data['trackId']}")
            logger.info(f"Payment URL: {payment_data['payLink']}")
            
            return payment_data
        else:
            logger.error(f"Payment creation failed: {payment_data['message']}")
            return None
            
    except Exception as e:
        logger.error(f"Error creating payment: {e}")
        return None


async def verify_payment_example(track_id: str):
    """Example: Verify a payment."""
    try:
        logger.info(f"Verifying payment: {track_id}")
        
        # Check payment status
        verification_result = await check_oxapay_payment(track_id, OXA_PAY_API_KEY)
        
        if verification_result.get("status") == "success":
            payment_data = verification_result.get("data", {})
            status = payment_data.get("status", "").lower()
            
            logger.info(f"Payment status: {status}")
            
            if status in ["completed", "confirmed", "success", "paid"]:
                amount = payment_data.get("amount", 0)
                currency = payment_data.get("currency", "USDT")
                
                logger.info(f"Payment completed! Amount: {amount} {currency}")
                return True
            else:
                logger.info(f"Payment still processing: {status}")
                return False
        else:
            logger.error(f"Verification failed: {verification_result.get('message')}")
            return False
            
    except Exception as e:
        logger.error(f"Error verifying payment: {e}")
        return False


async def currency_conversion_example():
    """Example: Currency conversion."""
    try:
        logger.info("Testing currency conversion...")
        
        # Convert BTC to USDT
        converted_amount, conversion_info = await convert_currency(
            amount=0.001,
            from_currency="BTC",
            to_currency="USDT"
        )
        
        if conversion_info["success"]:
            logger.info(f"Conversion successful!")
            logger.info(f"0.001 BTC = {converted_amount} USDT")
            logger.info(f"Exchange rate: {conversion_info['exchange_rate']}")
        else:
            logger.error(f"Conversion failed: {conversion_info['error']}")
            
    except Exception as e:
        logger.error(f"Error in currency conversion: {e}")


async def main():
    """Main function to run examples."""
    try:
        # Test currency conversion
        await currency_conversion_example()
        
        # Create a payment
        payment_data = await create_custom_payment()
        
        if payment_data:
            track_id = payment_data["trackId"]
            
            # Wait a bit (in real scenario, user would complete payment)
            logger.info("Waiting for payment completion...")
            await asyncio.sleep(5)
            
            # Verify payment
            await verify_payment_example(track_id)
        
    except Exception as e:
        logger.error(f"Error in main: {e}")


if __name__ == "__main__":
    asyncio.run(main())

