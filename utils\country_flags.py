"""
Centralized Country Flag Utility (Legacy)
DEPRECATED: Use utils.dynamic_country_flags for filter.json-based flags

This module provides backward compatibility for existing code.
New implementations should use the dynamic flag system.
"""

from typing import Optional, Dict


class CountryFlagManager:
    """Legacy flag manager - use dynamic_country_flags instead"""
    
    # Comprehensive country flag mapping - All filter.json countries supported
    COUNTRY_FLAGS: Dict[str, str] = {
        # A
        "AF": "🇦🇫", "AFGHANISTAN": "🇦🇫",
        "AL": "🇦🇱", "ALBANIA": "🇦🇱", 
        "DZ": "🇩🇿", "ALGERIA": "🇩🇿",
        "AS": "🇦🇸", "AMERICAN SAMOA": "🇦🇸",
        "AD": "🇦🇩", "ANDORRA": "🇦🇩",
        "AO": "🇦🇴", "ANGOLA": "🇦🇴",
        "AI": "🇦🇮", "ANGUILLA": "🇦🇮",
        "AG": "🇦🇬", "ANTIGUA AND BARBUDA": "🇦🇬",
        "AR": "🇦🇷", "ARGENTINA": "🇦🇷",
        "AM": "🇦🇲", "ARMENIA": "🇦🇲",
        "AW": "🇦🇼", "ARUBA": "🇦🇼",
        "AU": "🇦🇺", "AUSTRALIA": "🇦🇺",
        "AT": "🇦🇹", "AUSTRIA": "🇦🇹",
        "AZ": "🇦🇿", "AZERBAIJAN": "🇦🇿",
        
        # B
        "BS": "🇧🇸", "BAHAMAS": "🇧🇸",
        "BH": "🇧🇭", "BAHRAIN": "🇧🇭",
        "BD": "🇧🇩", "BANGLADESH": "🇧🇩",
        "BB": "🇧🇧", "BARBADOS": "🇧🇧",
        "BY": "🇧🇾", "BELARUS": "🇧🇾",
        "BE": "🇧🇪", "BELGIUM": "🇧🇪",
        "BZ": "🇧🇿", "BELIZE": "🇧🇿",
        "BJ": "🇧🇯", "BENIN": "🇧🇯",
        "BM": "🇧🇲", "BERMUDA": "🇧🇲",
        "BT": "🇧🇹", "BHUTAN": "🇧🇹",
        "BO": "🇧🇴", "BOLIVIA": "🇧🇴", "BOLIVIA, PLURINATIONAL STATE OF": "🇧🇴",
        "BA": "🇧🇦", "BOSNIA AND HERZEGOVINA": "🇧🇦",
        "BW": "🇧🇼", "BOTSWANA": "🇧🇼",
        "BR": "🇧🇷", "BRAZIL": "🇧🇷",
        "BN": "🇧🇳", "BRUNEI": "🇧🇳", "BRUNEI DARUSSALAM": "🇧🇳",
        "BG": "🇧🇬", "BULGARIA": "🇧🇬",
        "BF": "🇧🇫", "BURKINA FASO": "🇧🇫",
        "BI": "🇧🇮", "BURUNDI": "🇧🇮",
        
        # C
        "CV": "🇨🇻", "CABO VERDE": "🇨🇻", "CAPE VERDE": "🇨🇻",
        "KH": "🇰🇭", "CAMBODIA": "🇰🇭",
        "CM": "🇨🇲", "CAMEROON": "🇨🇲",
        "CA": "🇨🇦", "CANADA": "🇨🇦",
        "KY": "🇰🇾", "CAYMAN ISLANDS": "🇰🇾",
        "CF": "🇨🇫", "CENTRAL AFRICAN REPUBLIC": "🇨🇫",
        "TD": "🇹🇩", "CHAD": "🇹🇩",
        "CL": "🇨🇱", "CHILE": "🇨🇱",
        "CN": "🇨🇳", "CHINA": "🇨🇳",
        "CO": "🇨🇴", "COLOMBIA": "🇨🇴",
        "KM": "🇰🇲", "COMOROS": "🇰🇲",
        "CG": "🇨🇬", "CONGO": "🇨🇬",
        "CD": "🇨🇩", "CONGO, THE DEMOCRATIC REPUBLIC OF THE": "🇨🇩",
        "CR": "🇨🇷", "COSTA RICA": "🇨🇷",
        "CI": "🇨🇮", "COTE D'IVOIRE": "🇨🇮", "IVORY COAST": "🇨🇮",
        "HR": "🇭🇷", "CROATIA": "🇭🇷",
        "CU": "🇨🇺", "CUBA": "🇨🇺",
        "CW": "🇨🇼", "CURACAO": "🇨🇼",
        "CY": "🇨🇾", "CYPRUS": "🇨🇾",
        "CZ": "🇨🇿", "CZECH REPUBLIC": "🇨🇿", "CZECHIA": "🇨🇿",
        
        # D
        "DK": "🇩🇰", "DENMARK": "🇩🇰",
        "DJ": "🇩🇯", "DJIBOUTI": "🇩🇯",
        "DM": "🇩🇲", "DOMINICA": "🇩🇲",
        "DO": "🇩🇴", "DOMINICAN REPUBLIC": "🇩🇴",
        
        # E
        "EC": "🇪🇨", "ECUADOR": "🇪🇨",
        "EG": "🇪🇬", "EGYPT": "🇪🇬",
        "SV": "🇸🇻", "EL SALVADOR": "🇸🇻",
        "GQ": "🇬🇶", "EQUATORIAL GUINEA": "🇬🇶",
        "ER": "🇪🇷", "ERITREA": "🇪🇷",
        "EE": "🇪🇪", "ESTONIA": "🇪🇪",
        "SZ": "🇸🇿", "SWAZILAND": "🇸🇿", "ESWATINI": "🇸🇿",
        "ET": "🇪🇹", "ETHIOPIA": "🇪🇹",
        
        # F
        "FK": "🇫🇰", "FALKLAND ISLANDS": "🇫🇰",
        "FO": "🇫🇴", "FAROE ISLANDS": "🇫🇴",
        "FJ": "🇫🇯", "FIJI": "🇫🇯",
        "FI": "🇫🇮", "FINLAND": "🇫🇮",
        "FR": "🇫🇷", "FRANCE": "🇫🇷",
        "GF": "🇬🇫", "FRENCH GUIANA": "🇬🇫",
        "PF": "🇵🇫", "FRENCH POLYNESIA": "🇵🇫",
        
        # G
        "GA": "🇬🇦", "GABON": "🇬🇦",
        "GM": "🇬🇲", "GAMBIA": "🇬🇲",
        "GE": "🇬🇪", "GEORGIA": "🇬🇪",
        "DE": "🇩🇪", "GERMANY": "🇩🇪",
        "GH": "🇬🇭", "GHANA": "🇬🇭",
        "GI": "🇬🇮", "GIBRALTAR": "🇬🇮",
        "GR": "🇬🇷", "GREECE": "🇬🇷",
        "GL": "🇬🇱", "GREENLAND": "🇬🇱",
        "GD": "🇬🇩", "GRENADA": "🇬🇩",
        "GP": "🇬🇵", "GUADELOUPE": "🇬🇵",
        "GU": "🇬🇺", "GUAM": "🇬🇺",
        "GT": "🇬🇹", "GUATEMALA": "🇬🇹",
        "GG": "🇬🇬", "GUERNSEY": "🇬🇬", "GUERSNEY": "🇬🇬",
        "GN": "🇬🇳", "GUINEA": "🇬🇳",
        "GW": "🇬🇼", "GUINEA-BISSAU": "🇬🇼",
        "GY": "🇬🇾", "GUYANA": "🇬🇾",
        
        # H
        "HT": "🇭🇹", "HAITI": "🇭🇹",
        "HN": "🇭🇳", "HONDURAS": "🇭🇳",
        "HK": "🇭🇰", "HONG KONG": "🇭🇰",
        "HU": "🇭🇺", "HUNGARY": "🇭🇺",
        
        # I
        "IS": "🇮🇸", "ICELAND": "🇮🇸",
        "IN": "🇮🇳", "INDIA": "🇮🇳",
        "ID": "🇮🇩", "INDONESIA": "🇮🇩",
        "IR": "🇮🇷", "IRAN": "🇮🇷", "IRAN, ISLAMIC REPUBLIC OF": "🇮🇷",
        "IQ": "🇮🇶", "IRAQ": "🇮🇶",
        "IE": "🇮🇪", "IRELAND": "🇮🇪",
        "IM": "🇮🇲", "ISLE OF MAN": "🇮🇲",
        "IL": "🇮🇱", "ISRAEL": "🇮🇱",
        "IT": "🇮🇹", "ITALY": "🇮🇹",
        
        # J
        "JM": "🇯🇲", "JAMAICA": "🇯🇲",
        "JP": "🇯🇵", "JAPAN": "🇯🇵",
        "JE": "🇯🇪", "JERSEY": "🇯🇪",
        "JO": "🇯🇴", "JORDAN": "🇯🇴",
        
        # K
        "KZ": "🇰🇿", "KAZAKHSTAN": "🇰🇿",
        "KE": "🇰🇪", "KENYA": "🇰🇪",
        "KI": "🇰🇮", "KIRIBATI": "🇰🇮",
        "KP": "🇰🇵", "NORTH KOREA": "🇰🇵", "KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF": "🇰🇵",
        "KR": "🇰🇷", "SOUTH KOREA": "🇰🇷", "KOREA, REPUBLIC OF": "🇰🇷",
        "XK": "🏴", "KOSOVO": "🏴", "KOSOVO, REPUBLIC OF": "🏴",
        "KW": "🇰🇼", "KUWAIT": "🇰🇼",
        "KG": "🇰🇬", "KYRGYZSTAN": "🇰🇬",
        
        # L
        "LA": "🇱🇦", "LAOS": "🇱🇦", "LAO PEOPLE'S DEMOCRATIC REPUBLIC": "🇱🇦",
        "LV": "🇱🇻", "LATVIA": "🇱🇻",
        "LB": "🇱🇧", "LEBANON": "🇱🇧",
        "LS": "🇱🇸", "LESOTHO": "🇱🇸",
        "LR": "🇱🇷", "LIBERIA": "🇱🇷",
        "LY": "🇱🇾", "LIBYA": "🇱🇾", "LIBYAN ARAB JAMAHIRIYA": "🇱🇾",
        "LI": "🇱🇮", "LIECHTENSTEIN": "🇱🇮",
        "LT": "🇱🇹", "LITHUANIA": "🇱🇹",
        "LU": "🇱🇺", "LUXEMBOURG": "🇱🇺",
        
        # M
        "MO": "🇲🇴", "MACAO": "🇲🇴", "MACAU": "🇲🇴",
        "MK": "🇲🇰", "MACEDONIA": "🇲🇰", "MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF": "🇲🇰",
        "MG": "🇲🇬", "MADAGASCAR": "🇲🇬",
        "MW": "🇲🇼", "MALAWI": "🇲🇼",
        "MY": "🇲🇾", "MALAYSIA": "🇲🇾",
        "MV": "🇲🇻", "MALDIVES": "🇲🇻",
        "ML": "🇲🇱", "MALI": "🇲🇱",
        "MT": "🇲🇹", "MALTA": "🇲🇹",
        "MH": "🇲🇭", "MARSHALL ISLANDS": "🇲🇭",
        "MQ": "🇲🇶", "MARTINIQUE": "🇲🇶",
        "MR": "🇲🇷", "MAURITANIA": "🇲🇷",
        "MU": "🇲🇺", "MAURITIUS": "🇲🇺",
        "YT": "🇾🇹", "MAYOTTE": "🇾🇹",
        "MX": "🇲🇽", "MEXICO": "🇲🇽",
        "FM": "🇫🇲", "MICRONESIA": "🇫🇲", "MICRONESIA, FEDERATED STATES OF": "🇫🇲",
        "MD": "🇲🇩", "MOLDOVA": "🇲🇩", "MOLDOVA, REPUBLIC OF": "🇲🇩",
        "MC": "🇲🇨", "MONACO": "🇲🇨",
        "MN": "🇲🇳", "MONGOLIA": "🇲🇳",
        "ME": "🇲🇪", "MONTENEGRO": "🇲🇪",
        "MS": "🇲🇸", "MONTSERRAT": "🇲🇸",
        "MA": "🇲🇦", "MOROCCO": "🇲🇦",
        "MZ": "🇲🇿", "MOZAMBIQUE": "🇲🇿",
        "MM": "🇲🇲", "MYANMAR": "🇲🇲", "BURMA": "🇲🇲",
        
        # N
        "NA": "🇳🇦", "NAMIBIA": "🇳🇦",
        "NR": "🇳🇷", "NAURU": "🇳🇷",
        "NP": "🇳🇵", "NEPAL": "🇳🇵",
        "NL": "🇳🇱", "NETHERLANDS": "🇳🇱",
        "NC": "🇳🇨", "NEW CALEDONIA": "🇳🇨",
        "NZ": "🇳🇿", "NEW ZEALAND": "🇳🇿",
        "NI": "🇳🇮", "NICARAGUA": "🇳🇮",
        "NE": "🇳🇪", "NIGER": "🇳🇪",
        "NG": "🇳🇬", "NIGERIA": "🇳🇬",
        "NU": "🇳🇺", "NIUE": "🇳🇺",
        "NF": "🇳🇫", "NORFOLK ISLAND": "🇳🇫",
        "MP": "🇲🇵", "NORTHERN MARIANA ISLANDS": "🇲🇵",
        "NO": "🇳🇴", "NORWAY": "🇳🇴",
        
        # O
        "OM": "🇴🇲", "OMAN": "🇴🇲",
        
        # P
        "PK": "🇵🇰", "PAKISTAN": "🇵🇰",
        "PW": "🇵🇼", "PALAU": "🇵🇼",
        "PS": "🇵🇸", "PALESTINE": "🇵🇸", "PALESTINE, STATE OF": "🇵🇸", "PALESTINIAN TERRITORY, OCCUPIED": "🇵🇸",
        "PA": "🇵🇦", "PANAMA": "🇵🇦",
        "PG": "🇵🇬", "PAPUA NEW GUINEA": "🇵🇬",
        "PY": "🇵🇾", "PARAGUAY": "🇵🇾",
        "PE": "🇵🇪", "PERU": "🇵🇪",
        "PH": "🇵🇭", "PHILIPPINES": "🇵🇭",
        "PN": "🇵🇳", "PITCAIRN": "🇵🇳",
        "PL": "🇵🇱", "POLAND": "🇵🇱",
        "PT": "🇵🇹", "PORTUGAL": "🇵🇹",
        "PR": "🇵🇷", "PUERTO RICO": "🇵🇷",
        
        # Q
        "QA": "🇶🇦", "QATAR": "🇶🇦",
        
        # R
        "RE": "🇷🇪", "REUNION": "🇷🇪",
        "RO": "🇷🇴", "ROMANIA": "🇷🇴",
        "RU": "🇷🇺", "RUSSIA": "🇷🇺", "RUSSIAN FEDERATION": "🇷🇺",
        "RW": "🇷🇼", "RWANDA": "🇷🇼",
        
        # S
        "BL": "🇧🇱", "SAINT BARTHELEMY": "🇧🇱",
        "SH": "🇸🇭", "SAINT HELENA": "🇸🇭",
        "KN": "🇰🇳", "SAINT KITTS AND NEVIS": "🇰🇳",
        "LC": "🇱🇨", "SAINT LUCIA": "🇱🇨",
        "MF": "🇲🇫", "SAINT MARTIN": "🇲🇫",
        "PM": "🇵🇲", "SAINT PIERRE AND MIQUELON": "🇵🇲",
        "VC": "🇻🇨", "SAINT VINCENT AND THE GRENADINES": "🇻🇨",
        "WS": "🇼🇸", "SAMOA": "🇼🇸",
        "SM": "🇸🇲", "SAN MARINO": "🇸🇲",
        "ST": "🇸🇹", "SAO TOME AND PRINCIPE": "🇸🇹",
        "SA": "🇸🇦", "SAUDI ARABIA": "🇸🇦",
        "SN": "🇸🇳", "SENEGAL": "🇸🇳",
        "RS": "🇷🇸", "SERBIA": "🇷🇸",
        "SC": "🇸🇨", "SEYCHELLES": "🇸🇨",
        "SL": "🇸🇱", "SIERRA LEONE": "🇸🇱",
        "SG": "🇸🇬", "SINGAPORE": "🇸🇬",
        "SX": "🇸🇽", "SINT MAARTEN": "🇸🇽",
        "SK": "🇸🇰", "SLOVAKIA": "🇸🇰",
        "SI": "🇸🇮", "SLOVENIA": "🇸🇮",
        "SB": "🇸🇧", "SOLOMON ISLANDS": "🇸🇧",
        "SO": "🇸🇴", "SOMALIA": "🇸🇴",
        "ZA": "🇿🇦", "SOUTH AFRICA": "🇿🇦",
        "GS": "🇬🇸", "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS": "🇬🇸",
        "SS": "🇸🇸", "SOUTH SUDAN": "🇸🇸",
        "ES": "🇪🇸", "SPAIN": "🇪🇸",
        "LK": "🇱🇰", "SRI LANKA": "🇱🇰",
        "SD": "🇸🇩", "SUDAN": "🇸🇩",
        "SR": "🇸🇷", "SURINAME": "🇸🇷",
        "SJ": "🇸🇯", "SVALBARD AND JAN MAYEN": "🇸🇯",
        "SE": "🇸🇪", "SWEDEN": "🇸🇪",
        "CH": "🇨🇭", "SWITZERLAND": "🇨🇭",
        "SY": "🇸🇾", "SYRIA": "🇸🇾", "SYRIAN ARAB REPUBLIC": "🇸🇾",
        
        # T
        "TW": "🇹🇼", "TAIWAN": "🇹🇼", "TAIWAN, PROVINCE OF CHINA": "🇹🇼",
        "TJ": "🇹🇯", "TAJIKISTAN": "🇹🇯",
        "TZ": "🇹🇿", "TANZANIA": "🇹🇿", "TANZANIA, UNITED REPUBLIC OF": "🇹🇿",
        "TH": "🇹🇭", "THAILAND": "🇹🇭",
        "TL": "🇹🇱", "TIMOR-LESTE": "🇹🇱", "EAST TIMOR": "🇹🇱",
        "TG": "🇹🇬", "TOGO": "🇹🇬",
        "TK": "🇹🇰", "TOKELAU": "🇹🇰",
        "TO": "🇹🇴", "TONGA": "🇹🇴",
        "TT": "🇹🇹", "TRINIDAD AND TOBAGO": "🇹🇹",
        "TN": "🇹🇳", "TUNISIA": "🇹🇳",
        "TR": "🇹🇷", "TURKEY": "🇹🇷",
        "TM": "🇹🇲", "TURKMENISTAN": "🇹🇲",
        "TC": "🇹🇨", "TURKS AND CAICOS ISLANDS": "🇹🇨",
        "TV": "🇹🇻", "TUVALU": "🇹🇻",
        
        # U
        "UG": "🇺🇬", "UGANDA": "🇺🇬",
        "UA": "🇺🇦", "UKRAINE": "🇺🇦",
        "AE": "🇦🇪", "UAE": "🇦🇪", "UNITED ARAB EMIRATES": "🇦🇪",
        "GB": "🇬🇧", "UK": "🇬🇧", "UNITED KINGDOM": "🇬🇧",
        "US": "🇺🇸", "USA": "🇺🇸", "UNITED STATES": "🇺🇸",
        "UM": "🇺🇲", "UNITED STATES MINOR OUTLYING ISLANDS": "🇺🇲",
        "UY": "🇺🇾", "URUGUAY": "🇺🇾",
        "UZ": "🇺🇿", "UZBEKISTAN": "🇺🇿",
        
        # V
        "VU": "🇻🇺", "VANUATU": "🇻🇺",
        "VA": "🇻🇦", "VATICAN CITY": "🇻🇦",
        "VE": "🇻🇪", "VENEZUELA": "🇻🇪", "VENEZUELA, BOLIVARIAN REPUBLIC OF": "🇻🇪",
        "VN": "🇻🇳", "VIETNAM": "🇻🇳", "VIET NAM": "🇻🇳",
        "VG": "🇻🇬", "VIRGIN ISLANDS, BRITISH": "🇻🇬",
        "VI": "🇻🇮", "VIRGIN ISLANDS, U.S.": "🇻🇮",
        
        # W
        "WF": "🇼🇫", "WALLIS AND FUTUNA": "🇼🇫",
        "EH": "🇪🇭", "WESTERN SAHARA": "🇪🇭",
        
        # Y
        "YE": "🇾🇪", "YEMEN": "🇾🇪",
        
        # Z
        "ZM": "🇿🇲", "ZAMBIA": "🇿🇲",
        "ZW": "🇿🇼", "ZIMBABWE": "🇿🇼",
    }
    
    # Comprehensive name to code mapping for all filter.json countries
    NAME_TO_CODE_MAPPING: Dict[str, str] = {
        # Common variations and full country names
        "afghanistan": "AF", "albania": "AL", "algeria": "DZ", "american samoa": "AS",
        "andorra": "AD", "angola": "AO", "anguilla": "AI", "antigua and barbuda": "AG",
        "argentina": "AR", "armenia": "AM", "aruba": "AW", "australia": "AU",
        "austria": "AT", "azerbaijan": "AZ", "bahamas": "BS", "bahrain": "BH",
        "bangladesh": "BD", "barbados": "BB", "belarus": "BY", "belgium": "BE",
        "belize": "BZ", "benin": "BJ", "bermuda": "BM", "bhutan": "BT",
        "bolivia": "BO", "bolivia, plurinational state of": "BO",
        "bosnia and herzegovina": "BA", "botswana": "BW", "brazil": "BR",
        "brunei": "BN", "brunei darussalam": "BN", "bulgaria": "BG",
        "burkina faso": "BF", "burundi": "BI", "cabo verde": "CV", "cape verde": "CV",
        "cambodia": "KH", "cameroon": "CM", "canada": "CA", "cayman islands": "KY",
        "central african republic": "CF", "chad": "TD", "chile": "CL", "china": "CN",
        "colombia": "CO", "comoros": "KM", "congo": "CG",
        "congo, the democratic republic of the": "CD", "costa rica": "CR",
        "cote d'ivoire": "CI", "ivory coast": "CI", "croatia": "HR", "cuba": "CU",
        "curacao": "CW", "cyprus": "CY", "czech republic": "CZ", "czechia": "CZ",
        "denmark": "DK", "djibouti": "DJ", "dominica": "DM", "dominican republic": "DO",
        "ecuador": "EC", "egypt": "EG", "el salvador": "SV", "equatorial guinea": "GQ",
        "eritrea": "ER", "estonia": "EE", "swaziland": "SZ", "eswatini": "SZ",
        "ethiopia": "ET", "falkland islands": "FK", "faroe islands": "FO", "fiji": "FJ",
        "finland": "FI", "france": "FR", "french guiana": "GF", "french polynesia": "PF",
        "gabon": "GA", "gambia": "GM", "georgia": "GE", "germany": "DE", "deutschland": "DE",
        "ghana": "GH", "gibraltar": "GI", "greece": "GR", "greenland": "GL",
        "grenada": "GD", "guadeloupe": "GP", "guam": "GU", "guatemala": "GT",
        "guernsey": "GG", "guinea": "GN", "guinea-bissau": "GW", "guyana": "GY",
        "haiti": "HT", "honduras": "HN", "hong kong": "HK", "hungary": "HU",
        "iceland": "IS", "india": "IN", "indonesia": "ID", "iran": "IR",
        "iran, islamic republic of": "IR", "iraq": "IQ", "ireland": "IE",
        "isle of man": "IM", "israel": "IL", "italy": "IT", "jamaica": "JM",
        "japan": "JP", "jersey": "JE", "jordan": "JO", "kazakhstan": "KZ",
        "kenya": "KE", "kiribati": "KI", "north korea": "KP",
        "korea, democratic people's republic of": "KP", "south korea": "KR",
        "korea, republic of": "KR", "kuwait": "KW", "kyrgyzstan": "KG",
        "laos": "LA", "lao people's democratic republic": "LA", "latvia": "LV",
        "lebanon": "LB", "lesotho": "LS", "liberia": "LR", "libya": "LY",
        "liechtenstein": "LI", "lithuania": "LT", "luxembourg": "LU", "macao": "MO",
        "macedonia": "MK", "macedonia, the former yugoslav republic of": "MK",
        "madagascar": "MG", "malawi": "MW", "malaysia": "MY", "maldives": "MV",
        "mali": "ML", "malta": "MT", "marshall islands": "MH", "martinique": "MQ",
        "mauritania": "MR", "mauritius": "MU", "mayotte": "YT", "mexico": "MX",
        "micronesia": "FM", "micronesia, federated states of": "FM", "moldova": "MD",
        "moldova, republic of": "MD", "monaco": "MC", "mongolia": "MN",
        "montenegro": "ME", "montserrat": "MS", "morocco": "MA", "mozambique": "MZ",
        "myanmar": "MM", "burma": "MM", "namibia": "NA", "nauru": "NR", "nepal": "NP",
        "netherlands": "NL", "new caledonia": "NC", "new zealand": "NZ",
        "nicaragua": "NI", "niger": "NE", "nigeria": "NG", "niue": "NU",
        "norfolk island": "NF", "northern mariana islands": "MP", "norway": "NO",
        "oman": "OM", "pakistan": "PK", "palau": "PW", "palestine": "PS",
        "palestine, state of": "PS", "panama": "PA", "papua new guinea": "PG",
        "paraguay": "PY", "peru": "PE", "philippines": "PH", "pitcairn": "PN",
        "poland": "PL", "portugal": "PT", "puerto rico": "PR", "qatar": "QA",
        "reunion": "RE", "romania": "RO", "russia": "RU", "russian federation": "RU",
        "rwanda": "RW", "saint barthelemy": "BL", "saint helena": "SH",
        "saint kitts and nevis": "KN", "saint lucia": "LC", "saint martin": "MF",
        "saint pierre and miquelon": "PM", "saint vincent and the grenadines": "VC",
        "samoa": "WS", "san marino": "SM", "sao tome and principe": "ST",
        "saudi arabia": "SA", "senegal": "SN", "serbia": "RS", "seychelles": "SC",
        "sierra leone": "SL", "singapore": "SG", "sint maarten": "SX", "slovakia": "SK",
        "slovenia": "SI", "solomon islands": "SB", "somalia": "SO", "south africa": "ZA",
        "south georgia and the south sandwich islands": "GS", "south sudan": "SS",
        "spain": "ES", "sri lanka": "LK", "sudan": "SD", "suriname": "SR",
        "svalbard and jan mayen": "SJ", "sweden": "SE", "switzerland": "CH",
        "syria": "SY", "syrian arab republic": "SY", "taiwan": "TW",
        "taiwan, province of china": "TW", "tajikistan": "TJ", "tanzania": "TZ",
        "tanzania, united republic of": "TZ", "thailand": "TH", "timor-leste": "TL",
        "east timor": "TL", "togo": "TG", "tokelau": "TK", "tonga": "TO",
        "trinidad and tobago": "TT", "tunisia": "TN", "turkey": "TR",
        "turkmenistan": "TM", "turks and caicos islands": "TC", "tuvalu": "TV",
        "uganda": "UG", "ukraine": "UA", "united arab emirates": "AE", "uae": "AE",
        "united kingdom": "GB", "uk": "GB", "united states": "US", "usa": "US",
        "united states minor outlying islands": "UM", "uruguay": "UY", "uzbekistan": "UZ",
        "vanuatu": "VU", "vatican city": "VA", "venezuela": "VE",
        "venezuela, bolivarian republic of": "VE", "vietnam": "VN", "viet nam": "VN",
        "virgin islands, british": "VG", "virgin islands, u.s.": "VI",
        "wallis and futuna": "WF", "western sahara": "EH", "yemen": "YE",
        "zambia": "ZM", "zimbabwe": "ZW",
        # Additional mappings for 100% coverage
        "guersney": "GG", "kosovo": "XK", "kosovo, republic of": "XK",
        "libyan arab jamahiriya": "LY", "macau": "MO",
        "palestinian territory, occupied": "PS"
    }

    @classmethod
    def get_flag(cls, country_input: Optional[str]) -> str:
        """
        DEPRECATED: Use utils.dynamic_country_flags.get_dynamic_country_flag instead
        
        Args:
            country_input: Country name, code, or identifier
            
        Returns:
            Country flag emoji or fallback emoji
        """
        if not country_input or not isinstance(country_input, str):
            return "🌍"
        
        # Try basic legacy mapping first
        country_code = country_input.upper().strip()
        flag = cls.COUNTRY_FLAGS.get(country_code)
        if flag:
            return flag
        
        # Try name mapping
        canonical = country_input.lower().strip()
        country_code_mapped = cls.NAME_TO_CODE_MAPPING.get(canonical)
        if country_code_mapped:
            flag = cls.COUNTRY_FLAGS.get(country_code_mapped)
            if flag:
                return flag
        
        # Return fallback for unsupported countries
        return "🌍"

    @classmethod
    def get_flag_with_name(cls, country_input: Optional[str]) -> str:
        """
        Get country flag with name formatted for display
        
        Args:
            country_input: Country name, code, or identifier
            
        Returns:
            Formatted string with flag and country name
        """
        if not country_input or not isinstance(country_input, str):
            return "🌍 Unknown"
        
        flag = cls.get_flag(country_input)
        clean_name = country_input.strip()
        
        # Don't duplicate flag if it's already in the name
        if flag != "🌍" and not clean_name.startswith(flag):
            return f"{flag} {clean_name}"
        
        return clean_name

    @classmethod
    def add_flag_prefix(cls, country_input: Optional[str]) -> str:
        """
        Add flag prefix to country name if not already present
        
        Args:
            country_input: Country name or code
            
        Returns:
            Country name with flag prefix
        """
        if not country_input or not isinstance(country_input, str):
            return "🌍 Unknown"
        
        clean_name = country_input.strip()
        
        # Check if flag is already present
        if len(clean_name) >= 2 and clean_name[:2] in [
            "🇺🇸", "🇨🇦", "🇬🇧", "🇩🇪", "🇫🇷", "🇮🇹", "🇪🇸", "🇦🇺", "🇯🇵", "🇨🇳",
            "🇮🇳", "🇧🇷", "🇲🇽", "🇷🇺", "🇰🇷", "🇳🇱", "🇸🇪", "🇳🇴", "🇩🇰", "🇫🇮"
        ]:
            return clean_name
        
        # Add flag prefix
        flag = cls.get_flag(country_input)
        if flag != "🌍":
            return f"{flag} {clean_name}"
        
        return clean_name

    @classmethod 
    def is_valid_country(cls, country_input: Optional[str]) -> bool:
        """
        Check if country input has a valid flag (not fallback)
        
        Args:
            country_input: Country name or code to validate
            
        Returns:
            True if country has a specific flag, False otherwise
        """
        if not country_input:
            return False
        
        flag = cls.get_flag(country_input)
        return flag != "🌍"

    @classmethod
    def get_supported_countries(cls) -> list[str]:
        """
        Get list of all supported country codes
        
        Returns:
            List of supported ISO country codes
        """
        # Extract unique 2-letter ISO codes
        iso_codes = set()
        for code in cls.COUNTRY_FLAGS.keys():
            if len(code) == 2 and code.isalpha():
                iso_codes.add(code)
        
        return sorted(list(iso_codes))

    @classmethod
    def get_country_count(cls) -> int:
        """
        Get total number of supported countries
        
        Returns:
            Number of countries with flag support
        """
        return len(cls.get_supported_countries())


# Global instances for easy import
flag_manager = CountryFlagManager()

# Legacy convenience functions - use manager only to avoid circular imports
def get_country_flag(country_input: Optional[str]) -> str:
    """DEPRECATED: Use utils.dynamic_country_flags.get_dynamic_country_flag instead"""
    return flag_manager.get_flag(country_input)

def get_country_flag_with_name(country_input: Optional[str]) -> str:
    """DEPRECATED: Use utils.dynamic_country_flags.get_dynamic_country_with_flag instead"""
    return flag_manager.get_flag_with_name(country_input)

def add_country_flag_prefix(country_input: Optional[str]) -> str:
    """DEPRECATED: Use utils.dynamic_country_flags.get_dynamic_country_with_flag instead"""
    return flag_manager.add_flag_prefix(country_input)