"""
Database operations for payment processing.

This module provides database operations specifically for payment processing,
using the main bot's database connection directly.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


def _get_payments_collection():
    """Get the payments collection from the main bot's database or create a new connection."""
    try:
        from database.connection import get_collection, is_database_connected
        if is_database_connected():
            return get_collection("payments")
        else:
            logger.warning("Main bot database not connected, creating new connection for payment module")
            # Create a new database connection for the payment module
            import asyncio
            from motor.motor_asyncio import AsyncIOMotorClient
            from config.settings import get_settings
            
            settings = get_settings()
            client = AsyncIOMotorClient(settings.MONGODB_URL)
            db = client[settings.DATABASE_NAME]
            return db.payments
    except Exception as e:
        logger.error(f"Failed to get payments collection: {e}")
        raise RuntimeError(f"Payment module requires database connection: {e}")


async def save_payment_details(**kwargs) -> Optional[Dict[str, Any]]:
    """
    Saves payment details. Returns payment dict on success.

    This function tracks both the requested amount and the actual paid amount.
    The actual_paid_amount will be updated later during payment verification.
    """
    try:
        # Extract the amount from kwargs
        amount = float(kwargs.get("amount", 0))

        # Create the payment record with additional security fields
        payment = {
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "status": "pending",
            "requested_amount": amount,  # Store the original requested amount
            "actual_paid_amount": 0,  # Will be updated during verification
            "payment_verified": False,  # Will be set to True after verification
            **kwargs,
        }

        # Log the payment creation
        logger.info(
            f"Creating payment record for track_id {kwargs.get('track_id')}: "
            f"Requested amount: ${amount:.2f}"
        )

        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()
        result = await payments_collection.insert_one(payment)
        
        if result.acknowledged:
            payment["_id"] = result.inserted_id
            logger.info(f"✅ Payment saved to main bot's database: track_id={kwargs.get('track_id')}")
            return payment
        else:
            logger.error(f"Payment insert not acknowledged for track_id {kwargs.get('track_id')}")
            return None
                
    except Exception as e:
        logger.error(f"Error saving payment details: {e}")
        return None


async def get_payment_by_track_id(track_id: str) -> Optional[Dict[str, Any]]:
    """Gets payment details by track_id."""
    try:
        # Ensure track_id is a string
        track_id = str(track_id) if track_id else None
        
        if not track_id:
            logger.error("get_payment_by_track_id called with empty track_id")
            return None
        
        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()
        payment = await payments_collection.find_one({"track_id": track_id})
        
        if payment:
            logger.debug(f"✅ Payment found in main bot's database")
        else:
            logger.debug(f"❌ Payment NOT found in main bot's database")
        
        return payment
            
    except Exception as e:
        logger.error(f"Error getting payment by track_id ({track_id}): {e}")
        return None


async def update_payment_status_atomic(
    track_id: str, new_status: str, expected_status: Optional[str] = None, **kwargs
) -> Optional[Dict[str, Any]]:
    """
    Atomically updates payment status only if it matches the expected status.
    This prevents race conditions during concurrent payment processing.

    Args:
        track_id: The payment track ID
        new_status: The new payment status to set
        expected_status: The expected current status (None to skip check)
        **kwargs: Additional fields to update, such as actual_paid_amount

    Returns:
        The updated payment document or None if no update occurred
    """
    if not track_id:
        return None

    try:
        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()
        
        # Build the query filter
        query_filter = {"track_id": track_id}
        if expected_status is not None:
            query_filter["status"] = expected_status

        # Prepare update data
        update_data = {"status": new_status, "updated_at": datetime.now(), **kwargs}

        # Use find_one_and_update for atomic operation
        updated_payment = await payments_collection.find_one_and_update(
            query_filter,
            {"$set": update_data},
            return_document=True,  # Return updated document
        )

        if updated_payment:
            logger.info(
                f"Atomically updated payment {track_id} from {expected_status} to {new_status}"
            )
            return updated_payment
        else:
            logger.warning(
                f"No payment found or status mismatch for track_id {track_id}"
            )
            return None

    except Exception as e:
        logger.error(f"Error updating payment status atomically ({track_id}): {e}")
        return None


async def update_payment_status(
    track_id: str, status: str, **kwargs
) -> Optional[Dict[str, Any]]:
    """
    Updates payment status and optionally other fields.

    Args:
        track_id: The payment track ID
        status: The new payment status
        **kwargs: Additional fields to update, such as actual_paid_amount

    Returns:
        The updated payment document or None if error
    """
    if not track_id:
        return None
        
    try:
        # Get the current payment record to compare amounts if needed
        current_payment = await get_payment_by_track_id(track_id)

        # Prepare update data
        update_data = {"status": status, "updated_at": datetime.now(), **kwargs}

        # If actual_paid_amount is provided, validate it
        if "actual_paid_amount" in kwargs:
            actual_paid_amount = float(kwargs.get("actual_paid_amount", 0))
            requested_amount = (
                float(current_payment.get("amount", 0)) if current_payment else 0
            )

            # Log the amount comparison
            logger.info(
                f"Payment {track_id} update: Requested=${requested_amount:.2f}, "
                f"Actual=${actual_paid_amount:.2f}"
            )

            # Check for suspicious amount differences
            if actual_paid_amount > 0 and requested_amount > 0:
                if actual_paid_amount < requested_amount * 0.5:  # Less than 50% paid
                    logger.warning(
                        f"SUSPICIOUS: Payment {track_id} received only "
                        f"{(actual_paid_amount/requested_amount)*100:.1f}% of requested amount"
                    )
                elif actual_paid_amount > requested_amount * 1.5:  # More than 150% paid
                    logger.warning(
                        f"UNUSUAL: Payment {track_id} received "
                        f"{(actual_paid_amount/requested_amount)*100:.1f}% of requested amount"
                    )

        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()
        result = await payments_collection.update_one(
            {"track_id": track_id}, {"$set": update_data}
        )
        
        if result.matched_count > 0:
            payment = await get_payment_by_track_id(track_id)
            return payment
        else:
            logger.warning(
                f"Update payment status found no match for track_id {track_id}"
            )
            return None
                
    except Exception as e:
        logger.error(f"Error updating payment status ({track_id}): {e}")
        return None


async def get_user_pending_payments(user_id: int) -> List[Dict[str, Any]]:
    """Get all pending payments for a user."""
    try:
        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()
        return await payments_collection.find({
            "user_id": user_id,
            "status": {"$in": ["pending", "confirming", "waiting"]}
        }).sort("created_at", -1).to_list(None)
            
    except Exception as e:
        logger.error(f"Error getting pending payments for user {user_id}: {e}")
        return []


async def get_user_payments(
    user_id: int, completed_only: bool = False
) -> List[Dict[str, Any]]:
    """
    Get payments for a specific user.

    Args:
        user_id: User ID to get payments for
        completed_only: If True, only return completed payments (optimized for VIP eligibility)

    Returns:
        List of payment records
    """
    try:
        user_id_int = int(user_id)
        
        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()

        # Build query - filter for completed payments if requested
        query = {"user_id": user_id_int}
        if completed_only:
            # Only include completed/verified payments for VIP calculations
            query["status"] = {
                "$in": ["completed", "verified", "confirmed", "success", "paid"]
            }

        payments = await payments_collection.find(query).sort("created_at", -1).to_list(None)
        return payments

    except Exception as e:
        logger.error(f"Error getting payments for user {user_id}: {e}")
        return []


async def get_latest_payment(user_id: int) -> Optional[Dict[str, Any]]:
    """Get the latest payment for a user."""
    try:
        payments = await get_user_payments(user_id)
        return payments[0] if payments else None
    except Exception as e:
        logger.error(f"Error getting latest payment for user {user_id}: {e}")
        return None


async def ensure_payment_indexes():
    """Ensure proper indexes exist for payment collection."""
    try:
        # Use main bot's database connection directly
        payments_collection = _get_payments_collection()
        
        # MongoDB indexes
        await payments_collection.create_index("track_id", unique=True)
        await payments_collection.create_index("user_id")
        await payments_collection.create_index("status")
        await payments_collection.create_index("created_at")
        await payments_collection.create_index([("user_id", 1), ("status", 1)])
        logger.info("Payment indexes ensured")
        return True
            
    except Exception as e:
        logger.error(f"Error ensuring payment indexes: {e}")
        return False


def get_payment_statistics(user_id: Optional[int] = None, days: int = 30) -> Dict[str, Any]:
    """
    Get payment statistics for a user or globally.
    
    Args:
        user_id: User ID (optional, if None returns global stats)
        days: Number of days to look back
        
    Returns:
        Dictionary with payment statistics
    """
    try:
        payments_collection = _get_payments_collection()
        
        # Calculate date range
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Build query
        query = {"created_at": {"$gte": start_date, "$lte": end_date}}
        if user_id:
            query["user_id"] = user_id
        
        # Get payment statistics
        pipeline = [
            {"$match": query},
            {
                "$group": {
                    "_id": "$status",
                    "count": {"$sum": 1},
                    "total_amount": {"$sum": "$amount"},
                    "avg_amount": {"$avg": "$amount"}
                }
            }
        ]
        
        if isinstance(payments_collection, dict):
            # In-memory fallback - basic statistics
            stats = {
                "period_days": days,
                "user_id": user_id,
                "total_payments": 0,
                "total_amount": 0,
                "by_status": {},
                "success_rate": 0,
                "underpayment_rate": 0,
                "overpayment_rate": 0
            }
            
            # Calculate basic stats from in-memory data
            for payment in payments_collection.values():
                if user_id and payment.get("user_id") != user_id:
                    continue
                    
                payment_date = payment.get("created_at", datetime.min)
                if payment_date >= start_date and payment_date <= end_date:
                    status = payment.get("status", "unknown")
                    amount = payment.get("amount", 0)
                    
                    if status not in stats["by_status"]:
                        stats["by_status"][status] = {"count": 0, "total_amount": 0, "avg_amount": 0}
                    
                    stats["by_status"][status]["count"] += 1
                    stats["by_status"][status]["total_amount"] += amount
                    stats["total_payments"] += 1
                    stats["total_amount"] += amount
            
            # Calculate averages
            for status_data in stats["by_status"].values():
                if status_data["count"] > 0:
                    status_data["avg_amount"] = status_data["total_amount"] / status_data["count"]
            
            # Calculate rates
            if stats["total_payments"] > 0:
                completed = stats["by_status"].get("completed", {}).get("count", 0)
                underpaid = stats["by_status"].get("underpaid", {}).get("count", 0)
                overpaid = stats["by_status"].get("overpaid", {}).get("count", 0)
                
                stats["success_rate"] = (completed / stats["total_payments"]) * 100
                stats["underpayment_rate"] = (underpaid / stats["total_payments"]) * 100
                stats["overpayment_rate"] = (overpaid / stats["total_payments"]) * 100
            
            return stats
        else:
            # MongoDB (either direct connection or main bot's connection)
            results = list(payments_collection.aggregate(pipeline))
            
            # Process results
            stats = {
                "period_days": days,
                "user_id": user_id,
                "total_payments": 0,
                "total_amount": 0,
                "by_status": {},
                "success_rate": 0,
                "underpayment_rate": 0,
                "overpayment_rate": 0
            }
            
            for result in results:
                status = result["_id"]
                count = result["count"]
                total_amount = result["total_amount"]
                avg_amount = result["avg_amount"]
                
                stats["by_status"][status] = {
                    "count": count,
                    "total_amount": total_amount,
                    "avg_amount": avg_amount
                }
                
                stats["total_payments"] += count
                stats["total_amount"] += total_amount
            
            # Calculate rates
            if stats["total_payments"] > 0:
                completed = stats["by_status"].get("completed", {}).get("count", 0)
                underpaid = stats["by_status"].get("underpaid", {}).get("count", 0)
                overpaid = stats["by_status"].get("overpaid", {}).get("count", 0)
                
                stats["success_rate"] = (completed / stats["total_payments"]) * 100
                stats["underpayment_rate"] = (underpaid / stats["total_payments"]) * 100
                stats["overpayment_rate"] = (overpaid / stats["total_payments"]) * 100
            
            return stats
        
    except Exception as e:
        logger.error(f"Error getting payment statistics: {e}")
        return {}


def get_payments_by_date_range(
    start_date: datetime, 
    end_date: datetime, 
    user_id: Optional[int] = None,
    status: Optional[str] = None,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """
    Get payments within a date range with optional filters.
    
    Args:
        start_date: Start date for query
        end_date: End date for query
        user_id: Optional user ID filter
        status: Optional status filter
        limit: Maximum number of results
        
    Returns:
        List of payment records
    """
    try:
        payments_collection = _get_payments_collection()
        
        # Build query
        query = {
            "created_at": {"$gte": start_date, "$lte": end_date}
        }
        
        if user_id:
            query["user_id"] = user_id
        
        if status:
            query["status"] = status
        
        # Execute query
        payments = list(payments_collection.find(query).sort("created_at", -1).limit(limit))
        
        # Convert ObjectId to string for JSON serialization
        for payment in payments:
            if "_id" in payment:
                payment["_id"] = str(payment["_id"])
        
        logger.info(f"Retrieved {len(payments)} payments for date range")
        return payments
        
    except Exception as e:
        logger.error(f"Error getting payments by date range: {e}")
        return []


def search_payments(
    query_text: str, 
    user_id: Optional[int] = None,
    limit: int = 50
) -> List[Dict[str, Any]]:
    """
    Search payments by track_id, order_id, or description.
    
    Args:
        query_text: Text to search for
        user_id: Optional user ID filter
        limit: Maximum number of results
        
    Returns:
        List of matching payment records
    """
    try:
        payments_collection = _get_payments_collection()
        
        # Build search query
        search_query = {
            "$or": [
                {"track_id": {"$regex": query_text, "$options": "i"}},
                {"order_id": {"$regex": query_text, "$options": "i"}},
                {"description": {"$regex": query_text, "$options": "i"}}
            ]
        }
        
        if user_id:
            search_query["user_id"] = user_id
        
        # Execute search
        payments = list(payments_collection.find(search_query).sort("created_at", -1).limit(limit))
        
        # Convert ObjectId to string for JSON serialization
        for payment in payments:
            if "_id" in payment:
                payment["_id"] = str(payment["_id"])
        
        logger.info(f"Found {len(payments)} payments matching '{query_text}'")
        return payments
        
    except Exception as e:
        logger.error(f"Error searching payments: {e}")
        return []


def get_payment_analytics(
    user_id: Optional[int] = None,
    days: int = 30
) -> Dict[str, Any]:
    """
    Get comprehensive payment analytics.
    
    Args:
        user_id: User ID (optional, if None returns global analytics)
        days: Number of days to analyze
        
    Returns:
        Dictionary with comprehensive analytics
    """
    try:
        payments_collection = _get_payments_collection()
        
        # Calculate date range
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Build base query
        base_query = {"created_at": {"$gte": start_date, "$lte": end_date}}
        if user_id:
            base_query["user_id"] = user_id
        
        # Get basic statistics
        stats = get_payment_statistics(user_id, days)
        
        # Get daily payment trends
        daily_pipeline = [
            {"$match": base_query},
            {
                "$group": {
                    "_id": {
                        "year": {"$year": "$created_at"},
                        "month": {"$month": "$created_at"},
                        "day": {"$dayOfMonth": "$created_at"}
                    },
                    "count": {"$sum": 1},
                    "total_amount": {"$sum": "$amount"},
                    "completed_count": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "completed_amount": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, "$amount", 0]}
                    }
                }
            },
            {"$sort": {"_id.year": 1, "_id.month": 1, "_id.day": 1}}
        ]
        
        daily_trends = list(payments_collection.aggregate(daily_pipeline))
        
        # Get amount distribution
        amount_pipeline = [
            {"$match": base_query},
            {
                "$bucket": {
                    "groupBy": "$amount",
                    "boundaries": [0, 10, 25, 50, 100, 250, 500, 1000, float('inf')],
                    "default": "1000+",
                    "output": {
                        "count": {"$sum": 1},
                        "total_amount": {"$sum": "$amount"}
                    }
                }
            }
        ]
        
        amount_distribution = list(payments_collection.aggregate(amount_pipeline))
        
        # Get status distribution over time
        status_trends = {}
        for status in ["pending", "completed", "failed", "underpaid", "overpaid"]:
            status_pipeline = [
                {"$match": {**base_query, "status": status}},
                {
                    "$group": {
                        "_id": {
                            "year": {"$year": "$created_at"},
                            "month": {"$month": "$created_at"},
                            "day": {"$dayOfMonth": "$created_at"}
                        },
                        "count": {"$sum": 1}
                    }
                },
                {"$sort": {"_id.year": 1, "_id.month": 1, "_id.day": 1}}
            ]
            
            status_trends[status] = list(payments_collection.aggregate(status_pipeline))
        
        analytics = {
            "period": {
                "days": days,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "user_id": user_id,
            "statistics": stats,
            "daily_trends": daily_trends,
            "amount_distribution": amount_distribution,
            "status_trends": status_trends,
            "summary": {
                "total_payments": stats["total_payments"],
                "total_amount": stats["total_amount"],
                "success_rate": stats["success_rate"],
                "avg_daily_payments": stats["total_payments"] / days if days > 0 else 0,
                "avg_daily_amount": stats["total_amount"] / days if days > 0 else 0
            }
        }
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting payment analytics: {e}")
        return {}


def cleanup_old_payments(days_to_keep: int = 90) -> int:
    """
    Clean up old payment records.
    
    Args:
        days_to_keep: Number of days to keep payment records
        
    Returns:
        Number of records deleted
    """
    try:
        payments_collection = _get_payments_collection()
        
        # Calculate cutoff date
        from datetime import datetime, timedelta
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # Delete old records
        result = payments_collection.delete_many({
            "created_at": {"$lt": cutoff_date},
            "status": {"$in": ["completed", "failed", "expired"]}
        })
        
        deleted_count = result.deleted_count
        logger.info(f"Cleaned up {deleted_count} old payment records")
        
        return deleted_count
        
    except Exception as e:
        logger.error(f"Error cleaning up old payments: {e}")
        return 0


def backup_payments(backup_path: str) -> bool:
    """
    Backup payment records to file.
    
    Args:
        backup_path: Path to backup file
        
    Returns:
        True if backup was successful
    """
    try:
        import json
        from datetime import datetime
        
        payments_collection = _get_payments_collection()
        
        # Get all payment records
        payments = list(payments_collection.find())
        
        # Convert ObjectId to string for JSON serialization
        for payment in payments:
            if "_id" in payment:
                payment["_id"] = str(payment["_id"])
        
        # Create backup data
        backup_data = {
            "backup_date": datetime.now().isoformat(),
            "total_records": len(payments),
            "payments": payments
        }
        
        # Write to file
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"Backed up {len(payments)} payment records to {backup_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error backing up payments: {e}")
        return False


def restore_payments(backup_path: str) -> bool:
    """
    Restore payment records from backup file.
    
    Args:
        backup_path: Path to backup file
        
    Returns:
        True if restore was successful
    """
    try:
        import json
        
        payments_collection = _get_payments_collection()
        
        # Read backup file
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        payments = backup_data.get("payments", [])
        
        if not payments:
            logger.warning("No payment records found in backup file")
            return False
        
        # Insert payments (skip duplicates)
        inserted_count = 0
        for payment in payments:
            try:
                # Remove _id to let MongoDB generate new one
                if "_id" in payment:
                    del payment["_id"]
                
                payments_collection.insert_one(payment)
                inserted_count += 1
            except Exception as e:
                logger.warning(f"Skipped duplicate payment: {e}")
        
        logger.info(f"Restored {inserted_count} payment records from {backup_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error restoring payments: {e}")
        return False

