"""
Empty cart flow: reuse authenticated session to POST to /cart with 'Empty cart' action.
This removes all items from the cart.
"""

import json
import os
from urllib.parse import urljoin

import requests

from login import (
    logger,
    REFERER,
    log_request,
    log_response,
    refresh_xsrf_headers_from_cookies,
    prune_cookie_duplicates,
)
from session_manager import (
    get_authenticated_session,
    save_session_cookies,
)

# Reuse HTML -> JSON extraction from add_to_cart
try:
    from add_to_cart import _response_to_jsonable
except ImportError:
    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _cart_url() -> str:
    """Build the absolute URL for the cart page."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "cart")


def _extract_post_form_token(html: str) -> str | None:
    """Extract hidden _token value from the first POST form on the page."""
    try:
        from bs4 import BeautifulSoup
        from urllib.parse import urljoin

        soup = BeautifulSoup(html, "html.parser")
        forms = []

        for form in soup.find_all("form"):
            form_data = {
                "method": form.get("method", "GET").upper(),
                "action": urljoin(REFERER, form.get("action", "")),
                "inputs": [],
            }

            # Extract input fields
            for inp in form.find_all("input"):
                input_data = {
                    "name": inp.get("name", ""),
                    "type": inp.get("type", "text"),
                    "value": inp.get("value", ""),
                }
                form_data["inputs"].append(input_data)

            # Create payload for POST forms
            if form_data["method"] == "POST":
                payload = {}
                for inp in form_data["inputs"]:
                    if inp["name"] and inp["type"] not in ("submit", "button"):
                        payload[inp["name"]] = inp["value"]
                form_data["payload"] = payload

            forms.append(form_data)

        for f in forms:
            if (f.get("method") or "").upper() != "POST":
                continue
            payload = f.get("payload") or {}
            tok = payload.get("_token")
            if tok:
                return tok
            # Fallback: scan inputs
            for i in f.get("inputs", []):
                if (i.get("name") or "").lower() == "_token":
                    v = i.get("value")
                    if v:
                        return v
    except Exception:
        pass
    return None


def fetch_cart_for_token(session: requests.Session) -> tuple[str, requests.Response]:
    """Fetch the cart page to extract CSRF token."""
    url = _cart_url()
    logger.info("Fetching cart page for CSRF token: %s", url)
    r = session.get(url, allow_redirects=True, timeout=60)
    log_request(r)
    log_response(r)
    return r.url or url, r


def _build_empty_cart_payload(csrf_token: str) -> dict:
    """
    Build the payload for emptying the cart.
    Based on the demo curl: _token=...&_method=PUT&target=Empty+cart
    """
    return {
        "_token": csrf_token or "",
        "_method": "PUT",
        "target": "Empty cart",
    }


def post_empty_cart(
    session: requests.Session, csrf_token: str, referer: str
) -> requests.Response:
    """
    POST to /cart with empty cart action.
    Headers match the format from demo/empty_cart.py.
    """
    url = _cart_url()
    data = _build_empty_cart_payload(csrf_token)
    
    logger.info("POSTing empty cart request to: %s", url)
    
    # Headers match the demo curl/fetch format
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.8",
        "Cache-Control": "max-age=0",
        "Content-Type": "application/x-www-form-urlencoded",
        "Referer": referer,
        "Origin": REFERER.rstrip("/"),
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-User": "?1",
        "Sec-GPC": "1",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Mobile Safari/537.36",
        "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": '"Android"',
    }
    
    r = session.post(
        url,
        data=data,
        headers=headers,
        allow_redirects=False,  # Match demo behavior
        timeout=60,
    )
    log_request(r)
    log_response(r)
    return r


def follow_redirect_if_any(
    session: requests.Session, r: requests.Response
) -> requests.Response:
    """Follow 302 redirects as per demo response headers."""
    try:
        status = getattr(r, "status_code", 0) or 0
        loc = r.headers.get("Location")
        if loc and 300 <= status < 400:
            base = getattr(r, "url", None) or _cart_url()
            from urllib.parse import urljoin as _uj

            target = _uj(base, loc)
            logger.info("Following redirect to: %s", target)
            g = session.get(target, allow_redirects=True, timeout=60)
            log_request(g)
            log_response(g)
            return g
    except Exception:
        pass
    return r


def save_empty_cart_result(
    final_resp: requests.Response,
    out_path: str = "empty_cart_response.json",
) -> str:
    """Save the empty cart response to JSON."""
    data = {"empty_cart": _response_to_jsonable(final_resp)}
    
    # Print JSON response to console
    print("\n" + "=" * 60)
    print("EMPTY CART RESPONSE (JSON)")
    print("=" * 60)
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print("=" * 60 + "\n")
    
    # Save to file
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return out_path


def main() -> None:
    """Main execution flow for emptying the cart."""
    session = get_authenticated_session(logger)

    # Fetch cart page to get CSRF token
    referer_full, cart_resp = fetch_cart_for_token(session)
    token = _extract_post_form_token(cart_resp.text or "")
    
    if not token:
        logger.error("Failed to extract CSRF token from cart page")
        return

    # Post empty cart request
    post_resp = post_empty_cart(session, token, referer_full)
    
    # Follow redirect (302 -> /cart)
    final_resp = follow_redirect_if_any(session, post_resp)
    
    # Save result
    out_file = save_empty_cart_result(final_resp)
    logger.info("Saved empty cart response JSON to: %s", out_file)

    # Save session cookies
    save_session_cookies(session, logger=logger)


if __name__ == "__main__":
    main()
