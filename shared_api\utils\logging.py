"""
Shared API Logging Utilities

Provides logging functionality for the shared API system.
"""

import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
from utils.central_logger import get_logger

logger = get_logger()


class SharedAPILogger:
    """
    Logger for shared API system with structured logging support
    """
    
    def __init__(self, name: str, level: str = "INFO"):
        self.logger = get_logger()
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Add handler if not already present
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> None:
        """Log API request"""
        log_data = {
            "type": "request",
            "method": method,
            "url": url,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        if headers:
            log_data["headers"] = self._mask_sensitive_headers(headers)
        
        if params:
            log_data["params"] = params
        
        if data:
            log_data["data"] = self._mask_sensitive_data(data)
        
        log_data.update(kwargs)
        
        self.logger.info(f"API Request: {json.dumps(log_data, default=str)}")
    
    def log_response(
        self,
        status_code: int,
        response_data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None,
        **kwargs
    ) -> None:
        """Log API response"""
        log_data = {
            "type": "response",
            "status_code": status_code,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        if duration_ms is not None:
            log_data["duration_ms"] = duration_ms
        
        if response_data:
            # Truncate large responses
            if isinstance(response_data, dict) and len(str(response_data)) > 1000:
                log_data["response"] = "Response too large to log"
            else:
                log_data["response"] = response_data
        
        log_data.update(kwargs)
        
        self.logger.info(f"API Response: {json.dumps(log_data, default=str)}")
    
    def log_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> None:
        """Log API error"""
        log_data = {
            "type": "error",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        if context:
            log_data["context"] = context
        
        log_data.update(kwargs)
        
        self.logger.error(f"API Error: {json.dumps(log_data, default=str)}")
    
    def _mask_sensitive_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Mask sensitive header values"""
        masked = {}
        sensitive_keys = {
            'authorization', 'x-api-key', 'api-key', 'token',
            'bearer', 'auth', 'secret', 'password', 'cookie'
        }
        
        for key, value in headers.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                if len(value) > 8:
                    masked[key] = f"{value[:4]}***{value[-4:]}"
                else:
                    masked[key] = "***"
            else:
                masked[key] = value
        
        return masked
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Mask sensitive data values"""
        if not isinstance(data, dict):
            return data
        
        masked = {}
        sensitive_keys = {
            'password', 'token', 'key', 'secret', 'auth', 'credential',
            'api_key', 'bearer_token', 'access_token', 'refresh_token'
        }
        
        for key, value in data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                if isinstance(value, str) and len(value) > 8:
                    masked[key] = f"{value[:4]}***{value[-4:]}"
                else:
                    masked[key] = "***"
            elif isinstance(value, dict):
                masked[key] = self._mask_sensitive_data(value)
            else:
                masked[key] = value
        
        return masked


def get_shared_api_logger(name: str, level: str = "INFO") -> SharedAPILogger:
    """
    Get a shared API logger instance
    
    Args:
        name: Logger name
        level: Log level
        
    Returns:
        SharedAPILogger instance
    """
    return SharedAPILogger(name, level)
