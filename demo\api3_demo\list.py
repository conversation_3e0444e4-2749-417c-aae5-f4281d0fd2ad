"""Simple filter script - extract POST forms with BIN filtering."""

import json
from datetime import datetime
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from login import logger, REFERER
from session_manager import get_authenticated_session, save_session_cookies

# Import standard response converter for consistency
try:
    from add_to_cart import _response_to_jsonable
except ImportError:

    def _response_to_jsonable(r: requests.Response) -> dict:
        return {
            "status": r.status_code,
            "url": r.url,
            "headers": dict(r.headers),
            "body_preview": (r.text or "")[:800],
        }


def _filter_url():
    """Return shop filter URL."""
    base = REFERER if REFERER.endswith("/") else REFERER + "/"
    return urljoin(base, "shop")


def _filter_params(bin_number=None):
    """Build filter parameters with BIN."""
    return {
        "continent[]": "",
        "country[]": "",
        "scheme[]": "",
        "type[]": "",
        "level[]": "",
        "ethnicity": "",
        "postal_code": "",
        "searched_bank": "",
        "selected_bank": "",
        "region": "",
        "city": "",
        "bins": bin_number or "405621",
        "with_billing": "",
        "with_phone": "",
        "with_dob": "",
    }


def extract_forms_fast(html):
    """Extract forms from HTML."""
    try:
        from bs4 import BeautifulSoup
        from urllib.parse import urljoin

        # Extract forms manually since _extract_forms was removed
        soup = BeautifulSoup(html, "html.parser")
        forms = []

        for form in soup.find_all("form"):
            form_data = {
                "method": form.get("method", "GET").upper(),
                "action": urljoin(REFERER, form.get("action", "")),
                "inputs": [],
                "selects": [],
                "html": str(form),
            }

            # Extract input fields
            for inp in form.find_all("input"):
                input_data = {
                    "name": inp.get("name", ""),
                    "type": inp.get("type", "text"),
                    "value": inp.get("value", ""),
                    "checked": inp.has_attr("checked"),
                    "required": inp.has_attr("required"),
                }
                form_data["inputs"].append(input_data)

            # Extract select fields
            for select in form.find_all("select"):
                select_data = {
                    "name": select.get("name", ""),
                    "options": [],
                }

                for option in select.find_all("option"):
                    option_data = {
                        "value": option.get("value", ""),
                        "label": option.get_text(strip=True),
                        "selected": option.has_attr("selected"),
                    }
                    select_data["options"].append(option_data)

                form_data["selects"].append(select_data)

            # Create payload for POST forms
            if form_data["method"] == "POST":
                payload = {}
                for inp in form_data["inputs"]:
                    if inp["name"] and inp["type"] not in ("submit", "button"):
                        # Only include checkbox values if they are checked
                        if inp["type"] == "checkbox" and not inp["checked"]:
                            continue
                        payload[inp["name"]] = inp["value"]
                form_data["payload"] = payload

            forms.append(form_data)

        post_forms = []
        for f in forms:
            if (f.get("method") or "").upper() == "POST" and "/logout" not in f.get(
                "action", ""
            ):
                post_forms.append(f)

        return post_forms
    except Exception as e:
        logger.error(f"Form extraction error: {e}")
        return []


def fetch_shop(session, bin_number=None):
    """Fetch shop page with BIN filter."""
    url = _filter_url()
    params = _filter_params(bin_number)

    logger.info(f"Fetching shop with BIN: {params['bins']}")

    try:
        r = session.get(url, params=params, timeout=60)
        logger.info(f"Response: {r.status_code}")
        return r
    except Exception as e:
        logger.error(f"Fetch error: {e}")
        raise


def save_forms_json(forms, filename="list_response.json"):
    """Save only payload, headers and rows - nothing else."""
    try:
        # Extract from the first form only (usually there's just one POST form)
        if not forms:
            output_data = {"payload": {}, "headers": [], "rows": []}
        else:
            form = forms[0]

            # Extract payload
            payload = form.get("payload", {})

            # Initialize variables
            headers = []
            rows = []

            # Extract table data from HTML
            form_html = form.get("html", "")

            if form_html:
                soup = BeautifulSoup(form_html, "html.parser")
                table = soup.find("table")

                if table:
                    # Extract headers
                    thead = table.find("thead")
                    if thead:
                        header_row = thead.find("tr")
                        if header_row:
                            for th in header_row.find_all(["th", "td"]):
                                headers.append(th.get_text(strip=True))

                    # Extract rows
                    tbody = table.find("tbody")
                    if tbody:
                        for tr in tbody.find_all("tr"):
                            row = []
                            for td in tr.find_all("td"):
                                cell_text = td.get_text(strip=True)

                                # Check for input values
                                input_elem = td.find("input")
                                if input_elem:
                                    input_value = input_elem.get("value", "")
                                    input_type = input_elem.get("type", "")
                                    input_name = input_elem.get("name", "")

                                    cell_data = {
                                        "text": cell_text,
                                        "input_type": input_type,
                                        "input_name": input_name,
                                        "input_value": input_value,
                                        "input_checked": input_elem.has_attr("checked"),
                                    }
                                else:
                                    cell_data = {"text": cell_text}

                                row.append(cell_data)
                            rows.append(row)

            # Create minimal output - ONLY payload, headers, rows
            output_data = {"payload": payload, "headers": headers, "rows": rows}

        # Print JSON response to console
        print("\n" + "=" * 60)
        print("LIST RESPONSE (JSON)")
        print("=" * 60)
        print(json.dumps(output_data, ensure_ascii=False, indent=2))
        print("=" * 60 + "\n")
        
        # Save to file
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(output_data, f, indent=2)

        logger.info(
            f"Saved payload + {len(headers)} headers + {len(rows)} rows to {filename}"
        )
        return filename
    except Exception as e:
        logger.error(f"Save error: {e}")
        return None


def main(bin_number=None):
    """Main function - simple and clean."""
    try:
        logger.info("=== Starting Filter ===")

        # Get session
        session = get_authenticated_session(logger)

        # Fetch shop page
        resp = fetch_shop(session, bin_number)

        # Extract POST forms
        forms = extract_forms_fast(resp.text or "")

        # Save results
        save_forms_json(forms)

        # Save session
        save_session_cookies(session, logger=logger)

        logger.info(f"=== Complete: Found {len(forms)} POST forms ===")

    except Exception as e:
        logger.error(f"Error: {e}")
        raise


if __name__ == "__main__":
    import sys

    bin_arg = sys.argv[1] if len(sys.argv) > 1 else None
    main(bin_arg)
