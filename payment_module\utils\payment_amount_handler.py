"""
Underpayment and Overpayment Detection and Handling

This module provides comprehensive underpayment and overpayment detection,
handling, and user notification functionality for the payment system.
"""

import logging
from typing import Dict, Any, Optional, Tuple, Union
from datetime import datetime
from aiogram import Bot
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

logger = logging.getLogger(__name__)

# Constants for underpayment/overpayment thresholds
UNDERPAYMENT_THRESHOLD = 0.95  # 95% of required amount
SLIGHT_UNDERPAYMENT_THRESHOLD = 0.99  # 99% of required amount (within 1%)
OVERPAYMENT_THRESHOLD = 1.1  # 110% of required amount (10% over)
SIGNIFICANT_OVERPAYMENT_THRESHOLD = 1.5  # 150% of required amount (50% over)

# Status constants
UNDERPAID_STATUS = "underpaid"
OVERPAID_STATUS = "overpaid"
COMPLETED_STATUS = "completed"


def get_underpayment_threshold(payment_data: Dict[str, Any], payment_record: Optional[Dict[str, Any]] = None) -> float:
    """
    Get the underpayment threshold for a payment.
    
    Args:
        payment_data: Payment data from OXA Pay
        payment_record: Payment record from database
        
    Returns:
        float: Underpayment threshold (0.0 to 1.0)
    """
    # Check if threshold is specified in payment data
    if "underpayment_threshold" in payment_data:
        return float(payment_data["underpayment_threshold"])
    
    # Check if threshold is specified in payment record
    if payment_record and "underpayment_threshold" in payment_record:
        return float(payment_record["underpayment_threshold"])
    
    # Default threshold
    return UNDERPAYMENT_THRESHOLD


def check_payment_amounts(
    actual_amount: float, 
    required_amount: float, 
    track_id: str
) -> Tuple[str, Dict[str, Any]]:
    """
    Check if payment is underpaid, overpaid, or normal.
    
    Args:
        actual_amount: Amount actually received
        required_amount: Amount required
        track_id: Payment tracking ID
        
    Returns:
        tuple: (status, details)
    """
    if actual_amount <= 0 or required_amount <= 0:
        return "invalid", {"error": "Invalid amounts"}
    
    # Calculate percentage
    percentage = (actual_amount / required_amount) * 100
    
    details = {
        "actual_amount": actual_amount,
        "required_amount": required_amount,
        "difference": actual_amount - required_amount,
        "percentage": percentage,
        "track_id": track_id
    }
    
    # Check for underpayment
    if actual_amount < required_amount * UNDERPAYMENT_THRESHOLD:
        return "underpayment", details
    elif actual_amount < required_amount:
        return "slight_underpayment", details
    
    # Check for overpayment (with small epsilon for floating point precision)
    elif actual_amount > required_amount * OVERPAYMENT_THRESHOLD - 0.01:
        if actual_amount > required_amount * SIGNIFICANT_OVERPAYMENT_THRESHOLD - 0.01:
            return "significant_overpayment", details
        else:
            return "overpayment", details
    
    # Normal payment
    else:
        return "normal", details


def format_amount_difference(actual_amount: float, required_amount: float) -> str:
    """
    Format amount difference for display.
    
    Args:
        actual_amount: Amount actually received
        required_amount: Amount required
        
    Returns:
        str: Formatted difference string
    """
    difference = actual_amount - required_amount
    percentage = ((actual_amount / required_amount) - 1) * 100
    
    if difference > 0:
        return f"+${difference:.2f} ({percentage:+.1f}%)"
    else:
        return f"-${abs(difference):.2f} ({percentage:+.1f}%)"


def create_underpayment_message(
    track_id: str,
    received_amount: float,
    required_amount: float,
    remaining_amount: float,
    payment_url: Optional[str] = None
) -> Tuple[str, InlineKeyboardMarkup]:
    """
    Create underpayment message and keyboard.
    
    Args:
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        remaining_amount: Amount remaining
        payment_url: Payment URL for completion
        
    Returns:
        tuple: (message_text, keyboard)
    """
    underpayment_percent = ((required_amount - received_amount) / required_amount) * 100
    
    message = (
        "⚠️ <b>• UNDERPAYMENT DETECTED •</b>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>PAYMENT DETAILS</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"• <b>Required:</b> <code>${required_amount:.2f}</code>\n"
        f"• <b>Received:</b> <code>${received_amount:.2f}</code>\n"
        f"• <b>Remaining:</b> <code>${remaining_amount:.2f}</code>\n"
        f"• <b>Shortfall:</b> <code>{underpayment_percent:.1f}%</code>\n\n"
        "🚨 <b>ACTION REQUIRED</b>\n"
        "Please complete the remaining payment to finalize your deposit.\n\n"
        "<i>You can use the same payment link or create a new one for the remaining amount.</i>"
    )
    
    # Create keyboard
    keyboard_buttons = []
    
    if payment_url:
        keyboard_buttons.append([
            InlineKeyboardButton(
                text="💳 Complete Payment",
                url=payment_url
            )
        ])
    
    keyboard_buttons.append([
        InlineKeyboardButton(
            text="🔄 Check Status",
            callback_data=f"check_payment:{track_id}"
        ),
        InlineKeyboardButton(
            text="❌ Cancel",
            callback_data=f"cancel_payment:{track_id}"
        )
    ])
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    return message, keyboard


def create_overpayment_message(
    track_id: str,
    received_amount: float,
    required_amount: float,
    overpayment_amount: float,
    overpayment_percent: float
) -> Tuple[str, InlineKeyboardMarkup]:
    """
    Create overpayment message and keyboard.
    
    Args:
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        overpayment_amount: Amount overpaid
        overpayment_percent: Overpayment percentage
        
    Returns:
        tuple: (message_text, keyboard)
    """
    if overpayment_percent > 50:  # Significant overpayment
        message = (
            "🚨 <b>• SIGNIFICANT OVERPAYMENT DETECTED •</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>PAYMENT DETAILS</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"• <b>Required:</b> <code>${required_amount:.2f}</code>\n"
            f"• <b>Received:</b> <code>${received_amount:.2f}</code>\n"
            f"• <b>Overpayment:</b> <code>${overpayment_amount:.2f}</code>\n"
            f"• <b>Excess:</b> <code>{overpayment_percent:.1f}%</code>\n\n"
            "⚠️ <b>ADMIN REVIEW REQUIRED</b>\n"
            "This significant overpayment requires manual review.\n"
            "Your payment will be processed after verification.\n\n"
            "<i>Please contact support if you have any questions.</i>"
        )
    else:  # Normal overpayment
        message = (
            "💰 <b>• OVERPAYMENT DETECTED •</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>PAYMENT DETAILS</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"• <b>Required:</b> <code>${required_amount:.2f}</code>\n"
            f"• <b>Received:</b> <code>${received_amount:.2f}</code>\n"
            f"• <b>Overpayment:</b> <code>${overpayment_amount:.2f}</code>\n"
            f"• <b>Excess:</b> <code>{overpayment_percent:.1f}%</code>\n\n"
            "✅ <b>PAYMENT PROCESSED</b>\n"
            "Your payment has been processed successfully.\n"
            "The overpayment amount has been credited to your account.\n\n"
            "<i>Thank you for your payment!</i>"
        )
    
    # Create keyboard
    keyboard_buttons = [
        [
            InlineKeyboardButton(
                text="🔄 Check Status",
                callback_data=f"check_payment:{track_id}"
            ),
            InlineKeyboardButton(
                text="📊 View Balance",
                callback_data="view_balance"
            )
        ]
    ]
    
    if overpayment_percent > 50:
        keyboard_buttons.append([
            InlineKeyboardButton(
                text="📞 Contact Support",
                callback_data="contact_support"
            )
        ])
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    return message, keyboard


def create_payment_completion_message(
    track_id: str,
    received_amount: float,
    required_amount: float
) -> Tuple[str, InlineKeyboardMarkup]:
    """
    Create payment completion message.
    
    Args:
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        
    Returns:
        tuple: (message_text, keyboard)
    """
    message = (
        "🎉 <b>• PAYMENT COMPLETED •</b>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>TRANSACTION SUMMARY</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"• <b>Amount Paid:</b> <code>${received_amount:.2f}</code>\n"
        f"• <b>Transaction ID:</b> <code>{track_id}</code>\n\n"
        "✅ <b>SUCCESS</b>\n"
        "Your deposit has been processed successfully!\n"
        "Your balance has been updated.\n\n"
        "<i>Thank you for your payment!</i>"
    )
    
    # Create keyboard
    keyboard_buttons = [
        [
            InlineKeyboardButton(
                text="📊 View Balance",
                callback_data="view_balance"
            ),
            InlineKeyboardButton(
                text="📋 Transaction History",
                callback_data="transaction_history"
            )
        ],
        [
            InlineKeyboardButton(
                text="💰 Make Another Deposit",
                callback_data="deposit_funds"
            )
        ]
    ]
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    return message, keyboard


async def handle_underpayment(
    bot: Bot,
    user_id: int,
    track_id: str,
    received_amount: float,
    required_amount: float,
    payment_url: Optional[str] = None
) -> bool:
    """
    Handle underpayment detection and notification.
    
    Args:
        bot: Telegram bot instance
        user_id: User ID
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        payment_url: Payment URL for completion
        
    Returns:
        bool: True if handled successfully
    """
    try:
        remaining_amount = max(0.01, required_amount - received_amount)
        
        # Create message and keyboard
        message, keyboard = create_underpayment_message(
            track_id, received_amount, required_amount, remaining_amount, payment_url
        )
        
        # Send message to user
        await bot.send_message(
            chat_id=user_id,
            text=message,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        
        logger.info(
            f"Underpayment notification sent to user {user_id} for track_id {track_id}: "
            f"Received ${received_amount:.2f}, Required ${required_amount:.2f}, "
            f"Remaining ${remaining_amount:.2f}"
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling underpayment for {track_id}: {e}")
        return False


async def handle_overpayment(
    bot: Bot,
    user_id: int,
    track_id: str,
    received_amount: float,
    required_amount: float
) -> bool:
    """
    Handle overpayment detection and notification.
    
    Args:
        bot: Telegram bot instance
        user_id: User ID
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        
    Returns:
        bool: True if handled successfully
    """
    try:
        overpayment_amount = received_amount - required_amount
        overpayment_percent = (overpayment_amount / required_amount) * 100
        
        # Create message and keyboard
        message, keyboard = create_overpayment_message(
            track_id, received_amount, required_amount, overpayment_amount, overpayment_percent
        )
        
        # Send message to user
        await bot.send_message(
            chat_id=user_id,
            text=message,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        
        logger.info(
            f"Overpayment notification sent to user {user_id} for track_id {track_id}: "
            f"Received ${received_amount:.2f}, Required ${required_amount:.2f}, "
            f"Overpayment ${overpayment_amount:.2f} ({overpayment_percent:.1f}%)"
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling overpayment for {track_id}: {e}")
        return False


async def handle_payment_completion(
    bot: Bot,
    user_id: int,
    track_id: str,
    received_amount: float,
    required_amount: float
) -> bool:
    """
    Handle successful payment completion notification.
    
    Args:
        bot: Telegram bot instance
        user_id: User ID
        track_id: Payment tracking ID
        received_amount: Amount received
        required_amount: Amount required
        
    Returns:
        bool: True if handled successfully
    """
    try:
        # Create message and keyboard
        message, keyboard = create_payment_completion_message(
            track_id, received_amount, required_amount
        )
        
        # Send message to user
        await bot.send_message(
            chat_id=user_id,
            text=message,
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        
        logger.info(
            f"Payment completion notification sent to user {user_id} for track_id {track_id}: "
            f"Amount ${received_amount:.2f}, Bonus ${bonus_amount:.2f}"
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling payment completion for {track_id}: {e}")
        return False


def log_payment_amount_analysis(
    track_id: str,
    actual_amount: float,
    required_amount: float,
    status: str,
    details: Dict[str, Any]
) -> None:
    """
    Log payment amount analysis for monitoring.
    
    Args:
        track_id: Payment tracking ID
        actual_amount: Amount actually received
        required_amount: Amount required
        status: Payment status
        details: Additional details
    """
    percentage = details.get("percentage", 0)
    difference = details.get("difference", 0)
    
    if status == "underpayment":
        logger.warning(
            f"UNDERPAYMENT: {track_id} - Received ${actual_amount:.2f} "
            f"({percentage:.1f}% of ${required_amount:.2f}) - Shortfall: ${abs(difference):.2f}"
        )
    elif status == "overpayment":
        logger.info(
            f"OVERPAYMENT: {track_id} - Received ${actual_amount:.2f} "
            f"({percentage:.1f}% of ${required_amount:.2f}) - Excess: ${difference:.2f}"
        )
    elif status == "significant_overpayment":
        logger.warning(
            f"SIGNIFICANT OVERPAYMENT: {track_id} - Received ${actual_amount:.2f} "
            f"({percentage:.1f}% of ${required_amount:.2f}) - Excess: ${difference:.2f}"
        )
    else:
        logger.info(
            f"NORMAL PAYMENT: {track_id} - Received ${actual_amount:.2f} "
            f"({percentage:.1f}% of ${required_amount:.2f})"
        )

