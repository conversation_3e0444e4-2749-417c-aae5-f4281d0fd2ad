# API v3 Field Mapping & Display

## Overview

This document describes how API v3 response fields are mapped and displayed in the bot's UI to ensure consistency with API v1 display format.

## Sample API v3 Response

```json
{
  "_id": "01e0894a33b42a37fbaa70b3ebf6c9f8e6d1d8f8",
  "bin": "401367",
  "expiry": "10/25",
  "cardholder": "เอกพล �.",
  "card_number_status": "requires_download",
  "country": "THAILAND",
  "continent": "Asia",
  "brand": "VISA",
  "type": "DEBIT",
  "level": "CLASSIC",
  "bank": "TMBTHANACHART BANK PUBLIC COMPANY LIMITED",
  "address": "",
  "phone": "",
  "dob_available": false,
  "original_price": 8.5,
  "current_price": 4.25,
  "discount_percentage": 50,
  "is_expiring": true,
  "price": 4.25
}
```

## Field Mapping & Normalization

### Location: `handlers/catalog_handlers.py` - `_ensure_display_fields()`

| API v3 Field | Normalized Field | Purpose |
|--------------|------------------|---------|
| `discount_percentage` | `discount` | Maps to standard discount field for display formatting |
| `current_price` | `price` | Fallback to ensure price field exists |
| `original_price` | `price` | Secondary fallback for price |
| `name` / `cardholder_name` | `cardholder` | Standardizes cardholder name field |
| `card_id` | `_id` | Ensures cart operation compatibility |
| `is_expiring` | *(no mapping)* | Already correctly named, used as-is |
| `dob_available` | *(no mapping)* | Already correctly named, used as-is |

## Display Components

### 1. **Discount Badge Display**

**Location**: `utils/product_display.py` - `_build_financial_section()` (lines 914-933)

**How it works**:
```python
# Reads the normalized 'discount' field
discount = card.get('discount', 0)

# If discount > 0, shows strikethrough original price
# Example output:
💵 $8.50 → $4.25 (50% OFF)
```

**Display formats**:
- Budget (< $5): `💵 <s>$8.50</s> → <b>$4.25</b> <i>(Budget, 50% OFF)</i>`
- Standard ($5-15): `💰 <s>$8.50</s> → <b>$4.25</b> <i>(Standard, 50% OFF)</i>`
- Premium (> $15): `💎 <s>$old</s> → <b>$new</b> <i>(Premium, X% OFF)</i>`

### 2. **Expiring Status Indicator**

**Location**: `utils/product_display.py` - `_build_additional_details_section()` (lines 1215-1219)

**How it works**:
```python
is_expiring = card.get('is_expiring')
expiring_soon = card.get('expiring_soon')
if self._is_truthy_value(is_expiring) or self._is_truthy_value(expiring_soon):
    details.append("⏳ Expiring Soon")
```

**Display**: Shows as badge with text: `⏳ Expiring Soon`

**Fix Applied**: The code was previously only checking `expiring_soon` field. Updated to check both `is_expiring` (API v3) and `expiring_soon` (legacy) using a robust truthy value checker. Now displays both emoji and text for better clarity.

### 3. **DOB Available Indicator**

**Location**: `utils/product_display.py` - `_build_additional_details_section()` (lines 1209-1213)

**How it works**:
```python
dob = card.get('dob')
dob_available = card.get('dob_available')
if (dob and str(dob).strip().upper() in ['YES', 'TRUE', 'AVAILABLE', '1']) or self._is_truthy_value(dob_available):
    details.append("🎂")
```

**Display**: Shows as badge: `🎂`

**Fix Applied**: Updated to check both `dob` and `dob_available` fields using robust truthy value checker.

### 4. **Brand/Scheme Display**

**Location**: `utils/product_display.py` - `_build_type_brand_info()` (line 790)

**How it works**:
```python
brand = card.get('brand') or card.get('scheme')
```

**Display**: `🏷️ VISA`

### 5. **Card Type & Level**

**Location**: `utils/product_display.py` - `_build_type_brand_info()` (lines 776-787)

**How it works**:
```python
type_str = card.get('type')  # "DEBIT"
level = card.get('level')     # "CLASSIC"
# Combines as: "DEBIT (CLASSIC)"
```

**Display**: `💳 DEBIT (CLASSIC)`

### 6. **Bank Information**

**Location**: `utils/product_display.py` - `_build_bank_info()` (lines 741-769)

**Features**:
- Smart truncation for long bank names (max 60 chars)
- Automatic ellipsis for truncated names

**Display**: `🏛️ TMBTHANACHART BANK PUBLIC COMPANY LIMITED`

### 7. **Country Display**

**Location**: `utils/product_display.py` - `_build_personal_info_section()` (lines 824-834)

**Features**:
- Removes continent suffix (e.g., "THAILAND,Asia" → "THAILAND")
- Adds country flag emoji
- Shows with cardholder name if no address

**Display**: `👤 เอกพล �. • 🇹🇭 THAILAND`

### 8. **Expiry Date**

**Location**: `utils/product_display.py` - `_get_actual_expiry()` (lines 704-727)

**How it works**:
- Reads from `expiry` field
- Formats as MM/YY

**Display**: `⏳ 10/25`

## Complete Card Display Example

Given the sample API v3 response above, the card will be displayed as:

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💳 Card #1
━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔢 401367

🏛️ TMBTHANACHART BANK PUBLIC COMPANY LIMITED
💳 DEBIT (CLASSIC) • 🏷️ VISA

👤 เอกพล �. • 🇹🇭 THAILAND

⏳ 10/25

⏳ Expiring Soon

   💵 $8.50 → $4.25 (Budget, 50% OFF)
```

## Field Priority Order

**Location**: `utils/product_display.py` - `PRIMARY_FIELD_ORDER` (lines 138-162)

Display order:
1. BIN
2. Bank
3. Brand/Scheme
4. Type
5. Level
6. Expiry
7. Cardholder
8. Country/Location
9. Address (if available)
10. Phone (if available)
11. DOB indicator (if available)
12. Price (with discount if applicable)
13. Expiring status (if applicable)

## Hidden Fields

**Location**: `utils/product_display.py` - `HIDDEN_FIELDS` (lines 166-179)

The following fields are NOT displayed:
- `card_id` - Internal identifier
- `_id` - Internal identifier
- `continent` - Already shown with country
- `card_number_status` - Internal status
- `seller*` - Seller information
- `base` - Quality base (converted to quality display)

## Code Changes Made

### 1. Field Normalization Enhancement

**File**: `handlers/catalog_handlers.py` (lines 2878-2884)

```python
# Normalize API v3 specific fields
# Map discount_percentage to discount for consistent display
if 'discount_percentage' in card and 'discount' not in card:
    card['discount'] = card['discount_percentage']

# Ensure is_expiring is properly set (already in API v3 response)
# Keep as-is, already correctly named
```

### 2. Expiring Status Fix (CRITICAL)

**File**: `utils/product_display.py` (lines 1215-1219)

**Problem**: Code was only checking `expiring_soon` field, but API v3 returns `is_expiring`

**Solution**: Updated to check both fields with robust truthy value checker and display text with emoji
```python
is_expiring = card.get('is_expiring')
expiring_soon = card.get('expiring_soon')
if self._is_truthy_value(is_expiring) or self._is_truthy_value(expiring_soon):
    details.append("⏳ Expiring Soon")
```

### 3. Truthy Value Helper

**File**: `utils/product_display.py` (lines 183-189)

Added helper function to handle boolean/string/int values consistently:
```python
def _is_truthy_value(self, value) -> bool:
    """Check if a value should be considered True (handles bool, str, int)"""
    if value is True or value == 1:
        return True
    if isinstance(value, str):
        return value.lower() in ['true', 'yes', '1', 'on']
    return False
```

### 4. Auto-Detection Logic

Auto-detects API v3 when credentials are configured (see `API_V3_FILTER_FIX.md`)

## Verification Checklist

✅ **Discount Display**: Shows strikethrough original price and percentage  
✅ **Expiring Status**: Shows "⏳ Expiring Soon" badge with emoji and text  
✅ **DOB Available**: Shows "🎂 ✅" when true  
✅ **Brand/Scheme**: Shows correctly as "🏷️ VISA"  
✅ **Type & Level**: Shows as "💳 DEBIT (CLASSIC)"  
✅ **Bank**: Shows with proper truncation  
✅ **Country**: Shows with flag emoji  
✅ **Expiry**: Shows as MM/YY format  
✅ **Price Tiers**: Budget/Standard/Premium categorization works  

## Testing

To test the display:

1. Ensure API v3 is configured and detected
2. Browse or search for cards
3. Verify that cards show:
   - Discount badges with strikethrough prices
   - Expiring badges for cards with `is_expiring: true`
   - All fields in the correct order
   - Proper formatting and styling

## Related Files

- `handlers/catalog_handlers.py` - Field normalization
- `utils/product_display.py` - Display formatting and rendering
- `api_v3/services/browse_service.py` - API v3 data fetching
- `api_v3/adapter.py` - API v3 adapter integration

