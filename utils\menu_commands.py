"""
Menu button commands configuration for Telegram bot
"""

from __future__ import annotations

from aiogram import <PERSON><PERSON>
from aiogram.types import Bot<PERSON>ommand, BotCommandScopeDefault

from utils.central_logger import get_logger

logger = get_logger()


class MenuCommands:
    """Manages Telegram bot menu button commands"""

    # Define all available commands with descriptions
    COMMANDS = [
        BotCommand(command="start", description="🏠 Start bot / Main menu"),
        BotCommand(command="balance", description="💰 Check wallet balance"),
        # Help removed from user menu
        BotCommand(command="health", description="🔍 Bot health status"),
        BotCommand(command="version", description="📋 Bot version info"),
        BotCommand(command="dumps", description="🗂️ Browse DUMP cards"),
        BotCommand(command="admin", description="⚙️ Admin panel (admins only)"),
    ]

    # Commands available to all users
    USER_COMMANDS = [
        BotCommand(command="start", description="🏠 Start bot / Main menu"),
        BotCommand(command="balance", description="💰 Check wallet balance"),
        Bot<PERSON>ommand(command="verify", description="🔍 Verify payment manually"),
        # Help removed from user menu
        BotCommand(command="health", description="🔍 Bot health status"),
        BotCommand(command="version", description="📋 Bot version info"),
        BotCommand(command="dumps", description="🗂️ Browse DUMP cards"),
    ]

    # Additional commands for admin users
    ADMIN_COMMANDS = [
        BotCommand(command="start", description="🏠 Start bot / Main menu"),
        BotCommand(command="balance", description="💰 Check wallet balance"),
        BotCommand(command="help", description="❓ Help and support"),
        BotCommand(command="health", description="🔍 Bot health status"),
        BotCommand(command="version", description="📋 Bot version info"),
        BotCommand(command="dumps", description="🗂️ Browse DUMP cards"),
        BotCommand(command="admin", description="⚙️ Admin panel"),
        BotCommand(command="api", description="🔧 API Management"),
        BotCommand(command="processor", description="🔄 Response processor status"),
        BotCommand(command="auth_profiles", description="🔑 Auth profiles management"),
    ]

    @classmethod
    async def setup_menu_commands(cls, bot: Bot) -> None:
        """
        Set up menu button commands for the bot
        
        Args:
            bot: The aiogram Bot instance
        """
        try:
            # Set default commands for all users
            await bot.set_my_commands(
                commands=cls.USER_COMMANDS,
                scope=BotCommandScopeDefault()
            )
            
            logger.info(f"Menu button commands configured: {len(cls.USER_COMMANDS)} user commands")
            
            # Log command details for debugging
            for cmd in cls.USER_COMMANDS:
                logger.debug(f"Menu command: /{cmd.command} - {cmd.description}")
                
        except Exception as e:
            logger.error(f"Failed to set menu button commands: {e}")
            raise

    @classmethod
    async def setup_admin_commands(cls, bot: Bot, admin_user_ids: list[int]) -> None:
        """
        Set up admin-specific menu commands for admin users
        
        Args:
            bot: The aiogram Bot instance  
            admin_user_ids: List of admin user IDs
        """
        try:
            from aiogram.types import BotCommandScopeChat
            
            # Set admin commands for each admin user
            for admin_id in admin_user_ids:
                try:
                    await bot.set_my_commands(
                        commands=cls.ADMIN_COMMANDS,
                        scope=BotCommandScopeChat(chat_id=admin_id)
                    )
                    logger.debug(f"Admin menu commands set for user {admin_id}")
                except Exception as e:
                    logger.warning(f"Failed to set admin commands for user {admin_id}: {e}")
            
            if admin_user_ids:
                logger.info(f"Admin menu commands configured for {len(admin_user_ids)} admins")
                
        except Exception as e:
            logger.error(f"Failed to set admin menu commands: {e}")
            # Don't raise - this is not critical for bot functionality

    @classmethod
    async def update_commands(cls, bot: Bot, admin_user_ids: list[int] | None = None) -> None:
        """
        Update menu button commands (useful for runtime configuration changes)
        
        Args:
            bot: The aiogram Bot instance
            admin_user_ids: Optional list of admin user IDs
        """
        try:
            # Always update user commands
            await cls.setup_menu_commands(bot)
            
            # Update admin commands if admin IDs provided
            if admin_user_ids:
                await cls.setup_admin_commands(bot, admin_user_ids)
                
            logger.info("Menu button commands updated successfully")
            
        except Exception as e:
            logger.error(f"Failed to update menu commands: {e}")
            raise

    @classmethod
    def get_available_commands(cls, is_admin: bool = False) -> list[BotCommand]:
        """
        Get list of available commands for a user
        
        Args:
            is_admin: Whether the user is an admin
            
        Returns:
            List of BotCommand objects
        """
        return cls.ADMIN_COMMANDS if is_admin else cls.USER_COMMANDS

    @classmethod
    def get_command_list_text(cls, is_admin: bool = False) -> str:
        """
        Get formatted text list of available commands
        
        Args:
            is_admin: Whether the user is an admin
            
        Returns:
            Formatted string with command descriptions
        """
        commands = cls.get_available_commands(is_admin)
        
        text = "📋 <b>Available Commands:</b>\n\n"
        
        for cmd in commands:
            text += f"/{cmd.command} - {cmd.description}\n"
        
        text += "\n💡 <i>Tip: Use the menu button (☰) next to the message input to access commands quickly!</i>"
        
        return text


# Convenience functions for easy import
async def setup_menu_commands(bot: Bot) -> None:
    """Set up menu button commands for the bot"""
    await MenuCommands.setup_menu_commands(bot)


async def setup_admin_commands(bot: Bot, admin_user_ids: list[int]) -> None:
    """Set up admin-specific menu commands"""
    await MenuCommands.setup_admin_commands(bot, admin_user_ids)


async def update_menu_commands(bot: Bot, admin_user_ids: list[int] | None = None) -> None:
    """Update menu button commands"""
    await MenuCommands.update_commands(bot, admin_user_ids)


def get_command_help_text(is_admin: bool = False) -> str:
    """Get formatted command help text"""
    return MenuCommands.get_command_list_text(is_admin)