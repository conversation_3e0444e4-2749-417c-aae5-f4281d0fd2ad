# API v3 Implementation

Clean and optimized API v3 module for session-based authentication and card browsing.

## Overview

This module provides a complete API v3 integration with the following components:

- **Authentication**: Form-based login with session management and persistence
- **HTTP Client**: Optimized client with connection pooling and SOCKS proxy support
- **Browse Service**: Card listing and filtering with comprehensive parameter support
- **Cart Services**: Virtual cart management and API cart operations
- **Workflow Services**: End-to-end cart transfer and checkout workflows
- **Utilities**: Filter management, Tor detection, and SOCKS recovery

## Quick Start

### PowerShell (Windows)

```powershell
# Configure environment variables
$env:EXTERNAL_API_VERSION="v3"
$env:EXTERNAL_V3_BASE_URL="https://example.com"
$env:EXTERNAL_V3_USERNAME="your_username"
$env:EXTERNAL_V3_PASSWORD="your_password"
$env:EXTERNAL_V3_USE_TOR_PROXY="true"  # Optional for .onion domains
```

### Bash (Linux/macOS)

```bash
# Configure environment variables
export EXTERNAL_API_VERSION=v3
export EXTERNAL_V3_BASE_URL=https://example.com
export EXTERNAL_V3_USERNAME=your_username
export EXTERNAL_V3_PASSWORD=your_password
export EXTERNAL_V3_USE_TOR_PROXY=true  # Optional for .onion domains
```

## Architecture

### Module Structure

```
api_v3/
├── __init__.py          # Main exports and package information
├── adapter.py           # Unified interface adapter
├── auth/                # Authentication components
├── config/              # Configuration management
├── http/                # HTTP client with session handling
├── models/              # Data models (cards, responses)
├── services/            # Core services (browse, cart, workflow)
└── utils/               # Utilities (filters, SOCKS, Tor)
```

### Key Components

1. **APIV3Adapter**: Main entry point providing unified interface
2. **APIV3BrowseService**: Card browsing with filters and pagination
3. **APIV3CartService**: API cart operations (view, add, remove, clear)
4. **APIV3VirtualCartWorkflow**: Virtual-to-API cart transfer workflows
5. **APIV3HTTPClient**: Session-based HTTP client with proxy support

## Usage Example

```python
from api_v3 import get_api_v3_adapter

# Get adapter instance
adapter = get_api_v3_adapter()

# Browse cards with filters
result = await adapter.browse_cards(
    filters={"country": "US", "scheme": "visa"},
    page=1,
    limit=50,
    user_id="user123"
)

if result["success"]:
    cards = result["data"]
    print(f"Found {len(cards)} cards")

# Get available filters
filters = await adapter.get_filters(user_id="user123")
if filters["success"]:
    available_filters = filters["filters"]
```

## Features

### ✅ Optimized & Clean

- Removed unused imports and dependencies
- Cleaned up debug comments and redundant code
- Standardized logging with centralized logger
- Improved module organization and exports

### ✅ Performance Improvements

- Connection pooling for HTTP requests
- Optimized SOCKS proxy handling with faster recovery
- Intelligent request timeouts and retry mechanisms
- Efficient session reuse and persistence

### ✅ Comprehensive Functionality

- **5,000+ filter options** across countries, schemes, types, levels
- **Robust error handling** with graceful fallbacks
- **Session management** with automatic renewal
- **Proxy support** for .onion domains via SOCKS

### ✅ Well-Structured

- Clear separation of concerns across modules
- Consistent import patterns and exports
- Professional documentation and comments
- Type hints and proper error handling

## Configuration

All configuration is managed through environment variables with sensible defaults:

| Variable                    | Required | Description                                         |
| --------------------------- | -------- | --------------------------------------------------- |
| `EXTERNAL_V3_BASE_URL`      | Yes      | Base URL for the API                                |
| `EXTERNAL_V3_USERNAME`      | Yes      | Login username                                      |
| `EXTERNAL_V3_PASSWORD`      | Yes      | Login password                                      |
| `EXTERNAL_V3_USE_TOR_PROXY` | No       | Enable SOCKS proxy (default: false)                 |
| `EXTERNAL_V3_SOCKS_URL`     | No       | SOCKS proxy URL (default: socks5h://127.0.0.1:9150) |

## Logging Configuration

### Response Logging Control

By default, full API response logging is **disabled** to prevent log spam. To enable detailed response logging for debugging:

```bash
# Enable full API response logging
export LOG_FULL_API_RESPONSES=true

# Disable (default)
export LOG_FULL_API_RESPONSES=false
```

**Note**: When enabled, API responses are truncated to prevent excessive log sizes. Only enable for debugging purposes.

### Logging Levels

- **Disabled** (default): Only shows summary information (item counts, success/error status)
- **Enabled**: Shows full JSON responses (truncated to reasonable size)

## Status

✅ **Fully Functional and Optimized**

- All components tested and working
- Performance optimized and cleaned up
- Ready for production use
- Comprehensive error handling and logging
- Configurable logging to prevent spam
