# API v3 Discount Pricing Fix

## Problem

API v3 cards with discounts were displaying **incorrect prices** - the discount was being applied **twice**:

### Example Issue
```
API Response:
- original_price: $8.50
- current_price: $4.25 (already 50% off)
- discount_percentage: 50

UI Display (WRONG):
💰 $4.25 → $2.12 (50% OFF)
```

The UI was taking the already-discounted `current_price` ($4.25) and applying the 50% discount **again**, resulting in $2.12 instead of just showing $4.25.

### Root Cause

The price display methods were:
1. Extracting `current_price` first (which is **already discounted** by the API)
2. Then checking for `discount_percentage` field
3. Applying the discount calculation **again** to the already-discounted price

This created a **double discount** effect.

## Solution

### Fixed Files: `utils/product_display.py`

Modified 5 methods to properly handle API v3 pricing:

1. **`_build_financial_section`** (lines 898-1010) - Main card price display
2. **`_build_simplified_price_section`** (lines 1271-1361) - Simplified view
3. **`_get_centered_price_fast`** (lines 2787-2834) - Fast centered display
4. **`_get_price_fast`** (lines 2851-2891) - Quick price extraction
5. **`_format_button_price`** (lines 401-434) - Button text pricing ⚠️ NEW

### Fix Logic

**Case 1: Both `original_price` and `current_price` exist (API v3 format)**
```python
if original_price is not None and current_price is not None and original_price > current_price:
    # Prices are already calculated by API - use them directly!
    price_val = original_price
    final_price = current_price
    has_discount = True
    # Calculate discount % if not provided
    if discount_percentage <= 0:
        discount_percentage = ((original_price - current_price) / original_price) * 100
```

**Case 2: Only one price with discount percentage (needs calculation)**
```python
elif current_price is not None and discount_percentage > 0:
    # Only one price exists, so calculate the discount
    price_val = current_price
    final_price = self._calculate_discounted_price(price_val, discount_percentage)
    has_discount = True
```

**Case 3: Fallback to old format**
```python
else:
    # Try current_price, price, or original_price
    # Check for old-style 'discount' field
```

### Key Improvements

1. **Smart Detection**: Checks if both `original_price` and `current_price` exist
2. **No Recalculation**: If both prices exist, uses them **as-is** without recalculating
3. **Backwards Compatible**: Falls back to old logic for non-API v3 cards
4. **Auto-Calculate Percentage**: If discount percentage is missing but both prices exist, calculates it for display

## Testing Results

### Before Fix
```
Card Data:
{
  "original_price": 8.5,
  "current_price": 4.25,
  "discount_percentage": 50
}

Display: 💰 $4.25 → $2.12 (50% OFF) ❌
```

### After Fix
```
Card Data:
{
  "original_price": 8.5,
  "current_price": 4.25,
  "discount_percentage": 50
}

Display: 💰 $8.50 → $4.25 (50% OFF) ✅
```

## Impact

### All Fixed Locations

| Method | Purpose | Lines | Status |
|--------|---------|-------|--------|
| `_build_financial_section` | Main price display | 898-1010 | ✅ Fixed |
| `_build_simplified_price_section` | Simplified price view | 1271-1361 | ✅ Fixed |
| `_get_centered_price_fast` | Fast centered display | 2787-2834 | ✅ Fixed |
| `_get_price_fast` | Quick price extraction | 2851-2891 | ✅ Fixed |
| `_format_button_price` | Button text pricing | 401-434 | ✅ Fixed |

### Benefits

1. **Accurate Pricing**: Shows correct original and discounted prices
2. **No Double Discount**: Discount only applied once (by API)
3. **API v3 Compatible**: Properly handles `original_price`, `current_price`, `discount_percentage` fields
4. **Backwards Compatible**: Still works with older card formats
5. **Better UX**: Users see the actual deal they're getting

## Related Issues

This fix complements the earlier fix in `API_V3_PRICING_FIX.md` which addressed:
- Using API response price instead of cart cached price during checkout
- Saving correct prices to database

Together, these fixes ensure:
1. **Checkout**: Correct prices saved from API response ✅
2. **Display**: Correct prices shown in UI ✅  
3. **Discounts**: Applied once, not twice ✅

## Code Examples

### Correct API v3 Price Handling
```python
# Extract both prices
original_price = card.get('original_price')  # $8.50
current_price = card.get('current_price')    # $4.25
discount_percentage = card.get('discount_percentage')  # 50

# If both exist, they're already calculated - use directly!
if original_price and current_price and original_price > current_price:
    display = f"${original_price:.2f} → ${current_price:.2f} ({discount_percentage}% OFF)"
    # Result: $8.50 → $4.25 (50% OFF) ✅
```

### Wrong Approach (Before Fix)
```python
# WRONG: Extracting current_price and applying discount again
price = card.get('current_price')  # $4.25 (already discounted!)
discount = card.get('discount_percentage')  # 50
final = price * (1 - discount/100)  # $4.25 * 0.5 = $2.12 ❌
# Result: $4.25 → $2.12 (50% OFF) - WRONG!
```

## Testing Checklist

- [x] Verify discounted prices display correctly
- [x] Check that discount percentage matches API response
- [x] Test cards with no discount (single price)
- [x] Test cards with old discount format (backwards compatibility)
- [x] Verify free items display correctly
- [x] Test expiring items with discounts
- [x] Confirm no linter errors

## Button Price Fix

The button prices were also affected by the double discount issue. When users browse cards, the button text shows a compact view like:

**Before:**
```
1. 432265 • 🇪🇸 • $2.12 ♻️
```

**After:**
```
1. 432265 • 🇪🇸 • $4.25 ♻️
```

### Fixed Method: `_format_button_price`

This method now checks if both `original_price` and `current_price` exist. If so, it shows the `current_price` directly in the button (which is already the discounted price from the API).

```python
def _format_button_price(self, card: Dict[str, Any]) -> str:
    # Extract API v3 price fields
    original_price = card.get('original_price')
    current_price = card.get('current_price')
    
    # If both exist, current_price is already discounted - use it directly!
    if orig is not None and curr is not None and orig > curr:
        return f"${curr:.2f}"  # Show $4.25, not $2.12!
    
    # Fallback to single price for non-API v3 cards
    ...
```

---

**Status**: ✅ Fixed and Tested  
**Date**: 2025-10-25  
**Affected File**: `utils/product_display.py`  
**Methods Fixed**: 5  
**Lines Changed**: ~220

