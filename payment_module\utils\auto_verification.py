"""
Automatic Payment Verification System

This module provides comprehensive automatic payment verification with
scheduled checks, retry logic, and status monitoring.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class VerificationStatus(Enum):
    """Payment verification status enumeration."""
    PENDING = "pending"
    VERIFYING = "verifying"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"
    UNDERPAID = "underpaid"
    OVERPAID = "overpaid"


@dataclass
class VerificationTask:
    """Payment verification task data structure."""
    track_id: str
    user_id: int
    amount: float
    created_at: datetime
    last_checked: Optional[datetime] = None
    attempts: int = 0
    max_attempts: int = 10
    status: VerificationStatus = VerificationStatus.PENDING
    next_check: Optional[datetime] = None
    callback_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class AutoVerificationManager:
    """
    Automatic payment verification manager with scheduling and retry logic.
    """
    
    def __init__(self, check_interval: int = 30, max_concurrent: int = 10):
        """
        Initialize auto verification manager.
        
        Args:
            check_interval: Interval between verification checks in seconds
            max_concurrent: Maximum concurrent verification tasks
        """
        self.check_interval = check_interval
        self.max_concurrent = max_concurrent
        self.verification_tasks: Dict[str, VerificationTask] = {}
        self.running = False
        self.task_queue = asyncio.Queue()
        self.verification_callbacks: List[Callable] = []
        self.api_key: Optional[str] = None  # API key for payment verification
        self.stats = {
            "total_verifications": 0,
            "successful_verifications": 0,
            "failed_verifications": 0,
            "expired_verifications": 0,
            "underpaid_verifications": 0,
            "overpaid_verifications": 0
        }
    
    def set_api_key(self, api_key: str) -> None:
        """
        Set the API key for payment verification.
        
        Args:
            api_key: OXA Pay API key
        """
        self.api_key = api_key
        logger.info("API key set for auto verification manager")
    
    def add_verification_task(
        self, 
        track_id: str, 
        user_id: int, 
        amount: float,
        callback_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        max_attempts: int = 10
    ) -> bool:
        """
        Add a new verification task.
        
        Args:
            track_id: Payment tracking ID
            user_id: User ID
            amount: Payment amount
            callback_url: Optional callback URL
            metadata: Optional metadata
            max_attempts: Maximum verification attempts
            
        Returns:
            True if task was added successfully
        """
        if track_id in self.verification_tasks:
            logger.warning(f"Verification task for {track_id} already exists")
            return False
        
        task = VerificationTask(
            track_id=track_id,
            user_id=user_id,
            amount=amount,
            created_at=datetime.now(),
            max_attempts=max_attempts,
            callback_url=callback_url,
            metadata=metadata or {}
        )
        
        # Set initial check time (immediate for first check)
        task.next_check = datetime.now()
        
        self.verification_tasks[track_id] = task
        logger.info(f"Added verification task for {track_id}")
        
        return True
    
    def remove_verification_task(self, track_id: str) -> bool:
        """
        Remove verification task.
        
        Args:
            track_id: Payment tracking ID
            
        Returns:
            True if task was removed successfully
        """
        if track_id in self.verification_tasks:
            del self.verification_tasks[track_id]
            logger.info(f"Removed verification task for {track_id}")
            return True
        return False
    
    def get_task_status(self, track_id: str) -> Optional[VerificationStatus]:
        """
        Get task verification status.
        
        Args:
            track_id: Payment tracking ID
            
        Returns:
            Verification status or None if task not found
        """
        task = self.verification_tasks.get(track_id)
        return task.status if task else None
    
    def update_task_status(self, track_id: str, status: VerificationStatus) -> bool:
        """
        Update task verification status.
        
        Args:
            track_id: Payment tracking ID
            status: New verification status
            
        Returns:
            True if status was updated successfully
        """
        task = self.verification_tasks.get(track_id)
        if task:
            task.status = status
            logger.info(f"Updated task {track_id} status to {status.value}")
            return True
        return False
    
    async def verify_payment(self, task: VerificationTask) -> Dict[str, Any]:
        """
        Verify a single payment task.
        
        Args:
            task: Verification task
            
        Returns:
            Verification result dictionary
        """
        try:
            # Import here to avoid circular imports
            from ..core.oxa_verify import check_oxapay_payment
            from ..database.payment_operations import get_payment_by_track_id
            
            # Check if payment was already processed via callback
            existing_payment = get_payment_by_track_id(task.track_id)
            if existing_payment and existing_payment.get("status") in ["completed", "verified", "confirmed", "success", "paid"]:
                logger.info(f"⏭️  Payment {task.track_id} already completed via callback - skipping verification")
                task.status = VerificationStatus.COMPLETED
                self.stats["successful_verifications"] += 1
                return {
                    "status": "completed",
                    "message": "Payment already processed",
                    "via": "callback"
                }
            
            # Check if API key is set
            if not self.api_key:
                logger.error(f"API key not set for auto verification manager")
                return {"status": "error", "message": "API key not configured"}
            
            logger.info(f"🔄 Auto-verifying payment {task.track_id} (attempt {task.attempts + 1})")
            
            # Update task status
            task.status = VerificationStatus.VERIFYING
            task.last_checked = datetime.now()
            task.attempts += 1
            
            # Perform verification with API key
            result = await check_oxapay_payment(task.track_id, self.api_key)
            
            if result.get("status") == "completed":
                task.status = VerificationStatus.COMPLETED
                self.stats["successful_verifications"] += 1
                logger.info(f"Payment {task.track_id} verified successfully")
                
            elif result.get("status") == "failed":
                task.status = VerificationStatus.FAILED
                self.stats["failed_verifications"] += 1
                logger.warning(f"Payment {task.track_id} verification failed")
                
            elif result.get("status") == "expired":
                task.status = VerificationStatus.EXPIRED
                self.stats["expired_verifications"] += 1
                logger.warning(f"Payment {task.track_id} has expired")
                
            else:
                # Still pending, schedule next check
                task.status = VerificationStatus.PENDING
                self._schedule_next_check(task)
            
            # Update statistics
            self.stats["total_verifications"] += 1
            
            # Trigger callbacks
            await self._trigger_callbacks(task, result)
            
            return {
                "track_id": task.track_id,
                "status": task.status.value,
                "result": result,
                "attempts": task.attempts,
                "next_check": task.next_check.isoformat() if task.next_check else None
            }
            
        except Exception as e:
            logger.error(f"Error verifying payment {task.track_id}: {e}")
            task.status = VerificationStatus.FAILED
            self.stats["failed_verifications"] += 1
            
            return {
                "track_id": task.track_id,
                "status": "error",
                "error": str(e),
                "attempts": task.attempts
            }
    
    def _schedule_next_check(self, task: VerificationTask):
        """
        Schedule next verification check for task.
        
        Args:
            task: Verification task
        """
        if task.attempts >= task.max_attempts:
            task.status = VerificationStatus.EXPIRED
            logger.warning(f"Payment {task.track_id} exceeded max attempts ({task.max_attempts})")
            return
        
        # Progressive delay: 30s, 1m, 2m, 5m, 10m, 15m, 30m, 1h, 2h, 4h
        delays = [30, 60, 120, 300, 600, 900, 1800, 3600, 7200, 14400]
        delay_seconds = delays[min(task.attempts - 1, len(delays) - 1)]
        
        task.next_check = datetime.now() + timedelta(seconds=delay_seconds)
        logger.info(f"Scheduled next check for {task.track_id} in {delay_seconds}s")
    
    async def _trigger_callbacks(self, task: VerificationTask, result: Dict[str, Any]):
        """
        Trigger verification callbacks.
        
        Args:
            task: Verification task
            result: Verification result
        """
        for callback in self.verification_callbacks:
            try:
                await callback(task, result)
            except Exception as e:
                logger.error(f"Error in verification callback: {e}")
    
    def add_verification_callback(self, callback: Callable):
        """
        Add verification callback function.
        
        Args:
            callback: Callback function to call after verification
        """
        self.verification_callbacks.append(callback)
        logger.info("Added verification callback")
    
    async def process_verification_queue(self):
        """
        Process verification queue continuously.
        """
        logger.info("Starting automatic payment verification")
        
        while self.running:
            try:
                # Get tasks ready for verification
                ready_tasks = [
                    task for task in self.verification_tasks.values()
                    if (task.next_check and task.next_check <= datetime.now() and 
                        task.status in [VerificationStatus.PENDING, VerificationStatus.VERIFYING])
                ]
                
                if ready_tasks:
                    # Process tasks concurrently (limited by max_concurrent)
                    semaphore = asyncio.Semaphore(self.max_concurrent)
                    
                    async def verify_with_semaphore(task):
                        async with semaphore:
                            return await self.verify_payment(task)
                    
                    # Process ready tasks
                    tasks_to_process = ready_tasks[:self.max_concurrent]
                    await asyncio.gather(*[verify_with_semaphore(task) for task in tasks_to_process])
                
                # Clean up expired tasks
                self._cleanup_expired_tasks()
                
                # Wait for next check interval
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in verification queue processing: {e}")
                await asyncio.sleep(self.check_interval)
    
    def _cleanup_expired_tasks(self):
        """Clean up expired verification tasks."""
        current_time = datetime.now()
        expired_tasks = []
        
        for track_id, task in self.verification_tasks.items():
            # Remove tasks that are completed, failed, or expired
            if task.status in [VerificationStatus.COMPLETED, VerificationStatus.FAILED, VerificationStatus.EXPIRED]:
                expired_tasks.append(track_id)
            # Remove tasks older than 24 hours
            elif (current_time - task.created_at).total_seconds() > 86400:
                task.status = VerificationStatus.EXPIRED
                expired_tasks.append(track_id)
        
        for track_id in expired_tasks:
            del self.verification_tasks[track_id]
        
        if expired_tasks:
            logger.info(f"Cleaned up {len(expired_tasks)} expired verification tasks")
    
    async def start_verification(self):
        """Start automatic verification process."""
        if self.running:
            logger.warning("Verification is already running")
            return
        
        self.running = True
        logger.info("Starting automatic payment verification system")
        
        # Start verification queue processing
        await self.process_verification_queue()
    
    def stop_verification(self):
        """Stop automatic verification process."""
        self.running = False
        logger.info("Stopping automatic payment verification system")
    
    def get_verification_stats(self) -> Dict[str, Any]:
        """
        Get verification statistics.
        
        Returns:
            Dictionary with verification statistics
        """
        active_tasks = len([t for t in self.verification_tasks.values() 
                           if t.status in [VerificationStatus.PENDING, VerificationStatus.VERIFYING]])
        
        return {
            **self.stats,
            "active_tasks": active_tasks,
            "total_tasks": len(self.verification_tasks),
            "running": self.running,
            "check_interval": self.check_interval,
            "max_concurrent": self.max_concurrent
        }
    
    def get_task_details(self, track_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed task information.
        
        Args:
            track_id: Payment tracking ID
            
        Returns:
            Task details dictionary or None if not found
        """
        task = self.verification_tasks.get(track_id)
        if not task:
            return None
        
        return {
            "track_id": task.track_id,
            "user_id": task.user_id,
            "amount": task.amount,
            "status": task.status.value,
            "created_at": task.created_at.isoformat(),
            "last_checked": task.last_checked.isoformat() if task.last_checked else None,
            "attempts": task.attempts,
            "max_attempts": task.max_attempts,
            "next_check": task.next_check.isoformat() if task.next_check else None,
            "callback_url": task.callback_url,
            "metadata": task.metadata
        }


# Global verification manager instance
_verification_manager: Optional[AutoVerificationManager] = None


def get_verification_manager() -> AutoVerificationManager:
    """
    Get global verification manager instance.
    
    Returns:
        AutoVerificationManager instance
    """
    global _verification_manager
    if _verification_manager is None:
        _verification_manager = AutoVerificationManager()
    return _verification_manager


def start_auto_verification():
    """
    Start automatic payment verification system.
    """
    manager = get_verification_manager()
    asyncio.create_task(manager.start_verification())


def stop_auto_verification():
    """
    Stop automatic payment verification system.
    """
    manager = get_verification_manager()
    manager.stop_verification()


# Utility functions
async def add_payment_for_verification(
    track_id: str, 
    user_id: int, 
    amount: float,
    callback_url: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Add payment for automatic verification.
    
    Args:
        track_id: Payment tracking ID
        user_id: User ID
        amount: Payment amount
        callback_url: Optional callback URL
        metadata: Optional metadata
        
    Returns:
        True if payment was added successfully
    """
    manager = get_verification_manager()
    return manager.add_verification_task(track_id, user_id, amount, callback_url, metadata)


def get_payment_verification_status(track_id: str) -> Optional[VerificationStatus]:
    """
    Get payment verification status.
    
    Args:
        track_id: Payment tracking ID
        
    Returns:
        Verification status or None if not found
    """
    manager = get_verification_manager()
    return manager.get_task_status(track_id)


def get_verification_statistics() -> Dict[str, Any]:
    """
    Get verification statistics.
    
    Returns:
        Dictionary with verification statistics
    """
    manager = get_verification_manager()
    return manager.get_verification_stats()
