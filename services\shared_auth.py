"""
Shared Authentication Module

Provides unified authentication configuration for both API v1 and API v2.
This module consolidates all authentication logic to eliminate duplication
and ensure both APIs use identical credentials and session cookies.
"""

import os
from typing import Dict, Optional, Any
from dataclasses import dataclass
from dotenv import load_dotenv

from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class SharedAuthConfig:
    """Unified authentication configuration for all APIs"""
    login_token: str
    session_cookies: Dict[str, str]
    headers: Dict[str, str]
    
    @property
    def cookie_header(self) -> str:
        """Get formatted cookie header string"""
        return "; ".join(f"{k}={v}" for k, v in self.session_cookies.items() if v)


def get_shared_auth_config() -> Optional[SharedAuthConfig]:
    """
    Get unified authentication configuration for both API v1 and API v2.
    
    This is the single source of truth for authentication credentials.
    Both APIs should use this function to get their authentication config.
    
    Returns:
        SharedAuthConfig with login token and session cookies, or None if not available
    """
    try:
        # Use centralized authentication helper to eliminate duplicate code
        from utils.auth_helper import AuthenticationHelper
        
        login_token = AuthenticationHelper.get_login_token()
        
        if not login_token:
            logger.warning("No EXTERNAL_LOGIN_TOKEN found - authentication will not work")
            return None
        
        session_cookies = AuthenticationHelper.get_session_cookies()
        
        # Build headers with cookie
        headers = {}
        if session_cookies:
            cookie_header = "; ".join(f"{k}={v}" for k, v in session_cookies.items())
            headers["cookie"] = cookie_header
        
        return SharedAuthConfig(
            login_token=login_token,
            session_cookies=session_cookies,
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"Failed to get shared authentication config: {e}")
        return None


def get_api_v1_compatible_config() -> Optional[Dict[str, Any]]:
    """
    Get authentication config in API v1 compatible format.
    
    This provides the same structure that ExternalAPIService expects,
    but using the shared authentication source.
    
    Returns:
        Dictionary with login_token and session_cookies for API v1
    """
    auth_config = get_shared_auth_config()
    if not auth_config:
        return None
    
    return {
        "login_token": auth_config.login_token,
        "session_cookies": auth_config.session_cookies,
        "headers": {}  # API v1 builds its own headers
    }


def get_api_v2_compatible_config() -> Optional[Dict[str, Any]]:
    """
    Get authentication config in API v2 compatible format.
    
    This provides the same structure that API v2 configuration expects,
    but using the shared authentication source.
    
    Returns:
        Dictionary with authentication details for API v2
    """
    auth_config = get_shared_auth_config()
    if not auth_config:
        return None
    
    return {
        "authentication": {
            "type": "session_cookies",
            "login_token": auth_config.login_token,
        },
        "credentials": {
            "login_token": auth_config.login_token,
            "session_cookies": auth_config.session_cookies,
            "headers": {}  # No Authorization header needed for session cookies
        },
        "shared_config": {
            "authentication": {
                "login_token": auth_config.login_token,
                "session_cookies": auth_config.session_cookies
            },
            "default_headers": auth_config.headers,
            "session_cookies": auth_config.session_cookies
        }
    }


def build_default_headers_with_auth() -> Dict[str, str]:
    """
    Build default headers including authentication cookies.
    
    This can be used by both APIs to get a consistent set of headers
    with proper authentication.
    
    Returns:
        Dictionary of headers including cookie authentication
    """
    auth_config = get_shared_auth_config()
    
    # Base headers (common to both APIs)
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Content-Type": "application/json",
        "accept": "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        "origin": "https://ronaldo-club.to",
        "referer": "https://ronaldo-club.to/",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    }
    
    # Add authentication cookies if available
    if auth_config and auth_config.headers:
        headers.update(auth_config.headers)
    
    return headers


# Backward compatibility functions for existing code
def get_session_cookies() -> Dict[str, str]:
    """Get session cookies for backward compatibility"""
    auth_config = get_shared_auth_config()
    return auth_config.session_cookies if auth_config else {}


def get_login_token() -> str:
    """Get login token for backward compatibility"""
    auth_config = get_shared_auth_config()
    return auth_config.login_token if auth_config else ""
