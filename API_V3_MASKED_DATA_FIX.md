# API v3 Masked Data Display Fix

## Issue
API v3 orders were showing masked card data (e.g., `3752************`) but being treated as "unmasked", causing:
- Download button shown instead of Unmask button
- `show_sensitive=True` set incorrectly
- User confusion about card state

### Root Cause
Unlike API v1 which returns full card data immediately, API v3:
1. Returns masked card numbers initially (like `3752************`)
2. Requires explicit unmask operation to get full card numbers
3. System was treating any extracted card data as "unmasked"

## Solution

Added API v3-specific logic to validate card data and only mark as unmasked when card number has NO asterisks.

### File Modified

**handlers/orders_handlers.py** (Lines 2454-2496)

### Before Fix

```python
# Determine if card is unmasked
is_unmasked = self._is_unmask_data(order)
# For API v1, if we have extracted_cards with card data, it's already unmasked
if (api_version == "v1" and "extracted_cards" in order and order["extracted_cards"]):
    card_data = order["extracted_cards"][0]
    # If card has cc/cvv/exp fields, it's unmasked
    if card_data.get("cc") or card_data.get("cvv"):
        is_unmasked = True
```

**Problem**: No specific handling for API v3, so masked cards were treated as unmasked.

### After Fix

```python
# Determine if card is unmasked
is_unmasked = self._is_unmask_data(order)

# For API v1, if we have extracted_cards with card data, it's already unmasked
if (api_version == "v1" and "extracted_cards" in order and order["extracted_cards"]):
    card_data = order["extracted_cards"][0]
    # If card has cc/cvv/exp fields, it's unmasked
    if card_data.get("cc") or card_data.get("cvv"):
        is_unmasked = True
        logger.info(f"✅ [View Card] API v1 card is unmasked (has cc/cvv data)")

# For API v3, cards are ONLY unmasked if they have been explicitly unmasked (no asterisks in card number)
elif api_version == "v3":
    # Check if card actually has unmasked data (not just extracted data)
    if "extracted_cards" in order and order["extracted_cards"]:
        card_data = order["extracted_cards"][0]
        card_number = card_data.get("card_number") or card_data.get("cc", "")
        if card_number:
            # If card has asterisks, it's masked (not unmasked)
            has_asterisks = "*" in card_number
            is_redacted = "[PAN_REDACTED]" in card_number or "[UNMASKED" in card_number
            if has_asterisks or is_redacted:
                # Card has masked data - NOT unmasked
                is_unmasked = False
                logger.info(f"🔒 [API v3] Card has masked data (asterisks/redacted) - treating as masked: {card_number[:12]}...")
            else:
                # Card number is full (no asterisks) - it's unmasked
                is_unmasked = True
                logger.info(f"✅ [API v3] Card has full unmasked data (no asterisks)")
        else:
            # No card number - not unmasked
            is_unmasked = False
            logger.info(f"🔒 [API v3] No card number found - treating as masked")
    else:
        # No extracted cards - not unmasked
        is_unmasked = False
        logger.info(f"🔒 [API v3] No extracted cards - treating as masked")
```

## API v3 Validation Logic

For API v3, the system now validates card numbers and marks as unmasked ONLY when:

1. ✅ Card has extracted_cards with card_number
2. ✅ Card number has NO asterisks (`"*" not in card_number`)
3. ✅ Card number has NO redaction markers (no `[PAN_REDACTED]` or `[UNMASKED`)

If ANY of these fail, card is marked as `is_unmasked=False`.

## New Log Messages

### For Masked Cards (Initial State)
- `🔒 [API v3] Card has masked data (asterisks/redacted) - treating as masked: 3752********...`
- `🔒 [API v3] No card number found - treating as masked`
- `🔒 [API v3] No extracted cards - treating as masked`

### For Unmasked Cards (After Unmask Operation)
- `✅ [API v3] Card has full unmasked data (no asterisks)`

### For API v1 (No Change)
- `✅ [View Card] API v1 card is unmasked (has cc/cvv data)`

## Updated Behavior

### Scenario 1: API v3 Card Just Purchased

**Before Fix**:
- Card Data: `3752************` (masked)
- System: `is_unmasked=True` (incorrect)
- Display: Showed as "unmasked" with masked number
- Button: "📥 Download Card Data" (wrong)
- Log: `✅ [View Card] API {v3} unmasked path`

**After Fix**:
- Card Data: `3752************` (masked)
- System: `is_unmasked=False` (correct)
- Display: Shows as masked
- Button: "🔓 Unmask Card" (correct)
- Log: `🔒 [API v3] Card has masked data (asterisks/redacted) - treating as masked`

### Scenario 2: API v3 Card After Unmask Operation

**Before Fix**:
- Card Data: `3752567890123456` (full number)
- System: `is_unmasked=True` (correct)
- Display: Showed full number
- Button: "📥 Download Card Data" (correct)

**After Fix**:
- Card Data: `3752567890123456` (full number)
- System: `is_unmasked=True` (correct)
- Display: Shows full number (same)
- Button: "📥 Download Card Data" (same)
- Log: `✅ [API v3] Card has full unmasked data (no asterisks)`

### Scenario 3: API v1 Card (No Change)

**Before Fix**:
- Card Data: Has `cc` and `cvv` fields
- System: `is_unmasked=True`
- Button: "📥 Download Card Data"

**After Fix**:
- Card Data: Has `cc` and `cvv` fields
- System: `is_unmasked=True` (same)
- Button: "📥 Download Card Data" (same)
- Log: `✅ [View Card] API v1 card is unmasked (has cc/cvv data)`

## Key Differences: API v1 vs API v3

### API v1
- Returns full card data immediately on view/checkout
- Cards with `cc` or `cvv` fields are automatically unmasked
- No need for separate unmask operation
- **No changes made to API v1 logic**

### API v3  
- Returns masked data initially (`3752************`)
- Requires explicit unmask operation via `/orders/{id}/unmask`
- Only cards with full numbers (no asterisks) are marked as unmasked
- **New validation logic added specifically for API v3**

## Testing Checklist

### API v3 Tests
- [ ] Purchase API v3 card → Should show masked data (`****`)
- [ ] Click on API v3 card → Should show "🔓 Unmask Card" button
- [ ] Log should show: `🔒 [API v3] Card has masked data`
- [ ] Click unmask → Should show full card number
- [ ] Click on unmasked API v3 card → Should show "📥 Download" button
- [ ] Log should show: `✅ [API v3] Card has full unmasked data`

### API v1 Tests (Should Not Change)
- [ ] Purchase API v1 card → Should show full data immediately
- [ ] Click on API v1 card → Should show "📥 Download Card Data" button
- [ ] Log should show: `✅ [View Card] API v1 card is unmasked`
- [ ] No unmask button should appear for API v1

## Benefits

1. **Correct Initial State**: API v3 cards start as masked (not unmasked)
2. **Proper Button Logic**: Shows unmask button for masked API v3 cards
3. **Clear User Flow**: Purchase → View (masked) → Unmask → View (unmasked)
4. **API v1 Unchanged**: No changes to working API v1 logic
5. **Better Logging**: Clear indication of card state per API version

## Related Fixes

This fix builds on previous improvements:
1. **CARD_DISPLAY_IMPROVEMENTS.md**: Database data prioritization
2. **API_V3_POST_CHECKOUT_FIXES.md**: Complete data storage
3. **MASKED_DATA_DISPLAY_FIX.md**: General masked data validation

Together, these ensure:
- ✅ Complete data stored in database
- ✅ Database data prioritized for cached cards
- ✅ Masked data validated and shown correctly
- ✅ **API v3 has specific masked/unmasked logic** (THIS FIX)

## Implementation Date
October 26, 2025

## Summary

API v3 now has proper masked/unmasked detection based on actual card data content (asterisks check), not just database flags. This ensures:

- **Masked cards show as masked** → Unmask button appears
- **Unmasked cards show as unmasked** → Download button appears  
- **API v1 unchanged** → Works as before
- **Clear logging** → Easy to debug card state

The key principle: **API v3 cards are only unmasked when card number has NO asterisks.**

