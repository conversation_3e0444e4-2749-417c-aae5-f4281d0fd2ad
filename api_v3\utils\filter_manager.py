"""
Smart Filter Manager for API v3

Provides intelligent, data-driven filter management with:
- Dynamic country flag detection from filter.json data
- Smart country name normalization and mapping
- Automatic fallback and caching system
- Integration with centralized flag management
"""

from __future__ import annotations

import json
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Tuple
from functools import lru_cache

from utils.central_logger import get_logger

logger = get_logger()

# Centralized filter file path
FILTER_DATA_DIR = Path("data/filters")
FILTER_RESPONSE_FILE = FILTER_DATA_DIR / "filter_response.json"

# Cache for filter data to prevent excessive loading
_filter_cache: Optional[Dict[str, Any]] = None
_cache_timestamp: Optional[float] = None
_cache_ttl: float = 300.0  # 5 minutes cache TTL


def get_centralized_filters() -> Dict[str, Any]:
    """
    Get filter data from the centralized filter_response.json file.
    Uses caching to prevent excessive loading.

    Returns:
        Dictionary containing filter data, or empty dict if not available
    """
    global _filter_cache, _cache_timestamp
    
    # Check cache first
    current_time = time.time()
    if (_filter_cache is not None and 
        _cache_timestamp is not None and 
        (current_time - _cache_timestamp) < _cache_ttl):
        return _filter_cache
    
    try:
        if not FILTER_RESPONSE_FILE.exists():
            logger.warning(f"Centralized filter file not found: {FILTER_RESPONSE_FILE}")
            result = _get_fallback_filters()
            _filter_cache = result
            _cache_timestamp = current_time
            return result

        with open(FILTER_RESPONSE_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)

        # Expect clean format (just the filters array)
        if isinstance(data, list):
            filters = data
            logger.info(f"Using centralized filter data with {len(data)} filter groups")
        else:
            # Handle legacy format for backward compatibility (but prefer clean format)
            filters = data.get("filters", [])
            logger.warning(
                "Using legacy format with metadata (consider regenerating with fresh login)"
            )

        logger.info(f"Loaded {len(filters)} filter groups from centralized file")

        result = {
            "success": True,
            "filters": filters,
            "source": "centralized",
        }
        
        # Cache the result
        _filter_cache = result
        _cache_timestamp = current_time
        
        return result

    except Exception as e:
        logger.error(f"Failed to load centralized filters: {e}")
        result = _get_fallback_filters()
        _filter_cache = result
        _cache_timestamp = current_time
        return result


def clear_filter_cache() -> None:
    """
    Clear the filter cache to force reload on next access.
    Should be called when new filter data is saved.
    """
    global _filter_cache, _cache_timestamp
    _filter_cache = None
    _cache_timestamp = None
    logger.debug("Filter cache cleared")


def get_filter_options_by_name(filter_name: str) -> List[Dict[str, Any]]:
    """
    Get options for a specific filter by name.

    Args:
        filter_name: Name of the filter (e.g., 'country[]', 'scheme[]')

    Returns:
        List of filter options
    """
    try:
        filter_data = get_centralized_filters()
        if not filter_data.get("success"):
            return []

        filters = filter_data.get("filters", [])

        for filter_group in filters:
            if filter_group.get("name") == filter_name:
                options = filter_group.get("options", [])
                logger.info(f"Found {len(options)} options for {filter_name}")
                return options

        logger.warning(f"Filter '{filter_name}' not found in centralized data")
        return []

    except Exception as e:
        logger.error(f"Failed to get options for filter '{filter_name}': {e}")
        return []


def convert_filters_to_standard_format() -> Dict[str, List[str]]:
    """
    Convert API v3 filter response to standard format used by the bot.
    Now includes smart country processing and flag integration.

    Returns:
        Dictionary with standardized filter format
    """
    try:
        filter_data = get_centralized_filters()
        if not filter_data.get("success"):
            return {}

        filters = filter_data.get("filters", [])

        # Standard format expected by the bot
        standard_filters = {
            "countries": [],
            "continents": [],
            "schemes": [],
            "types": [],
            "levels": [],
            "banks": [],
            "brands": [],
        }

        for filter_group in filters:
            filter_name = filter_group.get("name", "")
            options = filter_group.get("options", [])

            # Extract just the values
            values = [opt.get("value", "") for opt in options if opt.get("value")]

            # Map API v3 filter names to standard names
            if filter_name == "country[]":
                # Smart country processing with flag integration
                processed_countries = _process_countries_smart(options)
                standard_filters["countries"] = processed_countries
                # Update dynamic flag mappings
                _update_dynamic_flag_mappings(options)
            elif filter_name == "continent[]":
                standard_filters["continents"] = values
            elif filter_name == "scheme[]":
                standard_filters["schemes"] = values
                standard_filters["brands"] = values  # brands = schemes for API v3
            elif filter_name == "type[]":
                standard_filters["types"] = values
            elif filter_name == "level[]":
                standard_filters["levels"] = values
            elif filter_name == "selected_bank":
                # Filter out empty bank names and special entries
                bank_values = []
                for value in values:
                    value = value.strip()
                    if (
                        value
                        and not value.startswith("- Empty")
                        and not value.startswith("(")
                    ):
                        bank_values.append(value)
                standard_filters["banks"] = bank_values  # Full dataset for pagination

        logger.info(
            f"Converted to standard format with {sum(len(v) for v in standard_filters.values())} total options"
        )
        return standard_filters

    except Exception as e:
        logger.error(f"Failed to convert filters to standard format: {e}")
        return {}


def is_filter_data_available() -> bool:
    """
    Check if centralized filter data is available.

    Returns:
        True if filter data exists and is accessible
    """
    try:
        return FILTER_RESPONSE_FILE.exists() and FILTER_RESPONSE_FILE.is_file()
    except Exception:
        return False


def get_filter_data_info() -> Dict[str, Any]:
    """
    Get information about the current filter data.

    Returns:
        Dictionary with filter data metadata
    """
    try:
        if not is_filter_data_available():
            return {
                "available": False,
                "message": "Filter data not available. Please login with API v3 to generate it.",
            }

        with open(FILTER_RESPONSE_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)

        # Handle clean format (preferred) and legacy format
        if isinstance(data, list):
            filters = data
            timestamp_str = "clean format"
            age_str = "no metadata (fetched from API)"
            base_url = "from API"
        else:
            # Legacy format with metadata (deprecated)
            filters = data.get("filters", [])
            timestamp_str = data.get("timestamp", "unknown")
            base_url = data.get("base_url", "unknown")
            age_str = "legacy format"

        total_options = sum(len(f.get("options", [])) for f in filters)

        return {
            "available": True,
            "file_path": str(FILTER_RESPONSE_FILE),
            "timestamp": timestamp_str,
            "age": age_str,
            "base_url": base_url,
            "filter_groups": len(filters),
            "total_options": total_options,
            "filter_names": [f.get("name") for f in filters],
        }

    except Exception as e:
        return {"available": False, "error": str(e)}


def _get_fallback_filters() -> Dict[str, Any]:
    """
    Get fallback filter data when centralized file is not available.

    Returns:
        Basic fallback filter structure
    """
    logger.info("Using fallback filter data")

    return {
        "success": False,
        "filters": [],
        "source": "fallback",
        "message": "Centralized filter data not available. Please login with API v3 to generate it.",
    }


def refresh_filter_data_from_demo() -> bool:
    """
    Copy filter data from demo file if available (for development/testing).

    NOTE: This is for fallback only. For production, use API login to fetch fresh data.

    Returns:
        True if successfully loaded from live API
    """
    try:
        # Note: Demo filter file was removed during cleanup
        # This function now relies on fetching fresh data from the API
        logger.info("Loading filter data from live API instead of demo file")
        
        # This function now intentionally returns False to force fresh API fetch
        return False

        # Save in clean format (just the filters array, no metadata)
        clean_filter_data = (
            demo_data if isinstance(demo_data, list) else demo_data.get("filters", [])
        )

        with open(FILTER_RESPONSE_FILE, "w", encoding="utf-8") as f:
            json.dump(clean_filter_data, f, indent=2, ensure_ascii=False)

        logger.info(
            f"✅ Successfully copied demo filter data to {FILTER_RESPONSE_FILE}"
        )
        logger.warning(
            "⚠️ Using demo data. For fresh data, login with API v3 credentials."
        )
        return True

    except Exception as e:
        logger.error(f"Failed to refresh from demo data: {e}")
        return False


def force_refresh_filters() -> bool:
    """
    Force refresh filter data by removing existing file.
    Next login will fetch fresh data from API.

    Returns:
        True if existing filter file was removed
    """
    try:
        if FILTER_RESPONSE_FILE.exists():
            FILTER_RESPONSE_FILE.unlink()
            logger.info(
                "🗑️ Removed existing filter data. Next login will fetch fresh data."
            )
            return True
        else:
            logger.info("No existing filter data to remove.")
            return True
    except Exception as e:
        logger.error(f"Failed to remove filter data: {e}")
        return False


# Smart Country Processing Functions

@lru_cache(maxsize=1)
def _get_country_data() -> List[Dict[str, Any]]:
    """Get cached country data from filter.json"""
    try:
        filter_data = get_centralized_filters()
        if not filter_data.get("success"):
            return []
            
        filters = filter_data.get("filters", [])
        for filter_group in filters:
            if filter_group.get("name") == "country[]":
                return filter_group.get("options", [])
        return []
    except Exception as e:
        logger.error(f"Failed to get country data: {e}")
        return []


def _process_countries_smart(country_options: List[Dict[str, Any]]) -> List[str]:
    """
    Smart processing of country data with normalization and deduplication.
    
    Args:
        country_options: Raw country options from filter.json
        
    Returns:
        Processed list of country names
    """
    try:
        processed_countries = set()
        
        for option in country_options:
            country_name = option.get("value", "").strip()
            if not country_name:
                continue
                
            # Normalize country names
            normalized = _normalize_country_name(country_name)
            if normalized:
                processed_countries.add(normalized)
                
        result = sorted(list(processed_countries))
        logger.info(f"Processed {len(result)} unique countries from {len(country_options)} options")
        return result
        
    except Exception as e:
        logger.error(f"Failed to process countries: {e}")
        return [opt.get("value", "") for opt in country_options if opt.get("value")]


def _normalize_country_name(country_name: str) -> Optional[str]:
    """
    Normalize country name for consistent processing.
    
    Args:
        country_name: Raw country name from API
        
    Returns:
        Normalized country name or None if invalid
    """
    if not country_name or not isinstance(country_name, str):
        return None
        
    # Basic cleanup
    normalized = country_name.strip().upper()
    
    # Handle special cases from API data
    if ", " in normalized:
        # Handle "VENEZUELA, BOLIVARIAN REPUBLIC OF" -> "VENEZUELA"
        if "REPUBLIC OF" in normalized:
            normalized = normalized.split(", ")[0]
        # Handle "VIRGIN ISLANDS, BRITISH" -> "BRITISH VIRGIN ISLANDS"
        elif "VIRGIN ISLANDS" in normalized:
            parts = normalized.split(", ")
            if len(parts) == 2:
                normalized = f"{parts[1]} {parts[0]}"
        # Handle other special cases
        elif "DEMOCRATIC REPUBLIC" not in normalized:
            normalized = normalized.split(", ")[0]
    
    # Skip empty or invalid entries
    if len(normalized) < 2:
        return None
        
    return normalized


def _update_dynamic_flag_mappings(country_options: List[Dict[str, Any]]) -> None:
    """
    Update dynamic flag mappings based on actual country data from filter.json.
    This creates a smart mapping that adapts to the API's country list.
    
    Args:
        country_options: Country options from filter.json
    """
    try:
        from utils.country_flags import flag_manager
        
        # Build dynamic mappings for countries in filter.json
        dynamic_mappings = {}
        countries_with_flags = 0
        
        for option in country_options:
            country_name = option.get("value", "").strip()
            if not country_name:
                continue
                
            # Normalize for mapping
            normalized = _normalize_country_name(country_name)
            if not normalized:
                continue
                
            # Check if we already have a flag for this country
            existing_flag = flag_manager.get_flag(normalized)
            if existing_flag != "🌍":
                countries_with_flags += 1
                # Add both original and normalized forms
                dynamic_mappings[country_name.upper()] = existing_flag
                if normalized != country_name.upper():
                    dynamic_mappings[normalized] = existing_flag
            else:
                # Try to find flag through intelligent matching
                smart_flag = _find_flag_smart(normalized)
                if smart_flag and smart_flag != "🌍":
                    countries_with_flags += 1
                    dynamic_mappings[country_name.upper()] = smart_flag
                    dynamic_mappings[normalized] = smart_flag
        
        # Store dynamic mappings for use by flag system
        _store_dynamic_mappings(dynamic_mappings)
        
        logger.info(
            f"Updated dynamic flag mappings: {countries_with_flags}/{len(country_options)} "
            f"countries have flags ({countries_with_flags/len(country_options)*100:.1f}%)"
        )
        
    except Exception as e:
        logger.error(f"Failed to update dynamic flag mappings: {e}")


def _find_flag_smart(country_name: str) -> Optional[str]:
    """
    Intelligent flag detection using various matching strategies.
    
    Args:
        country_name: Normalized country name
        
    Returns:
        Flag emoji or None if not found
    """
    try:
        from utils.country_flags import flag_manager
        
        # Strategy 1: Direct lookup
        flag = flag_manager.get_flag(country_name)
        if flag != "🌍":
            return flag
            
        # Strategy 2: Common variations
        variations = _generate_country_variations(country_name)
        for variation in variations:
            flag = flag_manager.get_flag(variation)
            if flag != "🌍":
                return flag
                
        # Strategy 3: Partial matching for complex names
        if len(country_name) > 10:  # Only for longer names
            words = country_name.split()
            for word in words:
                if len(word) > 3:  # Skip short words like "OF", "THE"
                    flag = flag_manager.get_flag(word)
                    if flag != "🌍":
                        return flag
        
        return None
        
    except Exception as e:
        logger.error(f"Failed smart flag detection for {country_name}: {e}")
        return None


def _generate_country_variations(country_name: str) -> List[str]:
    """
    Generate common variations of a country name for flag matching.
    
    Args:
        country_name: Base country name
        
    Returns:
        List of variations to try
    """
    variations = []
    
    # Remove common suffixes/prefixes
    clean_name = country_name
    
    # Remove "REPUBLIC OF", "KINGDOM OF", etc.
    for prefix in ["REPUBLIC OF ", "KINGDOM OF ", "UNITED REPUBLIC OF ", "ISLAMIC REPUBLIC OF "]:
        if clean_name.startswith(prefix):
            clean_name = clean_name[len(prefix):]
            variations.append(clean_name)
            
    # Remove "THE " prefix
    if clean_name.startswith("THE "):
        variations.append(clean_name[4:])
        
    # Handle "DEMOCRATIC REPUBLIC OF THE CONGO" -> "CONGO"
    if "DEMOCRATIC REPUBLIC" in clean_name and "CONGO" in clean_name:
        variations.extend(["CONGO", "DRC", "DEMOCRATIC REPUBLIC OF THE CONGO"])
        
    # Handle "KOREA" variations
    if "KOREA" in clean_name:
        if "DEMOCRATIC" in clean_name:
            variations.extend(["NORTH KOREA", "DPRK"])
        else:
            variations.extend(["SOUTH KOREA", "KOREA"])
            
    # Handle "RUSSIAN FEDERATION" -> "RUSSIA"
    if "RUSSIAN FEDERATION" in clean_name:
        variations.append("RUSSIA")
        
    # Handle "UNITED STATES" variations
    if "UNITED STATES" in clean_name:
        variations.extend(["USA", "US", "AMERICA"])
        
    # Handle "UNITED KINGDOM" variations
    if "UNITED KINGDOM" in clean_name:
        variations.extend(["UK", "BRITAIN", "ENGLAND"])
    
    return variations


# Dynamic mapping storage
_dynamic_flag_mappings: Dict[str, str] = {}

def _store_dynamic_mappings(mappings: Dict[str, str]) -> None:
    """Store dynamic flag mappings for use by other components"""
    global _dynamic_flag_mappings
    _dynamic_flag_mappings.update(mappings)
    
def get_dynamic_flag_mappings() -> Dict[str, str]:
    """Get current dynamic flag mappings"""
    return _dynamic_flag_mappings.copy()


# Smart Filter Interface Functions

def get_countries_with_flags() -> List[Tuple[str, str]]:
    """
    Get list of countries from filter.json with their corresponding flags.
    
    Returns:
        List of (country_name, flag_emoji) tuples
    """
    try:
        country_data = _get_country_data()
        results = []
        
        for option in country_data:
            country_name = option.get("value", "").strip()
            if not country_name:
                continue
                
            # Get flag using smart detection
            from utils.country_flags import flag_manager
            flag = flag_manager.get_flag(country_name)
            
            # Try dynamic mappings if no flag found
            if flag == "🌍" and country_name.upper() in _dynamic_flag_mappings:
                flag = _dynamic_flag_mappings[country_name.upper()]
                
            results.append((country_name, flag))
            
        logger.debug(f"Retrieved {len(results)} countries with flag data")
        return results
        
    except Exception as e:
        logger.error(f"Failed to get countries with flags: {e}")
        return []


def get_filter_statistics() -> Dict[str, Any]:
    """
    Get comprehensive statistics about filter data and flag coverage.
    
    Returns:
        Dictionary with filter statistics
    """
    try:
        filter_data = get_centralized_filters()
        if not filter_data.get("success"):
            return {"available": False, "error": "Filter data not available"}
            
        filters = filter_data.get("filters", [])
        stats = {
            "available": True,
            "total_filter_groups": len(filters),
            "filter_breakdown": {},
            "flag_coverage": {},
        }
        
        for filter_group in filters:
            filter_name = filter_group.get("name", "unknown")
            options = filter_group.get("options", [])
            stats["filter_breakdown"][filter_name] = len(options)
            
            # Calculate flag coverage for countries
            if filter_name == "country[]":
                countries_with_flags = 0
                for option in options:
                    country_name = option.get("value", "")
                    if country_name:
                        from utils.country_flags import flag_manager
                        flag = flag_manager.get_flag(country_name)
                        if flag != "🌍":
                            countries_with_flags += 1
                            
                stats["flag_coverage"] = {
                    "total_countries": len(options),
                    "countries_with_flags": countries_with_flags,
                    "coverage_percentage": (countries_with_flags / len(options) * 100) if options else 0,
                }
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get filter statistics: {e}")
        return {"available": False, "error": str(e)}
