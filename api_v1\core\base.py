"""
API v1 Core Base Classes

Defines base classes and common functionality used throughout the API v1 system.
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .constants import DEFAULT_CACHE_TTL
from .exceptions import APIv1Exception

from utils.central_logger import get_logger

logger = get_logger()


@dataclass
class BaseResponse:
    """Base response class for all API operations"""
    success: bool
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class BaseService(ABC):
    """
    Base service class providing common functionality for all services.
    
    Provides:
    - Logging setup
    - Error handling patterns
    - Caching infrastructure
    - Common utility methods
    """

    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = get_logger()
        self._cache: Dict[str, tuple] = {}
        self._cache_ttl = DEFAULT_CACHE_TTL

    def _get_cache_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        key_parts = [str(arg) for arg in args]
        key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
        return f"{self.service_name}:{':'.join(key_parts)}"

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """Get value from cache if not expired"""
        if cache_key in self._cache:
            value, timestamp = self._cache[cache_key]
            if (datetime.utcnow() - timestamp).seconds < self._cache_ttl:
                return value
            else:
                # Remove expired entry
                del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, value: Any):
        """Set value in cache with timestamp"""
        self._cache[cache_key] = (value, datetime.utcnow())

    def _clear_cache(self, pattern: Optional[str] = None):
        """Clear cache entries, optionally matching a pattern"""
        if pattern is None:
            self._cache.clear()
        else:
            keys_to_remove = [k for k in self._cache.keys() if pattern in k]
            for key in keys_to_remove:
                del self._cache[key]

    async def _handle_error(self, error: Exception, operation: str) -> BaseResponse:
        """Handle errors consistently across services"""
        error_msg = f"Error in {operation}: {str(error)}"
        self.logger.error(error_msg, exc_info=True)
        
        if isinstance(error, APIv1Exception):
            return BaseResponse(
                success=False,
                message=error.message,
                error_code=error.error_code,
            )
        else:
            return BaseResponse(
                success=False,
                message=error_msg,
                error_code="UNKNOWN_ERROR",
            )

    @abstractmethod
    async def health_check(self) -> BaseResponse:
        """Perform health check for the service"""
        pass


class BaseManager(ABC):
    """
    Base manager class for coordinating multiple services.
    
    Provides:
    - Service lifecycle management
    - Dependency injection
    - Event coordination
    """

    def __init__(self, manager_name: str):
        self.manager_name = manager_name
        self.logger = get_logger()
        self._services: Dict[str, BaseService] = {}
        self._initialized = False

    def register_service(self, name: str, service: BaseService):
        """Register a service with the manager"""
        self._services[name] = service
        self.logger.debug(f"Registered service: {name}")

    def get_service(self, name: str) -> Optional[BaseService]:
        """Get a registered service by name"""
        return self._services.get(name)

    async def initialize(self):
        """Initialize all registered services"""
        if self._initialized:
            return

        self.logger.info(f"Initializing {self.manager_name}...")
        
        for name, service in self._services.items():
            try:
                if hasattr(service, 'initialize'):
                    await service.initialize()
                self.logger.debug(f"Initialized service: {name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize service {name}: {e}")
                raise

        self._initialized = True
        self.logger.info(f"{self.manager_name} initialized successfully")

    async def shutdown(self):
        """Shutdown all registered services"""
        if not self._initialized:
            return

        self.logger.info(f"Shutting down {self.manager_name}...")
        
        for name, service in self._services.items():
            try:
                if hasattr(service, 'shutdown'):
                    await service.shutdown()
                self.logger.debug(f"Shutdown service: {name}")
            except Exception as e:
                self.logger.error(f"Error shutting down service {name}: {e}")

        self._initialized = False
        self.logger.info(f"{self.manager_name} shutdown complete")

    async def health_check_all(self) -> Dict[str, BaseResponse]:
        """Perform health check on all services"""
        results = {}
        
        for name, service in self._services.items():
            try:
                results[name] = await service.health_check()
            except Exception as e:
                results[name] = BaseResponse(
                    success=False,
                    message=f"Health check failed: {str(e)}",
                    error_code="HEALTH_CHECK_ERROR",
                )
        
        return results


class SingletonMeta(type):
    """Metaclass for implementing singleton pattern"""
    _instances = {}
    _lock = asyncio.Lock()

    async def __call__(cls, *args, **kwargs):
        async with cls._lock:
            if cls not in cls._instances:
                instance = super().__call__(*args, **kwargs)
                cls._instances[cls] = instance
            return cls._instances[cls]


class AsyncSingleton(metaclass=SingletonMeta):
    """Base class for async singletons"""
    pass


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator for retrying failed operations
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff: Multiplier for delay on each retry
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {current_delay}s..."
                        )
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(
                            f"All {max_retries + 1} attempts failed for {func.__name__}"
                        )
            
            raise last_exception
        
        return wrapper
    return decorator
